
.card-content{
    width: 100%;
}
.wrap-box{
    width: 100%;
    color: #333;
}
.card-step{
    background: #fff;
    border-bottom: 1px solid #EAEAEA;
    margin-top: 0 !important;
    height: 60px;
}
.title{
    width: 100%;
    height: 60px;
    line-height: 60px;
    font-size: 16px;
    font-weight: bold;
    padding-left: 24px;
    border-bottom: 1px solid #EAEAEA;
}
/* 标签选择 */
.tab-box{
    padding:20px 0;
}
.tab-box .item{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}
.tab-box .item .tab{
    display: inline-block;
    width: 100px;
    font-weight: bold;
}
.tab-box .item a{
    margin-right: 40px;
}
.tab-box .el-radio-button__orig-radio:checked+.el-radio-button__inner{
    background: #fff;
    color: #32AF50;
    border: 0;
}
.tab-box .el-radio-button__inner{
    border: 0;
}
.tab-box .el-radio-button:first-child .el-radio-button__inner{
    border: 0;
}
.tab-box .el-radio-button__orig-radio:checked+.el-radio-button__inner{
    -webkit-box-shadow: -1px 0 0 0 #fff;
    box-shadow: -1px 0 0 0 #fff;
}
.icon-plus{
    font-size: 35px;
    color: #DCDADA;
}
/* 卡数据列表 */
.card-wrap{
    border: 1px solid #E7E7E7;
    margin: 0 0 20px;
    background-color: #fff;
    padding-bottom: 20px;
}
.card-box{
    display: flex;
    flex-wrap: wrap;
    margin: 20px 20px 0 20px;
}
.card-box .item{
    position: relative;
    width: 270px;
    height: 160px;
    color: #fff;
    font-size: 20px;
    font-weight: bold;
    text-align: center;
    border-radius: 8px;
    margin: 28px;
    border: 1px solid #EAEAEA;
    background: url("../images/card_bg.png") no-repeat center center;
    cursor: pointer;
}
.card-box .item.defaultCard{
    background: url("../images/card_default.png") no-repeat center center;
}
.card-box .item.disableCard{
    background: url("../images/card_bg_disabled.png") no-repeat center center;
}
.card-box .item.first-item{
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction:column;
    line-height: 2;
    border: 1px dashed #E7E7E7;
    cursor: pointer;
    background: none;
}
.card-box .item.first-item:hover .icon-plus{
    color: #32AF50;
}
.card-box .item .tips{
    position: absolute;
    right: 0;
    top: 0;
    font-size: 14px;
    font-weight: normal;
    height: 24px;
    line-height: 24px;
    background: #888;
    padding-right: 12px;
    padding-left: 8px;
    border-radius: 100px 70px 0 100px;
    color: #fff;
}
.card-box .item .edit{
    position: absolute;
    bottom: 18px;
    left: 13px;
    height: 32px;
    font-size: 14px;
    font-weight: bolder;
    color: #fff;
}
.card-box .item .card-id{
    position: absolute;
    bottom: 18px;
    right: 13px;
    font-size: 14px;
    font-weight: bolder;
    color: #fff;
}
.card-box .name{
    display: inline-block;
    margin-top: 27px;
    text-align: left;
    width: 250px;
    overflow: hidden;
    text-overflow:ellipsis;
    white-space:nowrap;
}
.card-box .time{
    font-size: 14px;
    display: inline-block;
    margin-top: 5px;
    text-align: left;
    width: 250px;
    overflow: hidden;
    font-weight: normal;
    text-overflow:ellipsis;
    /* white-space:nowrap; */
}
.card-pagination{
    margin-left: 30px;
}

/* 基础设置 */
.card-form{
    margin: 30px 0 0 20px;
}
.card-form i{
    color:rgb(153, 153, 153);
    font-style: normal;
    margin-left:10px;
}
.card-form .warning-icon{
    color: #F56C6C;
    font-size: 14px;
    margin-left: 60px;
}
.card-form .warning-rule{
    font-size: 14px;
    margin-left: 9px;
    margin-bottom: 88px;
    display: inline-block;
    color: #2c3e50;
}

.base-form{
    margin: 30px 0 0 20px;
}
.base-form textarea{
    height: 100%;
}
/*充值设置*/
.recharge-form .title,.spend-form .title{
    padding-left: 0;
    font-size: 14px;
    height: 50px;
    line-height: 50px;
}
.TopUpCeiling {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: flex-start;
}
.card-tooltip{
    border: 0 !important;
    box-shadow:0px 0px 5px 0px rgba(0,0,0,0.1);
}
.el-tooltip__popper.is-light[x-placement^=right] .popper__arrow{
    border-right-color: #eee;
}
.el-form-item{
    margin-bottom: 20px;
}
.el-form-item.last-item{
    margin-bottom: 10px;
}
.card-radio-group{
    display: flex;
    flex-direction: column;
}
.card-radio-group .el-radio{
    margin-right: 5px;
}
.btn-box{
    margin: 0 0 30px 130px;
}

/* 消费设置 */
.radio-form{
    margin: 20px;
}
.tips-icon{
    display: inline-block;
    border-radius: 50%;
}
.spend-form .el-form-item:last-child{
    margin-left: 110px;
    margin-top: 56px;
}

/* 页码 */
.page_content {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
}