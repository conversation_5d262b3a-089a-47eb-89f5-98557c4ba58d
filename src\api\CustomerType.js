import {get, post} from "../utils/http";

/**
 * 获取公司类型信息
 * @function getCompanyType
 * @param {Object} data - 请求参数对象
 * @param {number} data.group_id - 必填，集团ID，类型为整数
 * @param {string} data.bh - 必填，客户编号，类型为字符串
 * @param {string} data.type - 必填，客户类型，类型为字符串
 * @param {string} data.page - 必填，分页标识，类型为字符串
 * @param {string} data.page_size - 必填，分页数量，类型为字符串
 * @param {string} data.status - 必填，状态。空或不传表示全部（默认），'0'表示禁用，'1'表示启用
 * @param {string} data.s_time - 必填，开始时间，类型为字符串
 * @param {string} [data.e_time] - 可选，结束时间，类型为字符串
 * @returns {Promise<AxiosResponse>} 返回包含公司类型信息的axios响应Promise对象
 */
export function getCompanyType(data) {
  return get('/CustomerGroup/getCompanyTypeList', data);
}


/**
 * 新增客户类型信息
 * @function addCompanyType
 * @param {Object} data - 请求参数对象
 * @param {string} data.bh - 必填，客户编号，类型为字符串
 * @param {string} data.type - 必填，客户类型，类型为字符串
 * @param {number} data.group_id - 必填，集团ID，类型为整数
 * @param {string} [data.remark] - 可选，备注信息，类型为字符串
 * @returns {Promise<AxiosResponse>} 返回新增操作结果的axios响应Promise对象
 */
export function addCompanyType(data) {
  return post('/CustomerGroup/addCompanyType', data);
}


/**
 * 修改客户类型信息
 * @function updateCompanyType
 * @param {Object} data - 请求参数对象
 * @param {number} data.group_id - 必填，集团ID，类型为整数
 * @param {string} data.bh - 必填，客户编号，类型为字符串
 * @param {string} data.type - 必填，客户类型，类型为字符串
 * @param {number} data.status - 必填，状态，'0'表示禁用，'1'表示启用
 * @param {string} [data.remark] - 可选，备注信息，类型为字符串
 * @returns {Promise<AxiosResponse>} 返回修改操作结果的axios响应Promise对象
 */
export function updateCompanyType(data) {
  return post('/CustomerGroup/updateCompanyType', data);
}

/**
 * 下载客户类型信息
 * @function downloadCompanyType
 * @param {Object} data - 请求参数对象
 * @param {number} data.group_id - 必填，集团ID，类型为整数
 * @param {string} data.bh - 必填，客户编号，类型为字符串
 * @param {string} data.type - 必填，客户类型，类型为字符串
 * @param {string} data.status - 必填，状态。空/不传表示全部（默认），'0'表示禁用，'1'表示启用
 * @param {string} data.s_time - 必填，开始时间，类型为字符串
 * @param {string} [data.e_time] - 可选，结束时间，类型为字符串
 * @returns {Promise<AxiosResponse>} 返回下载文件的axios响应Promise对象
 */
export default function downloadCompanyType(data) {
  return post('/CustomerGroup/downloadCompanyType', data, { responseType: 'blob' });
}
