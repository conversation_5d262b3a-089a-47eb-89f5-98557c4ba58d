<template>
    <div class="report">
        <div class="report-content">
            <el-radio-group v-model="typeValue" @change="changeTypeValue">
                <el-radio-button v-for="item in typeOptions" :key="item.value" :label="item.value">{{item.label}}</el-radio-button>
            </el-radio-group>
            <div class="content-header">
                <span class="txt" v-if="getCurrentStation.merchant_type == 2">油站名称</span>
                <el-select v-model="stationId" :multiple="typeValue == 1" clearable collapse-tags style="width:250px;margin-right:20px;" placeholder="请选择油站" v-if="getCurrentStation.merchant_type == 2 && update" @change="changeStationValue">
                    <el-option
                    v-for="(item,index) in stationOptions"
                    :key="index"
                    :label="item.stname"
                    :value="item.stid">
                    </el-option>
                </el-select>
                <el-date-picker
                v-show="typeValue == 1"
                style="margin-right:15px"
                v-model="dateValue"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="EndTime"
                @change="clearData">
                </el-date-picker>
                <banci-date-time
                    ref="banciRef"
                    :stationValue="stationId"
                    :dateValue="dateBanciValue"
                    @searchDate="searchBanciDate"
                    @changeDate="changeBnaciDate"
                    :picker-options="EndTime"
                    v-show="typeValue == 4">
                </banci-date-time>
                <!-- <el-date-picker
                v-show="typeValue == 4"
                style="margin-right:15px"
                v-model="dateBanciValue"
                type="date"
                placeholder="选择日期"
                format="yyyy 年 MM 月 dd 日"
                value-format="yyyy-MM-dd"
                @change="clearData">
                </el-date-picker>
                <span class="txt" v-show="typeValue == 4">查看班次</span>
                <el-checkbox-group v-model="checkList" v-show="typeValue == 4" @change="searchBanci" class="banci">
                    <el-checkbox v-for="item in classList" :key="item.index" :label="item">{{item.bcmc}}</el-checkbox>
                </el-checkbox-group>
                <span v-show="typeValue == 4 && !classList.length " class="banci">暂无班次</span> -->
                <span class="txt">商品</span>
                <el-select v-model="goodsValue" style="width:100px;margin-right:20px;" placeholder="请选择商品">
                    <el-option
                    v-for="(item,index) in goodsOptions"
                    :key="index"
                    :label="item.oil_name"
                    :value="item.oil_id">
                    </el-option>
                </el-select>
                <span class="txt" v-show="typeValue == 1">车队名称</span>
                <el-select v-show="typeValue == 1" v-model="companyValue" filterable style="width:130px;margin-right:20px;" placeholder="请选择车队">
                    <el-option
                    v-for="(item,index) in companyOptions"
                    :key="index"
                    :label="item.CompanyName"
                    :value="item.ID">
                    </el-option>
                </el-select>
                <span class="txt" v-show="typeValue == 1">卡类型</span>
                <el-select v-show="typeValue == 1" v-model="cardTypeValue" style="width:130px;margin-right:20px;" placeholder="请选择卡类型">
                    <el-option
                    v-for="(item,index) in cardTypeOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value">
                    </el-option>
                </el-select>
                <span class="txt" v-show="typeValue == 1">卡名称</span>
                <el-select v-show="typeValue == 1" v-model="cardValue" filterable style="width:130px;margin-right:20px;" placeholder="请选择卡名称">
                    <el-option
                    v-for="(item,index) in cardOptions"
                    :key="index"
                    :label="item.name"
                    :value="item.ID">
                    </el-option>
                </el-select>
                <div style="margin-top:10px" v-show="typeValue == 1">
                    <span class="txt">查询类型</span>
                    <el-radio-group v-model="searchTypeVlaue">
                        <el-radio label="1">手机号</el-radio>
                        <el-radio label="0">卡号</el-radio>
                        <el-radio label="2">卡面卡号</el-radio>
                        <el-radio label="4">持卡人</el-radio>
                    </el-radio-group>
                    <el-input v-model="inputTxt" style="width:210px"
                    :placeholder="searchTypeVlaue=='1'?'请输入手机号'
                    :searchTypeVlaue=='0'?'请输入卡号'
                    :searchTypeVlaue=='2'?'请输入卡面卡号':'请输入持卡人名称'" clearable></el-input>
                    <el-button type="primary" @click="page=1;tableData=[];noMore=true;changeDate()" :disabled="!dateValue">生成</el-button>
                </div>
                <el-button v-show="typeValue == 4" type="primary" @click="page=1;tableData=[];noMore=true;changeDate()" :disabled="!dateBanciValue">生成</el-button>

            </div>
            <div div class="search-box">
                <el-button type="primary" :disabled="tableData.length == 0" @click="printContent">打印</el-button>
                <el-button type="primary" :disabled="tableData.length == 0" @click="cardChargeDownload" v-show="isTotalReportForm">下载数据</el-button>
            </div>

            <div id="myTable" >
                <div class="tableData reportData">
                    <!-- 班结日报 -->
                    <div class="report_title">储值卡消费明细报表</div>
                    <div class="report_header">
                        <div v-if="isGroup">集团名称：{{getCurrentStation.label}}</div>
                        <div v-else>油站名称：{{getCurrentStation.label}}</div>
                        <!-- <div v-if="typeValue == 4">日期：{{dateBanciValue}}</div> -->
                        <div >开始日期：{{dateValue?dateValue[0]:""}}</div>
                        <div >结束日期：{{dateValue?dateValue[1]:""}}</div>
                        <div>单位：元</div>
                    </div>
                    <el-table :data="tableData" border v-loading="loading" size="small" align="center" ref="table">
                        <el-table-column align="center" prop="activate_card_stid_name" label="开卡油站" width="160" :formatter="formatterCellval"></el-table-column>
                        <el-table-column align="center" prop="stid_name" label="消费油站" width="160" v-if="getCurrentStation.merchant_type == 2" :formatter="formatterCellval"></el-table-column>
                        <el-table-column align="center" prop="card_no" label="卡号" width="160" :formatter="formatterCellval"></el-table-column>
                        <el-table-column align="center" prop="card_number" label="卡面卡号" :formatter="formatterCellval"> </el-table-column>
                        <el-table-column align="center" prop="phone" label="手机号" width="100" :formatter="formatterCellval"></el-table-column>
                        <el-table-column align="center" prop="cardholder_name" label="持卡人" :formatter="formatterCellval"></el-table-column>
                        <el-table-column align="center" prop="car_no" label="车牌号" :formatter="formatterCellval"></el-table-column>
                        <el-table-column align="center" prop="company_name" label="车队名称" :formatter="formatterCellval" width="120"></el-table-column>
                        <el-table-column align="center" prop="goods_name" label="商品名称" :formatter="formatterCellval"> </el-table-column>
                        <el-table-column align="center" prop="gun_name" label="油枪" :formatter="formatterCellval" width="60"></el-table-column>
                        <el-table-column align="center" prop="han_price" label="挂牌价" :formatter="formatterCellval" width="60"></el-table-column>
                        <el-table-column align="center" prop="sl" label="升量" :formatter="formatterCellval" width="70"></el-table-column>
                        <el-table-column align="center" prop="origin_price" label="订单原价" :formatter="formatterCellval" width="70"></el-table-column>
                        <el-table-column align="center" prop="credit_price" label="积分抵现" :formatter="formatterCellval" width="55"></el-table-column>
                        <el-table-column align="center" prop="dis_money" label="价格优惠" :formatter="formatterCellval" width="55"></el-table-column>
                        <el-table-column align="center" prop="coupon_money" label="券优惠" :formatter="formatterCellval" width="60"></el-table-column>
                        <el-table-column align="center" prop="total_dis_money" label="总优惠" :formatter="formatterCellval" width="60"></el-table-column>
                        <el-table-column align="center" prop="dis_price" label="实付单价" :formatter="formatterCellval" width="70"></el-table-column>
                        <el-table-column align="center" prop="pay_price" label="支付金额" :formatter="formatterCellval" width="70"></el-table-column>
                        <el-table-column align="center" prop="pay_money" label="支付本金" :formatter="formatterCellval" width="70"></el-table-column>
                        <el-table-column align="center" prop="git_money" label="支付赠金" :formatter="formatterCellval" width="70"></el-table-column>
                        <el-table-column align="center" prop="credit_money" label="信用额度支付" :formatter="formatterCellval" width="70"></el-table-column>
                        <el-table-column align="center" label="卡余额" :formatter="formatterCellval" width="70">
                          <template slot-scope="scope">
                            <span v-if="scope.row.only_mz == 1">--</span>
                            <span v-else>{{scope.row.card_balance}}</span>
                          </template>
                        </el-table-column>
                        <el-table-column align="center" label="卡本金" :formatter="formatterCellval" width="70">
                          <template slot-scope="scope">
                            <span v-if="scope.row.only_mz == 1">--</span>
                            <span v-else>{{scope.row.card_money}}</span>
                          </template>
                        </el-table-column>
                        <el-table-column align="center" label="卡赠金" :formatter="formatterCellval" width="70">
                          <template slot-scope="scope">
                            <span v-if="scope.row.only_mz == 1">--</span>
                            <span v-else>{{scope.row.git_balance}}</span>
                          </template>
                        </el-table-column>
                        <el-table-column align="center" prop="card_name" label="卡名称" width="160" :formatter="formatterCellval"></el-table-column>
                        <el-table-column align="center" prop="card_type_name" label="卡类型" :formatter="formatterCellval"></el-table-column>
                        <el-table-column align="center" prop="order_no" label="订单号" width="160" :formatter="formatterCellval"></el-table-column>
                        <el-table-column align="center" prop="pay_time" label="交易时间" width="140" :formatter="formatterCellval"></el-table-column>
                        <el-table-column align="center" prop="discount_mark" label="价格优惠详情" width="280" :formatter="formatterCellval"></el-table-column>
                    </el-table>
                    <!-- 页码 -->
                    <div class="page_content">
                        <el-pagination class="page_left"
                            @current-change="handleCurrentChange"
                            :current-page="page"
                            :page-size="pageSize"
                            layout="prev, pager, next"
                            :total="total">
                        </el-pagination>
                        <el-pagination class="page_right"
                            @size-change="handleSizeChange"
                            :page-sizes="[20, 30, 40, 50]"
                            :page-size="pageSize"
                            layout="total, sizes"
                            :total="total">
                        </el-pagination>
                    </div>
                </div>
                <!-- <p v-if="!noMore" style="cursor: pointer; color:#32AF50;text-align: center;" @click="scrollMore">点击加载更多</p> -->
                <div class="table_des" >
                    <div class="table_des_text">
                        <p>注：</p>
                        <div>
                            <p>1.车队子卡仅使用母账扣款的订单，卡余额显示为：-</p>
                            <p class="stations" v-if="stationId.length != stationOptions.length && isGroup">2.取数油站：{{stationName}}</p>
                        </div>
                    </div>
                </div>
                <div class="des_bottom">
                    <div>制表人：{{orderMaker}}</div>
                    <div>制表时间：{{orderMakingTime}}</div>
                    <div>签字：</div>
                </div>
            </div>
        </div>

        <!-- 下载中心提示 -->
        <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>
    </div>
</template>

<script>
import axiosReport from "axios"
import DownloadTips from '../DownloadTips.vue';
import BanciDateTime from '../Banci/banciDateTime.vue'
import {mapGetters} from 'vuex'
import {EndTime} from "./endTime";
export default {
    name: 'SpendDetailReport',
    components:{
        DownloadTips,
        BanciDateTime
    },
    data () {
        return {
          EndTime:EndTime,
          isTotalReportForm: true,
            typeOptions:[{
                value:1,
                label:"按自然日期",
            },{
                value:4,
                label:"按班结日期",
            }],
            typeValue:1,
            dateValue:[],
            dateBanciValue:"",
            stationId:[],//选中油站
            stationName:"",//所选油站名称
            stationOptions: [],//油站列表
            companyOptions:[{
                ID: '0',
                CompanyName: '全部'
            }],
            companyValue:"0",//选中车队
            goodsOptions:[
                {oil_name:"全部",oil_id:"0"}
            ],
            goodsValue:"0",
            cardTypeOptions:[{
            value: '0',
            label: '全部'
            },{
            value: '1',
            label: '个人卡'
            },{
            value: '2',
            label: '车队卡'
            },{
            value: '3',
            label: '不记名卡'
            }],
            cardTypeValue:'0',
            cardOptions:[
                {name:"全部",ID:"0"}
            ],
            cardValue:"0",
            searchTypeVlaue:"1",
            inputTxt:"",
            tableData: [],
            loading: false,
            noMore:true,
            page:1,
            pageSize:20,
            total:0,
            start_time:"",
            end_time:"",
            orderMaker:"",
            orderMakingTime:"",
            isGroup:true,//是否是集团账号
            showDownloadTips:false,

            checkList:[],   //选择班次
            classList:[],   //班次列表
            update:true,
            nowEndTime: "",
        }
    },
    mounted(){
        let userInfo = localStorage.getItem("__userInfo__") || "";
        this.isTotalReportForm = JSON.parse(userInfo).group_id != 426;
        //自然日默认为前一天的数据
        let currentDateStart = this.$moment(new Date()).subtract(1,'days').format('YYYY-MM-DD HH:mm:ss');
        let currentDateEnd = this.$moment().format('YYYY-MM-DD HH:mm:ss');
        this.dateValue = [currentDateStart,currentDateEnd];
        this.nowEndTime = currentDateEnd.slice(11)
        //班结默认为前一天的数据
        let _today = this.$moment();
        let yesterday = _today.subtract(1, 'days').format('YYYY-MM-DD');
        this.dateBanciValue = yesterday;

        if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
            return false;
        }
        if(this.getCurrentStation && this.getCurrentStation.merchant_type == 2){
            this.isGroup = true;
        }else{
            this.isGroup = false;
        }
        this.getStationList();
        this.getCompanyList();
        this.getOilList();
        this.getCardDatas();
        this.changeDate();
    },
    computed:{
        ...mapGetters({
            "getCurrentStation":"getCurrentStation"
        })
    },
    methods: {
        changeBnaciDate(value){
            this.dateBanciValue = value
        },
        //查询班次
        searchBanciDate(value){
            this.dateValue = value
            this.changeDate()
        },
        searchBanci(e){
            if(e.length){
                let startTime = []
                let endTime = []
                //获取时间戳存入数组中
                startTime = e.map(item=> new Date(item.stime).getTime())
                endTime = e.map(item=> new Date(item.etime).getTime())
                //判断大小，选出最小值为开始时间 最大值为结束时间
                let searchEndTime = Math.max.apply(null, endTime)
                let searchStartTime = Math.min.apply(null, startTime)

                this.dateValue[0] = this.$moment(searchStartTime).format('YYYY-MM-DD hh:mm:ss')
                this.dateValue[1] = this.$moment(searchEndTime).format('YYYY-MM-DD hh:mm:ss')
                this.$forceUpdate()
            }else{
                this.dateValue = [];
                let startDate = this.$moment(new Date(this.dateBanciValue))
                let endDate = this.$moment(new Date(this.dateBanciValue));
                this.dateValue.push(this.$moment(startDate).format('YYYY-MM-DD')+ ' 00:00:00');
                this.dateValue.push(this.$moment(endDate).format('YYYY-MM-DD')+ ` ${this.nowEndTime}`);
            }
        },
        //加载更多
        scrollMore() {
            this.page += 1;
            this.changeDate();
        },
        //获取商品列表
        getOilList(){
            let that = this;
            this.$axios.post('/CardReport/getOil',{
                stids:this.stationId
            }).then((res)=>{
                if(res.data.status == 200){
                    that.goodsOptions = [{
                        oil_id: '0',
                        oil_name: '全部'
                    }];
                    that.goodsOptions = that.goodsOptions.concat(res.data.data);
                }
            })
        },
        //获取卡列表
        getCardDatas(){
            let that = this;
            this.$axios.post('/CardReport/getCardDatas',{
                state:0,
                station_id:this.stationId,
                page:1,
                page_size:100,
            }).then((res)=>{
                if(res.data.status == 200){
                    that.cardOptions = [{
                        ID: '0',
                        name: '全部'
                    }];
                    that.cardOptions = that.cardOptions.concat(res.data.data);
                }
            })
        },
        //改变油站，相对于商品列表和卡列表都得变化
        changeStationValue(e){
            this.stationName = "";
            let len = this.stationId.length;
            if(this.typeValue == 1){
                this.stationId.forEach((item,index)=>{
                    this.stationOptions.forEach((subitem)=>{
                        if(subitem.stid == item){
                            if(index == len-1){
                                this.stationName += subitem.stname;

                            }else{
                                this.stationName += subitem.stname + "，";
                            }
                        }
                    })
                })
            }else{
                this.stationOptions.forEach((subitem)=>{
                    if(subitem.stid == e){
                        this.stationName = subitem.stname;
                    }
                })
            }
            if(this.typeValue == 1){
                this.getOilList();
                this.getCardDatas();
            }else{
                this.getOilList();
            }
            //获取班次
            if(this.typeValue == 4 && this.stationId){
                this.$refs.banciRef.getBanci(e)
                // this.getSettlementList()
            }
        },
        //获取可用油站
        getStationList(){
            this.stationId = [];
            let that = this;
            this.$axios.post('/Stations/getStationList',{}).then((res)=>{
                if(res.status == 200){
                    that.stationOptions = [];
                    res.data.data.forEach((item)=>{
                        that.stationId.push(item.stid);
                    })
                    that.stationOptions = res.data.data;
                }
            })
        },
        //获取车队信息列表
        getCompanyList(){
            let that = this;
            that.$axios.post('/CompanyCard/getSimpleCompanyList', {
                page: 1,
                page_size: 1250,
                input: "",
            })
            .then(function (res) {
                if(res.data.status == 200){
                    that.companyOptions = [{
                        ID: '0',
                        CompanyName: '全部'
                    }];
                    that.companyOptions = that.companyOptions.concat(res.data.data.dt);
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
            });
        },
        changeTypeValue(e){
             //判断是否是单站，单站默认选中油站
            if(e == 4){
                this.stationId = ''
                this.$refs.banciRef.clearDate()
            }else{
                this.stationId = []
            }
            if(this.getCurrentStation.merchant_type == 1){
                this.stationId = this.getCurrentStation.merchant_id
            }
            //自然日默认为前一天的数据
            let currentDateStart = this.$moment(new Date()).subtract(1,'days').format('YYYY-MM-DD HH:mm:ss');
            let currentDateEnd = this.$moment().format('YYYY-MM-DD HH:mm:ss');
            this.dateValue = [currentDateStart,currentDateEnd];
            //班结默认为前一天的数据
            let _today = this.$moment();
            let yesterday = _today.subtract(1, 'days').format('YYYY-MM-DD');
            this.dateBanciValue = yesterday;
            this.companyValue = "0";
            this.goodsValue = "0";
            this.cardTypeValue = "0";
            this.cardValue = "0";

            this.orderMakingTime = "";
            this.tableData = [];
            this.page = 1;
        },
        //选中时间生成报表
        changeDate(){
            let that = this;
            that.loading = true;
            let params = {};
            if(that.dateValue[0].length==10) that.dateValue[0]=that.dateValue[0]+' 00:00:00'
            if(that.dateValue[1].length==10) {
                if(that.dateValue[1] == that.$moment(new Date()).format('YYYY-MM-DD')){
                    that.dateValue[1] = that.dateValue[1]+` ${this.$moment().format('HH:mm:ss')}`;
                }else{
                    that.dateValue[1] = that.dateValue[1]+' 23:59:59'
                }
            }
            if(that.typeValue == 1){
                params = {
                    page_index: that.page,
                    page_size: that.pageSize,
                    start_time: that.dateValue?that.dateValue[0]:"",
                    end_time: that.dateValue?that.dateValue[1]:"",
                    company_id:that.companyValue,//车队id
                    card_type:that.cardTypeValue,//卡类型
                    card_id:that.cardValue,//卡名称
                    oil_id:that.goodsValue,//商品
                    type:1,
                    input_type:that.searchTypeVlaue,
                    input:that.inputTxt,
                    stid:that.stationId
                }
            }else{
                params = {
                    page_index: that.page,
                    page_size: that.pageSize,
                    start_time: that.dateValue?that.dateValue[0]:"",
                    end_time: that.dateValue?that.dateValue[1]:"",
                    oil_id:that.goodsValue,//商品
                    type:1,
                    stid:[that.stationId]
                }
            }

            that.$axios.post('/CardReport/getOsCostDetails', params)
            .then(function (res) {
                that.loading = false;
                that.tableData = []
                if(res.data.status == 200){
                    that.orderMakingTime = that.$moment().format("YYYY-MM-DD");
                    that.noMore = res.data.data.pageInfo.pageIndex * res.data.data.pageInfo.pageSize >= res.data.data.pageInfo.totalCount ? true : false ;
                    that.total = res.data.data.pageInfo.totalCount;
                    that.pageSize = res.data.data.pageInfo.pageSize;
                    that.tableData = that.tableData.concat(res.data.data.order_list);
                    let userInfo = localStorage.getItem('__userInfo__');
                    if(userInfo && (userInfo !== "" || userInfo !== "undefined")){
                        that.orderMaker = JSON.parse(userInfo).name;
                    }
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
            });
        },
        clearData(e){
            if(!this.dateValue){
                this.tableData = [];
                this.orderMakingTime = "";
                this.orderMaker = "";
            }
            if(
              this.$moment(this.dateValue[1]).format('YYYY-MM-DD') === this.$moment().format('YYYY-MM-DD') &&
              this.$moment(this.dateValue[1]).unix() > this.$moment().unix()
            ){
              this.dateValue[1] = this.$moment().format('YYYY-MM-DD HH:mm:ss')
            }
        },
        //打印
        printContent(){
            let wpt = document.querySelector('#myTable');
            let newContent = wpt.innerHTML;
            let oldContent = document.body.innerHTML;
            document.body.innerHTML = newContent;
            document.getElementsByClassName("el-table__header")[0].style.width = "100%";
            document.getElementsByClassName("el-table__header")[0].style["table-layout"] = "auto";
            document.getElementsByClassName("el-table__body")[0].style.width = "100%";
            document.getElementsByClassName("el-table__body")[0].style["table-layout"] = "auto";
            window.print(); //打印方法
            history.go(0);
            document.body.innerHTML = oldContent;
        },
        //切换页码
        handleCurrentChange(val){
            this.page = val;
            this.changeDate();
        },
        handleSizeChange(val){
            this.pageSize = val;
            this.changeDate();
        },
        //导出
        cardChargeDownload(){
            let params = {};
            let that = this;
            if(this.typeValue == 1){
                params = {
                    page_index: that.page,
                    page_size: that.pageSize,
                    start_time: that.dateValue?that.dateValue[0]:"",
                    end_time: that.dateValue?that.dateValue[1]:"",
                    company_id:that.companyValue,//车队id
                    card_type:that.cardTypeValue,//卡类型
                    card_id:that.cardValue,//卡名称
                    oil_id:that.goodsValue,//商品
                    type:1,
                    input_type:that.searchTypeVlaue,
                    input:that.inputTxt,
                    stid:that.stationId
                }
            }else{
                params = {
                    page_index: that.page,
                    page_size: that.pageSize,
                    start_time: that.dateValue?that.dateValue[0]:"",
                    end_time: that.dateValue?that.dateValue[1]:"",
                    oil_id:that.goodsValue,//商品
                    type:1,
                    stid:[that.stationId]
                }
            }
            this.$axios.get('/CardReport/osCostDetailsDownload',{params:params}).then((res)=>{
                if(res.data.status == 200){
                    this.showDownloadTips = true;
                }else{
                    this.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
        },
    },
    watch: {
        getCurrentStation(newValue,oldValue){
            if(newValue.merchant_type != 0 && newValue.value != oldValue.value){
                if(this.getCurrentStation && this.getCurrentStation.merchant_type == 2){
                    this.isGroup = true;
                }else{
                    this.isGroup = false;
                }
                this.getStationList();
                this.getCompanyList();
                this.getOilList();
                this.getCardDatas();
                this.changeDate();
            }
        },
        //监听班次切换变化，让多选单选重新渲染，防止报错
        'typeValue'(){
            this.update = false
            setTimeout(() => {
                this.update = true
            }, 0);
        }
    },
}
</script>

<style scoped>
    .report {
        position: relative;
        height: 100%;
        background:rgba(245,245,245,1);
        margin: 0px auto;
    }
    .report-content{
        background: #fff;
        padding:20px 0;

    }
    .report .content-header {
        display: flex;
        flex-flow: wrap;
        align-items: center;
        margin-top: 20px;
    }
    .report .content-header .txt{
        margin-right: 5px;
    }
    .report .table_des {
        margin: 20px 0;
    }
    .report .table_des_text {
        font-size: 14px;
        display: flex;
        text-align: left;
    }
    .report .table_des_text p{
        margin: 0
    }
    .tableData{
        text-align: center;
    }
    .tableData .report_title {
        font-size: 24px;
        font-weight: bolder;
        margin-top: 20px;
    }
    .tableData .report_header {
        display: flex;
        margin:10px 20px;
    }
    .tableData .report_header div{
        min-width: 100px;
        text-align: left;
        margin-right: 40px;
    }
    .tableData .table_des {
        margin-top: 40px;
        margin-bottom: 20px;
    }
    .tableData .table_des_text {
        text-align: left;
    }
    .des_bottom {
        display: flex;
        justify-content: flex-end;
        font-size: 14px;
        font-weight: 500;
        margin-top: 10px;
    }
    .des_bottom div{
        padding-right: 100px;
    }
    .search-box{
        display: flex;
        justify-content: flex-end;
        margin-top: 10px;
    }/* 页码 */
    .page_content {
        margin-top: 20px;
        display: flex;
        justify-content: space-between;
    }
    .banci{
        margin-right:15px;
        font-size: 14px;
        color: #606266;
    }
    >>> .el-table .cell {
        white-space: pre-line !important
    }
</style>
<style>
    .reportData td:first-child, .reportData th:first-child{
        padding-left: 0 !important;
    }
</style>
