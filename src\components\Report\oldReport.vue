<template>
  <div class="report" v-loading>
    <div class="report-content">
      <!-- <el-tag v-if="isGroupSettle">{{typeOptions[0].label}}</el-tag> -->
      <div v-if="!isGroupSettle">
        <el-radio-group v-model="typeValue" @change="changeTypeValue">
          <el-radio-button v-for="item in typeOptions" :key="item.value" :label="item.value">{{item.label}}</el-radio-button>
        </el-radio-group>
      </div>
      <div class="content_header">
        <div class="left">
          <el-date-picker
            v-model="dateValue"
            type="date"
            placeholder="选择日期"
            format="yyyy 年 MM 月 dd 日"
            value-format="yyyy-MM-dd"
            @change="clearData">
          </el-date-picker>
          <el-button type="primary" @click="changeDate" :disabled="!dateValue">生成</el-button>
        </div>
        <div class="right">
          <el-button type="primary" :disabled="DailyTableData1.length == 0" @click="printContent">打印</el-button>
          <el-button type="primary" :disabled="DailyTableData1.length == 0" @click="cardChargeDownload" v-show="isTotalReportForm">下载数据</el-button>
        </div>
      </div>

      <div id="myTable" >
        <div class="tableData reportData">
          <!-- 班结日报 -->
          <div class="report_title">储值卡充值资金日报表</div>
          <div class="report_header">
            <div v-if="isGroup">集团名称：{{getCurrentStation.label}}</div>
            <div v-else>油站名称：{{getCurrentStation.label}}</div>
            <div>日期：{{dateValue}}</div>
            <div>单位：元</div>
          </div>
          <el-table :data="DailyTableData1" :span-method="objectSpanMethod" border v-loading="loading" size="small" align="center" ref="table">
            <el-table-column align="center" prop="stname" label="油站" v-if="getCurrentStation.merchant_type == 2 || getCurrentStation.merchant_type == 3"></el-table-column>
            <el-table-column align="center" label="班次" prop="banci" min-width="72px" v-if="typeValue == 4">
              <!-- <template slot-scope="scope">
                  {{$moment(scope.row.start_time*1000).format("YYYY-MM-DD HH:mm:ss") + "至" + $moment(scope.row.end_time*1000).format("YYYY-MM-DD HH:mm:ss")}}
              </template> -->
            </el-table-column>
            <el-table-column align="center" prop="source_name" label="终端来源"></el-table-column>
            <el-table-column align="center" label="充值">
              <el-table-column align="center" prop="recharge_order_cnt" label="充值笔数"> </el-table-column>
              <el-table-column align="center" prop="pay_amt" label="实付金额">
                <template slot-scope="scope">{{Number(scope.row.pay_amt).toFixed(2)}}</template>
              </el-table-column>
              <el-table-column align="center" prop="discount_amt" label="优惠金额">
                <template slot-scope="scope">{{Number(scope.row.discount_amt).toFixed(2)}}</template>
              </el-table-column>
              <el-table-column align="center" label="支付方式">
                <template v-if="payWayList.length > 0">
                  <el-table-column v-for="item in payWayList" :key="item.index" align="center" :label="item.title" :prop="item.prop">
                    <template slot-scope="scope">{{Number(scope.row[item.prop]).toFixed(2)}}</template>
                  </el-table-column>
                </template>
                <template v-else>
                  无
                </template>
              </el-table-column>
              <el-table-column align="center" prop="recharge_amt" label="充值金额">
                <template slot-scope="scope">{{Number(scope.row.recharge_amt).toFixed(2)}}</template>
              </el-table-column>
              <el-table-column align="center" prop="order_amt" label="本金">
                <template slot-scope="scope">{{Number(scope.row.order_amt).toFixed(2)}}</template>
              </el-table-column>
              <el-table-column align="center" prop="gift_amt" label="赠金">
                <template slot-scope="scope">{{Number(scope.row.gift_amt).toFixed(2)}}</template>
              </el-table-column>
            </el-table-column>
            <el-table-column align="center" label="退款">
              <el-table-column align="center" prop="recharge_refund_order_cnt" label="退款笔数"> </el-table-column>
              <el-table-column align="center" label="退款本金">
                <template slot-scope="scope">{{Number(scope.row.recharge_refund_order_amt).toFixed(2)}}</template>
              </el-table-column>
              <el-table-column align="center" label="退款赠金">
                <template slot-scope="scope">{{Number(scope.row.recharge_refund_gift_amt).toFixed(2)}}</template>
              </el-table-column>
              <el-table-column align="center" prop="recharge_refund_real_amt" label="实退金额">
                <template slot-scope="scope">{{Number(scope.row.recharge_refund_real_amt).toFixed(2)}}</template>
              </el-table-column>
            </el-table-column>
            <el-table-column align="center" label="到账金额">
              <template slot-scope="scope">{{Number(scope.row.received_amt).toFixed(2)}}</template>
            </el-table-column>
          </el-table>
        </div>
        <div class="table_des">
          <div class="table_des_text">
            <p>注:</p>
            <div>
              <p>1.实付金额：车主充值实际支付的金额，实付金额+优惠金额=本金。</p>
              <p>2.充值金额：实际计算到储值卡内的金额，本金+赠金=充值金额。</p>
              <p>3.到账金额：油站实际到账的金额，充值实付金额-退款实退金额=到账金额。</p>
            </div>
          </div>
        </div>
        <div class="des_bottom">
          <div>制表人：{{orderMaker}}</div>
          <div>制表时间：{{orderMakingTime}}</div>
          <div>签字：</div>
        </div>
      </div>
    </div>

    <!-- 下载中心提示 -->
    <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>
  </div>
</template>

<script>
import DownloadTips from '../DownloadTips.vue';
import {mapGetters} from 'vuex'
export default {
  name: 'Report',
  components:{
    DownloadTips
  },
  props: {
    report: ''
  } ,
  data () {
    return {
      isTotalReportForm: true,
      typeOptions:[{
        value:1,
        label:"按自然日期",
      },{
        value:4,
        label:"按开班日期",
      }],
      typeValue:1,
      dateValue:"",
      options:[],
      DailyTableData1: [],
      loading: false,
      start_time:"",
      end_time:"",
      orderMaker:"",
      orderMakingTime:"",
      payWayList:[],
      arr:[],
      isGroup:true,//是否是集团账号
      showDownloadTips:false,
      isGroupSettle: Boolean,
    }
  },
  mounted(){
    let userInfo = localStorage.getItem("__userInfo__") || "";
    this.isTotalReportForm = JSON.parse(userInfo).group_id != 426;
    let _today = this.$moment();
    //默认为前一天的数据
    let yesterday = _today.subtract(1, 'days').format('YYYY-MM-DD');
    this.dateValue = yesterday;

    if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
      return false;
    }
    if(this.getCurrentStation && this.getCurrentStation.merchant_type == 2){
      this.isGroup = true;
    }else{
      this.isGroup = false;
    }
    this.changeDate();
  },
  computed:{
    ...mapGetters({
      "getCurrentStation":"getCurrentStation"
    })
  },
  methods: {
    changeTypeValue(){
      this.dateValue = "";
      this.orderMakingTime = "";
      this.DailyTableData1 = [];
    },
    //选中时间生成报表
    changeDate(){
      let that = this;
      if(that.dateValue){
        that.loading = true;
        that.$axios.post('/CardReport/getCardChargeReport', {
          type: that.typeValue,
          start_time: that.$moment(that.dateValue+' 00:00:00', 'YYYY-MM-DD HH:mm:ss').unix(),
          end_time: that.$moment(that.dateValue+' 23:59:59', 'YYYY-MM-DD HH:mm:ss').unix(),
        })
          .then(function (res) {
            that.DailyTableData1 = [];
            that.loading = false;
            if(res.data.status == 200){
              that.$nextTick(()=>{
                that.isGroupSettle = res.data.data.group_settle == 1 ? true : false;
                that.payWayList = res.data.data.pay_way;
                that.DailyTableData1 = res.data.data.charge;
                if(that.typeValue == 4 && that.getCurrentStation.merchant_type == 2){
                  that.setData1(that.DailyTableData1,"stname");
                  that.setData(that.DailyTableData1,"stname","banci");
                  that.setTable(that.DailyTableData1,"stname","banci");
                }else if (that.typeValue == 4){
                  that.setData1(that.DailyTableData1,"banci");
                  that.setTable(that.DailyTableData1,"banci");
                }

                that.start_time = res.data.data.start_time;
                that.end_time = res.data.data.end_time;
                that.orderMakingTime = that.$moment().format("YYYY-MM-DD");
                let userInfo = localStorage.getItem('__userInfo__');
                if(userInfo && (userInfo !== "" || userInfo !== "undefined")){
                  that.orderMaker = JSON.parse(userInfo).name;
                }

                //集团清结算模式
                if (that.isGroupSettle && that.getCurrentStation.merchant_type == 2){
                  that.setData1(that.DailyTableData1,"organize");
                  that.setTable(that.DailyTableData1,"organize");
                } else if(that.isGroupSettle && that.getCurrentStation.merchant_type == 3) {
                  that.setData1(that.DailyTableData1,"stname");
                  that.setTable(that.DailyTableData1,"stname");
                }
              })
            }else{
              that.$message({
                message: res.data.info,
                type: 'error'
              });
            }
          })
          .catch(function (error) {
          });
      }else{
        that.DailyTableData1 = [];
        that.orderMakingTime = "";
        that.orderMaker = "";
      }
    },
    clearData(){
      if(!this.dateValue){
        this.DailyTableData1 = [];
        this.orderMakingTime = "";
        this.orderMaker = "";
      }
    },
    //打印
    printContent(){
      let wpt = document.querySelector('#myTable');
      let newContent = wpt.innerHTML;
      let oldContent = document.body.innerHTML;
      document.body.innerHTML = newContent;
      document.getElementsByClassName("el-table__header")[0].style.width = "100%"
      document.getElementsByClassName("el-table__header")[0].style["table-layout"] = "auto"
      document.getElementsByClassName("el-table__body")[0].style.width = "100%"
      document.getElementsByClassName("el-table__body")[0].style["table-layout"] = "auto"
      window.print(); //打印方法
      history.go(0)
      document.body.innerHTML = oldContent;
    },
    //导出
    cardChargeDownload(){
      this.$axios.get('/CardReport/cardChargeDownload',{
        params:{
          start_time:this.start_time,
          end_time:this.end_time,
          type:this.typeValue,
        }}).then((res)=>{
        if(res.data.status == 200){
          this.showDownloadTips = true;
        }else{
        }
      })
    },

    setTable(data, key, key01) {
      let spanOneArr = [];
      let concatOne = 0;
      let subArr = [];
      let subConcatOne = 0;
      data.forEach((item, index) => {
        if (index === 0) {
          spanOneArr.push(1);
          subArr.push(1);
        } else {
          if (String(item[key]) && String(item[key]) == String(data[index - 1][key])) { //当前项和前一项比较
            spanOneArr[concatOne] += 1; //相同值第一个出现的位置，统计需要合并多少行
            spanOneArr.push(0);//新增一个被合并行
            if (String(item[key01]) && String(item[key01]) == String(data[index - 1][key01])) { //当前项和前一项比较
              subArr[subConcatOne] += 1; //相同值第一个出现的位置，统计需要合并多少行
              subArr.push(0);//新增一个被合并行
            }else{
              subArr.push(1);
              subConcatOne = index;
            }
          } else {
            spanOneArr.push(1); //否则不合并
            concatOne = index;//指向位移
            subArr.push(1);
            subConcatOne = index;
          }
        }
      })
      var obj = {};
      obj[key] = spanOneArr;
      obj[key01] = subArr;
      this.arr = [];
      this.arr.push(obj);
    },
    //整理数据
    setData(arr,key1,key2){
      var len = arr.length;
      var temp;
      for(var i = 0; i < len - 1; i++) {
        for(var j = i + 1; j < len; j++) {
          if(arr[j][key1] == arr[i][key1] && arr[j][key2] == arr[i][key2]) {
            temp = arr[i+1];
            arr[i+1] = arr[j];
            arr[j] = temp;
          }
        }
      }
      return arr;
    },
    setData1(arr,key){
      var len = arr.length;
      var temp;
      for(var i = 0; i < len - 1; i++) {
        for(var j = i + 1; j < len; j++) {
          if(arr[j][key] == arr[i][key]) {
            temp = arr[i+1];
            arr[i+1] = arr[j];
            arr[j] = temp;
          }
        }
      }
      return arr;
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      //集团清结算模式
      if(this.isGroupSettle && this.getCurrentStation.merchant_type == 2){
        if (columnIndex === 0) {
          const _row = this.arr[0].organize[rowIndex] //因为rowIndex出现会从1到结尾
          const _col = _row > 0 ? 1 : 0
          return {
            rowspan: _row,
            colspan: _col
          }
        }
      }
      if(this.isGroupSettle && this.getCurrentStation.merchant_type == 3){
        if (columnIndex === 0) {
          const _row = this.arr[0].stname[rowIndex] //因为rowIndex出现会从1到结尾
          const _col = _row > 0 ? 1 : 0
          return {
            rowspan: _row,
            colspan: _col
          }
        }
      }
      //只有类型是班结才进行合并
      if(this.typeValue == 4 && this.getCurrentStation.merchant_type != 2){
        if (columnIndex === 0) {
          const _row = this.arr[0].banci[rowIndex] //因为rowIndex出现会从1到结尾
          const _col = _row > 0 ? 1 : 0
          return {
            rowspan: _row,
            colspan: _col
          }
        }
      }
      if(this.typeValue == 4 && this.getCurrentStation.merchant_type == 2){
        if (columnIndex === 0) {
          let _row = this.arr[0].stname[rowIndex] //因为rowIndex出现会从1到结尾
          let _col = _row > 0 ? 1 : 0
          return {
            rowspan: _row,
            colspan: _col
          }
        }if (columnIndex === 1) {
          let _row = this.arr[0].banci[rowIndex] //因为rowIndex出现会从1到结尾
          let _col = _row > 0 ? 1 : 0
          return {
            rowspan: _row,
            colspan: _col
          }
        }
      }
    },
  },
  watch: {
    async getCurrentStation(newValue,oldValue){
      if(newValue.merchant_type != 0 && newValue.value != oldValue.value){
        if(this.getCurrentStation && this.getCurrentStation.merchant_type == 2){
          this.isGroup = true;
        }else{
          this.isGroup = false;
        }
        await this.$parent.getOldReport()
        let report = window.sessionStorage.getItem('report')
        console.log('report',report)
        if(report==0) {
          await this.changeDate();
        }
      }
    },
  },
}
</script>

<style scoped>
.report {
  position: relative;
  height: 100%;
  background:rgba(245,245,245,1);
  /* width: 1070px; */
  margin: 0px auto;
}
.report-content{
  background: #fff;
  padding:20px 0;

}
.report .segmentation_view {
  background-color: #e4e4e4;
  position: fixed;
  left: 27%;
  top: 62px;
  width: 10px;
  height: 100%;
}
.report .content_header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}
.report .content_header1 {
  display: flex;
  margin-top: 10px;
  margin-left: 10px;
}
.report .content_header2 {
  display: flex;
  margin-left: 10px;
  margin-top: 10px;
}
.report .table_des {
  margin: 20px 0;
}
.report .table_des_text {
  font-size: 14px;
  display: flex;
  text-align: left;
}
.report .table_des_text p{
  margin: 0
}
.tableData{
  text-align: center;
}
.tableData .report_title {
  font-size: 24px;
  font-weight: bolder;
  margin-top: 20px;
}
.tableData .report_header {
  display: flex;
  margin:10px 20px;
}
.tableData .report_header div{
  min-width: 100px;
  text-align: left;
  margin-right: 40px;
}
.tableData .header_table {
  width: 100%;
  border-right:1px solid #EBEEF5;
  border-bottom:1px solid #EBEEF5;
  margin-top: 20px;
}
.tableData .header_table td {
  border-left:1px solid #EBEEF5;
  border-top:1px solid #EBEEF5;
}
.tableData .header_table_row {
  height: 40px;
}
.tableData .table_des {
  margin-top: 40px;
  margin-bottom: 20px;
}
.tableData .table_des_text {
  text-align: left;
}
.des_bottom {
  display: flex;
  justify-content: flex-end;
  font-size: 14px;
  font-weight: 500;
  margin-top: 10px;
}
.des_bottom div{
  padding-right: 100px;
}
</style>
<style>
.reportData td:first-child, .reportData th:first-child{
  padding-left: 0 !important;
}
</style>
