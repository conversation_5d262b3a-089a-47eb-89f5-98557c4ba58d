webpackJsonp([35],{"2AKL":function(e,t){},A822:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a("Xxa5"),o=a.n(n),i=a("exGp"),r=a.n(i),l=a("Dd8w"),s=a.n(l),d=a("FZmr"),u=a("fTGR"),c=a("NYxO"),m=a("XAqB"),h={name:"RechargeRecodeList",components:{DownloadTips:d.a,BanciDateTime:u.a},data:function(){return{EndTime:m.a,isTotalReportForm:!0,typeOptions:[{value:1,label:"按自然日期"},{value:4,label:"按班结日期"}],typeValue:1,dateBanciValue:"",checkList:[],classList:[],stationId:[],stationOptions:[],dateValue:[],tableData:[],loading:!1,btn_disabled:!1,orderMaker:"",orderMakingTime:"",oilName:"",arr:[],isGroup:!0,showDownloadTips:!1,searchTypeValue:"1",phone:"",vipcard_no:"",order_code:"",page:1,page_size:20,noMore:!0,update:!0,nowEndTime:""}},mounted:function(){var e=localStorage.getItem("__userInfo__")||"";this.isTotalReportForm=426!=JSON.parse(e).group_id;var t=this.$moment().format("YYYY-MM-DD HH:mm:ss"),a=this.$moment().subtract(1,"months").format("YYYY-MM-DD");if(this.dateValue.push(a+" 00:00:00"),this.dateValue.push(t),this.nowEndTime=t.slice(11),void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.changeDate(),this.getCurrentStation&&2==this.getCurrentStation.merchant_type?this.isGroup=!0:this.isGroup=!1,this.getStationList()},computed:s()({},Object(c.c)({getCurrentStation:"getCurrentStation"})),methods:{changeBnaciDate:function(e){console.log("value",e),this.dateBanciValue=e},searchBanciDate:function(e){console.log("dataValue",e),this.dateValue=e,this.tableData=[],this.changeDate()},changeStationValue:function(e){console.log("e",e),4==this.typeValue&&e&&this.$refs.banciRef.getBanci(e)},getStationList:function(){var e=this;return r()(o.a.mark(function t(){var a;return o.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e.stationId=[],a=e,t.next=4,e.$axios.post("/Stations/getStationList",{}).then(function(e){200==e.data.status&&(a.stationOptions=[],e.data.data.forEach(function(e){a.stationId.push(e.stid)}),a.stationOptions=e.data.data)});case 4:console.log("id",a.stationId);case 5:case"end":return t.stop()}},t,e)}))()},searchBanci:function(e){if(console.log("班次",e),e.length){var t,a;t=e.map(function(e){return new Date(e.stime).getTime()}),a=e.map(function(e){return new Date(e.etime).getTime()}),console.log("startTime",t),console.log("endTime",a);var n=Math.max.apply(null,a),o=Math.min.apply(null,t);this.dateValue[0]=this.$moment(o).format("YYYY-MM-DD hh:mm:ss"),this.dateValue[1]=this.$moment(n).format("YYYY-MM-DD hh:mm:ss"),console.log("dateValue",this.dateValue),this.$forceUpdate()}else{this.dateValue=[];var i=this.$moment(new Date(this.dateBanciValue)),r=this.$moment(new Date(this.dateBanciValue));this.dateValue.push(this.$moment(i).format("YYYY-MM-DD")+" 00:00:00"),this.dateValue.push(this.$moment(r).format("YYYY-MM-DD")+" "+this.nowEndTime)}},scrollMore:function(){this.page+=1,this.changeDate()},changeTypeValue:function(e){4==e?(this.stationId="",this.$refs.banciRef.clearDate()):this.stationId=[];this.$moment().subtract(1,"days").format("YYYY-MM-DD");this.dateValue="",this.orderMakingTime="",this.tableData=[],this.dateBanciValue="",1==this.getCurrentStation.merchant_type&&(this.stationId=this.getCurrentStation.merchant_id)},changeValue:function(){1==this.searchTypeValue?(this.vipcard_no="",this.order_code=""):2==this.searchTypeValue?(this.phone="",this.vipcard_no=""):(this.phone="",this.order_code="")},changeDate:function(){var e=this;return r()(o.a.mark(function t(){var a;return o.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!(a=e).dateValue){t.next=12;break}return console.log("时间提交111",a.dateValue),10==a.dateValue[0].length&&(a.dateValue[0]=a.dateValue[0]+" 00:00:00"),10==a.dateValue[1].length&&(a.dateValue[1]==a.$moment(new Date).format("YYYY-MM-DD")?a.dateValue[1]=a.dateValue[1]+" "+e.$moment().format("HH:mm:ss"):a.dateValue[1]=a.dateValue[1]+" 23:59:59"),a.loading=!0,a.btn_disabled=!1,console.log("时间提交222",a.dateValue),t.next=10,a.$axios.post("/CardCharge/getTotalCompanyChargeRunfundOrder",{phone:a.phone,order_code:a.order_code,vipcard_no:a.vipcard_no,start_time:a.$moment(a.dateValue[0]).unix(),end_time:a.$moment(a.dateValue[1]).unix(),page:a.page,page_size:a.page_size,station_id:a.stationId}).then(function(e){if(a.loading=!1,a.btn_disabled=!0,200==e.data.status){a.tableData=a.tableData.concat(e.data.data.order_list),a.noMore=!(e.data.data.total-e.data.data.offset>a.page_size),a.orderMakingTime=a.$moment().format("YYYY-MM-DD");var t=localStorage.getItem("__userInfo__");!t||""===t&&"undefined"===t||(a.orderMaker=JSON.parse(t).name);var n=JSON.parse(localStorage.getItem("currentStation"));a.oilName=n.label}else a.tableData=[],a.$message({message:e.data.info,type:"error"})}).catch(function(e){a.loading=!1});case 10:t.next=16;break;case 12:a.tableData=[],a.orderMakingTime="",a.orderMaker="",a.oilName="";case 16:case"end":return t.stop()}},t,e)}))()},clearData:function(){if(this.dateValue||(this.tableData=[],this.orderMakingTime="",this.orderMaker="",this.oilName=""),4==this.typeValue){this.dateValue=[];var e=this.$moment(new Date(this.dateBanciValue)),t=this.$moment(new Date(this.dateBanciValue));this.dateValue.push(this.$moment(e).format("YYYY-MM-DD")+" 00:00:00"),this.dateValue.push(this.$moment(t).format("YYYY-MM-DD")+" "+this.nowEndTime)}this.$moment(this.dateValue[1]).format("YYYY-MM-DD")===this.$moment().format("YYYY-MM-DD")&&this.$moment(this.dateValue[1]).unix()>this.$moment().unix()&&(this.dateValue[1]=this.$moment().format("YYYY-MM-DD HH:mm:ss"))},printContent:function(){var e=document.querySelector("#myTable").innerHTML,t=document.body.innerHTML;document.body.innerHTML=e,document.getElementsByClassName("el-table__header")[0].style.width="100%",document.getElementsByClassName("el-table__header")[0].style["table-layout"]="auto",document.getElementsByClassName("el-table__body")[0].style.width="100%",document.getElementsByClassName("el-table__body")[0].style["table-layout"]="auto",window.print(),history.go(0),document.body.innerHTML=t},cardChargeDownload:function(){var e=this;this.$axios.post("/CardCharge/exportTotalCompanyChargeOrders",{phone:this.phone,order_code:this.order_code,vipcard_no:this.vipcard_no,start_time:this.$moment(this.dateValue[0]).unix(),end_time:this.$moment(this.dateValue[1]).unix(),station_id:this.stationId}).then(function(t){200==t.data.status?e.showDownloadTips=!0:e.$message.error(t.data.info)})}},watch:{getCurrentStation:function(e,t){var a=this;return r()(o.a.mark(function n(){return o.a.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(0==e.merchant_type||e.value==t.value){n.next=8;break}return n.next=3,a.getStationList();case 3:return a.page=1,a.tableData=[],n.next=7,a.changeDate();case 7:a.getCurrentStation&&2==a.getCurrentStation.merchant_type?a.isGroup=!0:a.isGroup=!1;case 8:case"end":return n.stop()}},n,a)}))()},typeValue:function(){var e=this;this.update=!1,setTimeout(function(){e.update=!0},0)},dateValue:function(){this.dateValue?this.btn_disabled=!0:this.btn_disabled=!1}}},p={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"left_select"},[a("div",{staticClass:"report-content"},[a("div",[a("el-radio-group",{on:{change:e.changeTypeValue},model:{value:e.typeValue,callback:function(t){e.typeValue=t},expression:"typeValue"}},e._l(e.typeOptions,function(t){return a("el-radio-button",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])}),1),e._v(" "),2==e.getCurrentStation.merchant_type?a("span",{staticClass:"txt"},[e._v("油站名称")]):e._e(),e._v(" "),2==e.getCurrentStation.merchant_type&&e.update?a("el-select",{staticStyle:{width:"250px","margin-right":"20px"},attrs:{multiple:1==e.typeValue,clearable:"","collapse-tags":"",placeholder:"请选择油站"},on:{change:e.changeStationValue},model:{value:e.stationId,callback:function(t){e.stationId=t},expression:"stationId"}},e._l(e.stationOptions,function(e,t){return a("el-option",{key:t,attrs:{label:e.stname,value:e.stid}})}),1):e._e(),e._v(" "),a("el-date-picker",{directives:[{name:"show",rawName:"v-show",value:1==e.typeValue,expression:"typeValue == 1"}],attrs:{type:"datetimerange","default-time":["00:00:00","23:59:59"],"range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd HH:mm:ss","picker-options":e.EndTime},on:{change:e.clearData},model:{value:e.dateValue,callback:function(t){e.dateValue=t},expression:"dateValue"}}),e._v(" "),a("banci-date-time",{directives:[{name:"show",rawName:"v-show",value:4==e.typeValue,expression:"typeValue == 4"}],ref:"banciRef",attrs:{stationValue:e.stationId,dateValue:e.dateBanciValue,"picker-options":e.EndTime},on:{searchDate:e.searchBanciDate,changeDate:e.changeBnaciDate}})],1),e._v(" "),a("div",{staticClass:"content_header"},[a("div",{staticClass:"left"},[a("span",{staticClass:"txt"},[e._v("查询类型")]),e._v(" "),a("el-radio-group",{on:{change:e.changeValue},model:{value:e.searchTypeValue,callback:function(t){e.searchTypeValue=t},expression:"searchTypeValue"}},[a("el-radio",{attrs:{label:"1"}},[e._v("手机号")]),e._v(" "),a("el-radio",{attrs:{label:"2"}},[e._v("订单号")]),e._v(" "),a("el-radio",{attrs:{label:"3"}},[e._v("卡号")])],1),e._v(" "),1==e.searchTypeValue?a("el-input",{staticStyle:{width:"210px"},attrs:{placeholder:"请输入手机号",clearable:""},model:{value:e.phone,callback:function(t){e.phone=t},expression:"phone"}}):e._e(),e._v(" "),2==e.searchTypeValue?a("el-input",{staticStyle:{width:"210px"},attrs:{placeholder:"请输入订单号",clearable:""},model:{value:e.order_code,callback:function(t){e.order_code=t},expression:"order_code"}}):e._e(),e._v(" "),3==e.searchTypeValue?a("el-input",{staticStyle:{width:"210px"},attrs:{placeholder:"请输入卡号",clearable:""},model:{value:e.vipcard_no,callback:function(t){e.vipcard_no=t},expression:"vipcard_no"}}):e._e(),e._v(" "),a("el-button",{attrs:{type:"primary",disabled:!e.btn_disabled},on:{click:function(t){e.page=1,e.tableData=[],e.noMore=!0,e.changeDate()}}},[e._v("生成")])],1),e._v(" "),a("div",{staticClass:"right"},[a("el-button",{attrs:{type:"primary",disabled:!e.dateValue},on:{click:e.printContent}},[e._v("打印")]),e._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:e.isTotalReportForm,expression:"isTotalReportForm"}],attrs:{type:"primary",disabled:!e.dateValue},on:{click:e.cardChargeDownload}},[e._v("下载数据")])],1)]),e._v(" "),a("div",{ref:"print",attrs:{id:"myTable"}},[a("div",{staticClass:"report",staticStyle:{"text-align":"center"}},[a("div",{staticClass:"report_title"},[e._v("充值收付款流水表")]),e._v(" "),a("div",{staticClass:"tips"},[e.isGroup?a("div",[e._v("集团名称："+e._s(e.getCurrentStation.label))]):a("div",[e._v("油站名称："+e._s(e.getCurrentStation.label))]),e._v(" "),a("div",[e._v("开始日期："+e._s(e.dateValue?e.dateValue[0]:""))]),e._v(" "),a("div",[e._v("结束日期："+e._s(e.dateValue?e.dateValue[1]:""))]),e._v(" "),a("div",[e._v("单位：元")])]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.tableData,size:"small",align:"center"}},[a("el-table-column",{attrs:{align:"center",prop:"pay_datetime",label:"充值时间","min-width":"140px"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"order_code",label:"订单号","min-width":"180px"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"card_no",label:"储值卡号","min-width":"180px",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"cardholder_name",label:"客户名称","min-width":"72px",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"phone",label:"客户手机号码","min-width":"95px",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"本金账户充值金额","min-width":"120px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.orig_amount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"赠金账户充值金额","min-width":"120px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.gift_amount).toFixed(2)))]}}])},[e._v("\n                    、")]),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值金额合计","min-width":"95px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.charge_money).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"refund_orig_amount",label:"本金账户退款金额","min-width":"120px",formatter:e.formatterCellval},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.refund_orig_amount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"refund_gift_amount",label:"赠金账户退款金额","min-width":"120px",formatter:e.formatterCellval},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.refund_gift_amount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"refund_charge_money",label:"退款金额合计","min-width":"95px",formatter:e.formatterCellval},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.refund_charge_money).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"净收款金额","min-width":"85px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.clear_charge_money).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"pay_way",label:"充值方式","min-width":"72px",formatter:e.formatterCellval}})],1),e._v(" "),e.noMore?e._e():a("p",{staticStyle:{cursor:"pointer",color:"#32AF50"},on:{click:e.scrollMore}},[e._v("点击加载更多")]),e._v(" "),e._m(0),e._v(" "),a("div",{staticClass:"des_bottom"},[a("div",[e._v("制表人："+e._s(e.orderMaker))]),e._v(" "),a("div",[e._v("制表时间："+e._s(e.orderMakingTime))]),e._v(" "),a("div",[e._v("签字：")])])],1)])]),e._v(" "),a("download-tips",{attrs:{showDownloadTips:e.showDownloadTips},on:{"update:showDownloadTips":function(t){e.showDownloadTips=t},"update:show-download-tips":function(t){e.showDownloadTips=t}}})],1)},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"table_des"},[t("div",{staticClass:"table_des_text"},[t("p",[this._v("注：")]),this._v(" "),t("div",[t("p",[this._v("1. 充值金额合计 = 本金账户充值金额 + 赠金账户充值金额。")]),this._v(" "),t("p",[this._v("2. 退款金额合计 = 本金账户退款金额 + 赠金账户退款金额。")]),this._v(" "),t("p",[this._v("3. 净收款金额 = 当笔本金账户充值金额 - 当笔本金账户退款金额。")])])])])}]};var _=a("VU/8")(h,p,!1,function(e){a("2AKL")},"data-v-4b012924",null);t.default=_.exports}});