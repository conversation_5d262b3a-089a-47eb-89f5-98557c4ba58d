<template>
<div>
  <div >
    <div class="table3_head">
      <span>卡号：{{tabledata.cardNo}} &nbsp; 卡面卡号：{{tabledata.card_no}} &nbsp; 车牌：{{tabledata.car}} &nbsp;</span>
      <span>车队：{{tabledata.carList}}</span>
    </div>
    <el-table :data="tabledata.child" :span-method="objectSpanMethod" border size="small" align="center">
      <el-table-column align="center" prop="type" label="变动类型"></el-table-column>
      <el-table-column align="center" prop="name" label="商品名称"></el-table-column>
      <el-table-column align="center" label="挂牌价">
        <template slot-scope="scope">{{Number(scope.row.p1).toFixed(2)}}</template>
      </el-table-column>
      <el-table-column align="center" label="实付单价">
        <template slot-scope="scope">{{Number(scope.row.p2).toFixed(2)}}</template>
      </el-table-column>
      <el-table-column align="center" label="油品升数">
        <template slot-scope="scope">{{Number(scope.row.p3).toFixed(2)}}</template>
      </el-table-column>
      <el-table-column align="center" label="油品应付">
        <template slot-scope="scope">{{Number(scope.row.p4).toFixed(2)}}</template>
      </el-table-column>
      <el-table-column align="center" label="非油应付">
        <template slot-scope="scope">{{Number(scope.row.p5).toFixed(2)}}</template>
      </el-table-column>
      <el-table-column align="center" label="优惠金额">
        <template slot-scope="scope">{{Number(scope.row.p6).toFixed(2)}}</template>
      </el-table-column>
      <el-table-column align="center" label="实付金额">
        <template slot-scope="scope">{{Number(scope.row.p7).toFixed(2)}}</template>
      </el-table-column>
      <el-table-column align="center" label="充值金额">
        <template slot-scope="scope">{{Number(scope.row.p8).toFixed(2)}}</template>
      </el-table-column>
      <el-table-column align="center" label="变动账户" prop="account"></el-table-column>
      <el-table-column align="center" label="余额">
        <template slot-scope="scope">{{Number(scope.row.yue).toFixed(2)}}</template>
      </el-table-column>
      <el-table-column align="center" label="交易时间" prop="time" width="150"></el-table-column>
    </el-table>
  </div>
</div>
</template>

<script>
export default {
  name: "CardCustomerTable",
  props: ['tabledata','spanlist'],
  data() {
    return {

    }
  },
  mounted() {
    // console.log('tabledata',this.tabledata)
    // console.log('spanlist',this.spanlist)
  },
  methods: {
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        let _row = this.spanlist.type[rowIndex] //因为rowIndex出现会从1到结尾
        let _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
      if (columnIndex === 1) {
        let _row = this.spanlist.name[rowIndex] //因为rowIndex出现会从1到结尾
        let _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
      if (columnIndex === 2) {
        let _row = this.spanlist.p1[rowIndex] //因为rowIndex出现会从1到结尾
        let _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
      if (columnIndex === 3) {
        let _row = this.spanlist.p2[rowIndex] //因为rowIndex出现会从1到结尾
        let _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
      if (columnIndex === 4) {
        let _row = this.spanlist.p3[rowIndex] //因为rowIndex出现会从1到结尾
        let _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
      if (columnIndex === 5) {
        let _row = this.spanlist.p4[rowIndex] //因为rowIndex出现会从1到结尾
        let _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
      if (columnIndex === 6) {
        let _row = this.spanlist.p5[rowIndex] //因为rowIndex出现会从1到结尾
        let _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
      if (columnIndex === 7) {
        let _row = this.spanlist.p6[rowIndex] //因为rowIndex出现会从1到结尾
        let _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
  }
}
</script>

<style scoped>
.table3_head {
  height: 40px;
  line-height: 40px;
  text-align: left;
  padding-left: 20px;
  border-left: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
  /*border-top: 1px solid #EBEEF5;*/
}
.table3_head:first-child {
  border-top: 1px solid #EBEEF5;
}
</style>
