<template>
    <div class="wrap-download" id="download">
        <div class="header">
            <el-form :inline="true" :model="searchForm">
                <el-form-item label="文件名称">
                    <el-input 
                        class="download-search-bar" 
                        size="medium" 
                        clearable 
                        v-model="searchForm.name" 
                        placeholder="请输入名称" 
                        @keyup.enter.native="searchDowloadList" 
                        @clear="searchDowloadList">
                    </el-input>
                </el-form-item>
                <el-form-item label="创建时间">
                    <el-date-picker
                        :clearable="false"
                        v-model="searchForm.createTime"
                        type="datetimerange"
                        size="medium"
                        :default-time="['00:00:00', '23:59:59']"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        @change="handleDateChange"
                        :picker-options="{
                            disabledDate(time) {
                                const tomorrow = $moment().add(1, 'day').startOf('day').valueOf();
                                return time.getTime() >= tomorrow;
                            }
                        }">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" size="medium" @click="searchDowloadList">搜索</el-button>
                </el-form-item>
            </el-form>
        </div>

        <div class="table-operations" v-if="showBatchDownload">
            <el-button 
                type="primary" 
                size="medium" 
                :disabled="!multipleSelection.length"
                :loading="batchDownloading"
                @click="batchDownload">
                批量下载
            </el-button>
        </div>
        
        <el-table
            v-loading="loading || batchDownloading"
            style="margin:20px 0"
            ref="multipleTable"
            :data="tableData"
            @selection-change="handleSelectionChange"
            tooltip-effect="dark">
            <el-table-column
                v-if="showBatchDownload"
                type="selection"
                width="55"
                :selectable="row => String(row.status) === '4'">
            </el-table-column>
            <el-table-column
                label="序号"
                width="70">
                <template slot-scope="scope">
                    {{(currentPage - 1) * pageSize + scope.$index + 1}}
                </template>
            </el-table-column>
            <el-table-column
                prop="name"
                label="名称">
            </el-table-column>
            <el-table-column
                prop="ctime"
                label="开始时间">
            </el-table-column>
            <el-table-column
                label="结束时间">
                <template slot-scope="scope">{{scope.row.status == 4 ? $moment(Number(scope.row.stime+"000")).format("YYYY-MM-DD HH:mm:ss") : "--"}}</template>
            </el-table-column>
            <el-table-column
                prop="operator_id"
                label="创建人">
            </el-table-column>
            <el-table-column
                prop="ctime"
                label="创建时间">
            </el-table-column>
            <el-table-column
                label="状态">
                <template slot-scope="scope">
                    <span class="statusTxt info" v-if="scope.row.status == 3">{{scope.row.status_name}}</span>
                    <span class="statusTxt" v-if="scope.row.status == 4">{{scope.row.status_name}}</span>
                    <span class="statusTxt danger" v-if="scope.row.status == 6">{{scope.row.status_name}}</span>
                    <span class="statusTxt out" v-if="scope.row.status == 100">{{scope.row.status_name}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="address"
                label="操作">
                <template slot-scope="scope">
                    <el-button :disabled="scope.row.status != 4" type="text" @click="downloadExcel(scope.row)">下载</el-button>
                    <el-button style="margin-left:10px" type="text" @click="deleteData(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 页码 -->
        <div class="page_content">
            <el-pagination class="page_left"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-size="pageSize"
                layout="prev, pager, next"
                :total="total">
            </el-pagination>
            <el-pagination class="page_right"
                @size-change="handleSizeChange"
                :page-sizes="[10, 15, 20, 30, 50, 100]"
                :page-size="pageSize"
                layout="total, sizes"
                :total="total">
            </el-pagination>
        </div>
    </div>
</template>

<script>
import {getDowloadList,downloadExcel,deleteData} from "./api/index"
import {mapGetters} from 'vuex'
export default {
    name: 'DownloadList',
    data () {
        return {
            searchForm:{
                name:"",//搜索名称
                createTime:[this.$moment().subtract(7,'days').startOf('day'), this.$moment().endOf('day')]//搜索时间,默认最近7天
            },
            loading:true,
            batchDownloading: false,
            tableData: [],//列表数据
            multipleSelection: [],
            pageSize:10,
            currentPage:1,
            total:0,
        }
    },
    mounted(){
        if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
            return false;
        }
        this.getDowloadList();
    },
    computed:{
        ...mapGetters({
            "getCurrentStation":"getCurrentStation",
            "showBatchDownload":"showBatchDownload"
        })
    },
    methods: {
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        async batchDownload() {
            if (!this.multipleSelection.length) return;
            
            this.batchDownloading = true;
            try {
                for (const item of this.multipleSelection) {
                    if (item.status === 4) {
                        const res = await downloadExcel({
                            task_id: item.id
                        });
                        if (res.data.status === 200) {
                            try {
                                window.location.href = res.data.data.url;
                                // 等待1秒再下载下一个
                                await new Promise(resolve => setTimeout(resolve, 1000));
                            } catch (error) {
                                console.error('下载文件失败:', error);
                                this.$message.error(`下载文件 ${item.name} 失败`);
                            }
                        }
                    }
                }
            } catch (error) {
                console.log("🚀 ~ file: DownloadList.vue:192 ~ batchDownload ~ error:", error)
                this.$message.error('批量下载出错');
            } finally {
                this.batchDownloading = false;
            }
        },
        //搜索
        searchDowloadList(){
            this.currentPage = 1;
            this.getDowloadList();
        },
        //获取下载列表
        async getDowloadList(){
            this.loading = true;
            let params = {
                page:this.currentPage,
                page_size:this.pageSize,
                task_name:this.searchForm.name,
                project_ids:"bf1fd528-b505-baef-c29b-865f98ae6040",
                start_time:this.$moment(this.searchForm.createTime[0]).format('YYYY-MM-DD HH:mm:ss'),
                end_time:this.$moment(this.searchForm.createTime[1]).format('YYYY-MM-DD HH:mm:ss')
            }
            let res = await getDowloadList(params);
            this.loading = false;
            if (res.data.status === 200) {
                this.tableData = res.data.data.items;
                this.total = res.data.data.total;
            } else {
                this.$message({
                    type: 'error',
                    message: res.data.info
                });
            }
        },
        //切换页码
        handleCurrentChange(val) {
            this.currentPage = val;
            this.getDowloadList();
        },
        //切换条数
        handleSizeChange(val) {
            this.pageSize = val;
            this.currentPage = 1;
            this.getDowloadList();
        },
        //下载
        downloadExcel(val){
            downloadExcel({
                task_id:val.id
            }).then((res)=>{
                if (res.data.status === 200) {
                    window.location.href =  res.data.data.url;
                } else {
                    this.$message({
                        type: 'error',
                        message: res.data.info
                    });
                }
            })
        },
        //删除数据
        deleteData(val){
            this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                deleteData({
                    task_ids:val.id,
                    project_ids:"bf1fd528-b505-baef-c29b-865f98ae6040"
                }).then((res)=>{
                    if (res.data.status === 200) {
                        this.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                        this.getDowloadList();
                    } else {
                        this.$message({
                            type: 'error',
                            message: res.data.info
                        });
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });          
            });
        },
        handleDateChange(val) {
            if (val && val.length === 2) {
                const [startDate, endDate] = val;
                // 计算两个日期之间的天数差,包含闰年
                const diffDays = this.$moment(endDate).diff(this.$moment(startDate), 'days');
                console.log("开始时间:", this.$moment(startDate).format("YYYY-MM-DD HH:mm:ss"));
                console.log("结束时间:", this.$moment(endDate).format("YYYY-MM-DD HH:mm:ss")); 
                console.log("🚀 ~ file: DownloadList.vue:284 ~ handleDateChange ~ diffDays:", startDate, endDate, diffDays)
                if (diffDays > 365) {
                    this.$message.warning('时间跨度不能超过1年');
                    this.$nextTick(() => {
                        this.searchForm.createTime = [
                            this.$moment().subtract(7,'days').startOf('day'),
                            this.$moment().endOf('day')
                        ];
                    });
                }
            }
        },
    },
    watch:{
        getCurrentStation(newValue,oldValue){
            if(newValue.merchant_type != 0 && newValue.value != oldValue.value){
                this.getDowloadList();
            }
        }
    }
}
</script>

<style scoped>
    .wrap-download{
        padding:20px 0 30px;
    }
    .wrap-download .header{
        display: flex;
        align-items: center;
    }
    .wrap-download .download-search-bar{
        width:220px;
        margin-right:10px;
    }
    .table-operations {
        margin-top: 20px;
    }
    .statusTxt{
        display:inline-block;
        padding:1px 8px;
        width:58px;
        text-align:center;
        color:#fff;
        background:#30a54f;
        border-radius:4px;
    }
    .statusTxt.danger{
        background:#e00322;
    }
    .statusTxt.info{
        background:#f4a452;
    }
    .statusTxt.out{
        background:#a1a1a1;
    }
        /* 页码 */
    .page_content {
        margin-top: 20px;
        display: flex;
        justify-content: space-between;
    }
</style>
