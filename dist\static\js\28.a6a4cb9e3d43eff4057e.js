webpackJsonp([28],{FwFp:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=a("Dd8w"),i=a.n(l),s=a("FZmr"),n=a("fTGR"),o=a("NYxO"),r=JSON.parse(localStorage.getItem("__userInfo__")),u={name:"CapitalRecord",components:{DownloadTips:s.a,BanciDateTime:n.a},data:function(){return{isTotalReportForm:!0,userInfo:r,typeList:[{value:"QB",label:"全部"},{value:"CZ",label:"充值"},{value:"XF",label:"消费"},{value:"FP",label:"资金划拨"},{value:"TK",label:"充值退款"},{value:"XFCX",label:"消费退款"},{value:"QK",label:"余额清零"}],checkboxGroup:"QB",dateValue:[],stationOptions:[],stationValue:[],tableData:[],loading:!0,total:0,pageSize:10,currentPage:1,inputTxt:"",searchTypeVlaue:"1",showDownloadTips:!1,typeClassValue:1,dateBanciValue:"",typeOptions:[{value:1,label:"按自然日期"},{value:4,label:"按班结日期"}],checkList:[],classList:[],nowEndTime:"",showDown:!0,update:!0}},mounted:function(){var t=this,e=localStorage.getItem("__userInfo__")||"";this.isTotalReportForm=426!=JSON.parse(e).group_id,this.showDown=383!=JSON.parse(e).group_id;var a=this.$moment(new Date).subtract(1,"months").format("YYYY-MM-DD HH:mm:ss"),l=this.$moment(new Date);if(this.dateValue.push(this.$moment(a).format("YYYY-MM-DD")+" 00:00:00"),this.dateValue.push(this.$moment(l).format("YYYY-MM-DD HH:mm:ss")),this.nowEndTime=this.$moment(l).format("YYYY-MM-DD HH:mm:ss").slice(11),void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.dateBanciValue=this.nowDate,this.getStations(),this.stationValue=[],this.$nextTick(function(){1==t.getCurrentStation.merchant_type&&t.stationValue.push(t.getCurrentStation.merchant_id)}),this.getCardCapitalRecordList()},computed:i()({},Object(o.c)({getCurrentStation:"getCurrentStation"}),{nowDate:function(){return this.$moment(new Date).format("YYYY-MM-DD")},startTimePicker:function(){var t=this;return{disabledDate:function(e){if(!t.isTotalReportForm){var a=new Date,l=t.$moment(new Date).format("YYYY-MM-DD HH:mm:ss");a.setTime(t.$moment(l).valueOf());var i=new Date;return i.setTime(a.getTime()-5184e6),e.getTime()>a||e.getTime()<i}var s=new Date,n=new Date;return 0===t.userInfo.has_mch_id?e.getTime()>new Date(new Date((new Date).toLocaleDateString()).getTime()+864e5-1).getTime():(n.setTime(s.getTime()-7776e6),e.getTime()>s||e.getTime()<n||e.getTime()>Date.now())}}}}),methods:{searchDate:function(t){console.log("value",t),this.dateValue=t,this.getCardCapitalRecordList()},changeDate:function(t){this.dateBanciValue=t},changeClassTypeValue:function(t){if(console.log("e",t),4==t)this.stationValue="",this.$refs.banciRef.clearDate();else{this.stationValue=[],this.dateValue=[];var e=this.$moment(new Date).subtract(1,"months").format("YYYY-MM-DD HH:mm:ss"),a=this.$moment(new Date);this.dateValue.push(this.$moment(e).format("YYYY-MM-DD")+" 00:00:00"),this.dateValue.push(this.$moment(a).format("YYYY-MM-DD")+" "+this.nowEndTime),this.getCardCapitalRecordList()}1==this.getCurrentStation.merchant_type&&(this.stationValue=this.getCurrentStation.merchant_id)},getStations:function(){var t=this;this.$axios.post("/Stations/getStations",{}).then(function(e){200==e.status&&(t.stationOptions=e.data.data.station_info)})},getStationName:function(t){var e="";return this.stationOptions.forEach(function(a){a.stid==t&&(e=a.stname)}),e},getAccountType:function(t,e){var a="";return(e?[{value:"BJ_MZ",label:"车队卡母账"},{value:"BJ_KZ",label:"子卡卡账"},{value:"XYED",label:"信用额度"},{value:"SKJ",label:"子卡赠金"}]:[{value:"BJ_MZ",label:"车队卡母账"},{value:"BJ_KZ",label:"子卡卡账"},{value:"XYED",label:"信用额度"},{value:"SKJ",label:"母账赠金"}]).forEach(function(e){e.value==t&&(a=e.label)}),a},getTime:function(t){return this.$moment(t).format("YYYY-MM-DD HH:mm:ss")},getCardCapitalRecordList:function(){var t=this;this.loading=!0;var e={page:this.currentPage,page_size:this.pageSize,input_type:this.searchTypeVlaue,input:this.inputTxt,start_time:this.dateValue[0],end_time:this.dateValue[1],type:this.checkboxGroup,station_list:this.stationValue,mch_arr:this.userInfo.mch_arr};this.dateValue&&(e.start_time=this.dateValue[0],e.end_time=this.dateValue[1]),10==this.dateValue[0].length&&(this.dateValue[0]=this.dateValue[0]+" 00:00:00",e.start_time=this.dateValue[0]),10==this.dateValue[1].length&&(this.dateValue[1]==this.$moment(new Date).format("YYYY-MM-DD")?this.dateValue[1]=this.dateValue[1]+" "+this.$moment().format("HH:mm:ss"):this.dateValue[1]=this.dateValue[1]+" 23:59:59",e.end_time=this.dateValue[1]),4==this.typeClassValue&&(console.log("判断",Object.prototype.toString.call(this.stationValue)),"[object Number]"==Object.prototype.toString.call(this.stationValue)&&(e.station_list=[this.stationValue])),this.$axios.post("/Card/getCardCapitalRecordList",e).then(function(e){t.loading=!1,t.tableData=[],200==e.data.status?(t.tableData=e.data.data.dt,t.total=e.data.data.TotalQty):(t.total=0,t.$message.error(e.info))})},changeBanciData:function(t){4==this.typeClassValue?this.$refs.banciRef.getBanci(t):this.getCardCapitalRecordList()},changeData:function(t){console.log("val",t),this.currentPage=1,this.dateValue?this.getCardCapitalRecordList():this.tableData=[]},changeType:function(t){this.currentPage=1,this.checkboxGroup=t,this.getCardCapitalRecordList()},handleCurrentChange:function(t){this.currentPage=t,this.getCardCapitalRecordList()},handleSizeChange:function(t){this.pageSize=t,this.getCardCapitalRecordList()},cardCapitalRecordDownload:function(){var t=this;this.$axios.post("/Card/cardCapitalRecordDownload",{input_type:this.searchTypeVlaue,input:this.inputTxt,start_time:this.dateValue[0],end_time:this.dateValue[1],type:this.checkboxGroup,mch_arr:this.userInfo.mch_arr,station_list:Array.isArray(this.stationValue)?this.stationValue:[this.stationValue]}).then(function(e){200==e.data.status?t.showDownloadTips=!0:t.$message.error(e.data.info)})}},watch:{getCurrentStation:function(t,e){var a=this;0!=t.merchant_type&&t.value!=e.value&&(this.getStations(),this.stationValue=[],this.$nextTick(function(){1==a.getCurrentStation.merchant_type&&a.stationValue.push(a.getCurrentStation.merchant_id)}),this.getCardCapitalRecordList())},dateValue:function(){this.$moment(this.dateValue[1]).format("YYYY-MM-DD")===this.$moment().format("YYYY-MM-DD")&&this.$moment(this.dateValue[1]).unix()>this.$moment().unix()&&(this.dateValue[1]=this.$moment().format("YYYY-MM-DD HH:mm:ss"))},typeClassValue:function(){var t=this;console.log("123"),this.update=!1,setTimeout(function(){t.update=!0},0)}}},c={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"capitalRecord",attrs:{id:"capitalRecord"}},[a("div",{staticClass:"header"},[a("div",{staticClass:"group"},[a("el-radio-group",{on:{change:t.changeData},model:{value:t.checkboxGroup,callback:function(e){t.checkboxGroup=e},expression:"checkboxGroup"}},t._l(t.typeList,function(e){return a("el-radio-button",{key:e.index,attrs:{label:e.value}},[a("span",{on:{click:function(a){return t.changeType(e.value)}}},[t._v(t._s(e.label))])])}),1)],1),t._v(" "),a("div",{staticClass:"classType"},[a("el-radio-group",{on:{change:t.changeClassTypeValue},model:{value:t.typeClassValue,callback:function(e){t.typeClassValue=e},expression:"typeClassValue"}},t._l(t.typeOptions,function(e){return a("el-radio-button",{key:e.value,attrs:{label:e.value}},[t._v(t._s(e.label))])}),1),t._v(" "),2==t.getCurrentStation.merchant_type&&t.update?a("el-select",{staticStyle:{width:"250px"},attrs:{multiple:1==t.typeClassValue,"collapse-tags":"",placeholder:"请选择油站"},on:{change:t.changeBanciData},model:{value:t.stationValue,callback:function(e){t.stationValue=e},expression:"stationValue"}},t._l(t.stationOptions,function(t){return a("el-option",{key:t.stid,attrs:{label:t.stname,value:t.stid}})}),1):t._e(),t._v(" "),a("el-date-picker",{directives:[{name:"show",rawName:"v-show",value:1==t.typeClassValue,expression:"typeClassValue == 1"}],staticClass:"date-picker",attrs:{"value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"],"picker-options":t.startTimePicker,type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:t.changeData},model:{value:t.dateValue,callback:function(e){t.dateValue=e},expression:"dateValue"}}),t._v(" "),a("banci-date-time",{directives:[{name:"show",rawName:"v-show",value:4==t.typeClassValue,expression:"typeClassValue == 4"}],ref:"banciRef",attrs:{stationValue:t.stationValue,dateValue:t.dateBanciValue,"picker-options":t.startTimePicker},on:{searchDate:t.searchDate,changeDate:t.changeDate}})],1),t._v(" "),a("div",{staticClass:"select"},[a("div",{staticStyle:{display:"flex"}},[a("div",{staticClass:"right"},[a("span",{staticClass:"txt"},[t._v("查询类型")]),t._v(" "),a("el-radio-group",{model:{value:t.searchTypeVlaue,callback:function(e){t.searchTypeVlaue=e},expression:"searchTypeVlaue"}},[a("el-radio",{attrs:{label:"1"}},[t._v("手机号")]),t._v(" "),a("el-radio",{attrs:{label:"0"}},[t._v("卡号")]),t._v(" "),a("el-radio",{attrs:{label:"2"}},[t._v("卡面卡号")]),t._v(" "),a("el-radio",{attrs:{label:"5"}},[t._v("订单号")])],1),t._v(" "),a("el-input",{staticStyle:{width:"210px"},attrs:{placeholder:"0"==t.searchTypeVlaue?"请输入卡号":"1"==t.searchTypeVlaue?"请输入手机号":"2"==t.searchTypeVlaue?"请输入卡面卡号":"请输入订单号",clearable:""},model:{value:t.inputTxt,callback:function(e){t.inputTxt=e},expression:"inputTxt"}}),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:t.changeData}},[t._v("查询")])],1)]),t._v(" "),0===t.userInfo.has_mch_id&&t.showDown?a("el-button",{attrs:{type:"primary",disabled:0==t.tableData.length},on:{click:t.cardCapitalRecordDownload}},[t._v("下载数据")]):t._e()],1)]),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"capitalRecordData",staticStyle:{width:"100%","margin-bottom":"20px"},attrs:{data:t.tableData}},[a("el-table-column",{attrs:{align:"left",label:"交易油站",width:"180",fixed:"",prop:"StationNO",formatter:t.formatterCellval},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t.getStationName(e.row.StationNO)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡号",width:"190",prop:"CardNO",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡面卡号",width:"190",prop:"CardNumber",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",width:"140",label:"手机号",prop:"Phone",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"持卡人","min-width":"120",prop:"cardholder_name",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center","min-width":"140",label:"车牌号",prop:"CarNumber",formatter:t.formatterCellval}}),t._v(" "),"QB"==t.checkboxGroup||"QK"==t.checkboxGroup?a("el-table-column",{attrs:{align:"center",prop:"Type",label:"变动类型",width:"150"}}):t._e(),t._v(" "),a("el-table-column",{attrs:{align:"left",prop:"Money",label:"变动金额(元)",width:"120",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"left",prop:"ChangeFrontBalance",label:"变动前金额(元)",formatter:t.formatterCellval,width:"140"}}),t._v(" "),a("el-table-column",{attrs:{align:"left",prop:"ChangeAfterBalance",label:"变动后金额(元)",width:"140",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"开户油站",width:"140",prop:"stNewName",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"TradeID",label:"订单号",width:"190",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"变动时间",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t.getTime(e.row.TradeTime)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"AmountType",label:"变动账户",width:"120",formatter:t.formatterCellval}})],1),t._v(" "),a("div",{staticClass:"page_content"},[a("el-pagination",{staticClass:"page_left",attrs:{"current-page":t.currentPage,"page-size":t.pageSize,layout:"prev, pager, next",total:t.total},on:{"current-change":t.handleCurrentChange}}),t._v(" "),a("el-pagination",{staticClass:"page_right",attrs:{"page-sizes":[10,15,20,30],"page-size":t.pageSize,layout:"total, sizes",total:t.total},on:{"size-change":t.handleSizeChange}})],1),t._v(" "),a("download-tips",{attrs:{showDownloadTips:t.showDownloadTips},on:{"update:showDownloadTips":function(e){t.showDownloadTips=e},"update:show-download-tips":function(e){t.showDownloadTips=e}}})],1)},staticRenderFns:[]};var h=a("VU/8")(u,c,!1,function(t){a("Zbb+")},"data-v-7986aec8",null);e.default=h.exports},"Zbb+":function(t,e){}});