webpackJsonp([32],{VVH7:function(t,e){},gLwA:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=a("Dd8w"),r=a.n(n),i=a("Xxa5"),o=a.n(i),s=a("exGp"),l=a.n(s),u=a("FZmr"),c=a("fTGR"),p=a("NYxO"),d={name:"fleetJXC",components:{DownloadTips:u.a,BanciDateTime:c.a},data:function(){return{typeOptions:[{value:1,label:"按自然日期"},{value:4,label:"按班结日期"}],typeValue:1,dateValue:[],dateBanciValue:[],stationId:[],stationName:"",stationOptions:[],companyOptions:[],companyValue:[],tableData:[],tableColumn:[{label:"车队名称",prop:"company_name"},{label:"期初余额",childs:[{label:"期初总余额",prop:"qichu_amt",width:100},{label:"期初本金",prop:"qichu_bj_amt"},{label:"期初赠金",prop:"qichu_skj_amt"}]},{label:"期间充值",childs:[{label:"充值金额",prop:"recharge_amt"},{label:"充值本金",prop:"recharge_bj_amt"},{label:"充值赠金",prop:"recharge_skj_amt"}]},{label:"期间充值退款",childs:[{label:"充值退款金额",prop:"recharge_refund_amt",width:100},{label:"充值退款本金",prop:"recharge_refund_bj_amt",width:100},{label:"充值退款赠金",prop:"recharge_refund_skj_amt",width:100}]},{label:"期间消费",childs:[{label:"消费金额",prop:"consume_amt"},{label:"消费本金",prop:"consume_bj_amt"},{label:"消费赠金",prop:"consume_skj_amt"}]},{label:"期间消费退款",childs:[{label:"消费退款金额",prop:"consume_refund_amt",width:100},{label:"消费退款本金",prop:"consume_refund_bj_amt",width:100},{label:"消费退款赠金",prop:"consume_refund_skj_amt",width:100}]},{label:"加油量(吨)",prop:"oil_v20_ton"},{label:"加油量(升)",prop:"oil_litre"},{label:"余额清零",childs:[{label:"清零金额",prop:"clear_amt"},{label:"清零本金",prop:"clear_bj_amt"},{label:"清零赠金",prop:"clear_skj_amt"}]},{label:"期末余额",childs:[{label:"期末总余额",prop:"qimo_amt",width:100},{label:"期末本金",prop:"qimo_bj_amt"},{label:"期末赠金",prop:"qimo_skj_amt"}]}],loading:!1,start_time:"",end_time:"",orderMaker:"",orderMakingTime:"",isGroup:!0,showDownloadTips:!1,update:!0,arr:[]}},mounted:function(){var t=this;return l()(o.a.mark(function e(){var a,n,r,i;return o.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(localStorage.getItem("__userInfo__")||"",a=t.$moment(new Date).subtract(1,"days").format("YYYY-MM-DD"),n=t.$moment().format("YYYY-MM-DD"),t.dateValue=[a,n],r=t.$moment(),i=r.subtract(1,"days").format("YYYY-MM-DD"),t.dateBanciValue=[i,t.$moment().format("YYYY-MM-DD")],void 0!=t.getCurrentStation.merchant_type&&0!=t.getCurrentStation.merchant_type){e.next=9;break}return e.abrupt("return",!1);case 9:return t.getCurrentStation&&2==t.getCurrentStation.merchant_type?t.isGroup=!0:t.isGroup=!1,e.next=12,t.getStationList();case 12:return e.next=14,t.getCompanyList();case 14:return e.next=16,t.changeDate();case 16:case"end":return e.stop()}},e,t)}))()},computed:r()({},Object(p.c)({getCurrentStation:"getCurrentStation"})),methods:{changeBnaciDate:function(t){this.dateBanciValue=t},searchBanciDate:function(t){this.dateValue=t,this.changeDate()},changeStationValue:function(t){4==this.typeValue&&this.stationId&&this.$refs.banciRef.getBanci(t)},getStationList:function(){var t=this;return l()(o.a.mark(function e(){var a;return o.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t.stationId=[],a=t,e.next=4,t.$axios.post("/Stations/getStationList",{}).then(function(e){200==e.status&&(a.stationOptions=e.data.data,1==t.getCurrentStation.merchant_type?4==t.typeValue?t.stationId=t.getCurrentStation.merchant_id:t.stationId=[t.getCurrentStation.merchant_id]:4==t.typeValue?t.stationId="":a.stationId=e.data.data.map(function(t){return t.stid}))});case 4:case"end":return e.stop()}},e,t)}))()},getCompanyList:function(){var t=this;return l()(o.a.mark(function e(){var a;return o.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a=t,e.next=3,a.$axios.post("/CompanyCard/getSimpleCompanyList",{page:1,page_size:1250,input:""}).then(function(t){200==t.data.status?(a.companyOptions=[],a.companyOptions=a.companyOptions.concat(t.data.data.dt)):a.$message({message:t.data.info,type:"error"})}).catch(function(t){a.$message.error("网络错误")});case 3:case"end":return e.stop()}},e,t)}))()},changeTypeValue:function(t){1==this.getCurrentStation.merchant_type?this.stationId=4==t?this.getCurrentStation.merchant_id:[this.getCurrentStation.merchant_id]:4==t?(this.stationId="",this.$refs.banciRef.clearDate()):this.stationId=[];var e=this.$moment(new Date).subtract(1,"days").format("YYYY-MM-DD"),a=this.$moment().format("YYYY-MM-DD");this.dateValue=[e,a];var n=this.$moment().subtract(1,"days").format("YYYY-MM-DD");this.dateBanciValue=[n,this.$moment().format("YYYY-MM-DD")],this.companyValue=[],this.orderMakingTime="",this.tableData=[],this.page=1},changeDate:function(){var t=this;return l()(o.a.mark(function e(){var a,n,r,i;return o.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if((a=t).dateValue&&0!==a.dateValue.length){e.next=3;break}return e.abrupt("return",t.$message.error("请选择查询时间"));case 3:if(4!=a.typeValue){e.next=6;break}if(a.stationId){e.next=6;break}return e.abrupt("return",t.$message.error("请选择开户油站"));case 6:return a.loading=!0,n=a.dateValue[0],r=a.dateValue[1],10==n.length&&(n+=" 00:00:00"),10==r.length&&(r+=" 23:59:59"),i={start_time:a.$moment(n).unix(),end_time:a.$moment(r).unix(),company_id:a.companyValue},1==a.typeValue?(i.type=1,i.stid=a.stationId):(i.type=4,i.stid=[a.stationId]),e.next=15,a.$axios.create({timeout:3e4}).post("/CardReport/getCardCompanyInvoicingReport",i).then(function(t){if(a.loading=!1,200==t.data.status){a.orderMakingTime=a.$moment().format("YYYY-MM-DD HH:mm:ss"),a.tableData=t.data.data.card_invoicing;var e=t.data.data.total;a.tableData&&a.tableData.length>0&&a.tableData.push(e),a.setTable(a.tableData,"stid","stname");var n=localStorage.getItem("__userInfo__");!n||""===n&&"undefined"===n||(a.orderMaker=JSON.parse(n).name)}else a.$message({message:t.data.info,type:"error"})}).catch(function(t){a.$message.error("网络错误")}).finally(function(){a.loading=!1});case 15:case"end":return e.stop()}},e,t)}))()},setTable:function(t,e,a){var n=[],r=0,i=[],o=0;t.forEach(function(s,l){0===l?(n.push(1),i.push(1)):String(s[e])&&String(s[e])==String(t[l-1][e])?(n[r]+=1,n.push(0),String(s[a])&&String(s[a])==String(t[l-1][a])?(i[o]+=1,i.push(0)):(i.push(1),o=l)):(n.push(1),r=l,i.push(1),o=l)});var s={};s[e]=n,s[a]=i,this.arr=[],this.arr.push(s)},objectSpanMethod:function(t){t.row,t.column;var e=t.rowIndex,a=t.columnIndex;if(2==this.getCurrentStation.merchant_type&&this.arr[0]&&0===a){var n=this.arr[0].stid[e];return{rowspan:n,colspan:n>0?1:0}}},clearData:function(t){this.dateValue||(this.tableData=[],this.orderMakingTime="",this.orderMaker="")},printContent:function(){var t=document.querySelector("#myTable").innerHTML,e=document.body.innerHTML;document.body.innerHTML=t,document.getElementsByClassName("el-table__header")[0].style.width="100%",document.getElementsByClassName("el-table__header")[0].style["table-layout"]="auto",document.getElementsByClassName("el-table__body")[0].style.width="100%",document.getElementsByClassName("el-table__body")[0].style["table-layout"]="auto",window.print(),history.go(0),document.body.innerHTML=e},handleCurrentChange:function(t){this.page=t,this.changeDate()},handleSizeChange:function(t){this.pageSize=t,this.changeDate()},cardChargeDownload:function(){var t=this,e=this.dateValue[0],a=this.dateValue[1];10==e.length&&(e+=" 00:00:00"),10==a.length&&(a+=" 23:59:59");var n={start_time:this.$moment(e).unix(),end_time:this.$moment(a).unix(),company_id:this.companyValue};1==this.typeValue?(n.type=1,n.stid=this.stationId):(n.type=4,n.stid=[this.stationId]),this.$axios.get("/CardReport/cardCompanyInvoicingReportDownload",{params:n}).then(function(e){200==e.data.status?t.showDownloadTips=!0:t.$message({message:e.data.info,type:"error"})})}},watch:{getCurrentStation:function(t,e){var a=this;return l()(o.a.mark(function n(){return o.a.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(0==t.merchant_type||t.value==e.value){n.next=8;break}return a.getCurrentStation&&2==a.getCurrentStation.merchant_type?a.isGroup=!0:a.isGroup=!1,n.next=4,a.getStationList();case 4:return n.next=6,a.getCompanyList();case 6:return n.next=8,a.changeDate();case 8:case"end":return n.stop()}},n,a)}))()},typeValue:function(){var t=this;this.update=!1,setTimeout(function(){t.update=!0},0)}}},m={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"pt-20px"},[a("el-radio-group",{on:{change:t.changeTypeValue},model:{value:t.typeValue,callback:function(e){t.typeValue=e},expression:"typeValue"}},t._l(t.typeOptions,function(e){return a("el-radio-button",{key:e.value,attrs:{label:e.value}},[t._v(t._s(e.label)+"\n      ")])}),1),t._v(" "),a("div",{staticClass:"mt-20px flex flex-wrap items-center"},[2==t.getCurrentStation.merchant_type?a("span",{staticClass:"mr-5px"},[t._v("开户油站")]):t._e(),t._v(" "),2==t.getCurrentStation.merchant_type&&t.update?a("el-select",{staticClass:"w-250px mr-20px mb-5px",attrs:{multiple:1==t.typeValue,clearable:"","collapse-tags":"",placeholder:"请选择油站"},on:{change:t.changeStationValue},model:{value:t.stationId,callback:function(e){t.stationId=e},expression:"stationId"}},t._l(t.stationOptions,function(t,e){return a("el-option",{key:e,attrs:{label:t.stname,value:t.stid}})}),1):t._e(),t._v(" "),a("span",{staticClass:"mr-5px"},[t._v("车队名称")]),t._v(" "),a("el-select",{staticClass:"w-250px mr-20px mb-5px",attrs:{filterable:"",multiple:"","collapse-tags":"",clearable:"",placeholder:"请选择车队"},model:{value:t.companyValue,callback:function(e){t.companyValue=e},expression:"companyValue"}},t._l(t.companyOptions,function(t,e){return a("el-option",{key:e,attrs:{label:t.CompanyName,value:t.ID}})}),1),t._v(" "),a("el-date-picker",{directives:[{name:"show",rawName:"v-show",value:1==t.typeValue,expression:"typeValue == 1"}],staticClass:"mb-5px",attrs:{"end-placeholder":"结束日期","range-separator":"至","start-placeholder":"开始日期",type:"daterange","value-format":"yyyy-MM-dd"},on:{change:t.clearData},model:{value:t.dateValue,callback:function(e){t.dateValue=e},expression:"dateValue"}}),t._v(" "),a("banci-date-time",{directives:[{name:"show",rawName:"v-show",value:4==t.typeValue,expression:"typeValue == 4"}],ref:"banciRef",staticClass:"mb-5px",attrs:{dateValue:t.dateBanciValue,stationValue:t.stationId},on:{changeDate:t.changeBnaciDate,searchDate:t.searchBanciDate}}),t._v(" "),a("el-button",{staticClass:"mb-5px ml-15px",attrs:{disabled:t.loading,type:"primary"},on:{click:function(e){return t.changeDate()}}},[t._v("生成")])],1),t._v(" "),a("div",{staticClass:"flex justify-end"},[a("el-button",{attrs:{disabled:t.loading||0===t.tableData.length,type:"primary"},on:{click:t.printContent}},[t._v("打印")]),t._v(" "),a("el-button",{attrs:{disabled:t.loading||0===t.tableData.length,type:"primary"},on:{click:t.cardChargeDownload}},[t._v("下载数据")])],1),t._v(" "),a("div",{attrs:{id:"myTable"}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"text-center"},[a("div",{staticClass:"text-24px font-bold mt-20px py-5px border border-solid border-#EBEEF5"},[t._v("车队进销存汇总表")]),t._v(" "),a("div",{staticClass:"flex py-10px px-20px border-x-1px border-x-solid border-#EBEEF5"},[t.isGroup?a("div",[t._v("集团名称："+t._s(t.getCurrentStation.label))]):a("div",[t._v("油站名称："+t._s(t.getCurrentStation.label))]),t._v(" "),a("div",{staticClass:"mx-40px"},[t._v("开始日期："+t._s(t.dateValue.length?10==t.dateValue[0].length?t.dateValue[0]+" 00:00:00":t.dateValue[0]:""))]),t._v(" "),a("div",[t._v("结束日期："+t._s(t.dateValue.length?10==t.dateValue[1].length?t.dateValue[1]+" 23:59:59":t.dateValue[1]:""))])]),t._v(" "),a("el-table",{ref:"table",attrs:{data:t.tableData,border:"",size:"small","span-method":t.objectSpanMethod}},[2==t.getCurrentStation.merchant_type?a("el-table-column",{attrs:{formatter:t.formatterCellval,align:"center",prop:"stname",label:"油站",width:"150"}}):t._e(),t._v(" "),t._l(t.tableColumn,function(e,n){return[a("el-table-column",{attrs:{align:"center",label:e.label,prop:e.prop,width:e.width}},[e.childs&&e.childs.length>0?[t._l(e.childs,function(t,e){return[a("el-table-column",{key:e,attrs:{align:"center",width:t.width,label:t.label,prop:t.prop}})]})]:t._e()],2)]})],2),t._v(" "),a("div",{staticClass:"flex justify-between py-10px text-14px border border-solid border-#EBEEF5 border-t-0"},[a("div",{staticClass:"ml-20px"},[t._v("制表人："+t._s(t.orderMaker))]),t._v(" "),a("div",{staticClass:"mx-100px"},[t._v("制表时间："+t._s(t.orderMakingTime))]),t._v(" "),a("div",{staticClass:"mr-100px"},[t._v("签字：")])])],1)])],1),t._v(" "),a("download-tips",{attrs:{showDownloadTips:t.showDownloadTips},on:{"update:showDownloadTips":function(e){t.showDownloadTips=e},"update:show-download-tips":function(e){t.showDownloadTips=e}}})],1)},staticRenderFns:[]};var h=a("VU/8")(d,m,!1,function(t){a("VVH7")},"data-v-55a580f2",null);e.default=h.exports}});