webpackJsonp([10],{"2lhR":function(e,t){},"4jyv":function(e,t,a){"use strict";a.d(t,"a",function(){return n});var n={DATE:1,CLASS:4};t.b={name:"TableQueryCondition",data:function(){return{TYPE_VALUE:n,query:{stid:"",customer_type:"",invoice_type:"",type:1,company_id:[],dateValue:[]}}}}},EQFf:function(e,t,a){"use strict";var n=a("mvHQ"),i=a.n(n),r=a("Dd8w"),s=a.n(r),o=a("iJJg"),d=a("V2BW"),c=a("4jyv"),l=a("M4fF"),u=a("NYxO"),p=a("fTGR"),m=a("XAqB"),_={name:"InvoiceQuery",mixins:[o.c,c.b],components:{BanciDateTime:p.a,SelectPlus:d.a},computed:s()({},Object(u.d)(["stationListLoading","stationList","currentStation","companyListLoading"]),Object(u.c)(["companyArray"]),{EndTime:function(){return m.a}}),props:{dateType:{type:String,default:"datetimerange"},disabledDate:{type:Function,default:function(){return!1}},showInvoiceType:{type:Boolean,default:!1},company_id:{type:Array,default:function(){return[]}},invoice_type:{type:[String,Number],default:""},customer_type:{type:[String,Number],default:""},date_value:{type:Array,default:function(){return[]}}},data:function(){return{classDate:[],typeOptions:[{value:c.a.DATE,label:"按时间查询"},{value:c.a.CLASS,label:"按班结查询"}],invoiceValue:"",invoiceOptions:[{value:0,label:"充值开票"},{value:1,label:"消费开票"},{value:2,label:"不开票"}]}},created:function(){this.getCustomerType(),this.getSimpleCompanyList(),this.getStationList(),Object(l.isEmpty)(this.date_value)},watch:{date_value:{handler:function(e){Object(l.isEmpty)(e)||Object(l.isEqual)(e,this.query.dateValue)||(this.query.dateValue=Object(l.cloneDeep)(e))},immediate:!0,deep:!0},currentStation:{deep:!0,immediate:!0,handler:function(e,t){if(console.log("=>(InvoiceQuery.vue:116) val",JSON.parse(i()(e||{})),JSON.parse(i()(t||{}))),this.query.company_id=[],this.query.stid="",this.$emit("update:company_id",[]),this.$emit("update:stid",""),e&&"2"===String(e.merchant_type))return this.$store.dispatch("getStationList"),void this.$store.dispatch("getSimpleCompanyList");this.query.stid=e.merchant_id,this.$refs.banciRef&&this.query.stid&&this.$refs.banciRef.getBanci(e.merchant_id)}}},methods:s()({},Object(u.b)(["getStationList","getSimpleCompanyList"]),{updateCompanySelected:function(e){this.$emit("update:company_id",e);var t=this.companyArray.filter(function(t){return e.includes(t.ID)});console.log("=>(InvoiceQuery.vue:140) currentCompanies",t),this.$emit("update:currentCompanies",t)},changeType:function(){this.$emit("update:type",this.query.type),this.query.dateValue=[],this.$emit("update:date_value",[this.$moment().subtract(1,"days").format("YYYY-MM-DD 00:00:00"),this.$moment().format("YYYY-MM-DD 23:59:59")]),this.query.type!==c.a.DATE&&(this.$emit("update:date_value",[]),this.$refs.banciRef&&this.$refs.banciRef.getBanci(this.query.stid))}})},y={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-radio-group",{on:{input:e.changeType},model:{value:e.query.type,callback:function(t){e.$set(e.query,"type",t)},expression:"query.type"}},e._l(e.typeOptions,function(t){return a("el-radio-button",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label)+"\n    ")])}),1),e._v(" "),a("div",{staticClass:"mt-20px"},[a("el-form",{attrs:{inline:""}},[a("el-form-item",{attrs:{label:"车队名称/ID"}},[a("select-plus",{directives:[{name:"loading",rawName:"v-loading",value:e.companyListLoading,expression:"companyListLoading"}],key:"company_id",attrs:{placeholder:"请选择车队",list:e.companyArray,multiple:"",filterable:"",clearable:"","collapse-tags":"",attr:{label:"CompanyName",value:"ID",getLabel:function(e){return"ID "+e.ID+" "+e.CompanyName}}},on:{input:e.updateCompanySelected},model:{value:e.query.company_id,callback:function(t){e.$set(e.query,"company_id",t)},expression:"query.company_id"}})],1),e._v(" "),e.query.type===e.TYPE_VALUE.CLASS?[e.currentStation&&"2"===String(e.currentStation.merchant_type)?a("el-form-item",{attrs:{label:"油站名称"}},[a("select-plus",{directives:[{name:"loading",rawName:"v-loading",value:e.stationListLoading,expression:"stationListLoading"}],key:"stid",attrs:{"check-all":!1,placeholder:"请选择油站",list:e.stationList,filterable:"",clearable:"","collapse-tags":"",attr:{label:"stname",value:"stid"}},on:{input:function(t){e.$refs.banciRef.getBanci(e.query.stid),e.$emit("update:stid",t)}},model:{value:e.query.stid,callback:function(t){e.$set(e.query,"stid",t)},expression:"query.stid"}})],1):e._e(),e._v(" "),a("el-form-item",[a("banci-date-time",{ref:"banciRef",attrs:{"show-loading":!0,"date-value":e.classDate,stationValue:e.query.stid,"picker-options":e.EndTime},on:{classTimeChange:function(t){return e.$emit("update:date_value",t)},changeDate:function(t){e.query.dateValue=t}}})],1)]:e._e(),e._v(" "),e.showInvoiceType?a("el-form-item",{attrs:{label:"开票方式"}},[a("el-select",{staticClass:"w-250px mr-20px mb-5px",attrs:{clearable:"",placeholder:"请选择开票方式"},on:{change:function(t){return e.$emit("update:invoice_type",e.query.invoice_type)}},model:{value:e.query.invoice_type,callback:function(t){e.$set(e.query,"invoice_type",t)},expression:"query.invoice_type"}},e._l(e.invoiceOptions,function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})}),1)],1):e._e(),e._v(" "),a("el-form-item",{attrs:{label:"客户类型"}},[a("select-plus",{directives:[{name:"loading",rawName:"v-loading",value:e.customerTypeLoading,expression:"customerTypeLoading"}],attrs:{"check-all":!1,placeholder:"请选择客户类型",list:e.customerTypeOptions,clearable:"",attr:{label:"CompanyTypeName",value:"CompanyTypeID"}},on:{input:function(t){return e.$emit("update:customer_type",e.query.customer_type)}},model:{value:e.query.customer_type,callback:function(t){e.$set(e.query,"customer_type",t)},expression:"query.customer_type"}})],1),e._v(" "),e.query.type===e.TYPE_VALUE.DATE?a("el-form-item",{attrs:{label:"时间范围"}},[a("el-date-picker",{staticClass:"mb-5px",attrs:{"picker-options":{disabledDate:e.disabledDate},clearable:!1,"end-placeholder":"结束日期","range-separator":"至","start-placeholder":"开始日期",type:e.dateType,"value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"]},on:{change:function(t){e.$emit("clearData"),e.$emit("update:date_value",t)}},model:{value:e.query.dateValue,callback:function(t){e.$set(e.query,"dateValue",t)},expression:"query.dateValue"}})],1):e._e(),e._v(" "),a("el-form-item",[e._t("btns")],2)],2)],1)],1)},staticRenderFns:[]},h=a("VU/8")(_,y,!1,null,null,null);t.a=h.exports},yRL2:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a("BO1k"),i=a.n(n),r=a("//Fk"),s=a.n(r),o=a("Gu7T"),d=a.n(o),c=a("d7EF"),l=a.n(c),u=a("Xxa5"),p=a.n(u),m=a("exGp"),_=a.n(m),y=a("mvHQ"),h=a.n(y),f=a("Dd8w"),b=a.n(f),g=a("FZmr"),v=a("NYxO"),C=a("mtWM"),k=a.n(C),x=a("i0w4"),w=a("4jyv"),q=a("EQFf"),D=a("M4fF"),S=a("uotZ"),$=a.n(S),L=a("38N9"),T=a.n(L);var V={name:"CustomerSpendReport",components:{InvoiceQuery:q.a,DownloadTips:g.a},mixins:[w.b],data:function(){return{copySon:[],typeValue:1,disableAll:!1,companyOptions:[],companyName:"",tableList:[],cardResult:[],tableColumns:[],isGroup:!0,loading:!1,showAllChecks:!1,checkedValue:[],isIndeterminate:!1,checkAll:!1,checkFlag:1,is_stid:0,single_stid:0,stids:[],searchFlag:1,downForm:{index:0,num:10},currentCompanies:[],orderMaker:"",orderMakingTime:"",showDownloadTips:!1,downloadLoading:!1}},mounted:function(){var e=localStorage.getItem("__userInfo__")||"";if(this.isTotalReportForm=426!=JSON.parse(e).group_id,void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.getCurrentStation&&2==this.getCurrentStation.merchant_type?this.isGroup=!0:this.isGroup=!1,this.setTime(),this.getStations(),this.getCompanyList(),this.getCardReportColumn()},computed:b()({},Object(v.c)({getCurrentStation:"getCurrentStation",customerTypeObject:"customerTypeObject"})),methods:{handleDateChange:function(e){var t=this;if(e&&2===e.length){var a=this.$moment(e[1]).diff(this.$moment(e[0]),"months",!0);console.log("🚀 ~ file: CustomerSpendReport.vue:160 ~ handleDateChange ~ diff:",a),a>3&&(this.$message.warning("时间范围不能超过3个月"),this.$nextTick(function(){t.query.dateValue=[t.$moment().subtract(1,"months").format("YYYY-MM-DD 00:00:00"),t.$moment().format("YYYY-MM-DD 23:59:59")]}))}},exportData:function(){var e,t=this;this.query.company_id.length?(this.downloadLoading=!0,(e={is_stid:this.is_stid,single_stid:this.single_stid,company_type:this.query.customer_type,company_id:this.query.company_id,start_time:this.$moment(this.query.dateValue[0]).format("YYYY-MM-DD HH:mm:ss"),end_time:this.$moment(this.query.dateValue[1]).format("YYYY-MM-DD HH:mm:ss"),company_names:h()(this.currentCompanies.filter(function(e){return t.query.company_id.includes(e.ID)}).reduce(function(e,t){return e[t.ID]=t.CompanyName,e},{}))},k.a.create({timeout:0}).post("/CardReport/batchDownLoadCustomerOrder",e)).then(function(e){t.showDownloadTips=!0}).finally(function(){t.downloadLoading=!1})):this.$message.error("请至少选择一个车队")},getCompanyCustomerType:function(e){var t=this.currentCompanies.find(function(t){return t.ID===e});return t&&this.customerTypeObject[t.CompanyType]||"-"},setTime:function(){this.query.dateValue=[];var e=this.$moment().format("YYYY-MM-DD"),t=this.$moment().subtract(1,"months").format("YYYY-MM-DD");this.query.dateValue.push(t+" 00:00:00"),this.query.dateValue.push(e+" 23:59:59")},handleCheckAllChange:function(e){this.checkedValue=e?this.companyOptions.map(function(e){return e.ID}):[],this.isIndeterminate=!1},checkOne:function(e){var t=e.length;this.checkAll=t===this.companyOptions.length,this.isIndeterminate=t>0&&t<this.companyOptions.length},closeAll:function(){this.checkAll=!1,this.isIndeterminate=!1,this.checkedValue=[]},batchPrint:function(e){console.log("批量"+(1==e?"打印":"下载"),e),this.checkFlag=e,this.showAllChecks=!0},getStations:function(){var e=this;e.$axios.post("/Stations/getStations",{}).then(function(t){200==t.data.status?(1==t.data.data.is_group?(e.is_stid=0,e.single_stid=0):(e.is_stid=1,e.single_stid=t.data.data.station_info[0].stid),e.stids=t.data.data.station_info.map(function(e){return e.stid}),console.log("is_stid",e.is_stid,e.single_stid,e.stids)):e.$message({message:t.data.info,type:"error"})})},getCompanyList:function(){var e=this;return _()(p.a.mark(function t(){var a;return p.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return a=e,t.next=3,k.a.create().post("/CompanyCard/getSimpleCompanyList",{page:1,page_size:1250,input:""},{timeout:3e4}).then(function(e){200==e.data.status?a.companyOptions=e.data.data.dt:a.$message({message:e.data.info,type:"error"})}).catch(function(e){console.log(e),a.$message.error("获取车队信息失败")});case 3:case"end":return t.stop()}},t,e)}))()},getCardReportColumn:function(){var e=this;return _()(p.a.mark(function t(){return p.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:e.tableColumns=[{cid:2,type:10,name:"变动类型",sort:2,field:"type",disabled:!0,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:1,is_checked:!0,children:[]},{cid:4,type:10,name:"卡面卡号",sort:4,field:"card_number",disabled:!1,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:1,is_checked:!1,children:[]},{cid:5,type:10,name:"车牌号",sort:5,field:"car_no",disabled:!1,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:1,is_checked:!0,children:[]},{cid:6,type:10,name:"油品名称",sort:6,field:"goods_name",disabled:!0,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:1,is_checked:!0,children:[]},{cid:7,type:10,name:"挂牌单价",sort:7,field:"han_price",disabled:!1,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:1,is_checked:!1,children:[]},{cid:8,type:10,name:"实付单价",sort:8,field:"zh_dj",disabled:!1,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:1,is_checked:!1,children:[]},{cid:9,type:10,name:"数量(升)",sort:9,field:"sl",disabled:!1,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:1,is_checked:!0,children:[]},{cid:10,type:10,name:"油品应付",sort:10,field:"origin_money",disabled:!1,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:1,is_checked:!1,children:[]},{cid:12,type:10,name:"优惠金额",sort:12,field:"dis_money",disabled:!1,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:1,is_checked:!1,children:[]},{cid:13,type:10,name:"实付金额",sort:13,field:"pay_money",disabled:!0,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:1,is_checked:!0,children:[]},{cid:14,type:10,name:"充值金额",sort:14,field:"recharge_money",disabled:!0,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:1,is_checked:!0,children:[]},{cid:15,type:10,name:"余额",sort:15,field:"after_money",disabled:!1,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:1,is_checked:!1,children:[]},{cid:16,type:10,name:"交易时间",sort:16,field:"pay_time",disabled:!1,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:1,is_checked:!1,children:[]}],e.copyMom=Object(D.cloneDeep)(e.tableColumns),e.sonList=[{cid:17,type:11,name:"油站名称",sort:1,field:"stid_name",disabled:!1,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:2,is_checked:!0,children:[]},{cid:18,type:11,name:"变动类型",sort:2,field:"type",disabled:!0,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:2,is_checked:!0,children:[]},{cid:19,type:11,name:"油品名称",sort:3,field:"goods_name",disabled:!0,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:2,is_checked:!0,children:[]},{cid:20,type:11,name:"挂牌单价",sort:4,field:"han_price",disabled:!1,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:2,is_checked:!0,children:[]},{cid:21,type:11,name:"实付单价",sort:5,field:"zh_dj",disabled:!1,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:2,is_checked:!1,children:[]},{cid:22,type:11,name:"数量(升)",sort:6,field:"sl",disabled:!1,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:2,is_checked:!1,children:[]},{cid:23,type:11,name:"油品应付",sort:7,field:"origin_money",disabled:!1,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:2,is_checked:!1,children:[]},{cid:24,type:11,name:"非油应付",sort:8,field:"goods_money",disabled:!1,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:2,is_checked:!0,children:[]},{cid:25,type:11,name:"优惠金额",sort:9,field:"dis_money",disabled:!1,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:2,is_checked:!1,children:[]},{cid:26,type:11,name:"实付金额",sort:10,field:"pay_money",disabled:!0,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:2,is_checked:!0,children:[]},{cid:27,type:11,name:"充值金额",sort:11,field:"recharge_money",disabled:!0,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:2,is_checked:!0,children:[]},{cid:28,type:11,name:"余额",sort:12,field:"after_money",disabled:!1,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:2,is_checked:!1,children:[]},{cid:29,type:11,name:"交易时间",sort:13,field:"pay_time",disabled:!1,is_default:0,pid:0,is_deleted:0,create_time:1685432373,update_time:1685432373,crid:2,is_checked:!1,children:[]}],e.copySon=Object(D.cloneDeep)(e.sonList);case 4:case"end":return t.stop()}},t,e)}))()},createReport:function(e,t,a){var n=this;return _()(p.a.mark(function i(){var r,s,o,c,u,m,_,y,h,f,b,g;return p.a.wrap(function(i){for(;;)switch(i.prev=i.next){case 0:if(i.prev=0,!Object(D.isEmpty)(n.query.dateValue)){i.next=4;break}return n.$message({message:"请选择时间",type:"error"}),i.abrupt("return");case 4:if(r={company_type:n.query.customer_type,is_stid:n.is_stid,single_stid:n.single_stid,start_time:n.query.dateValue[0],end_time:n.query.dateValue[1],stids:n.stids},!(t>=e)){i.next=9;break}return 2==n.searchFlag&&(n.disableAll=!1,setTimeout(function(){1==a?n.printContent():n.cardChargeDownload()},0)),n.tableList.sort(function(e,t){return Number(e.fleetId)-Number(t.fleetId)}),i.abrupt("return",console.log("生成截止",a));case 9:return r.company_id=n.query.company_id[t],n.disableAll=!0,i.next=13,n.$axios.post("/CardReport/getCustomerOrderListsV2",r);case 13:if(200===(s=i.sent).data.status){i.next=16;break}return i.abrupt("return",n.$message.error(s.data.info));case 16:if(console.log("查询res",e,t,a),!(e>0)){i.next=27;break}if(!(t<e)){i.next=27;break}if(Object(D.isEmpty)(s.data.data)||(o=function(e){return["sl","origin_money","dis_money","pay_money","recharge_money"].forEach(function(t){e&&e.hasOwnProperty(t)&&"-"===e[t]&&(e[t]="0.00")}),e},c=function(e){e.sort(function(e,t){var a=e.card_number.localeCompare(t.card_number);if(0!==a)return a;var n=e.car_no.localeCompare(t.car_no);if(0!==n)return n;var i=e.type_no-t.type_no;return 0!==i?i:new Date(e.pay_time)-new Date(t.pay_time)})},u=function(e){return isNaN(Number(e))?0:e},m=function(e){var t={};for(var a in e.forEach(function(e,a){var n=e.car_no+"_"+("消费退款"===e.type?"消费":e.type);t[n]||(t[n]={sl:new $.a(0),origin_money:new $.a(0),dis_money:new $.a(0),pay_money:new $.a(0),recharge_money:new $.a(0)}),t[n].sl=t[n].sl.plus(u(e.sl)),t[n].origin_money=t[n].origin_money.plus(u(e.origin_money)),t[n].dis_money=t[n].dis_money.plus(u(e.dis_money)),t[n].pay_money=t[n].pay_money.plus(u(e.pay_money)),t[n].recharge_money=t[n].recharge_money.plus(u(e.recharge_money))}),console.log("=>(CustomerSpendReport.vue:1268) typeMap",t),t)t.hasOwnProperty(a)&&function(){var n=a.split("_"),i=l()(n,2),r=i[0],s=i[1],o=e.findLastIndex(function(e){return!(!s.includes("消费")||e.car_no!==r||!e.type.includes("消费"))||(e.car_no===r&&e.type===s||void 0)});if(console.log("=>(CustomerSpendReport.vue:1272) lastIndex",o,a),-1!==o){var d={type:"-",car_no:"小计：",card_number:"-",goods_name:"-",han_price:"-",zh_dj:"-",sl:t[a].sl.toFixed(2),origin_money:t[a].origin_money.toFixed(2),dis_money:t[a].dis_money.toFixed(2),pay_money:t[a].pay_money.toFixed(2),recharge_money:t[a].recharge_money.toFixed(2),after_money:"-",pay_time:"-",order_no:"-"};e.splice(o+1,0,d)}}();return e},_=function(e){var t={sl:new $.a(0),origin_money:new $.a(0),dis_money:new $.a(0),pay_money:new $.a(0),recharge_money:new $.a(0)};e.forEach(function(e){"-"!==e.type&&(t.sl=t.sl.plus(new $.a(u(e.sl))),t.origin_money=t.origin_money.plus(new $.a(u(e.origin_money))),t.dis_money=t.dis_money.plus(new $.a(u(e.dis_money))),t.pay_money=t.pay_money.plus(new $.a(u(e.pay_money))),t.recharge_money=t.recharge_money.plus(new $.a(u(e.recharge_money))))});var a={stid_name:"中国钓鱼岛加油站",type:"-",card_no:"-",card_number:"-",car_no:"总计：",goods_name:"-",han_price:"-",zh_dj:"-",sl:t.sl.toFixed(2),origin_money:t.origin_money.toFixed(2),goods_money:"-",dis_money:t.dis_money.toFixed(2),pay_money:t.pay_money.toFixed(2),recharge_money:t.recharge_money.toFixed(2),after_money:"-",pay_time:"-",order_no:"-",fleet_id:"-",customer_type:"-"};e&&e.length>0&&e.push(a)},y=n.companyOptions.find(function(e){return r.company_id===e.ID}),s.data.data.result.forEach(function(e){e.fleet_id=r.company_id,"0"!==String(y.CompanyType)?e.customer_type=n.customerTypeObject[y.CompanyType]||y.CompanyType:e.customer_type="未分类"}),h=s.data.data.result,Object(D.isEmpty)(h)||(f=h.pop(),c(h),m(h),o(f),f.car_no="母账合计：",h.push(f)),b=s.data.data.card_result,console.log("=>(CustomerSpendReport.vue:746) cardResult",b),Object(D.isEmpty)(b)||b.forEach(function(e){var t=Object(D.cloneDeep)(e).card_list,a=t.pop();c(t),m(t),o(a),a.car_no="子卡合计：",t.push(a),h.push.apply(h,d()(t))}),_(h),n.tableList.push({companyName:n.getCompanyName(r.company_id),fleetId:r.company_id,tableData:h})),1!=a){i.next=25;break}return i.next=23,n.createReport(e,t+1,1);case 23:i.next=27;break;case 25:return i.next=27,n.createReport(e,t+1,2);case 27:!(g=localStorage.getItem("__userInfo__"))||""===g&&"undefined"===g||(n.orderMaker=JSON.parse(g).name),n.orderMakingTime=n.$moment().format("YYYY-MM-DD"),i.next=36;break;case 32:i.prev=32,i.t0=i.catch(0),console.log(i.t0),n.$message.error("生成报表失败");case 36:return i.prev=36,n.disableAll=!1,i.finish(36);case 39:case"end":return i.stop()}},i,n,[[0,32,36,39]])}))()},search:function(){if(0===this.query.company_id.length)return this.$message.error("请选择车队");this.tableList=[],this.searchFlag=1,this.createReport(this.query.company_id.length,0,1)},sureBranch:function(){if(0===this.query.company_id.length)return this.$message.error("请选择要"+(1==this.checkFlag?"打印":"下载")+"的车队");this.tableList=[],this.showAllChecks=!1,this.searchFlag=2,1==this.checkFlag?this.createReport(this.query.company_id.length,0,1):this.createReport(this.query.company_id.length,0,2)},printContent:function(){var e=this,t=this.tableList;if(console.log("=>  file: CustomerSpendReport.vue:956  data:",t),t&&(t=t.filter(function(e){return e.tableData&&e.tableData.length>0})),Object(D.isEmpty)(t))this.$message.error("当前无数据，无需打印");else{t.sort(function(e,t){return e.fleetId-t.fleetId});var a="";t.forEach(function(t,n){a+='<div class="'+(n>0?"next":"")+'"><h1>客户消费变动明细表</h1><p>'+(e.isGroup?"集团名称":"油站名称")+"："+e.getCurrentStation.label+"&emsp;开始时间："+(e.query.dateValue.length?e.query.dateValue[0]:"")+"&emsp;结束时间："+(e.query.dateValue.length?e.query.dateValue[1]:"")+'</p><div class="table-wrapper">\n    <span>'+t.companyName+"</span><span>车队ID："+t.fleetId+"</span><span>客户类型："+e.getCompanyCustomerType(t.fleetId)+'</span>\n    </div><table><thead><tr class=""><th><div class="cell">变动类型</div></th><th><div class="cell">卡面卡号</div></th><th><div class="cell">车牌号</div></th><th><div class="cell">油品名称</div></th><th><div class="cell">挂牌单价</div></th><th><div class="cell">实付单价</div></th><th><div class="cell">数量(升)</div></th><th><div class="cell">油品应付</div></th><th><div class="cell">优惠金额</div></th><th><div class="cell">实付金额</div></th><th><div class="cell">充值金额</div></th><th><div class="cell">余额</div></th><th><div class="cell">交易时间</div></th></tr></thead>\n        <tbody>\n        '+t.tableData.map(function(e){return"<tr><td>"+e.type+"</td><td>"+e.card_number+"</td><td>"+e.car_no+"</td><td>"+e.goods_name+"</td><td>"+e.han_price+"</td><td>"+e.zh_dj+"</td><td>"+e.sl+"</td><td>"+e.origin_money+"</td><td>"+e.dis_money+"</td><td>"+e.pay_money+"</td><td>"+e.recharge_money+"</td><td>"+e.after_money+"</td><td>"+e.pay_time+"</td>"}).join("")+"\n        </tbody>\n      </table>\n    </div>\n  </div>"}),T()({style:"@media print {\n      @page {\n        size: auto;\n        margin: 20pt;\n        margin-bottom: 10pt;\n        padding: 2pt;\n      }\n\n      body {\n        margin: 2pt;\n        padding: 2pt;\n        font-size: 12pt;\n        margin-left:-1pt;\n      }\n      #container{\n        width:100vw;\n      }\n      h1{font-size:16pt;text-align:center; margin:0; padding:0;}\n      p{font-size:10pt;}\n      table {\n        border-collapse: collapse;\n        width: 100%;\n        box-sizing: border-box;\n        font-size: 10pt;\n      }\n      th,\n      td {\n        border: 1px solid #999;\n        box-sizing: border-box;\n        padding: 2pt;\n        text-align: center;\n      }\n      th{font-size:9pt}\n      td{font-size:8pt}\n      .next{page-break-before: always;}\n      .table-wrapper {\n        font-size: 9pt;\n        margin-top: 1em;\n        padding: 0 1em;\n        height: 15pt;\n        border: 1px solid #999999;\n        border-bottom: 0;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n      }\n\n    }",printable:a,type:"raw-html"})}},pause:function(e){return new s.a(function(t,a){setTimeout(t,e||1e3)})},cardChargeDownload:function(){var e=this;return _()(p.a.mark(function t(){var a,n;return p.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:n=function(e,t,n){var i=this,r=e.cardResult;e=e.tableData;var s=t.map(function(e){return e.name}),o=e.map(function(e){return t.map(function(t){return e[t.field]})}),c=[n],l=[(this.isGroup?"集团":"油站")+"名称："+this.getCurrentStation.label,"开始时间："+(this.query.dateValue.length?this.query.dateValue[0]:""),"结束时间："+(this.query.dateValue.length?this.query.dateValue[1]:"")];o.unshift(s),o.unshift(c),o.unshift(l),o.unshift(["客户消费变动明细表"]),console.log("=>(CustomerSpendReport.vue:474) rows",o,r),r&&r.forEach(function(e){o.push.apply(o,[[],["卡号："+(e.card_info.CardNo||"-")+"   卡面卡号："+(e.card_info.CardNumber||"-")+"   车牌："+(e.card_info.CarNumber||"-")+"   卡名称："+(e.card_info.CardName||"-")],i.copySon.map(function(e){return e.name})].concat(d()(e.card_list.map(function(e){return i.copySon.map(function(t){return e[t.field]})}))))});var u=x.b.aoa_to_sheet(o);console.log("=>(CustomerSpendReport.vue:977) rows",o),u["!merges"]=[x.b.decode_range("A1:"+String.fromCharCode(t.length+a-1)+"1"),x.b.decode_range("A3:"+String.fromCharCode(t.length+a-1)+"3")],u["!cols"]=t.map(function(e,t){return[0,1,2,3,4,11].includes(t)?{wch:30}:{}});var p=x.b.book_new();x.b.book_append_sheet(p,u,"Sheet1"),x.c(p,"客户资金变动明细表-"+c+".xlsx")},a=65,e.$nextTick(_()(p.a.mark(function t(){var a,r,s,o,d,c,l,u;return p.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(a=0,console.log("=>  file: CustomerSpendReport.vue:1065  this.tableList:",e.tableList,e.tableList.length),r=[],e.tableList&&e.tableList.length>0&&(r=e.tableList.filter(function(e){return e.tableData.length>0})),!r||0!==r.length){t.next=7;break}return e.$message.error("当前无数据，无需下载"),t.abrupt("return");case 7:s=!0,o=!1,d=void 0,t.prev=10,c=i()(r);case 12:if(s=(l=c.next()).done){t.next=23;break}if(u=l.value,a++,n.call(e,u,e.tableColumns,u.companyName),!(a>=10)){t.next=20;break}return t.next=19,e.pause(2e3);case 19:a=0;case 20:s=!0,t.next=12;break;case 23:t.next=29;break;case 25:t.prev=25,t.t0=t.catch(10),o=!0,d=t.t0;case 29:t.prev=29,t.prev=30,!s&&c.return&&c.return();case 32:if(t.prev=32,!o){t.next=35;break}throw d;case 35:return t.finish(32);case 36:return t.finish(29);case 37:case"end":return t.stop()}},t,e,[[10,25,29,37],[30,,32,36]])})));case 3:case"end":return t.stop()}},t,e)}))()},getCompanyName:function(e){var t="";return this.companyOptions.forEach(function(a){a.ID==e&&(t=a.CompanyName)}),t}},watch:{getCurrentStation:function(e,t){var a=this;return _()(p.a.mark(function n(){return p.a.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:0!=e.merchant_type&&e.value!=t.value&&(a.getCurrentStation&&2==a.getCurrentStation.merchant_type?a.isGroup=!0:a.isGroup=!1,a.getStations(),a.getCompanyList(),a.getCardReportColumn());case 1:case"end":return n.stop()}},n,a)}))()}}},O={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"pt-2"},[a("invoice-query",{attrs:{currentCompanies:e.currentCompanies,company_id:e.query.company_id,stid:e.query.stid,type:e.query.type,customer_type:e.query.customer_type,invoice_type:e.query.invoice_type,disabledDate:function(e){return e.getTime()>new Date((new Date).setHours(0,0,0,0)+864e5-1)},date_value:e.query.dateValue},on:{"update:currentCompanies":function(t){e.currentCompanies=t},"update:current-companies":function(t){e.currentCompanies=t},"update:type":[function(t){e.query.dateValue=[]},function(t){return e.$set(e.query,"type",t)}],"update:company_id":function(t){return e.$set(e.query,"company_id",t)},"update:stid":function(t){return e.$set(e.query,"stid",t)},"update:customer_type":function(t){return e.$set(e.query,"customer_type",t)},"update:invoice_type":function(t){return e.$set(e.query,"invoice_type",t)},"update:date_value":[e.handleDateChange,function(t){return e.$set(e.query,"dateValue",t)}]},scopedSlots:e._u([{key:"btns",fn:function(){return[a("div",{staticClass:"flex items-center space-x-2"},[a("el-button",{attrs:{type:"primary",disabled:e.disableAll},on:{click:function(t){return e.search()}}},[e._v("生成")]),e._v(" "),a("el-button",{attrs:{type:"primary",disabled:e.disableAll},on:{click:function(t){e.checkFlag=1,e.sureBranch()}}},[e._v("批量打印")]),e._v(" "),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.downloadLoading,expression:"downloadLoading"}]},[a("el-button",{attrs:{type:"primary",disabled:e.disableAll},on:{click:e.exportData}},[e._v("批量下载")])],1)],1)]},proxy:!0}])}),e._v(" "),a("div",{attrs:{id:"myTable"}},[a("div",{staticClass:"text-center"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.disableAll,expression:"disableAll"}],attrs:{"element-loading-text":"正在处理数据，请稍等"}},[[a("div",{staticClass:"text-24px font-bold mt-20px py-5px"},[e._v("客户消费变动明细表")]),e._v(" "),a("div",{staticClass:"flex py-10px px-20px"},[e.isGroup?a("div",[e._v("集团名称："+e._s(e.getCurrentStation.label))]):a("div",[e._v("油站名称："+e._s(e.getCurrentStation.label))]),e._v(" "),a("div",{staticClass:"mx-40px"},[e._v("开始时间："+e._s(e.query.dateValue.length?e.query.dateValue[0]:""))]),e._v(" "),a("div",[e._v("结束时间："+e._s(e.query.dateValue.length?e.query.dateValue[1]:""))])])],e._v(" "),0===e.tableList.length?[a("el-table",{ref:"table",attrs:{data:e.tableList,border:"",size:"small"}},[e._l(e.tableColumns,function(e,t){return[a("el-table-column",{attrs:{align:"center",label:e.name,prop:e.field}})]})],2)]:e._l(e.tableList,function(t,n){return a("div",{key:n,attrs:{hidden:e.disableAll}},[a("div",{staticClass:"text-left text-14px mt-20px px-20px h-40px leading-40px border-x-1px border-t-1px border-b-0 border-solid border-#ebeef5 flex items-center justify-between"},[a("span",[e._v(e._s(t.companyName))]),e._v(" "),a("span",[e._v("车队ID:"+e._s(t.fleetId))]),e._v(" "),a("span",[e._v("客户类型："+e._s(e.getCompanyCustomerType(t.fleetId)))])]),e._v(" "),a("el-table",{ref:"table",refInFor:!0,attrs:{data:t.tableData,border:"",size:"small"}},[e._l(e.tableColumns,function(e){return[a("el-table-column",{attrs:{align:"center",label:e.name,prop:e.field}})]})],2),e._v(" "),n<e.tableList.length-1?a("div",{staticStyle:{"page-break-after":"always"}}):e._e(),e._v(" "),a("div",{staticClass:"page-break"})],1)})],2)])]),e._v(" "),a("el-dialog",{attrs:{title:1==e.checkFlag?"批量打印":"批量下载",visible:e.showAllChecks,"close-on-click-modal":!1,width:"800px"},on:{"update:visible":function(t){e.showAllChecks=t},open:e.closeAll}},[a("div",[a("el-checkbox",{staticClass:"mb-4px",attrs:{indeterminate:e.isIndeterminate},on:{change:e.handleCheckAllChange},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[e._v("\n        全选\n      ")]),e._v(" "),a("el-checkbox-group",{on:{change:e.checkOne},model:{value:e.checkedValue,callback:function(t){e.checkedValue=t},expression:"checkedValue"}},e._l(e.companyOptions,function(t){return a("el-checkbox",{key:t.ID,staticClass:"mb-4px",attrs:{label:t.ID,disabled:t.disabled}},[e._v("\n          "+e._s(t.CompanyName)+"\n        ")])}),1)],1),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.showAllChecks=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.sureBranch()}}},[e._v("确 认")])],1)]),e._v(" "),a("download-tips",{attrs:{showDownloadTips:e.showDownloadTips,autoClose:!0},on:{"update:showDownloadTips":function(t){e.showDownloadTips=t},"update:show-download-tips":function(t){e.showDownloadTips=t}}})],1)},staticRenderFns:[]};var A=a("VU/8")(V,O,!1,function(e){a("2lhR")},"data-v-1bc8c637",null);t.default=A.exports}});