<template>
    <!-- 下载中心提示 -->
    <el-dialog
        title="提示"
        :visible.sync="showDownloadTips"
        @close="close"
        width="30%">
        <span>下载任务已发起，请到下载中心下载数据</span>
        <span slot="footer" class="dialog-footer">
            <el-button size="mini" @click="close">关 闭{{autoClose ? `(${countdown}S)` : ''}}</el-button>
            <el-button size="mini" type="primary" @click="goToDownloadCenter">去下载中心</el-button>
        </span>
    </el-dialog>
</template>

<script>

export default {
    props:{
        showDownloadTips: Boolean,
        autoClose: {
            type: Boolean,
            default: false
        },
        autoCloseTime: {
            type: Number,
            default: 30
        }
    },
    data () {
        return {
            countdown: 0,
            timer: null
        }
    },
    watch: {
        showDownloadTips(val) {
            if(val && this.autoClose) {
                this.countdown = this.autoCloseTime;
                this.startCountdown();
            } else {
                this.clearTimer();
            }
        }
    },
    mounted(){
    },
    methods: {
        startCountdown() {
            this.clearTimer();
            this.timer = setInterval(() => {
                if(this.countdown > 0) {
                    this.countdown--;
                } else {
                    this.close();
                }
            }, 1000);
        },
        clearTimer() {
            if(this.timer) {
                clearInterval(this.timer);
                this.timer = null;
            }
        },
        close(){
            this.clearTimer();
            this.$emit("update:showDownloadTips",false);
        },
        goToDownloadCenter(){
            this.clearTimer();
            this.$emit("update:showDownloadTips",false);
            this.$router.push('/DownloadList');
        }
    },
    beforeDestroy() {
        this.clearTimer();
    }
}
</script>

<style scoped>

</style>
