<template>
    <div class="cardTheme" id="cardTheme">

        <div class="card-content">
            <!-- 储值卡列表 -->
            <div class="wrap-box" v-show="showCardList">

                <!-- 标签切换 -->
                <div class="tab-box">
                    <div class="item">
                        <span class="tab">状态</span>
                        <el-radio-group v-model="state" @change="stateChange">
                            <el-radio-button v-for="item in stateList" :key="item.key" :label="item.value">{{item.label}}</el-radio-button>
                        </el-radio-group>
                    </div>
                    <div class="item">
                        <span class="tab">可用油站</span>
                        <el-radio-group v-model="station_id" @change="stationChange">
                            <el-radio-button :label="0">全部</el-radio-button>
                            <el-radio-button v-for="item in stationList" :key="item.key" :label="item.stid">{{item.stname}}</el-radio-button>
                        </el-radio-group>
                    </div>
                </div>
                <!-- 卡数据列表 -->
                <div class="card-wrap">
                    <div class="card-box" v-loading="loading">
                        <div class="item first-item" @click="goToAddCard">
                            <i class="el-icon-plus icon-plus"></i>
                            <p style="color:#666;font-size:14px">添加卡名称</p>
                        </div>
                        <div class="item" :class="{defaultCard:item.Priority == 1,disableCard:item.State == 101 || !fliterCard(item.StartingTime,item.CutOffTime)}" v-for="item in cardThemeRuleList" :key="item.index" @click="edit(item.ID)">
                            <div class="tips" v-if="!fliterCard(item.StartingTime,item.CutOffTime)">已过期</div>
                            <div class="tips" v-if="item.State == 101">已禁用</div>
                            <el-button class="edit" type="text" icon="el-icon-edit-outline">编辑</el-button>
                            <span class="name">{{item.Name}}</span>
                            <span v-if="item.StartingTime" class="time">{{$moment(item.StartingTime).format("YYYY-MM-DD HH:mm:ss")+'至'+$moment(item.CutOffTime).format("YYYY-MM-DD HH:mm:ss")}}</span>
                        </div>
                    </div>

                    <!-- 页码 -->
                    <div class="page_content">
                        <el-pagination class="page_left"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-size="pageSize"
                            layout="prev, pager, next"
                            :total="totalNumber">
                        </el-pagination>
                        <el-pagination class="page_right"
                            @size-change="handleSizeChange"
                            :page-sizes="[9, 15, 30]"
                            :page-size="pageSize"
                            layout="total, sizes"
                            :total="totalNumber">
                        </el-pagination>
                    </div>
                </div>
            </div>
            <!-- 创建卡 -->
            <div v-show="!showCardList">
                <el-steps class="card-step" :active="stepActive" finish-status="success" simple style="margin-top: 20px">
                    <el-step title="基本设置" ></el-step>
                    <el-step title="充值设置" ></el-step>
                    <el-step title="消费设置" ></el-step>
                </el-steps>
                <!-- 基础设置 -->
                <el-form v-show="showBaseForm" :model="baseForm" :rules="baseRules" ref="baseRuleForm" label-width="100px" class="demo-ruleForm card-form base-form">
                    <el-form-item label="卡名称" prop="rule_name">
                        <el-input v-model="baseForm.rule_name" placeholder="请输入卡名称" style="width:250px"></el-input>
                    </el-form-item>

                    <el-form-item label="起止时间" prop="date">
                        <el-date-picker v-model="baseForm.date" :default-time="['00:00:00', '23:59:59']" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 360px;"></el-date-picker>
                    </el-form-item>
                    <el-form-item label="规则描述" prop="description">
                        <el-input type="textarea" v-model="baseForm.description" placeholder="请输入规则描述" :rows="5" style="width: 360px; " maxlength="100" show-word-limit></el-input>

                    </el-form-item>
                    <el-form-item label="卡有效期" prop="expire_value">
                        <el-input v-model="baseForm.expire_value" style="width:90px"></el-input>个月
                        <i class="el-icon-warning warning-icon"></i>到期后卡将无法使用，请慎重填写，0表示永久有效
                    </el-form-item>
                    <el-form-item label="可用油站">
                        <el-cascader v-model="baseForm.stationList" :options="stationList" :props="stationListProps" collapse-tags  style="width:250px">
                        </el-cascader>
                    </el-form-item>
                    <el-form-item label="规则适用类型">
                        <!-- <el-radio-group v-model="baseForm.cus_type" @change="changeRuleType" :disabled="isYKJ">
                            <el-radio label="">通用</el-radio>
                            <el-radio label="1">个人卡账</el-radio>
                        </el-radio-group> -->
                        <el-select v-model="baseForm.cus_type" @change="changeRuleType" :disabled="isYKJ || !isCreate" style="width:250px">
                            <el-option label="通用" value="">通用</el-option>
                            <el-option label="个人卡账" value="1">个人卡账</el-option>
                        </el-select>
                        <!-- <span style="margin-left:50px;">本条制卡规则适用的卡类型</span> -->
                    </el-form-item>
                    <el-form-item label="营销方式" v-show="baseForm.cus_type == 1">
                        <!-- <el-radio-group v-model="baseForm.market_type" @change="changeSaleType" :disabled="isYKJ">
                            <el-radio label="1">通用</el-radio>
                            <el-radio label="2">定额（一口价）</el-radio>
                            <el-radio label="3">定升锁价卡</el-radio>
                        </el-radio-group> -->
                        <el-select  v-model="baseForm.market_type" @change="changeSaleType" :disabled="isYKJ || !isCreate" style="width:250px" >
                            <el-option label="通用" value="1">通用</el-option>
                            <el-option label="定额锁价卡" value="2">定额锁价卡</el-option>
                            <el-option label="定升锁价卡" value="3">定升锁价卡</el-option>
                        </el-select>
                        <i class="el-icon-warning warning-icon" ></i>默认勾选通用，仅发行一口价卡时需要修改选项，规则确定后不可修改
                    </el-form-item>
                    <el-form-item label="可用项目">
                        <el-cascader v-model="baseForm.oilList" :options="oilList" :props="oilListProps" collapse-tags  style="width:250px">
                        </el-cascader>
                    </el-form-item>
                    <el-form-item label="开票方式">
                        <el-select v-model="baseForm.invoice_open_type" placeholder="请选择" style="width:250px">
                            <el-option label="消费开票" value="1">消费开票</el-option>
                            <el-option label="充值开票" value="0">充值开票</el-option>
                            <!-- <el-option label="增值税月底开票" value="2">增值税月底开票</el-option> -->
                        </el-select>
                    </el-form-item>

                    <el-form-item label="发行卡介质">
                        <el-radio-group v-model="baseForm.show_restriction_type" :disabled="!isCreate">
                            <el-radio label="0">通用</el-radio>
                            <el-radio label="1">仅发行云端卡</el-radio>
                            <el-radio label="2">仅发行实体卡</el-radio>
                        </el-radio-group>
                        <i class="el-icon-warning warning-icon"></i>规则限定后不可修改，请谨慎选择
                    </el-form-item>

                    <el-form-item label="默认规则">
                        <el-radio-group v-model="baseForm.priority">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-radio-group v-model="baseForm.state">
                            <el-radio label="100">启用</el-radio>
                            <el-radio label="101">禁用</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="充值">
                        <el-radio-group v-model="isAllowRecharge">
                            <el-radio label="1">允许</el-radio>
                            <el-radio label="0">禁止</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item>
                        <el-button @click="resetForm('baseRuleForm')">取消</el-button>
                        <el-button type="primary" @click="baseNext('baseRuleForm')">下一步</el-button>
                    </el-form-item>
                </el-form>
                <!-- 充值设置 -->
                <div v-show="showRechargeForm" class="card-form recharge-form">
                    <p class="title" v-show="baseForm.market_type == 1">卡启用预充值<span style="color:#999; margin-left:14px;">实体卡才需要填写</span></p>
                    <el-form :model="enable_rule" class="card-form" label-width="70px" v-show="baseForm.market_type == 1" :rules="topUpRule" >
                        <el-form-item label="充值本金">
                            <el-input onkeyup="value=value.replace(/[^\d\.]/g,'')" v-model="enable_rule.card_charge_money" placeholder="0.00" style="width:250px"></el-input>
                            <span>元 <i>领卡时车主需缴纳的充值金额</i></span>

                        </el-form-item>
                        <el-form-item label="卡面值" prop="card_initial_money">
                            <el-input onkeyup="value=value.replace(/[^\d\.]/g,'')" v-model="enable_rule.card_initial_money" placeholder="0.00" style="width:250px"></el-input>
                            <span>元 <i>充值本金+充值的赠送金额</i></span>

                        </el-form-item>
                        <el-form-item label="制卡费">
                            <el-input onkeyup="value=value.replace(/[^\d\.]/g,'')" v-model="enable_rule.card_printing_cost" placeholder="0.00" style="width:250px"></el-input>
                            <span>元 <i>卡片本体需收取的费用</i></span>

                        </el-form-item>
                    </el-form>
                    <div v-show="isAllowRecharge == 1">
                        <p class="title">充值金额</p>
                        <el-form :model="recharge_rule" :rules="rechargeRules" ref="rechargeRuleForm" class="card-form" label-width="110px">
                            <el-form-item label="单次充值上限">
                                <el-input onkeyup="value=value.replace(/[^\d\.]/g,'')" v-model="recharge_rule.charge_max" placeholder="0.00" style="width:250px"></el-input>
                                <span>元</span>
                            </el-form-item>
                            <el-form-item label="单次充值下限">
                                <el-input onkeyup="value=value.replace(/[^\d\.]/g,'')" v-model="recharge_rule.charge_min" placeholder="0.00" style="width:250px"></el-input>
                                <span>元</span>
                            </el-form-item>
                            <el-form-item label="余额上限">
                                <el-input v-model="recharge_rule.balance_max" placeholder="0.00" style="width:250px"></el-input>
                                <span>元（填0表示不限制）</span>
                            </el-form-item>
                            <el-form-item label="充值次数限制" prop="charge_limit_count">
                                <el-input v-model="recharge_rule.charge_limit_count" style="width:90px"></el-input> 次（最多充值次数，0为不限制）
                            </el-form-item>
                            <el-form-item label="限制类型">
                                <div class="card-radio-group">
                                    <div v-show="baseForm.market_type == 1">
                                        <el-radio v-model="recharge_rule.charge_money_limit_type" label="0">无</el-radio>
                                    </div>
                                    <div>
                                        <el-radio v-model="recharge_rule.charge_money_limit_type" label="2">固定值</el-radio>
                                        <el-input v-show="recharge_rule.charge_money_limit_type == 2" v-model="recharge_rule.charge_money_fixed" placeholder="请输入固定值" style="width:250px"></el-input>
                                        <span v-show="recharge_rule.charge_money_limit_type == 2"> 元</span>
                                        <i>仅允许充值输入的固定值，各数值之间以英文的逗号隔开。例输入值300,500,1000，则充值时只允许充这三个数值。</i>
                                    </div>
                                    <div>
                                        <el-radio v-model="recharge_rule.charge_money_limit_type" label="1" :disabled="baseForm.market_type == 2 || baseForm.market_type == 3 ? true :false">整倍数</el-radio>
                                        <el-input v-show="recharge_rule.charge_money_limit_type == 1" v-model="recharge_rule.charge_money_integer" placeholder="请输入整数" @keyup.native="integerNumber" style="width:250px"></el-input>
                                        <i>仅允许以输入值的整数倍充值。例输入值100，充值只能充100、200、300......</i>
                                    </div>
                                    <div>
                                        <el-radio v-model="recharge_rule.charge_money_limit_type" label="3" :disabled="baseForm.market_type == 2 || baseForm.market_type == 3 ? true :false">不限制</el-radio>
                                        <el-input v-show="recharge_rule.charge_money_limit_type == 3" v-model="recharge_rule.charge_money_unlimited" placeholder="请输入快捷充值金额" style="width:250px"></el-input>
                                        <i>允许充值任意金额，快捷充值金额显示于车主云端充值页面，最多设置4个，各数值之间以英文逗号隔开。</i>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-form>
                    </div>
                    <div class="btn-box">
                        <el-button @click="rechargeBefore()">上一步</el-button>
                        <el-button type="primary" @click="rechargeNext('rechargeRuleForm')">下一步</el-button>
                    </div>
                </div>
                <!-- 花费设置 -->
                <div v-show="showSpendForm" class="card-form spend-form">
                    <p class="title">消费设置</p>
                    <el-form :model="consume_rule" :rules="consumeRules" class="radio-form" ref="consume_rule">
                        <el-form-item label="优惠券抵扣" label-width="110px">
                            <el-radio-group v-model="consume_rule.is_allow_coupon">
                                <el-radio label="1">允许</el-radio>
                                <el-radio label="0">禁止</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <!-- <el-form-item label="积分抵油" label-width="110px">
                            <el-radio-group v-model="consume_rule.is_allow_bonus">
                                <el-radio label="1">允许</el-radio>
                                <el-radio label="0">禁止</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="积分兑换" label-width="110px">
                            <el-radio-group v-model="consume_rule.is_allow_bonus_exchange">
                                <el-radio label="1">允许</el-radio>
                                <el-radio label="0">禁止</el-radio>
                            </el-radio-group>
                        </el-form-item> -->
                        <el-form-item label="持实体卡免密" label-width="110px">
                            <el-radio-group v-model="consume_rule.is_need_password">
                                <el-radio label="1">允许</el-radio>
                                <el-radio label="0">禁止</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="消费次数限制" label-width="110px" prop="consumption_limit">
                            <el-input v-model="consume_rule.consumption_limit" style="width:90px"></el-input>
                            <span>次（每日最多消费次数，0为不限制）</span>
                        </el-form-item>
                        <el-form-item>
                            <el-button @click="spendBefore()">上一步</el-button>
                            <el-button type="primary" :disabled="btnDisabled" v-if="isCreate" @click="createCard('consume_rule')">创建</el-button>
                            <el-button type="primary" :disabled="btnDisabled" v-if="!isCreate" @click="createCard('consume_rule')">确定</el-button>
                        </el-form-item>
                    </el-form>

                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {mapGetters} from 'vuex'
export default {
    name: 'CardTheme',
    data() {
        let checkNum = (rule,value,callback) => {
            let regx = new RegExp("^[0-9][0-9]*$");
            if(value === "") {
                callback(new Error('请输入有效期'));
            }
            if(value < 0) {
                callback(new Error('有效期不能小于0'));
            }
            if(!regx.test(value)) {
                callback(new Error('请输入整数'));
            }else {
                callback()
            }
        }
        let checkTimes = (rule,value,callback) => {
            let regx = new RegExp("^[0-9][0-9]*$");
            if(value === "") {
                callback(new Error('请输入次数'));
            }
            if(value < 0) {
                callback(new Error('次数不能小于0'));
            }
            if(!regx.test(value)) {
                callback(new Error('请输入整数'));
            }else {
                callback()
            }
        }
        let validateCard = (rule,value,callback) =>{
            console.log('value',value);
            if(value < this.enable_rule.card_charge_money){
                callback(new Error('卡面值需要大于充值本金'))
            }else{
                callback()
            }
        }
        return {
            topUpRule:{
                card_initial_money:[{validator: validateCard, trigger: 'blur'}]
            },
            showCardList:true,//默认显示储值卡列表
            cardThemeRuleList:[],//卡列表
            loading:true,
            state:0,//当前状态默认全部为0
            stateList:[
                {
                    value:0,
                    label:"全部"
                },
                {
                    value:100,
                    label:"启用"
                },
                {
                    value:101,
                    label:"禁用"
                },
            ],
            station_id:0,//当前油站id默认全部0
            currentPage:1,//当前页面
            pageSize:9,
            totalNumber:1,//总条数
            stepActive:0,//步骤标识
            showBaseForm:false,//默认展示基础设置
            showRechargeForm:false,//充值设置，默认隐藏
            showSpendForm:false,//花费设置，默认隐藏
            stationList:[],//可用油站列表
            stationListProps:{
                multiple: true,
                label: "stname",
                value: "stid",
            },
            oilList:[],//可用项目列表
            oilListProps:{
                multiple: true,
                label: "name",
                value: "oil_id",
                children: "parent_arr"
            },
            baseForm:{
                rule_name:"",//卡名称
                date:[],//时间
                priority:"0",
                cus_type:"",//规则类型
                market_type:"1",//营销方式
                description:"",//规则说明
                expire_type:2,//有效期类型 0：不限制， 1:日, 2:月, 3:年
                expire_value:0,//有效期
                state:"100",//规则状态，100启用，101禁用
                stationList:[],//可用油站列表
                oilList:[],//可用项目
                originOilList:[], //原始可用项目
                invoice_open_type:"1",//默认消费开票
                show_restriction_type:"0",//发行卡介质,默认通用
            },
            isAllowRecharge:"1",//是否允许充值，1为允许0为禁止
            baseRules: {
                rule_name: [
                    { required: true, message: '请输入卡名称', trigger: 'blur' },
                    { max: 24, message: '主题不能超过24个字符', trigger: 'blur' }
                ],
                // date: [
                //     { required: true, message: '请选择日期', trigger: 'change' }
                // ],
                description:[
                    {max: 100, message: '规则不能超过100个字符', trigger: 'blur' }
                ],
                expire_value:[
                    { required: true, validator:checkNum, trigger: 'blur' },
                ]
            },
            enable_rule:{//启用规则
                card_printing_cost:"",//制卡费
                card_initial_money:"",//卡面值
                card_charge_money:"",//充值本金
            },
            recharge_rule:{//充值规则
                charge_max:"",//充值上限
                charge_min:"",//充值下限
                balance_max:"",//充值下限
                charge_money_limit_type:"0",//0不限制，1整倍数，2固定值
                charge_money_integer:"",//整数值
                charge_money_fixed:"",//固定值
                charge_money_unlimited:"",//不限制
                charge_limit_count:"0",//充值次数
            },
            rechargeRules:{
                charge_limit_count:[
                    { required: true, validator:checkTimes, trigger: 'blur' },
                ]
            },
            consume_rule:{
                oil_list:[],//可用油品列表
                is_allow_coupon:"1",//优惠券抵扣，0禁止，1允许
                is_allow_bonus:"1",//积分抵油，同上
                is_allow_bonus_exchange:"1",//积分兑换，同上
                is_need_password:"1",//持卡免密，同上
                consumption_limit:"0",//消费次数限制
            },
            consumeRules:{
                consumption_limit: [
                    { required: true, message: '请输入消费次数限制', trigger: 'blur' }
                ],
            },
            isCreate:true,//判断创建还是编辑，true为创建，false为编辑
            cardId:"",//卡id
            btnDisabled:false,//创建按钮禁止，默认可以点击
            isYKJ:false,//编辑的时候，如果是一口价的规则，则不能改动卡类型和营销方式
            groupSettleStid: -1 //集团清结算虚拟站id
        }
    },
    created() {
    },
    mounted() {
        if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
            return false;
        }
        this.getCardThemeRuleList(0,0);
        this.getStationList();
        this.getOilInfo();
    },
    computed:{
        ...mapGetters({
            "getCurrentStation":"getCurrentStation"
        })
    },
    methods: {
        //获取卡列表
        getCardThemeRuleList(){
            this.loading = true;
            this.$axios.post('/CardRule/getCardThemeRuleList',{
                state:this.state,
                station_id:this.station_id,
                page:this.currentPage,
                page_size:this.pageSize,
            }).then((res)=>{
                this.loading = false;
                if(res.data.status == 200){
                    this.cardThemeRuleList = res.data.data.dt;
                    this.totalNumber = res.data.data.TotalQty;
                }
            })
        },
        //切换页码
        handleCurrentChange(val){
            this.currentPage = val;
            this.getCardThemeRuleList();
        },
        handleSizeChange(val){
            this.pageSize = val;
            this.getCardThemeRuleList();
        },
        //状态切换
        stateChange(val){
            this.currentPage = 1;
            this.getCardThemeRuleList();
        },
        //油站切换
        stationChange(val){
            this.currentPage = 1;
            this.getCardThemeRuleList();
        },
        //获取可用油站
        getStationList(){
            this.baseForm.stationList = [];
            this.stationList = [];
            this.groupSettleStid = localStorage.getItem('__groupSettle__');
            console.log(this.groupSettleStid);

            this.$axios.post('/Stations/getStationList',{}).then((res)=>{
                if(res.data.status == 200){
                    this.stationList = res.data.data.filter(obj=>obj.stid != this.groupSettleStid);
                    res.data.data.forEach((element)=>{
                        this.baseForm.stationList.push([element.stid]);
                    });
                }
            })
        },
        //获取可用项目
        getOilInfo(){
            this.$axios.post('/Oil/getOilInfo',{}).then((res)=>{
                if(res.data.status == 200){
                    this.oilList = res.data.data;
                    this.oilList.push({
                        name: "便利店",
                        parent_arr: [{
                            name: "便利店",
                            oil_id: "shop"
                        }],
                        type_id: "-99"
                    });
                    this.oilList.forEach((element)=>{
                        element.parent_arr.forEach((sub)=>{
                            this.baseForm.oilList.push([undefined,sub.oil_id]);
                        })
                    });
                    this.originOilList = this.oilList
                }
            })
        },
        //处理输入数值为整数
        integerNumber(){
            this.recharge_rule.charge_money_integer=this.recharge_rule.charge_money_integer.replace(/[^\.\d]/g,'');
            this.recharge_rule.charge_money_integer=this.recharge_rule.charge_money_integer.replace('.','');
            if(/^0+/.test(this.recharge_rule.charge_money_integer)){
                this.recharge_rule.charge_money_integer = this.recharge_rule.charge_money_integer.replace(/^0+/, '');
            }
        },
        //去添加卡
        goToAddCard(){
            this.showCardList = false;
            this.showBaseForm = true;
            this.isCreate = true;
            this.clearFormData();
        },
        //基础设置下一步
        baseNext(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    //编辑状态下判断,如果此卡下有数据，则不能禁用
                    if(!this.isCreate && this.baseForm.state == "101"){
                        this.$axios.post('/Card/getUserCardList',{
                            page:1,//页码
                            page_size:50,//每页数量
                            input:"",//输入
                            card_theme_id:this.cardId,//卡名称id
                            card_type_id:0,//卡类型id
                            customer_group_id:0,//分组id
                            status:0,//状态
                        }).then((res)=>{
                            if(res.data.status == 200){
                                if(res.data.data.TotalQty > 0){
                                    this.baseForm.state = "100";
                                    this.$message.error("此卡不能禁用");
                                }else{
                                    this.stepActive = 1;
                                    this.showBaseForm = false;
                                    this.showRechargeForm = true;
                                };
                            }else{
                                this.$message.error(res.data.info);
                            }
                        });
                    }else{
                        this.stepActive = 1;
                        this.showBaseForm = false;
                        this.showRechargeForm = true;
                    }
                } else {
                    return false;
                }
            });
        },
        //取消
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.showCardList = true;
            this.showBaseForm = false;
        },
        //充值设置上一步
        rechargeBefore(){
            this.stepActive = 0;
            this.showBaseForm = true;
            this.showRechargeForm = false;
        },
        //充值设置下一步
        rechargeNext(formName){
            this.$refs[formName].validate((valid) => {
                if (valid) {

                    if(this.recharge_rule.charge_money_limit_type == 1 && !this.recharge_rule.charge_money_integer){
                        this.$message.error("请输入整倍数值");
                        return false;
                    }
                    if(this.recharge_rule.charge_money_limit_type == 2 && !this.recharge_rule.charge_money_fixed){
                        this.$message.error("请输入固定值");
                        return false;
                    }
                    if(this.recharge_rule.charge_money_limit_type == 3 && !this.recharge_rule.charge_money_unlimited){
                        this.$message.error("请输入金额");
                        return false;
                    }

                    if(this.recharge_rule.charge_max && this.recharge_rule.charge_min && Number(this.recharge_rule.charge_max) < Number(this.recharge_rule.charge_min)){
                        this.$message.error("单次充值上限不能小于单次充值下限");
                        return false;
                    }

                    if(this.recharge_rule.charge_money_fixed){

                        let numArr = this.recharge_rule.charge_money_fixed.split(",");
                        var nary = numArr.sort();
                        var ret=/^\d+(\.\d{1,2})?$/;
                        for(var i = 0; i < nary.length - 1; i++) {
                            if(nary[i] == nary[i + 1]) {
                                this.$message.error("固定值不能有重复");
                                return false;
                            }
                        }
                        for(var i = 0; i < numArr.length; i++) {
                            if(numArr[i] < 0){
                                this.$message.error("固定值不能有负数");
                                return false;
                            }
                            if(!numArr[i]){
                                this.$message.error("固定值不能为空");
                                return false;
                            }
                            if(!ret.test(numArr[i])){
                                this.$message.error("固定值不能超过两位小数");
                                return false;
                            }
                        }
                    }
                    if(this.recharge_rule.charge_money_unlimited){

                        if(this.recharge_rule.charge_money_unlimited.charAt(this.recharge_rule.charge_money_unlimited.length - 1) == ','){
                            this.$message.error("不能以逗号结尾");
                            return false;
                        }

                        let numArr = this.recharge_rule.charge_money_unlimited.split(",");
                        var nary = numArr.sort();
                        var ret=/^\d+(\.\d{1,2})?$/;
                        if(nary.length > 4){
                            this.$message.error("数值最多四个");
                            return false;
                        }
                        for(var i = 0; i < numArr.length; i++) {
                            if(numArr[i] < 0){
                                this.$message.error("数值不能有负数");
                                return false;
                            }
                            if(!numArr[i]){
                                this.$message.error("数值不能为空");
                                return false;
                            }
                            if(!ret.test(numArr[i])){
                                this.$message.error("数值不能超过两位小数");
                                return false;
                            }
                        }
                        for(var i = 0; i < nary.length - 1; i++) {
                            if(nary[i] == nary[i + 1]) {
                                this.$message.error("数值不能有重复");
                                return false;
                            }
                        }
                    }
                    this.stepActive = 2;
                    this.showRechargeForm = false;
                    this.showSpendForm = true;
                }
            })
        },
        //消费设置上一步
        spendBefore(){
            this.stepActive = 1;
            this.showRechargeForm = true;
            this.showSpendForm = false;
        },
        //创建
        createCard(formName){
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.btnDisabled = true;
                    let params = {
                        "public_config":{
                            "id":"",
                            "rule_name":"测试",//规则名称
                            "start_time":1579423414,//开始时间
                            "end_time":1589242211,//结束时间
                            "priority":0,
                            "cus_type":"",
                            "market_type":1,
                            "description":"",//规则说明
                            "expire_type":"",
                            "expire_value":"",//有效期
                            "state":100,//规则状态，100启用，101禁用
                            "use_station_list":[],//可用油站列表
                            "allow_charge":1,//允许充值
                            "show_restriction_type":"0"
                        },
                        "currency_rule":{//通用规则
                            "invoice_open_type":0//开票方式,0充值开票，1消费开票，2增值税月底开票
                        },
                        "enable_rule":{//启用规则
                            "card_printing_cost":100,//制卡费
                            "card_initial_money":100,//卡面值
                            "card_charge_money":100,//充值本金
                        },
                        "recharge_rule":{//充值规则
                            "charge_max":100,//充值上限
                            "charge_min":100,//充值下限
                            "balance_max":100,//余额上限
                            "charge_money_limit_type":0,//0不限制，1整倍数，2固定值
                            "charge_money_limit_data":100,//值
                            "charge_limit_count":0
                        },
                        "consume_rule":{//消费规则
                            "oil_list":[171073,177370],//可用油站列表
                            "is_allow_coupon":0,//优惠券抵扣，0禁止，1允许
                            "is_allow_bonus":0,//积分抵油，同上
                            "is_allow_bonus_exchange":0,//积分兑换，同上
                            "is_need_password":0,//持卡免密，同上
                            "consumption_limit":100,//消费次数限制
                        }
                    };

                    //public_config赋值
                    if(!this.isCreate){
                        params.public_config.id = this.cardId;
                    }
                    params.public_config.rule_name = this.baseForm.rule_name;
                    if(this.baseForm.date){
                        params.public_config.start_time = this.baseForm.date[0];
                        params.public_config.end_time = this.baseForm.date[1];
                    }else{
                        params.public_config.start_time = "";
                        params.public_config.end_time = "";
                    }
                    params.public_config.priority = this.baseForm.priority;
                    params.public_config.cus_type = this.baseForm.cus_type;
                    params.public_config.market_type = this.baseForm.market_type;
                    params.public_config.description = this.baseForm.description;
                    params.public_config.expire_type = this.baseForm.expire_type;
                    params.public_config.expire_value = this.baseForm.expire_value;
                    params.public_config.state = Number(this.baseForm.state);
                    params.public_config.allow_charge = Number(this.isAllowRecharge);
                    params.public_config.show_restriction_type = this.baseForm.show_restriction_type;

                    let stations = [];
                    this.baseForm.stationList.forEach((element)=>{
                        stations.push(element[0]);
                    });
                    // 默认添加虚拟站

                    if(this.groupSettleStid !== -1){
                      stations.push(this.groupSettleStid);
                    }
                    //去除重复元素
                    stations = [... new Set(stations)]

                    params.public_config.use_station_list = stations;

                    //currency_rule赋值
                    params.currency_rule.invoice_open_type = this.baseForm.invoice_open_type;

                    //enable_rule赋值
                    if(!this.enable_rule.card_printing_cost){
                        this.enable_rule.card_printing_cost = null;
                    }
                    if(!this.enable_rule.card_initial_money){
                        this.enable_rule.card_initial_money = null;
                    }
                    if(!this.enable_rule.card_charge_money){
                        this.enable_rule.card_charge_money = null;
                    }
                    params.enable_rule.card_printing_cost = this.enable_rule.card_printing_cost;
                    params.enable_rule.card_initial_money = this.enable_rule.card_initial_money;
                    params.enable_rule.card_charge_money = this.enable_rule.card_charge_money;

                    //recharge_rule赋值
                    if(!this.recharge_rule.charge_max){
                        this.recharge_rule.charge_max = null;
                    }
                    if(!this.recharge_rule.charge_min){
                        this.recharge_rule.charge_min = null;
                    }
                    if(!this.recharge_rule.balance_max){
                        this.recharge_rule.balance_max = 0;
                    }
                    if(this.recharge_rule.charge_money_limit_type == 0){
                        this.recharge_rule.charge_money_limit_data = 0;
                    }
                    if(this.recharge_rule.charge_money_limit_type == 1){
                        this.recharge_rule.charge_money_limit_data = this.recharge_rule.charge_money_integer;
                    }
                    if(this.recharge_rule.charge_money_limit_type == 2){
                        this.recharge_rule.charge_money_limit_data = this.recharge_rule.charge_money_fixed;
                    }
                    if(this.recharge_rule.charge_money_limit_type == 3){
                        this.recharge_rule.charge_money_limit_data = this.recharge_rule.charge_money_unlimited;
                    }

                    params.recharge_rule.charge_limit_count = this.recharge_rule.charge_limit_count;
                    params.recharge_rule.charge_max = this.recharge_rule.charge_max;
                    params.recharge_rule.charge_min = this.recharge_rule.charge_min;
                    params.recharge_rule.balance_max = this.recharge_rule.balance_max;
                    params.recharge_rule.charge_money_limit_type = this.recharge_rule.charge_money_limit_type;
                    params.recharge_rule.charge_money_limit_data = this.recharge_rule.charge_money_limit_data;

                    //consume_rule赋值
                    let oils = [];
                    this.baseForm.oilList.forEach((element)=>{
                        oils.push(element[1]);
                    });
                    params.consume_rule.oil_list = oils;
                    params.consume_rule.is_allow_coupon = Number(this.consume_rule.is_allow_coupon);
                    params.consume_rule.is_allow_bonus = Number(this.consume_rule.is_allow_bonus);
                    params.consume_rule.is_allow_bonus_exchange = Number(this.consume_rule.is_allow_bonus_exchange);
                    params.consume_rule.is_need_password = Number(this.consume_rule.is_need_password);
                    params.consume_rule.consumption_limit = this.consume_rule.consumption_limit;

                    this.$axios.post('/CardRule/setCardThemeRule',params).then((res)=>{
                        if(res.data.status == 200){
                            if(this.isCreate){
                                this.$message.success("创建成功");
                            }else{
                                this.$message.success("修改成功");
                            }
                            this.showCardList = true;
                            this.showBaseForm = false;
                            this.showRechargeForm = false;
                            this.showSpendForm = false;
                            this.btnDisabled = false;
                            this.getCardThemeRuleList();
                        }else{
                            this.btnDisabled = false;
                            this.$message.error(res.data.info);
                        }
                    })

                } else {
                    this.btnDisabled = false;
                    return false;
                }
            });
        },
        //编辑
        edit(id){
            this.cardId = id;
            this.isCreate = false;
            //获取卡信息并赋值
            this.$axios.post('/CardRule/getCardThemeRuleInfo',{
                id:id
            }).then((res)=>{
                if(res.data.status == 200){
                    let data = res.data.data;
                    this.baseForm.rule_name = data.public_config.rule_name;
                    this.baseForm.description = data.public_config.description;
                    this.baseForm.expire_type = data.public_config.expire_type;
                    this.baseForm.expire_value = data.public_config.expire_value;
                    this.baseForm.priority = String(data.public_config.priority);
                    this.baseForm.cus_type = data.public_config.cus_type;
                    this.baseForm.market_type = String(data.public_config.market_type);
                    this.baseForm.show_restriction_type = String(data.public_config.show_restriction_type);
                    //如果营销方式是一口价，则禁用卡类型和营销方式的选择
                    if(data.public_config.market_type == 2){
                        this.isYKJ = true;
                    }else{
                        this.isYKJ = false;
                    }
                    this.baseForm.date = [data.public_config.start_time,data.public_config.end_time];
                    this.baseForm.state = String(data.public_config.state);
                    this.isAllowRecharge = String(data.public_config.allow_charge);
                    this.baseForm.stationList = [];
                    if(data.public_config.use_station_list && data.public_config.use_station_list.length){
                        data.public_config.use_station_list.forEach((element)=>{
                            this.baseForm.stationList.push([element])
                        })
                    }else{
                        this.baseForm.stationList = [];
                    }

                    this.baseForm.oilList = [];
                    if(data.consume_rule.oil_list && data.consume_rule.oil_list.length){
                        data.consume_rule.oil_list.forEach((element)=>{
                            this.baseForm.oilList.push([undefined,element]);
                        })
                    }else{
                        this.baseForm.oilList = [];
                    }

                    this.enable_rule.card_charge_money = data.enable_rule.card_charge_money;
                    this.enable_rule.card_initial_money = data.enable_rule.card_initial_money;
                    this.enable_rule.card_printing_cost = data.enable_rule.card_printing_cost;

                    this.recharge_rule.charge_max = data.recharge_rule.charge_max;
                    this.recharge_rule.charge_min = data.recharge_rule.charge_min;
                    this.recharge_rule.balance_max = data.recharge_rule.balance_max;
                    this.recharge_rule.charge_limit_count = data.recharge_rule.charge_limit_count;
                    this.recharge_rule.charge_money_limit_type = String(data.recharge_rule.charge_money_limit_type);
                    if(data.recharge_rule.charge_money_limit_type == 1){
                        this.recharge_rule.charge_money_integer = data.recharge_rule.charge_money_limit_data
                    }
                    if(data.recharge_rule.charge_money_limit_type == 2){
                        this.recharge_rule.charge_money_fixed = data.recharge_rule.charge_money_limit_data
                    }
                    if(data.recharge_rule.charge_money_limit_type == 3){
                        this.recharge_rule.charge_money_unlimited = data.recharge_rule.charge_money_limit_data
                    }

                    this.baseForm.invoice_open_type = String(data.currency_rule.invoice_open_type);

                    this.consume_rule.consumption_limit = data.consume_rule.consumption_limit;
                    this.consume_rule.is_allow_bonus = String(data.consume_rule.is_allow_bonus);
                    this.consume_rule.is_allow_bonus_exchange = String(data.consume_rule.is_allow_bonus_exchange);
                    this.consume_rule.is_allow_coupon = String(data.consume_rule.is_allow_coupon);
                    this.consume_rule.is_need_password = String(data.consume_rule.is_need_password);

                    this.showCardList = false;
                    this.showBaseForm = true;

                }
            }).finally(()=>{
                // this.changeSaleType(this.baseForm.market_type)
                if(this.baseForm.market_type == 2 || this.baseForm.market_type == 3){
                    this.oilList = this.oilList.filter(item => item.name !== "便利店")
                    console.log(this.oilList);
                }else{
                    this.oilList = this.originOilList;
                }
            })

        },
        //清空表格数据
        clearFormData(){
            this.baseForm.description = "";
            this.baseForm.expire_type = "2";
            this.baseForm.expire_value = "0";
            this.baseForm.market_type = "1";
            this.baseForm.cus_type = "";
            this.baseForm.state = "100";
            this.baseForm.date = [];
            this.baseForm.stationList = [];
            this.stationList.forEach((element)=>{
                this.baseForm.stationList.push([element.stid]);
            });
            this.getOilInfo();
            this.baseForm.invoice_open_type = "1";
            this.baseForm.show_restriction_type = "0";

            this.enable_rule.card_printing_cost = "";
            this.enable_rule.card_initial_money = "";
            this.enable_rule.card_charge_money = "";
            this.recharge_rule.charge_max = "";
            this.recharge_rule.charge_min = "";
            this.recharge_rule.balance_max = "";
            this.recharge_rule.charge_money_integer = "";
            this.recharge_rule.charge_money_fixed = "";
            this.recharge_rule.charge_money_unlimited = "";
            this.recharge_rule.charge_limit_count = "0";
            this.recharge_rule.charge_money_limit_type = "0";

            this.consume_rule.is_allow_coupon = "1";
            this.consume_rule.is_allow_bonus = "1";
            this.consume_rule.is_allow_bonus_exchange = "1";
            this.consume_rule.is_need_password = "1";
            this.consume_rule.consumption_limit = "0";

        },
        //判断主题是否过期
        fliterCard(startTime,endTime){
            //如果时间存在进行判断是否过期
            if(startTime){
                let currentTime = new Date().getTime();
                let start = this.$moment(startTime).valueOf();
                let end = this.$moment(endTime).valueOf();
                if(currentTime>start && currentTime<end){
                    return true;
                }else{
                    return false;
                }
            }else{
                return true;
            }
        },
        //变更规则类型
        changeRuleType(val){
            if(val != 1){
                this.baseForm.market_type = "1";
                this.recharge_rule.charge_limit_count = 0;
                this.recharge_rule.charge_money_limit_type = "0";
            }
        },
        //变更营销方式
        changeSaleType(val){
            if(val == 2 || val == 3){
                this.recharge_rule.charge_limit_count = 1;
                this.recharge_rule.charge_money_limit_type = "2";
                this.oilList = this.oilList.filter(item => item.name !== "便利店")
                console.log(this.oilList);
            }else{
                this.oilList = this.originOilList;
                this.recharge_rule.charge_limit_count = 0;
                this.recharge_rule.charge_money_limit_type = "0";
            }
        }
    },
    watch: {
        "recharge_rule.charge_money_limit_type"(){
            if(this.recharge_rule.charge_money_limit_type == 0){
                this.recharge_rule.charge_money_integer = "";
                this.recharge_rule.charge_money_fixed = "";
                this.recharge_rule.charge_money_unlimited = "";
            }
            if(this.recharge_rule.charge_money_limit_type == 1){
                this.recharge_rule.charge_money_fixed = "";
                this.recharge_rule.charge_money_unlimited = "";
            }
            if(this.recharge_rule.charge_money_limit_type == 2){
                this.recharge_rule.charge_money_integer = "";
                this.recharge_rule.charge_money_unlimited = "";
            }
            if(this.recharge_rule.charge_money_limit_type == 3){
                this.recharge_rule.charge_money_integer = "";
                this.recharge_rule.charge_money_fixed = "";
            }
        },
        getCurrentStation(newValue,oldValue){
            if(newValue.merchant_type != 0 && newValue.value != oldValue.value){
                this.getCardThemeRuleList(0,0);
                this.getStationList();
                this.getOilInfo();
            }
        },
    },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->

<style scoped>
    @import '../assets/css/cardTheme.css';
</style>
