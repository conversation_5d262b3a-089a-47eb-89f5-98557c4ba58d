<template>
  <div class="pt-4 space-y-4">
    <!-- 搜索表单 -->
    <el-form :model="query" :inline="true" class="demo-form-inline">
      <el-form-item label="客户编号">
        <el-input v-model="query.customerNo" placeholder="请输入客户编号" maxlength="30" clearable/>
      </el-form-item>
      <el-form-item label="客户类型">
        <el-input v-model="query.customerType" placeholder="请输入客户类型" maxlength="30" clearable/>
      </el-form-item>
      <el-form-item label="时间范围">
        <el-date-picker
          v-model="query.createdTime"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00','23:59:59']"
          :clearable="false"
          :picker-options="{disabledDate:time => time.getTime() > $moment().set({m: 59, s: 59,h:23, ms: 999}).valueOf()}"
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="query.status" placeholder="请选择状态">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                     :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="tableData.currentPage=1,fetchData()" v-loading="query.loading"
                   :disabled="query.loading">查询
        </el-button>
        <el-button type="primary" key="downloadData" @click="downloadData" v-loading="query.downloading"
                   :disabled="query.downloading">
          下载数据
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 添加按钮 -->
    <el-button type="primary" @click="showAddDialog">添加客户类型</el-button>

    <!-- 添加 Dialog -->
    <el-dialog :visible.sync="dialogParams.visible" :close-on-press-escape="false" :close-on-click-modal="false"
               :title="`${dialogParams.form.companyTypeID?'编辑':'添加'}客户类型`" width="500px"
               :destroy-on-close="true">
      <el-form v-loading="dialogParams.loading" :model="dialogParams.form" :rules="rules" ref="addFormRef"
               label-width="120px">
        <el-form-item label="客户编号" prop="customerNo">
          <div class="flex">
            <el-input :disabled="dialogParams.form.companyTypeID" v-model="dialogParams.form.customerNo"
                      placeholder="请输入客户编号" maxlength="30" clearable/>
            <el-button :disabled="dialogParams.form.companyTypeID" type="primary" @click="generateCustomerNo">自动生成
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="客户类型名称" prop="customerTypeName">
          <el-input :disabled="dialogParams.form.companyTypeID" v-model="dialogParams.form.customerTypeName"
                    placeholder="请输入客户类型名称" maxlength="30"
                    clearable/>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="dialogParams.form.status">
            <el-radio label="100">启用</el-radio>
            <el-radio label="102">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注信息" prop="remark">
          <el-input type="textarea" rows="7" v-model="dialogParams.form.remark" placeholder="请输入备注信息"
                    maxlength="100" show-word-limit/>
        </el-form-item>
      </el-form>
      <template slot="footer">
        <span class="dialog-footer">
          <el-button :disabled="dialogParams.loading" @click="closeAddDialog">取消</el-button>
          <el-button :disabled="dialogParams.loading" type="primary"
                     @click="submitAddForm">{{ dialogParams.form.companyTypeID ? '保存' : '添加' }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 表格 -->
    <el-table
      border
      :data="tableData.data"
      v-loading="query.loading"
    >
      <el-table-column
        v-for="column in tableColumns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :formatter="column.formatter"
      >
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button type="primary" v-if="scope.row.Status === STATUS_DISABLED" size="small"
                     @click="handleEdit(scope.row)">
            编辑
          </el-button>
          <el-button :type="scope.row.Status === STATUS_OPEN ? 'danger' : 'success'" size="small"
                     @click="handleStatusChange(scope.row)">
            {{ scope.row.Status === STATUS_OPEN ? '禁用' : '启用' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @current-change="handlePageChange"
      @size-change="handleSizeChange"
      :current-page="tableData.currentPage"
      :page-sizes="[5,10, 20, 30, 40]"
      :page-size="tableData.pageSize"
      :total="tableData.total"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>


    <!-- 下载中心提示 -->
    <download-tips :showDownloadTips.sync="query.downloadDone"></download-tips>
  </div>
</template>

<script>
import '@/assets/css/unify.css'
import downloadCompanyType, {addCompanyType, getCompanyType, updateCompanyType} from "../../api/CustomerType";
import DownloadTips from "../../components/DownloadTips.vue";
import {DataCode} from "../../utils/http";
import moment from "moment";
import {mapState} from "vuex";
import {isEmpty} from "lodash";
import {STATUS_DISABLED, STATUS_OPEN} from "../../mixins/customerType";

const statusOptions = [
  {label: '全部', value: ''},
  {label: '启用', value: STATUS_OPEN},
  {label: '禁用', value: STATUS_DISABLED}
]
export default {
  components: {DownloadTips},
  computed: {
    ...mapState(['currentStation']),
    currentGroupId() {
      return this.currentStation.group_id;
    }
  },
  data() {
    return {
      STATUS_DISABLED, STATUS_OPEN,
      // 搜索表单
      query: {
        customerNo: '',
        customerType: '',
        createdTime: [moment().subtract(1, 'year').format('YYYY-MM-DD 00:00:00'), moment().format('YYYY-MM-DD 23:59:59')],
        status: '',
        loading: false,
        downloading: false,
        downloadDone: false,
      },
      // 添加数据的 dialog
      dialogParams: {
        visible: false,
        form: {
          customerNo: '',
          customerTypeName: '',
          status: STATUS_OPEN,
          remark: ''
        },
        loading: false
      },
      // 表格数据
      tableData: {
        data: [],
        total: 0,
        currentPage: 1,
        pageSize: 10
      },
      // 表格列配置
      tableColumns: [
        {prop: 'CompanyTypeID', label: '序号', width: '80'},
        {prop: 'BH', label: '客户编号', width: '200'},
        {prop: 'CompanyTypeName', label: '客户类型', width: '150'},
        {
          prop: 'Status',
          label: '状态',
          width: '100',
          formatter: (row) => {
            const status = statusOptions.find(item => row.Status === item.value)
            if (status && status.label) {
              return status.label;
            }
            return row.Status;
          }
        },
        {prop: 'Remark', label: '备注'},
        {prop: 'CreateTime', label: '创建时间', width: '180'},
        {prop: 'UpdateTime', label: '更新时间', width: '180'},
      ],
      // 表单验证规则
      rules: {
        customerNo: [
          {required: true, message: '请输入客户编号', trigger: 'blur'},
          {max: 30, message: '客户编号不能超过30个字符', trigger: 'blur'},
          {
            pattern: /^[a-zA-Z0-9]+$/,
            message: '仅支持大小写字母+数字，请重新输入',
            trigger: 'blur'
          }
        ],
        customerTypeName: [
          {required: true, message: '请输入客户类型名称', trigger: 'blur'},
          {max: 30, message: '客户类型名称不能超过30个字符', trigger: 'blur'}
        ],
        status: [
          {required: true, message: '请选择状态', trigger: 'change'}
        ],
        remark: [
          {max: 100, message: '备注不能超过100个字符', trigger: 'blur'}
        ]
      },
      // 状态选项
      statusOptions
    }
  },
  watch: {
    currentGroupId: {
      deep: true,
      immediate: true,
      handler(val, old) {
        if (val === old) {
          return;
        }
        this.query.page = 1;
        this.fetchData()
      }
    }
  },
  methods: {
    // 显示添加 dialog
    showAddDialog() {
      this.dialogParams.visible = true
      this.$refs.addFormRef.resetFields()
      this.dialogParams.form = {
        customerNo: '',
        customerTypeName: '',
        status: STATUS_OPEN,
        remark: ''
      }
    },
    // 关闭添加 dialog
    closeAddDialog() {
      this.dialogParams.visible = false
    },
    // 自动生成客户编号
    generateCustomerNo() {
      this.dialogParams.form.customerNo = `${Date.now()}${Math.floor(Math.random() * 10000)}`
      this.$refs.addFormRef.validateField('customerNo')
    },
    // 提交添加表单
    submitAddForm() {
      if (this.dialogParams.form.companyTypeID) {
        const {form} = this.dialogParams;
        // 状态变更事件逻辑
        const requestData = {
          bh: form.customerNo,
          remark: form.remark,
          group_id: this.currentGroupId, // 集团ID
          type_id: form.companyTypeID,
          type: form.customerTypeName,
          status: form.status, // 状态 102禁用 100启用
        };
        this.editCustomerType(requestData)
        return;
      }
      this.$refs.addFormRef.validate(async (valid) => {
        if (valid) {
          try { // 请求增加客户类型接口
            this.dialogParams.loading = true
            const requestData = {
              bh: this.dialogParams.form.customerNo, // 请替换为实际的客户编号
              type: this.dialogParams.form.customerTypeName, // 请替换为实际的客户类型
              group_id: this.currentGroupId, // 请替换为实际的集团ID
              remark: this.dialogParams.form.remark, // 可根据需要添加或注释此行
              status: this.dialogParams.form.status
            };

            const res = await addCompanyType(requestData)
            if (res.status !== 200) {
              this.$message.error(res.info || '添加客户类型失败')
              return;
            }
            // 提交表单逻辑
            this.closeAddDialog()
            this.tableData.page = 1;
            this.fetchData()
          } catch (e) {
            console.log("=>(CustomerType.vue:257) 添加客户类型失败 ", e);
            this.$message.error('添加客户类型失败')
          } finally {
            this.dialogParams.loading = false
          }
        }
      })
    },
    // 加载表格数据
    async fetchData() {
      this.query.loading = true
      try {
        const requestParams = {
          group_id: this.currentStation.group_id, // 必填，集团ID，类型为整数，具体值应根据实际情况填写
          page: this.tableData.currentPage, // 必填，分页标识，类型为字符串
          page_size: this.tableData.pageSize, // 必填，分页数量，类型为字符串
          bh: this.query.customerNo, // 客户编号，具体值应根据实际情况填写
          type: this.query.customerType, // 客户类型，具体值应根据实际情况填写
          status: this.query.status, // 状态，默认为""代表全部，可根据需要设置为"0"禁用或"1"启用
          s_time: (this.query.createdTime && this.query.createdTime[0]) || '', // 开始时间，具体值应根据实际情况填写
          e_time: (this.query.createdTime && this.query.createdTime[1]) || '' // 结束时间，非必填，如有需要可添加具体值
        };
        const res = await getCompanyType(requestParams)
        console.log("=>(CustomerType.vue:232) res", res);
        if (res.status !== DataCode.SUCCESS) {
          this.$message({type: 'error', message: res.info || '获取数据异常'});
          console.log("=>(CustomerType.vue:235) 获取数据异常", res);
          return;
        }
        this.tableData.data = res.data.list
        this.tableData.total = res.data.page_info.count
      } catch (e) {
        console.log("=>(CustomerType.vue:257) 获取数据异常", e);
      } finally {
        // 发起网络请求获取表格数据
        this.query.loading = false
      }
    },
    // 下载数据
    async downloadData() {
      // 发起下载数据的请求
      try {
        this.query.downloading = true;
        const requestData = {
          group_id: this.currentGroupId, // 集团ID，这里设置为0作为示例，请根据实际情况赋值
          bh: this.query.customerNo, // 客户编号，具体值应根据实际情况填写
          type: this.query.customerType, // 客户类型，具体值应根据实际情况填写
          status: this.query.status, // 状态，默认为""代表全部，可根据需要设置为"0"禁用或"1"启用
          s_time: this.query.createdTime[0], // 开始时间，具体值应根据实际情况填写
          e_time: this.query.createdTime[1] // 结束时间，非必填，如有需要可添加具体值
        };
        const res = await downloadCompanyType(requestData);
        console.log("=>(CustomerType.vue:252) res", res);
        this.query.downloadDone = res && res.status === DataCode.SUCCESS;
        if (res.status !== DataCode.SUCCESS) {
          this.$message.error(res.info || '下载数据异常');
          return;
        }
      } catch (e) {
        console.log("=>(CustomerType.vue:237) 下载数据失败", e);
        this.$message.error(`下载数据失败，请稍后重试${e.status}`)
      } finally {
        this.query.downloading = false;
      }
    },
    // 分页事件处理
    handlePageChange(currentPage) {
      this.tableData.currentPage = currentPage
      this.fetchData()
    },
    handleSizeChange(pageSize) {
      this.tableData.page = 1;
      this.tableData.pageSize = pageSize
      this.fetchData()
    },
    // 编辑事件处理
    handleEdit(row) {
      // 编辑事件逻辑
      this.dialogParams.visible = true
      this.dialogParams.form = {
        customerNo: row.BH,
        customerTypeName: row.CompanyTypeName,
        status: row.Status,
        remark: row.Remark,
        companyTypeID: row.CompanyTypeID
      }
    },
    // 状态变更事件处理
    handleStatusChange(row) {
      // 状态变更事件逻辑
      const requestData = {
        bh: row.BH,
        remark: row.Remark,
        group_id: this.currentGroupId, // 集团ID
        type_id: row.CompanyTypeID,
        type: row.CompanyTypeName,
        status: row.Status === STATUS_OPEN ? STATUS_DISABLED : STATUS_OPEN, // 状态 102禁用 100启用
      };
      console.log("=>(CustomerType.vue:368) row.Status", row.Status);
      this.editCustomerType(requestData)
    },
    async editCustomerType(data) {
      if (isEmpty(data)) {
        this.$message.error('更改客户类型数据异常');
        return;
      }
      try {
        this.query.loading = !(this.dialogParams.visible);
        this.dialogParams.loading = true
        const res = await updateCompanyType(data)
        if (res.status !== DataCode.SUCCESS) {
          this.$message.error(res.info || '更改客户类型数据异常');
          return;
        }
        this.fetchData();
        this.dialogParams.visible = false;
      } catch (e) {
        console.log("=>(CustomerType.vue:257) 更改客户类型数据异常", e);
        this.$message.error('更改客户类型数据异常');
      } finally {
        // 发起网络请求获取表格数据
        this.query.loading = false
        this.dialogParams.loading = false
      }
    }
  }
}
</script>
