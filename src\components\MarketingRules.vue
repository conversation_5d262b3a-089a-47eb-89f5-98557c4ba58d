<template>
    <div class="marketingRule" v-cloak>
        <div v-show="isShowList">
            <!-- 充值直降入口 -->
            <div v-for="(item,index) in ruleCardList" :key="index" class="ruleCard" @click="routeBtn(item)">
                <div class="ruleBoxs" v-if="item.show">
                    <img :src="item.url" alt="">
                    <div class="box">
                        <div class="title">{{item.title}}</div>
                        <div class="ruleContent">{{item.content}}</div>
                    </div>
                </div>
            </div>
            <!-- tab栏 -->
            <div class="tab">
                <ul class="tabBox">
                    <li>优惠类型</li>
                    <li v-for="(item,index) in TypeList" :key="index" 
                        :class="{active:typeValue == index}" @click="typeChange(index,item)">{{item.lable}}</li>
                </ul>
                <div class="tabTitle">可用油站</div>
                <div class="stationBox">
                    <ul class="tabBox">
                        <li v-for="(item,index) in stationList" :key="index" 
                            :class="{active:ostnTypeValue == index}" @click="OliStationChange(index,item)">{{item.lable}}</li>
                    </ul>
                </div>
            </div>
            <!-- 详情列表 -->
            <el-table :data="ruleData" style="width: 100%;margin-left: 10px;" v-loading="ruleLoading">
                <el-table-column prop="ID" label="ID" align="left" min-width="80"></el-table-column>
                <el-table-column prop="State" label="状态" align="center" header-align="center" min-width="120">
                    <template slot-scope="scope">
                        <span :class="{redfont:scope.row.State != 100}">{{scope.row.State == 100 ? '启用' : '禁用'}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="Name" label="名称" align="left" min-width="150"></el-table-column>
                <el-table-column prop="Priority" label="优先级" align="center" header-align="center" min-width="120">
                </el-table-column>
                <!-- <el-table-column prop="CalculationFormulaInfo" align="center" label="优惠规则" width="180"></el-table-column> -->
                <el-table-column prop="TimeRuleInfo" label="时间规则" align="center" header-align="center" min-width="360">
                </el-table-column>
                <el-table-column label="起止时间" align="center" header-align="center" min-width="360">
                    <template slot-scope="scope">
                        {{scope.row.StartingTime + '至' + scope.row.CutOffTime}}
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" min-width="160" fixed="right" header-align="center">
                    <template slot-scope="scope">
                        <el-button type="text" @click="detailBtn(scope.row)" size="small">查看详情</el-button>
                        <el-button type="text" @click="editBtn(scope.row)" :disabled="scope.row.Type == 1 && !is_show_create_lock_price" size="small">修改</el-button>
                        <el-button v-if="scope.row.State == 100" @click="disableRule(scope.row)" type="text" size="small">禁用
                        </el-button>
                        <el-button v-if="scope.row.State != 100" :disabled="scope.row.Type == 1 && !is_show_create_lock_price" @click="enableRule(scope.row)" type="text" size="small">启用
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页组件 -->
            <el-pagination
                class="pagination"
                background
                @current-change="handleCurrentChange"
                :current-page.sync="currentPage"
                :page-size="pagesize"
                layout=" prev, pager, next"
                :total="total">
            </el-pagination>
            <!-- 总数 -->
            <div class="showTotal">
                <span>共 {{total}} 条记录，每页显示</span>
                <el-input-number 
                    v-model="pagesize" controls-position="right" size="mini" style="width: 80px;"
                    @change="pageSizeChange" :min="1" :max="20"></el-input-number> 条
            </div>
        </div>
        <!-- 创建 编辑规则页面 -->
        <ruleCreation v-show="isRuleCreation" @exit="exit" ref="ruleCreation"></ruleCreation>
        <!-- 查看详情页面 -->
        <ruleDetailVue v-show="isRuleDetail" @baseOut="baseOut" ref="ruleDetail"></ruleDetailVue>
    </div>
</template>
<script>
import {mapGetters} from 'vuex'
import ruleCreation from './ruleCreation.vue'
import ruleDetailVue from './ruleDetail.vue'
export default {
    name:'MarketingRules',
    data() {
        return {
            is_show_create_lock_price: 0,
            ruleCardList:[  //入口
                {
                    url:require('../assets/images/icon_oil_chongzhi.png'),
                    title:'充值直降',
                    show: true,
                    content:'储值卡充值满1000元，每升优惠0.1元'
                },
                {
                    url:require('../assets/images/<EMAIL>'),
                    title:'单价锁定',
                    show: false,
                    content:'储值卡单价锁价5元/升'
                }
            ],
            TypeList:[ //优惠类型
                {value: 0, lable:'全部'},
                {value: 1, lable:'充值直降'},
                {value: 2, lable:'单价锁定'},
            ],
            typeValue:'0',
            stationList:[    //可用油站列表
                {value: 0, lable:'全部'}
            ],
            ostnTypeValue:'0',
            station_id:0,  //选中的油站id
            ruleData:[
                // {
                //     id:'00',
                //     state:'启用',
                //     name:'默认充值规则',
                //     priority:10,
                //     rule:'800<金额<1000',
                //     ruleTime:'每周六10:00',
                //     time:'2022-03-08',
                // }
            ],    //规则列表
            currentPage:0,  //当前页面
            pagesize:10,  
            total:0,
            ruleLoading:false,

            isShowList:true,
            isRuleCreation:false,

            isRuleDetail:false,

        }
    },
    components:{
        ruleCreation,
        ruleDetailVue
    },
    mounted() {
        if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
            return false;
        }
        this.getGroupBaseInfo()
        this.getStationList()
        this.getCardRuleLists()
    },
    activated(){
        if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
            return false;
        }
        this.getStationList()
        this.getCardRuleLists()
    },
    methods: {
        //退出充值直降
        exit(){
            this.isRuleCreation = false
            this.isShowList = true
            this.getStationList()
            this.getCardRuleLists()
        },
        //关闭查看详情
        baseOut(){
            this.isRuleDetail = false
            this.isShowList = true
            this.getStationList()
            this.getCardRuleLists()
        },
        async getGroupBaseInfo() {
            try {
            const res = await this.$axios.post('/Ostn/getGroupBaseInfo')
            if(res.data.status != 200) return this.$message.error(res.data.info)
            this.ruleCardList[1].show = res.data.data.is_show_create_lock_price == 1
            this.is_show_create_lock_price = res.data.data.is_show_create_lock_price == 1
            } catch (e) {
            this.ruleCardList[1].show = false
            this.$message.error('网络错误！')
            }
        },
        //获取卡规则列表
        getCardRuleLists(){
            this.ruleLoading = true;
            this.$axios.post("/CardRule/getCardChargeRuleList",{
                // state:this.state,
                accurate_name:this.accurate_name,
                station_id:this.station_id,
                page:this.currentPage,
                page_size:this.pagesize,
            }).then((res)=>{
                console.log('res',res);
                // this.loading = false;
                if(res.data.status == 200){
                    this.ruleData = res.data.data.dt;
                    this.total = res.data.data.TotalQty;
                }else{
                    this.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            }).finally(()=>{
                this.ruleLoading = false
            })
        },
        //切换类型
        typeChange(index,value){
            console.log('value',value);
            this.typeValue = index
            this.accurate_name = value.value
            this.getCardRuleLists()
        },
        //切换油站
        OliStationChange(index,value){
            this.ostnTypeValue = index
            this.station_id = value.value
            console.log('value',value);
            this.getCardRuleLists()
        },
        //获取可用油站
        getStationList(){
            this.$axios.post('/Stations/getStationList',{}).then((res)=>{
                if(res.status == 200){
                    this.stationList = [ {value: 0, lable:'全部'}]
                    res.data.data.forEach(item=>{
                        this.stationList.push({value:item.stid, lable:item.stname})
                    })
                }
            })
        },
        //路由跳转
        routeBtn(item){
            console.log('item',item);
            if(item.title == '充值直降'){
                // this.$router.push('/ruleCreation')
                this.isRuleCreation = true
                this.isShowList = false
            }else if(item.title == '单价锁定'){
                // this.$router.push('/unitPriceLock')
                this.$router.push({path:'/CardRule', hash: 'goToCreateLockRule'})
            }
            // this.cacheArray.remove('MarketingRules')
        },
        //查看详情
        detailBtn(value){
            console.log('详情',value);
            if(value.Type == 3){
                this.isShowList = false
                this.isRuleDetail = true
                //获取油站等信息(组件里不想改了)
                let oils = this.$refs.ruleCreation.oils
                let payType = this.$refs.ruleCreation.payType
                let station_list = this.$refs.ruleCreation.station_list
                let cards_list = this.$refs.ruleCreation.cards_list
                let levels = this.$refs.ruleCreation.levels
                let customerGroupList = this.$refs.ruleCreation.customerGroupList
                let companyList = this.$refs.ruleCreation.companyList
                // console.log('oils',oils);
                // console.log('payType',payType);
                // console.log('station_list',station_list);
                // console.log('cards_list',cards_list);
                this.$refs.ruleDetail.getRule(value.ID, oils, payType, station_list, cards_list, levels, customerGroupList, companyList)
            }
        },
        //编辑
        editBtn(value){
            console.log('编辑',value);
            if(value.Type != 3){
                // this.$router.push('/unitPriceLock?id='+value.ID)
                this.$router.push({path:'/CardRule?id='+ value.ID, hash: 'goToCreateLockRule'})
            }else{
                //  this.$router.push('/ruleCreation?id='+value.ID)
                // this.ruleId = value.ID
                this.isRuleCreation = true
                this.isShowList = false
                this.$refs.ruleCreation.createDate(value.ID)
            }
        },
        //选择第几页
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.currentPage = val
            console.log('currentPage',this.currentPage);
            this.getCardRuleLists()
        },
        //修改每页pagesize
        pageSizeChange(val){
            console.log(`每页 ${val} 条`);
            this.pagesize = val
            console.log('pagesize',this.pagesize);
            this.getCardRuleLists()
        },
        //禁用
        disableRule(row){
            console.log('禁用',row);
            this.$confirm('是否禁用“'+row.Name+'”？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$axios.post("/CardRule/changeCardChargeRuleState",{
                    state:101,
                    id:row.ID,
                }).then((res)=>{
                    if(res.status == 200){
                        this.getCardRuleLists();
                    }
                })
            }).catch(() => {
            });
        },
        //启用
        enableRule(row){
            console.log('启用',row);
            this.$confirm('是否启用“'+row.Name+'”？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$axios.post("/CardRule/changeCardChargeRuleState",{
                    state:100,
                    id:row.ID,
                }).then((res)=>{
                    if(res.status == 200){
                        this.getCardRuleLists();
                    }
                })
            }).catch(() => {
            });
        }
    },
    computed:{
        ...mapGetters({
            "getCurrentStation":"getCurrentStation"
        }),
        cacheArray:{
            get(){
                return this.$store.getters.getCacheArray
            },
            set(newValue){
                this.$store.commit("SETCACHEARRAY",newValue)
            }
        },
    },
    watch:{
        getCurrentStation(newValue,oldValue){
            if(newValue.merchant_type != 0 && newValue.value != oldValue.value){
                this.getGroupBaseInfo()
                this.getStationList()
                this.getCardRuleLists()
            }
        }
    },
    //keep-alive生命周期
    // activated(){
    //     console.log('状态变化');
    //     this.getStationList()
    //     this.getCardRuleLists()
    // },
}
</script>
<style>
    [v-cloak]{
        display: none;
    }
    .ruleCard{
        display: inline-block;
        margin-top: 10px;
    }
    .ruleBoxs:hover{
        border: 4px solid #32AF50;
    }
    li{
        list-style: none;
    }
    .ruleBoxs{
        padding: 40px 28px 0px 28px;
        margin: 0 36px 36px 0;
        background-color: #F9F9F9;
        height: 100px;
        display: inline-block;
        border: 4px solid #f9f9f9;
        cursor: pointer;
    }
    .ruleBoxs img{
        width: 56px;
        height: 56px;
    }
    .ruleBoxs .box{
        display: inline-block;
        margin-left: 10px;
        height: 70px;
        width:240px
    }
    .ruleBoxs .ruleContent{
        /* height: 30px; */
        color: #666666;
        font-size: 14px;
    }
    .ruleBoxs .box .title{
        font-size: 18px;
        color: #333;
        font-weight: bolder;
        height: 30px;
        line-height: 30px;
        text-align: left;
    }
    /* 选中状态 */
    .active{
        color:#32AF50
    }
    .marketingRule .tab{
        border-top: 1px solid #EAEAEA;
        border-bottom: 1px solid #EAEAEA;
        margin-bottom: 10px;
    }
    .marketingRule .tab .tabBox{
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-bottom: 24px;
        padding-left: 0px;
    }
    .tabBox li{
        margin-left: 40px;
        height: 32px;
    }
    .tabBox li:first-child,
    .tabTitle{
        font-weight: bold;
        display: inline-block;
        width: 70px;
    }
    .stationBox .tabBox li:first-child{
        font-weight: normal;
        width:auto;
        cursor: pointer;
    }
    .stationBox .tabBox{
        padding-left: 0;
        margin-top: 0;
    }
    .tabBox li:not(:first-child) {
        cursor: pointer;
    }
    .tabTitle{
        margin-left: 40px;
        height: 32px;
    }
    .stationBox{
        display: inline-block;
        width: 80%;
    }
    .pagination{
        margin-top: 20px;
        display: inline-block;
    }
    .showTotal{
        margin-top: 20px;
        float: right;
    }
    .redfont{
        color: red;
    }
</style>