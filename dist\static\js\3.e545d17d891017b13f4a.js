webpackJsonp([3,7],{"/Wgw":function(e,a){e.exports="data:image/png;base64,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"},"3TpU":function(e,a,t){"use strict";var r=t("uotZ"),i=t.n(r),n=t("bOzO");a.a={data:function(){return{FIXED_PRICE_MODE:n.a}},methods:{zero2Formater:function(e){return e&&0!==Number(e)?e:"-"},sumUnitLiter:function(e){return e&&Array.isArray(e)?e.reduce(function(e,a){return a&&"number"==typeof a.UnitLiter?e.plus(new i.a(a.UnitLiter)):e},new i.a(0)).toFixed(4):0}}}},KN9e:function(e,a,t){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var r=t("BO1k"),i=t.n(r),n=t("d7EF"),o=t.n(n),s=t("//Fk"),l=t.n(s),c=t("Xxa5"),u=t.n(c),d=t("exGp"),m=t.n(d),p=t("Dd8w"),g=t.n(p),f=t("NYxO"),v=t("mtWM"),h=t.n(v),_=t("i0w4"),y=t("uotZ"),b=t.n(y),C=t("bOzO"),x=t("3pLw");function w(e){return Object(x.b)("/CompanyCard/getOilList?stid="+e)}function P(e){return Object(x.c)("/CompanyCard/transferCompanyAccount",e)}var S=t("3TpU"),A={components:{RechargeApproval:t("yMQS").default},mixins:[S.a],name:"CardTransfer",data:function(){var e=this;return{companyListShow:!0,transferListShow:!1,showRechargeApprovalView:!1,searchText:"",stationList:[],companyLoading:!0,companyListData:[],currentPage:1,pageSize:10,total:0,company_id:"65",companyName:"65",companyInfo:{},searchType:"1",searchCardText:"",cardListData:[],multipleSelection:[],currentCardPage:1,pageCardSize:10,totalCard:0,orig_amount:"",giveAmount:"",PrincipalAmount:"",cardInfo:{},singleDialogVisible:!1,transfer_info:[],moreDialogVisible:!1,radioType:"1",transferType:1,rechargeDialogVisible:!1,rechargeLoading:!1,amount:"",showCompany:!1,searchDate:[],searchTxt:"",dialogVisible:!1,cardLoading:!1,cardList:[],currentPageTransfer:1,pageSizeTransfer:10,totalTransfer:0,multipleSelectionCard:[],transferAble:!1,couponInfo:"",fullscreenLoading:!1,capitalAllocationContent:"",timeout:null,companyOptions:[],dialogParams:{charging:!1,rules:{oils:[{required:!0,message:"请选择充值油品",trigger:"change"}],amount:[{required:!0,message:"请输入充值金额",trigger:"blur"},{type:"number",min:1,message:"充值金额必须大于等于1",trigger:"blur"}],unit_price:[{required:!0,message:"请输入油品单价",trigger:"blur"},{required:!0,type:"number",min:.01,message:"油品单价必须大于0",trigger:"blur"}],liters:[{required:!0,message:"请输入油品升数",trigger:"change"},{required:!0,type:"number",min:1,message:"油品升数必须大于等于1",trigger:"blur"},{validator:function(a,t,r){if(e.currentFixPriceMode!==C.a.FixedRise){var i=e.dialogParams.query,n=i.amount,o=i.unit_price;(t=new b.a(t).toFixed(4))!==new b.a(n||0).div(new b.a(o||1)).toFixed(4)?r(new Error("油品升数与充值金额和油品单价不匹配")):r()}else r()},trigger:"change"}]},showFixedCharge:!1,showBatchAllocation:!1,radioType:1,oilLoading:!1,oilList:[],item:{},query:{oils:"",liters:0,unit_price:0,amount:0,remark:""},companyStationList:[],companyStationListLoading:!1,allocationRules:{oils:[{required:!0,message:"请选择划拨油品",trigger:"change"}],amount:[{required:!0,message:"请输入划拨资金",trigger:"blur"},{validator:function(a,t,r){if(null===t||""===t||void 0===t)r(new Error("划拨资金不能为空"));else if(t<=0)r(new Error("划拨资金必须大于0"));else if(t<.1&&e.currentFixPriceMode===C.a.FixedRise)r(new Error("划拨升数不能小于0.1"));else{var i=e.dialogParams.query.oils,n=e.currentCompanyBatchInfo[i];if(void 0!==n&&t>n)r(new Error("划拨总额不能大于当前余额（当前剩余："+n+(e.currentFixPriceMode===C.a.FixedAmount?"元":"升")+"）"));else{if(e.dialogParams.showBatchAllocation&&1===e.dialogParams.radioType){var o=b()(e.multipleSelection.length).times(t).toFixed(e.currentFixPriceMode===C.a.FixedAmount?2:4);if(b()(o).gt(n))return void r(new Error("划拨总额不能大于当前余额（当前剩余："+n+(e.currentFixPriceMode===C.a.FixedAmount?"元":"升")+"）"))}r()}}},trigger:"blur"}]},allocationLoading:!1,loadingInfo:!1,refundOilList:[]}}},mounted:function(){var e=this;if(this.$watch("amount",this.debounce(function(a){e.showRechargeApproval||e.getChargeAmount(a),e.$emit("amount",a)},500)),void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.getStationList(),this.getCompanyList()},computed:g()({currentFixPriceMode:function(){return this.dialogParams.item?String(this.dialogParams.item.CardLockType):String(C.a.No)},currentCompanyBatchInfo:function(){var e={};if(this.currentFixPriceMode===C.a.No||!this.dialogParams.item||!this.dialogParams.item.companyBatches||0===this.dialogParams.item.companyBatches.length)return e;var a="UnitLiter";return this.currentFixPriceMode===C.a.FixedAmount&&(a="RechargeAmount"),this.dialogParams.item.companyBatches.forEach(function(t){console.log("=>(CardTransfer.vue:749) item",t.OilNo,t[a]),t.OilNo in e?e[t.OilNo]=b()(t[a]).plus(e[t.OilNo]||0).toFixed("UnitLiter"===a?4:2):e[t.OilNo]=b()(t[a]||0).toFixed("UnitLiter"===a?4:2)}),e}},Object(f.d)(["isGroup"]),Object(f.c)(["showLockPrice","showRechargeApproval"]),Object(f.c)({getCurrentStation:"getCurrentStation"})),watch:{getCurrentStation:function(e,a){0!=e.merchant_type&&e.value!=a.value&&(this.getStationList(),this.currentPage=1,this.getCompanyList())}},methods:{submitRechargeApproval:function(){var e=this;return m()(u.a.mark(function a(){var t,r,i,n,o;return u.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(!e.dialogParams.showFixedCharge){a.next=3;break}return a.next=3,e.$refs.form.validate();case 3:if((t=e.dialogParams.showFixedCharge?e.dialogParams.query.amount:e.amount)>0){a.next=7;break}return e.$message.error("本次充值金额必须大于0"),a.abrupt("return");case 7:e.dialogParams.showFixedCharge?e.dialogParams.charging=!0:e.rechargeLoading=!0,r=JSON.parse(localStorage.getItem("__userInfo__")),i="",e.dialogParams.query.oils&&e.companyInfo.oil_list&&(n=e.companyInfo.oil_list.find(function(a){return String(a.oil_id)===String(e.dialogParams.query.oils)}),i=n?n.oil_name:""),o={CardLockType:e.dialogParams.item.CardLockType,adid:r&&r.adid||"",company_id:e.company_id,company_name:e.companyInfo.company_name,liters:e.dialogParams.query.liters,oils:e.dialogParams.query.oils,oils_name:i,pay_way:1,remark:e.dialogParams.query.remark,unit_price:e.dialogParams.query.unit_price,amount:t},e.$axios.post("/CompanyCard/subApplyRecharge",o).then(function(a){200===a.data.status?(e.$message.success("提交成功"),e.dialogParams.showFixedCharge?(e.dialogParams.showFixedCharge=!1,e.dialogParams.charging=!1):(e.rechargeDialogVisible=!1,e.rechargeLoading=!1)):e.$message.error(a.data.info||"提交失败")}).catch(function(a){e.$message.error("请求失败，请稍后重试")}).finally(function(){e.dialogParams.showFixedCharge?e.dialogParams.charging=!1:e.rechargeLoading=!1});case 13:case"end":return a.stop()}},a,e)}))()},showBatchRefund:function(){this.dialogVisible=!0,this.showLockPrice&&this.getRefundOilList()},getOilName:function(e){if(!e||!this.dialogParams.refundOilList||this.dialogParams.refundOilList&&0===this.dialogParams.refundOilList.length)return e;var a=this.dialogParams.refundOilList.find(function(a){return a.oil_id===e});return a?a.oil_name:e},allocationDialogInit:function(){var e=this;this.currentFixPriceMode===C.a.No&&this.showLockPrice||(this.getCompanyStationList(),this.fixedChargeInit(),this.$nextTick(function(){e.$refs.allocationForm&&e.$refs.allocationForm.resetFields(),e.$refs.allocationBatchForm&&e.$refs.allocationBatchForm.resetFields()}),this.dialogParams.showBatchAllocation&&this.multipleSelection.forEach(function(e){e.TransferUnitLiter=0}))},fixedChargeInit:function(){this.dialogParams.query={oils:"",liters:0,unit_price:0,amount:0,remark:""}},automaticCalculation:function(e){if(console.log("=>(CardTransfer.vue:999) this.currentFixPriceMode === FIXED_PRICE_MODE.FixedRise",this.currentFixPriceMode),this.currentFixPriceMode!==C.a.FixedRise){if(this.currentFixPriceMode!==C.a.FixedAmount||"liters"!==e){var a=this.dialogParams.query,t=a.amount,r=void 0===t?"":t,i=a.unit_price,n=void 0===i?"":i;r=this.sanitizeInput(r),n=this.sanitizeInput(n);var o=new b.a(r||0),s=new b.a(n||1),l=o.div(s);this.dialogParams.query.liters=0,l.isFinite()&&(this.dialogParams.query.liters=l.toFixed(4))}}else{var c=this.dialogParams.query,u=c.amount,d=void 0===u?"":u,m=c.liters,p=void 0===m?"":m;if(d=this.sanitizeInput(d)||0,p=this.sanitizeInput(p)||1,new b.a(d).isEqualTo(0)&&new b.a(p).isEqualTo(0))return;var g=new b.a(d).dividedBy(p);g.isFinite()&&(this.dialogParams.query.unit_price=g.toFixed(8))}},sanitizeInput:function(e){var a=e.toString().replace(/[^0-9.]/g,""),t=a.split(".");return t.length>2&&(a=t[0]+"."+t.slice(1).join("")),a},remoteMethod:function(e){var a=this;return m()(u.a.mark(function t(){var r;return u.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(t.prev=0,e){t.next=3;break}return t.abrupt("return",a.companyOptions=[]);case 3:return t.next=5,a.$axios.post("/CompanyCard/getCompanyNameList",{input:e});case 5:if(r=t.sent,console.log("搜索结果",r),200==r.data.status){t.next=9;break}return t.abrupt("return",a.$message.error(r.data.info));case 9:a.companyOptions=JSON.parse(r.data.data),t.next=16;break;case 12:t.prev=12,t.t0=t.catch(0),console.log(t.t0),a.$message.error("查询车队名称异常");case 16:case"end":return t.stop()}},t,a,[[0,12]])}))()},handleInput:function(e){var a=this;null!==this.timeout&&clearTimeout(this.timeout),this.timeout=setTimeout(function(){a.getGiveAmount(e)},500)},debounce:function(e,a){var t=void 0;return function(){for(var r=this,i=arguments.length,n=Array(i),o=0;o<i;o++)n[o]=arguments[o];t&&clearTimeout(t),t=setTimeout(function(){e.apply(r,n)},a)}},getChargeAmount:function(e){var a=this;a.transferAble=!1,e>0?a.$axios.post("/CompanyCard/getChargeAmount",{company_id:a.company_id,amount:e,pay_way:1}).then(function(e){if(200==e.data.status){a.giveAmount=e.data.data.donate_money;var t=e.data.data.couponinfo;t&&1==t.coupon_type?2==t.retail_type?a.couponInfo=t.price+"元油品券*"+t.count:3==t.retail_type?a.couponInfo=t.price+"元非油券*"+t.count:4==t.retail_type?a.couponInfo=t.price+"元服务券*"+t.count:a.couponInfo=t.price+"元赠金券*"+t.count:t&&3==t.coupon_type?2==t.retail_type?a.couponInfo=t.price+"折油品券*"+t.count:3==t.retail_type?a.couponInfo=t.price+"折非油券*"+t.count:4==t.retail_type?a.couponInfo=t.price+"折服务券*"+t.count:a.couponInfo=t.price+"折赠金券*"+t.count:t&&2==t.coupon_type?2==t.retail_type?a.couponInfo="油品券*"+t.count:3==t.retail_type?a.couponInfo="非油券*"+t.count:4==t.retail_type?a.couponInfo="服务券*"+t.count:a.couponInfo="赠金券*"+t.count:t&&0==t.coupon_type?a.couponInfo="券包":a.couponInfo="",a.transferAble=!0}else a.$message({message:e.data.info,type:"error"})}):(a.giveAmount=0,a.couponInfo="")},getSingleGiveAmount:function(e){var a=this;a.transferAble=!1,e>0?a.$axios.post("/CompanyCard/getGiveAmount",{id:a.company_id,orig_amount:e}).then(function(e){200==e.data.status?(a.transferAble=!0,a.giveAmount=e.data.data.GiveAmount,a.PrincipalAmount=e.data.data.PrincipalAmount):a.$message({message:e.data.info,type:"error"})}).catch(function(e){}):(a.giveAmount=0,a.couponInfo="")},getGiveAmount:function(e){var a=this;return e.input?new l.a(function(t,r){a.$axios.post("/CompanyCard/getGiveAmount",{id:a.company_id,orig_amount:e.input}).then(function(r){200==r.data.status?(a.$set(e,"giveAmount",r.data.data.GiveAmount),a.$set(e,"PrincipalAmount",r.data.data.PrincipalAmount),t(r)):a.$message({message:r.data.info,type:"error"})}).catch(function(e){r(e)})}):(a.$set(e,"giveAmount",0),void a.$set(e,"PrincipalAmount",0))},getStationList:function(){var e=this;this.$axios.post("/Stations/getStationList",{}).then(function(a){200==a.status&&(e.stationList=a.data.data)})},getCompanyStationList:function(){var e=this,a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;return m()(u.a.mark(function t(){var r,i;return u.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,e.dialogParams.companyStationListLoading=!0,t.next=4,w(a||e.companyInfo.station_id);case 4:if(r=t.sent,console.log("=>(CardTransfer.vue:958) res",r),200===r.status){t.next=9;break}return e.$message.error(r.info||"获取油站列表失败，请稍后重试"),t.abrupt("return");case 9:if(r.data){t.next=12;break}return e.dialogParams.companyStationList=[],t.abrupt("return");case 12:if(i=r.data.oil_list||[],!a){t.next=15;break}return t.abrupt("return",i);case 15:i.length>0&&(i=i.filter(function(a){return e.currentCompanyBatchInfo[a.oil_id]})),e.dialogParams.companyStationList=i,t.next=23;break;case 19:t.prev=19,t.t0=t.catch(0),console.log("=>(CardTransfer.vue:960) 获取油站列表失败",t.t0),e.$message.error("获取油站列表失败，请稍后重试");case 23:return t.prev=23,e.dialogParams.companyStationListLoading=!1,t.finish(23);case 26:case"end":return t.stop()}},t,e,[[0,19,23,26]])}))()},getStationName:function(e){var a="";return this.stationList.forEach(function(t){t.stid==e&&(a=t.stname)}),a},getCreditName:function(e){var a="";return[{value:"0",label:"不启用"},{value:"1",label:"信贷"},{value:"2",label:"保证金"}].forEach(function(t){t.value==e&&(a=t.label)}),a},getCompanyList:function(){var e=this;e.companyLoading=!0,e.$axios.post("/CompanyCard/getCompanyList",{page:e.currentPage,page_size:e.pageSize,input:e.searchText,state:100}).then(function(a){e.companyLoading=!1,200==a.data.status?(e.companyListData=a.data.data.dt,e.total=a.data.data.TotalQty):e.$message({message:a.data.info,type:"error"})}).catch(function(e){})},handleCurrentChange:function(e){this.currentPage=e,this.getCompanyList()},handleSizeChange:function(e){this.pageSize=e,this.getCompanyList()},searchCompany:function(){this.currentPage=1,this.getCompanyList()},goToTransfer:function(e){this.company_id=e.ID,this.companyListShow=!1,this.transferListShow=!0,this.showCompany=!1,this.companyInfo={},this.companyInfo.amount=0,this.companyInfo.give_amount=0,this.getCompanyInfo(e.ID),this.currentCardPage=1,this.pageCardSize=10,this.getUserCardList(),this.dialogParams.item=e,this.searchCardText="",this.searchType="1"},getCompanyInfo:function(e){var a=this,t=this;this.dialogParams.oilLoading=!0,t.$axios.post("/CompanyCard/getCompanyInfo",{id:e}).then(function(e){200==e.data.status?t.companyInfo=e.data.data:t.$message({message:e.data.info,type:"error"})}).finally(function(){a.dialogParams.oilLoading=!1})},getCurrentCompanyInfo:function(){var e=this;return m()(u.a.mark(function a(){var t,r,i;return u.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(e.dialogParams.item&&e.dialogParams.item.ID){a.next=2;break}return a.abrupt("return");case 2:return a.prev=2,e.dialogParams.loadingInfo=!0,a.next=6,n=e.dialogParams.item.ID,Object(x.c)("/CompanyCard/getCompanyList",{page:1,page_size:10,input:n,state:100});case 6:if(t=a.sent,console.log("=>(CardTransfer.vue:1254) res",t),200===t.status){a.next=11;break}return e.$message.error(t.info||"获取车队详情失败"),a.abrupt("return");case 11:if(t.data&&t.data.dt&&0!==t.data.dt.length){a.next=14;break}return e.$message.error("获取车队详情失败，请刷新页面"),a.abrupt("return");case 14:r=o()(t.data.dt,1),i=r[0],e.dialogParams.item=i,a.next=22;break;case 18:a.prev=18,a.t0=a.catch(2),console.log("=>(CardTransfer.vue:1267) 获取车队详情失败",a.t0),e.$message.error("获取车队详情失败，请刷新页面");case 22:return a.prev=22,e.dialogParams.loadingInfo=!1,a.finish(22);case 25:case"end":return a.stop()}var n},a,e,[[2,18,22,25]])}))()},getUserCardList:function(){var e=this;e.cardLoading=!0,e.cardListData=[],e.$axios.post("/Card/getUserCardList",{page:e.currentCardPage,page_size:e.pageCardSize,input:e.searchCardText,input_type:e.searchType,company_id:e.company_id}).then(function(a){e.cardLoading=!1,200==a.data.status?(e.cardListData=a.data.data.dt,e.totalCard=a.data.data.TotalQty):e.$message({message:a.data.info,type:"error"})}).catch(function(e){})},handleCardCurrentChange:function(e){this.currentCardPage=e,this.getUserCardList()},handleCardSizeChange:function(e){this.pageCardSize=e,this.getUserCardList()},searchCard:function(){this.currentCardPage=1,this.getUserCardList()},searchAllCompany:function(){this.currentPage=1,this.searchText="",this.getCompanyList()},searchAllCard:function(){this.currentCardPage=1,this.searchCardText="",this.getUserCardList()},handleSelectionChange:function(e){this.multipleSelection=e},showSingleTransfer:function(e){this.cardInfo=e,this.transferType=1,this.giveAmount="",this.PrincipalAmount="",this.orig_amount="",this.singleDialogVisible=!0},showMoreTransfer:function(){if(console.log("=>(CardTransfer.vue:1316) this.dialogParams.item.CardLockType !== FIXED_PRICE_MODE.No",this.dialogParams.item.CardLockType,C.a.No),this.showLockPrice&&this.currentFixPriceMode!==C.a.No)return this.dialogParams.radioType=1,this.dialogParams.showBatchAllocation=!0,void this.allocationDialogInit();1==this.radioType?this.transferType=2:this.transferType=3,this.moreDialogVisible=!0,this.orig_amount="",this.multipleSelection.forEach(function(e){}),this.PrincipalAmount="",this.giveAmount=""},changeRadioType:function(e){1==e?this.transferType=2:2==e&&(this.transferType=3)},allocationConfirm:function(){var e=this;return m()(u.a.mark(function a(){var t,r,n,o,s,l,c,d,m,p,g,f,v;return u.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(e.dialogParams.companyStationList&&(!e.dialogParams.companyStationList||0!==e.dialogParams.companyStationList.length)){a.next=3;break}return e.$message.error("当前无可用划拨油品，不可执行划拨处理"),a.abrupt("return");case 3:if(!e.showLockPrice||e.currentFixPriceMode===C.a.No){a.next=11;break}if(!e.dialogParams.showBatchAllocation){a.next=9;break}return a.next=7,e.$refs.allocationBatchForm.validate();case 7:a.next=11;break;case 9:return a.next=11,e.$refs.allocationForm.validate();case 11:if(t=[],!e.dialogParams.showBatchAllocation){a.next=61;break}r=b()(0),n=!0,o=!1,s=void 0,a.prev=17,l=i()(e.multipleSelection);case 19:if(n=(c=l.next()).done){a.next=31;break}if(d=c.value,m=(1===e.dialogParams.radioType?e.dialogParams.query.amount:d.TransferUnitLiter)||0,!((p={GiveAmount:0,TransferAmount:e.currentFixPriceMode===C.a.FixedAmount?m:0,CardID:d.ID,TransferUnitLiter:e.currentFixPriceMode===C.a.FixedRise?m:0}).TransferUnitLiter<.1&&e.currentFixPriceMode===C.a.FixedRise)){a.next=26;break}return e.$message.error("划拨余额不能小于0.1升"),a.abrupt("return");case 26:r=r.plus(e.currentFixPriceMode===C.a.FixedRise?p.TransferUnitLiter:p.TransferAmount),t.push(p);case 28:n=!0,a.next=19;break;case 31:a.next=37;break;case 33:a.prev=33,a.t0=a.catch(17),o=!0,s=a.t0;case 37:a.prev=37,a.prev=38,!n&&l.return&&l.return();case 40:if(a.prev=40,!o){a.next=43;break}throw s;case 43:return a.finish(40);case 44:return a.finish(37);case 45:if(g=e.currentCompanyBatchInfo[e.dialogParams.query.oils]||0,!r.eq(0)){a.next=51;break}return e.$message.error("划拨总额不能为0"),a.abrupt("return");case 51:if(!r.lt(.1)||e.currentFixPriceMode!==C.a.FixedRise){a.next=56;break}return e.$message.error("划拨总额不能小于0.1升"),a.abrupt("return");case 56:if(!r.gt(b()(g))){a.next=59;break}return e.$message.error("划拨总额不能大于当前余额（当前剩余："+g+(e.currentFixPriceMode===C.a.FixedAmount?"元":"升")+"）"),a.abrupt("return");case 59:a.next=62;break;case 61:t.push({CardID:e.cardInfo.ID,TransferAmount:e.currentFixPriceMode===C.a.FixedAmount&&e.dialogParams.query.amount||0,GiveAmount:0,TransferUnitLiter:e.currentFixPriceMode===C.a.FixedRise&&e.dialogParams.query.amount||0});case 62:return console.log("=>(CardTransfer.vue:1565) transfer_info",t),f={type:e.dialogParams.item.CardLockType,id:e.company_id,oils:e.dialogParams.query.oils,transfer_info:t},a.prev=64,e.dialogParams.allocationLoading=!0,a.next=68,P(f);case 68:if(v=a.sent,console.log("=>(CardTransfer.vue:1482) res",v),200===v.status){a.next=73;break}return e.$message.error(v.info),a.abrupt("return");case 73:e.$message.success("操作成功"),e.getUserCardList(),e.getCurrentCompanyInfo(),e.dialogParams.showBatchAllocation=!1,e.singleDialogVisible=!1,a.next=84;break;case 80:a.prev=80,a.t1=a.catch(64),console.log("=>(CardTransfer.vue:1484) 划拨金额失败",a.t1),e.$message.error("划拨金额失败");case 84:return a.prev=84,e.dialogParams.allocationLoading=!1,a.finish(84);case 87:case"end":return a.stop()}},a,e,[[17,33,37,45],[38,,40,44],[64,80,84,87]])}))()},transferCompanyAccount:function(){var e=this,a=/^\d+(\.\d{1,2})?$/;if(e.transfer_info=[],1==this.transferType){if(Number(e.PrincipalAmount)+Number(e.giveAmount)>Number(e.companyInfo.amount)+Number(e.companyInfo.give_amount))return e.$message({message:"划拨金额过大",type:"error"}),!1;if(!a.test(e.PrincipalAmount))return e.$message.error("请输入正确的划拨本金，限两位小数");if(!a.test(e.giveAmount))return e.$message.error("请输入正确的划拨赠金，限两位小数");if(0==e.PrincipalAmount&&0==e.giveAmount)return e.$message.error("划拨本金与赠金不能同时为0元");e.transfer_info.push({CardID:e.cardInfo.ID,TransferAmount:e.PrincipalAmount,GiveAmount:e.giveAmount}),e.transferAble=!1}else if(2==this.transferType){if(0==e.PrincipalAmount&&0==e.giveAmount)return e.$message.error("划拨本金与赠金不能同时为0元");if(!a.test(e.PrincipalAmount))return e.$message.error("请输入正确的划拨本金，限两位小数");if(!a.test(e.giveAmount))return e.$message.error("请输入正确的划拨赠金，限两位小数");e.multipleSelection.forEach(function(a){e.transfer_info.push({CardID:a.ID,TransferAmount:e.PrincipalAmount,GiveAmount:e.giveAmount})}),e.transferAble=!1}else{for(var t=0;t<e.multipleSelection.length;t++){if(!e.multipleSelection[t].PrincipalAmount)return e.$message.error("请输入划拨本金");if(!a.test(e.multipleSelection[t].PrincipalAmount))return e.$message.error("请输入正确的划拨本金，限两位小数");if(!e.multipleSelection[t].giveAmount)return e.$message.error("请输入划拨赠金");if(!a.test(e.multipleSelection[t].giveAmount))return e.$message.error("请输入正确的划拨赠金，限两位小数");if(0==e.multipleSelection[t].PrincipalAmount&&0==e.multipleSelection[t].giveAmount)return e.$message.error("划拨本金与赠金不能同时为0元")}e.multipleSelection.forEach(function(a){e.transfer_info.push({CardID:a.ID,TransferAmount:a.PrincipalAmount,GiveAmount:a.giveAmount})})}e.$axios.post("/CompanyCard/transferCompanyAccount",{id:e.company_id,transfer_info:e.transfer_info}).then(function(a){200==a.data.status?(e.$message({message:"划拨成功",type:"success"}),e.singleDialogVisible=!1,e.moreDialogVisible=!1,e.getCompanyInfo(e.company_id),e.getUserCardList()):e.$message({message:a.data.info,type:"error"})}).catch(function(e){})},goToRecharge:function(e){var a=this;return m()(u.a.mark(function t(){return u.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(a.dialogParams.item=e,a.company_id=e.ID,a.amount="",a.companyInfo={},a.companyInfo.amount=0,a.companyInfo.give_amount=0,a.companyInfo.credit_type=0,a.transferAble=!1,a.giveAmount=0,a.couponInfo="",a.getCompanyInfo(e.ID),!a.isGroup){t.next=14;break}return a.$message.error("暂不支持集团"),t.abrupt("return");case 14:if(console.log("=>(CardTransfer.vue:1515) this.currentFixPriceMode !== FIXED_PRICE_MODE.No",a.currentFixPriceMode,C.a.No),!a.showLockPrice||a.currentFixPriceMode===C.a.No){t.next=18;break}return a.dialogParams.showFixedCharge=!0,t.abrupt("return");case 18:a.rechargeDialogVisible=!0;case 19:case"end":return t.stop()}},t,a)}))()},chargeCompanyCard:function(){var e=this;return m()(u.a.mark(function a(){var t,r,i;return u.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(t=e,r=JSON.parse(localStorage.getItem("__userInfo__")),i={company_id:t.company_id,amount:t.amount,pay_way:1,CardLockType:e.dialogParams.item.CardLockType,oils:"",adid:r?r.adid:"",liters:0,unit_price:0,remark:""},!e.showLockPrice||!e.dialogParams.showFixedCharge){a.next=7;break}return a.next=6,e.$refs.form.validate();case 6:i=g()({},i,e.dialogParams.query);case 7:e.dialogParams.charging=!0,t.transferAble=!1,t.$axios.post("/CompanyCard/chargeCompanyCard",i).then(function(e){t.rechargeDialogVisible=!1,t.dialogParams.showFixedCharge=!1,200==e.data.status?(t.$message({message:"充值成功",type:"success"}),t.getCompanyList()):2e3==e.data.status?(t.$message({message:"充值进行中，请稍后于资金流水中查询充值订单。",duration:5e3}),t.getCompanyList()):t.$message({message:e.data.info,type:"error"})}).catch(function(e){}).finally(function(){e.dialogParams.charging=!1});case 10:case"end":return a.stop()}},a,e)}))()},showCardList:function(e){this.company_id=e.ID,this.companyName=e.CompanyName,this.searchDate=[this.$moment().subtract(3,"months").format("YYYY-MM-DD"),this.$moment().format("YYYY-MM-DD")],this.getTransferDetailed(),this.showCompany=!0,this.companyListShow=!1,this.transferListShow=!1,this.multipleSelectionCard=[],this.searchTxt="",this.searchType="1",this.dialogParams.item=e},getTransferDetailed:function(){if(this.searchDate&&2===this.searchDate.length){var e=this.$moment(this.searchDate[0]);if(this.$moment(this.searchDate[1]).diff(e,"months",!0)>6)return this.$message.error("选择的时间范围不能超过6个月"),void(this.searchDate=[this.$moment().subtract(3,"months").format("YYYY-MM-DD"),this.$moment().format("YYYY-MM-DD")])}var a=this;a.cardLoading=!0,a.$axios.post("/CompanyCard/getTransferDetailed",{Page:a.currentPageTransfer,PageSize:a.pageSizeTransfer,CompanyID:a.company_id,QueryType:a.searchType,QueryInfo:a.searchTxt,StartTime:a.searchDate?a.$moment(a.searchDate[0]+" 00:00:00","YYYY-MM-DD HH:mm:ss").unix():"",EndTime:a.searchDate?a.$moment(a.searchDate[1]+" 23:59:59","YYYY-MM-DD HH:mm:ss").unix():""}).then(function(e){a.cardLoading=!1,200==e.data.status?(a.cardList=e.data.data.dt,a.totalTransfer=e.data.data.TotalQty):a.$message({message:e.data.info,type:"error"})}).catch(function(e){})},handleTransferCurrentChange:function(e){this.currentPageTransfer=e,this.getTransferDetailed()},handleTransferSizeChange:function(e){this.pageSizeTransfer=e,this.getTransferDetailed()},handleSelectionCardChange:function(e){this.multipleSelectionCard=e},getRefundOilList:function(){var e=this;return m()(u.a.mark(function a(){return u.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:e.getCompanyStationList(e.dialogParams.item.StationNO).then(function(a){a&&0!==a.length&&(e.dialogParams.refundOilList=a)});case 1:case"end":return a.stop()}},a,e)}))()},showRefund:function(e){var a=this;return m()(u.a.mark(function t(){return u.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:a.dialogVisible=!0,a.$refs.multipleTable.clearSelection(),a.multipleSelectionCard=[],a.multipleSelectionCard.push(e),a.showLockPrice&&a.getRefundOilList();case 5:case"end":return t.stop()}},t,a)}))()},refund:function(){var e=this,a=[];this.multipleSelectionCard.forEach(function(e){a.push({ID:e.ID,CompanyID:e.CompanyID,CardID:e.CardID,RefundsAmount:e.ReceiveAmount,RefundsGiveAmount:e.ReceiveGiveAmount})}),e.$axios.post("/CompanyCard/transferGoBack",{batchCardRefunds:a}).then(function(a){200==a.data.status?(e.$message({message:"返款成功",type:"success"}),e.dialogVisible=!1,e.currentPageTransfer=1,e.getTransferDetailed()):e.$message({message:a.data.info,type:"error"})}).catch(function(e){})},goBack:function(){this.showCompany=!1,this.transferListShow=!1,this.showRechargeApprovalView=!1,this.companyListShow=!0,this.getCompanyList()},downloadTemplate:function(){window.location.href=this.baseURL+"/CustomerGroup/downloadTransMoney"},httpRequest:function(e){var a=this,t=this;this.$axios.post("Card/commpayCardCache",{company_id:this.company_id}).then(function(r){if(console.log(r),200==r.data.status){var i=e.file,n=a;if(!i)return!1;if(!/\.(xls|xlsx)$/.test(i.name.toLowerCase()))return n.$message.error("上传格式不正确，请上传xls或者xlsx格式"),!1;var o=new FileReader;o.onload=function(e){try{var t=e.target.result,r=_.a.read(t,{type:"binary"}),i=r.SheetNames[0],o=_.a.utils.sheet_to_json(r.Sheets[i]),s={id:n.company_id,transfer_info:o};console.log("exl",o);for(var l=0;l<o.length;l++){if(!o[l]["卡号（必填）"])return n.$message.error("请填入卡号");if(!(o[l]["卡号（必填）"]>0))return n.$message.error("“"+o[l]["卡号（必填）"]+"”格式有误，请输入正确的卡号");if(!o[l]["划拨本金（必填）"]&&0!==o[l]["划拨本金（必填）"])return n.$message.error("请填入划拨本金");if(!(o[l]["划拨本金（必填）"]>=0))return n.$message.error("”"+o[l]["划拨本金（必填）"]+"“格式有误，请输入正确的划拨本金");if(!o[l]["划拨赠金（必填）"]&&0!==o[l]["划拨赠金（必填）"])return n.$message.error("请填入划拨赠金");if(!(o[l]["划拨赠金（必填）"]>=0))return n.$message.error("“"+o[l]["划拨赠金（必填）"]+"”格式有误，请输入正确的划拨赠金");if(0===o[l]["划拨本金（必填）"]&&0===o[l]["划拨赠金（必填）"])return n.$message.error("划拨本金与赠金不能同时为0元")}n.capitalAllocationContent="正在导入资金划拨中，请等待...",a.cardLoading=!0,h.a.create().post("/CompanyCard/batchImportTransferMoney ",s,{timeout:5e4}).then(function(e){200==e.data.status?(n.$message({message:"导入成功",type:"success"}),n.cardLoading=!1,n.getCompanyInfo(n.company_id),n.getUserCardList()):n.$message({message:e.data.info,type:"error"})}).catch(function(e){}).finally(function(){a.cardLoading=!1,a.capitalAllocationContent=""})}catch(e){return console.log(e),!1}},o.readAsBinaryString(i)}else t.$message.error(r.data.info)})},showWorkflowDetail:function(){this.companyListShow=!1,this.transferListShow=!1,this.showCompany=!1,this.showRechargeApprovalView=!0}}},k={render:function(){var e=this,a=e.$createElement,r=e._self._c||a;return r("div",{staticClass:"CardTransfer",attrs:{id:"CardTransfer"}},[r("div",{directives:[{name:"show",rawName:"v-show",value:e.companyListShow,expression:"companyListShow"}],staticClass:"companyList"},[r("div",{staticClass:"search flex items-center py-5 justify-between"},[r("div",[r("el-select",{staticClass:"w-200px mr-10px",attrs:{clearable:"",filterable:"",remote:"","reserve-keyword":"",placeholder:"请输入车队名称","remote-method":e.remoteMethod},on:{clear:e.searchAllCompany},model:{value:e.searchText,callback:function(a){e.searchText=a},expression:"searchText"}},e._l(e.companyOptions,function(e){return r("el-option",{key:e.CompanyID,attrs:{label:e.CompanyName,value:e.CompanyID}})}),1),e._v(" "),r("el-button",{attrs:{type:"primary",disabled:!e.searchText},on:{click:e.searchCompany}},[e._v("查询")]),e._v(" "),r("el-button",{staticClass:"ml-10px",on:{click:e.searchAllCompany}},[e._v("全部")])],1),e._v(" "),e.showRechargeApproval?r("el-button",{staticClass:"ml-auto",attrs:{type:"primary"},on:{click:e.showWorkflowDetail}},[e._v("\r\n              我提交的\r\n            ")]):e._e()],1),e._v(" "),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.companyLoading,expression:"companyLoading"}],staticClass:"cardTransferData",staticStyle:{width:"100%","margin-bottom":"20px"},attrs:{data:e.companyListData}},[r("el-table-column",{attrs:{align:"left",prop:"CompanyName","min-width":"240",label:"车队名称",formatter:e.formatterCellval}}),e._v(" "),r("el-table-column",{attrs:{align:"left","min-width":"180",label:"所属油站"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v(e._s(e.getStationName(a.row.StationNO)))]}}])}),e._v(" "),r("el-table-column",{attrs:{align:"center",prop:"CompanyCardNum","min-width":"120",label:"卡数量"}}),e._v(" "),r("el-table-column",{attrs:{align:"left",prop:"CompanyAccount","min-width":"120",label:"母账余额(元)",formatter:e.formatterCellval}}),e._v(" "),r("el-table-column",{attrs:{align:"left",prop:"CardSumAccount","min-width":"120",label:"子账余额(元)",formatter:e.formatterCellval}}),e._v(" "),r("el-table-column",{attrs:{align:"center",prop:"CompanyContacts","min-width":"120",label:"联系人",formatter:e.formatterCellval}}),e._v(" "),r("el-table-column",{attrs:{fixed:"right",align:"center","min-width":"200",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[r("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.goToRecharge(a.row)}}},[e._v("充值")]),e._v(" "),r("el-button",{attrs:{type:"text",disabled:0==a.row.CompanyAccount,size:"small"},on:{click:function(t){return e.goToTransfer(a.row)}}},[e._v("资金划拨")]),e._v(" "),r("el-button",{attrs:{type:"text",disabled:0==a.row.CardSumAccount,size:"small"},on:{click:function(t){return e.showCardList(a.row)}}},[e._v("资金返款")])]}}])})],1),e._v(" "),r("div",{staticClass:"page_content"},[r("el-pagination",{staticClass:"page_left",attrs:{"current-page":e.currentPage,"page-size":e.pageSize,layout:"prev, pager, next",total:e.total},on:{"current-change":e.handleCurrentChange}}),e._v(" "),r("el-pagination",{staticClass:"page_right",attrs:{"page-sizes":[10,15,20,30],"page-size":e.pageSize,layout:"total, sizes",total:e.total},on:{"size-change":e.handleSizeChange}})],1)],1),e._v(" "),r("el-dialog",{attrs:{"close-on-click-modal":!1,title:"车队账户充值",visible:e.rechargeDialogVisible,width:"600px"},on:{"update:visible":function(a){e.rechargeDialogVisible=a}}},[r("div",{staticClass:"card-info"},[r("div",{staticClass:"item"},[r("span",{staticClass:"tab"},[e._v("车队名称")]),e._v(" "),r("span",[e._v(e._s(e.companyInfo.company_name))])]),e._v(" "),r("div",{staticClass:"item"},[r("span",{staticClass:"tab"},[e._v("账户余额")]),e._v(" "),r("span",[e._v(e._s((Number(e.companyInfo.amount)+Number(e.companyInfo.give_amount)).toFixed(2)))])]),e._v(" "),r("div",{staticClass:"item"},[r("span",{staticClass:"tab"},[e._v("信用功能")]),e._v(" "),r("span",[e._v(e._s(e.getCreditName(e.companyInfo.credit_type))),"0"!=e.companyInfo.credit_type?r("span",[e._v("，额度"+e._s(e.companyInfo.credit_num)+"元")]):e._e()])]),e._v(" "),r("div",{staticClass:"item"},[r("span",{staticClass:"tab"},[e._v("本次充值")]),e._v(" "),r("div",[r("el-input-number",{attrs:{disabled:e.rechargeLoading,precision:2,min:0,max:1e8,controls:!1,placeholder:"请输入充值金额"},model:{value:e.amount,callback:function(a){e.amount=a},expression:"amount"}}),e._v(" "),e.showRechargeApproval?e._e():r("p",{directives:[{name:"show",rawName:"v-show",value:0!=e.giveAmount||e.couponInfo,expression:"giveAmount!=0 || couponInfo"}],staticStyle:{"font-size":"12px",display:"flex","align-items":"center"}},[r("img",{staticClass:"given-icon",attrs:{src:t("/Wgw"),alt:""}}),e._v(" "),r("span",{directives:[{name:"show",rawName:"v-show",value:0!=e.giveAmount,expression:"giveAmount!=0"}],staticStyle:{color:"#32af50"}},[e._v(e._s(e.giveAmount)+"元")]),e._v(" "),e.couponInfo?r("span",{staticStyle:{color:"#32af50"}},[r("span",{directives:[{name:"show",rawName:"v-show",value:0!=e.giveAmount,expression:"giveAmount!=0"}]},[e._v(" + ")]),e._v(e._s(e.couponInfo))]):e._e()])],1)])]),e._v(" "),r("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{size:"mini",disabled:e.rechargeLoading},on:{click:function(a){e.rechargeDialogVisible=!1}}},[e._v("取 消")]),e._v(" "),e.showRechargeApproval?r("el-button",{attrs:{type:"primary",size:"mini",disabled:e.rechargeLoading},on:{click:function(a){return e.submitRechargeApproval()}}},[e.rechargeLoading?r("i",{staticClass:"el-icon-loading"}):e._e(),e._v("\r\n          提交审核\r\n        ")]):r("el-button",{attrs:{type:"primary",size:"mini",disabled:!e.transferAble||e.rechargeLoading},on:{click:function(a){return e.chargeCompanyCard()}}},[e.rechargeLoading?r("i",{staticClass:"el-icon-loading"}):e._e(),e._v("\r\n          确 定\r\n        ")])],1)]),e._v(" "),r("el-dialog",{attrs:{"close-on-click-modal":!1,title:"车队账户充值",top:"50px",visible:e.dialogParams.showFixedCharge,width:"900px"},on:{opened:function(a){e.$nextTick(function(){return e.$refs.form.resetFields()})},closed:e.fixedChargeInit,"update:visible":function(a){return e.$set(e.dialogParams,"showFixedCharge",a)}}},[r("div",{directives:[{name:"loading",rawName:"v-loading",value:e.dialogParams.oilLoading||e.dialogParams.charging,expression:"dialogParams.oilLoading || dialogParams.charging"}]},[r("el-form",{ref:"form",attrs:{rules:e.dialogParams.rules,model:e.dialogParams.query,"label-width":"100px"}},[r("el-form-item",{attrs:{label:"车队名称"}},[r("span",[e._v(e._s(e.companyInfo.company_name))])]),e._v(" "),r("el-form-item",{attrs:{label:"账户余额"}},[r("span",[e._v(e._s(e.companyInfo.amount))])]),e._v(" "),r("el-form-item",{attrs:{label:"信用功能"}},[r("span",[e._v(e._s(e.getCreditName(e.companyInfo.credit_type))),"0"!=e.companyInfo.credit_type?r("span",[e._v("，额度"+e._s(e.companyInfo.credit_num)+"元")]):e._e()])]),e._v(" "),e.companyInfo&&e.companyInfo.oil_list?r("el-form-item",{attrs:{prop:"oils",label:"充值油品"}},[r("el-radio-group",{staticClass:"space-y-2",model:{value:e.dialogParams.query.oils,callback:function(a){e.$set(e.dialogParams.query,"oils",a)},expression:"dialogParams.query.oils"}},e._l(e.companyInfo.oil_list,function(a){return r("el-radio",{key:a.oil_id,attrs:{label:a.oil_id}},[e._v("\r\n                    "+e._s(a.oil_name)+"\r\n                ")])}),1)],1):e._e(),e._v(" "),r("el-form-item",{attrs:{prop:"amount",label:"充值金额"}},[r("el-input-number",{attrs:{precision:2,min:0,max:1e8,controls:!1,placeholder:"请输入充值金额"},on:{change:function(a){return e.automaticCalculation("amount")}},model:{value:e.dialogParams.query.amount,callback:function(a){e.$set(e.dialogParams.query,"amount",a)},expression:"dialogParams.query.amount"}}),e._v(" "),r("span",[e._v("元")])],1),e._v(" "),e.currentFixPriceMode===e.FIXED_PRICE_MODE.FixedAmount?r("el-form-item",{attrs:{prop:"unit_price",label:"油品单价"}},[r("el-input-number",{attrs:{precision:2,min:0,max:1e3,controls:!1,placeholder:"请输入油品单价"},on:{change:function(a){return e.automaticCalculation("unit_price")}},model:{value:e.dialogParams.query.unit_price,callback:function(a){e.$set(e.dialogParams.query,"unit_price",a)},expression:"dialogParams.query.unit_price"}}),e._v(" "),r("span",[e._v("元/升")])],1):e._e(),e._v(" "),r("el-form-item",{attrs:{prop:"liters",label:"油品升数"}},[r("el-input-number",{attrs:{precision:4,min:0,controls:!1,placeholder:"请输入油品升数"},on:{change:function(a){return e.automaticCalculation("liters")}},model:{value:e.dialogParams.query.liters,callback:function(a){e.$set(e.dialogParams.query,"liters",a)},expression:"dialogParams.query.liters"}}),e._v(" "),r("span",[e._v("升")]),e._v(" "),e.currentFixPriceMode===e.FIXED_PRICE_MODE.FixedRise?r("div",{staticClass:"p-0"},[e._v("油品单价换算为"),r("span",{staticClass:"text-primary font-bold"},[e._v(e._s(e.dialogParams.query.unit_price))]),e._v("元/升")]):e._e()],1),e._v(" "),r("el-form-item",{attrs:{label:"备注"}},[r("el-input",{staticClass:"w-90%",attrs:{rows:3,type:"textarea","show-word-limit":"",maxlength:200,placeholder:"请输入备注"},model:{value:e.dialogParams.query.remark,callback:function(a){e.$set(e.dialogParams.query,"remark",a)},expression:"dialogParams.query.remark"}})],1)],1)],1),e._v(" "),r("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{size:"mini",disabled:e.dialogParams.charging},on:{click:function(a){e.dialogParams.showFixedCharge=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary",disabled:e.dialogParams.charging,size:"mini"},on:{click:function(a){e.showRechargeApproval?e.submitRechargeApproval():e.chargeCompanyCard()}}},[e._v(e._s(e.showRechargeApproval?"提交审核":"确 定"))])],1)]),e._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:e.transferListShow,expression:"transferListShow"},{name:"loading",rawName:"v-loading",value:e.dialogParams.loadingInfo,expression:"dialogParams.loadingInfo"}]},[r("div",{staticStyle:{padding:"20px"}},[r("div",{staticClass:"header"},[r("el-button",{attrs:{size:"mini",plain:""},on:{click:e.goBack}},[e._v("返 回")]),e._v(" "),e.showLockPrice&&e.currentFixPriceMode!==e.FIXED_PRICE_MODE.No?e._e():r("div",{staticClass:"downloads"},[r("el-button",{attrs:{type:"text",size:"mini"},on:{click:e.downloadTemplate}},[e._v("下载导入模板")]),e._v(" "),r("el-divider",{attrs:{direction:"vertical"}}),e._v(" "),r("el-upload",{staticClass:"upload",staticStyle:{display:"inline"},attrs:{action:"",multiple:!1,"show-file-list":!1,accept:"csv, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","http-request":e.httpRequest}},[r("el-button",{attrs:{type:"text",size:"mini"}},[e._v("批量导入划拨")])],1)],1)],1),e._v(" "),r("div",{staticClass:"company-info"},[r("div",{staticClass:"line"},[r("div",{staticClass:"item"},[r("span",{staticClass:"tab"},[e._v("车队名称")]),e._v(" "),r("span",[e._v(e._s(e.companyInfo.company_name))])]),e._v(" "),r("div",{staticClass:"item"},[r("span",{staticClass:"tab"},[e._v("所属油站")]),e._v(" "),r("span",[e._v(e._s(e.getStationName(e.companyInfo.station_id)))])]),e._v(" "),r("div",{staticClass:"item"},[r("span",{staticClass:"tab"},[e._v("联系人")]),e._v(" "),r("span",[e._v(e._s(e.companyInfo.company_contacts))])])]),e._v(" "),r("div",{staticClass:"line"},[r("div",{staticClass:"item"},[r("span",{staticClass:"tab"},[e._v("本金")]),e._v(" "),r("span",[e._v(e._s(Number(e.companyInfo.amount).toFixed(2))+"元")])]),e._v(" "),r("div",{staticClass:"item"},[r("span",{staticClass:"tab"},[e._v("赠金")]),e._v(" "),r("span",[e._v(e._s(Number(e.companyInfo.give_amount).toFixed(2))+"元")])]),e._v(" "),e.showLockPrice&&e.currentFixPriceMode===e.FIXED_PRICE_MODE.FixedRise?r("div",{staticClass:"item"},[r("span",{staticClass:"tab"},[e._v("母账升数")]),e._v(" "),r("span",[e._v(e._s(e.sumUnitLiter(e.dialogParams.item.companyBatches))+"升")])]):e._e(),e._v(" "),r("div",{staticClass:"item"},[r("span",{staticClass:"tab"},[e._v("信用功能")]),e._v(" "),r("span",[e._v(e._s(e.getCreditName(e.companyInfo.credit_type)))])])])]),e._v(" "),r("div",{staticClass:"tips"},[r("div",[r("el-button",{attrs:{type:"primary",disabled:e.multipleSelection.length<2},on:{click:e.showMoreTransfer}},[e._v("批量划拨")]),e._v(" "),r("span",{staticClass:"txt"},[e._v("选中"+e._s(e.multipleSelection.length)+"张卡 ")])],1),e._v(" "),r("div",{staticClass:"search"},[r("span",[e._v("查询类型")]),e._v(" "),r("el-radio-group",{staticStyle:{margin:"0 15px"},model:{value:e.searchType,callback:function(a){e.searchType=a},expression:"searchType"}},[r("el-radio",{attrs:{label:"1"}},[e._v("手机号")]),e._v(" "),r("el-radio",{attrs:{label:"0"}},[e._v("卡号")]),e._v(" "),r("el-radio",{attrs:{label:"2"}},[e._v("卡面卡号")])],1),e._v(" "),r("div",{staticClass:"search"},[r("el-input",{staticStyle:{width:"210px","margin-right":"10px"},attrs:{placeholder:0==e.searchType?"请输入卡号":1==e.searchType?"请输入手机号":"请输入卡面卡号",clearable:""},model:{value:e.searchCardText,callback:function(a){e.searchCardText=a},expression:"searchCardText"}}),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:e.searchCard}},[e._v("查询")])],1)],1)])]),e._v(" "),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.cardLoading,expression:"cardLoading"}],staticStyle:{width:"100%","margin-bottom":"20px"},attrs:{"element-loading-text":e.capitalAllocationContent,data:e.cardListData},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"55"}}),e._v(" "),r("el-table-column",{attrs:{align:"left",prop:"CardNO",label:"卡号",width:"180",formatter:e.formatterCellval}}),e._v(" "),r("el-table-column",{attrs:{align:"center",prop:"Phone",label:"手机号",width:"180",formatter:e.formatterCellval}}),e._v(" "),r("el-table-column",{attrs:{align:"center",label:"卡面卡号",prop:"CardNumber",formatter:e.formatterCellval}}),e._v(" "),r("el-table-column",{attrs:{align:"center",label:"持卡人",prop:"cardholder_name",formatter:e.formatterCellval}}),e._v(" "),r("el-table-column",{attrs:{align:"center",label:"车牌号",prop:"CarNumber",formatter:e.formatterCellval}}),e._v(" "),r("el-table-column",{attrs:{align:"center",prop:"SUMAmount",label:"卡账余额(元)"}}),e._v(" "),e.showLockPrice&&e.currentFixPriceMode===e.FIXED_PRICE_MODE.FixedRise?r("el-table-column",{attrs:{align:"center",prop:"CardSumLiter",label:"子卡升数"}}):e._e(),e._v(" "),r("el-table-column",{attrs:{align:"center",prop:"Amount",label:"本金(元)"}}),e._v(" "),r("el-table-column",{attrs:{align:"center",prop:"GiveAmount",label:"赠金(元)"}}),e._v(" "),r("el-table-column",{attrs:{align:"center",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[r("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showSingleTransfer(a.row)}}},[e._v("资金划拨")])]}}])})],1),e._v(" "),r("div",{staticClass:"page_content"},[r("el-pagination",{staticClass:"page_left",attrs:{"current-page":e.currentCardPage,"page-size":e.pageCardSize,layout:"prev, pager, next",total:e.totalCard},on:{"current-change":e.handleCardCurrentChange}}),e._v(" "),r("el-pagination",{staticClass:"page_right",attrs:{"page-sizes":[10,15,20,30,50,100],"page-size":e.pageCardSize,layout:"total, sizes",total:e.totalCard},on:{"size-change":e.handleCardSizeChange}})],1),e._v(" "),r("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.dialogParams.allocationLoading,expression:"dialogParams.allocationLoading"}],attrs:{"destroy-on-close":!0,"close-on-click-modal":!1,title:"资金划拨",visible:e.singleDialogVisible,width:"800px"},on:{opened:e.allocationDialogInit,"update:visible":function(a){e.singleDialogVisible=a}}},[r("div",{staticClass:"card-info"},[r("el-form",{ref:"allocationForm",attrs:{model:e.dialogParams.query,"label-width":"90px",rules:e.dialogParams.allocationRules}},[r("el-form-item",{attrs:{label:"卡号"}},[r("span",[e._v(e._s(e.cardInfo.CardNO))])]),e._v(" "),r("el-form-item",{attrs:{label:"手机号"}},[r("span",[e._v(e._s(e.cardInfo.Phone))])]),e._v(" "),r("el-form-item",{attrs:{label:"卡面卡号"}},[r("span",[e._v(e._s(e.cardInfo.CardNumber||"--"))])]),e._v(" "),r("el-form-item",{attrs:{label:"卡账余额"}},[r("span",[e._v(e._s(e.cardInfo.SUMAmount)+"元")])]),e._v(" "),e.showLockPrice&&e.currentFixPriceMode!==e.FIXED_PRICE_MODE.No?[r("el-form-item",{directives:[{name:"loading",rawName:"v-loading",value:e.dialogParams.companyStationListLoading,expression:"dialogParams.companyStationListLoading"}],attrs:{label:"划拨油品",prop:"oils",required:""}},[!e.dialogParams.companyStationList||e.dialogParams.companyStationList&&0===e.dialogParams.companyStationList.length?r("p",{staticClass:"m-0"},[e._v("无可用油品")]):[r("el-radio-group",{staticClass:"space-y-2",on:{change:function(a){return e.$refs.allocationForm.validateField("amount")}},model:{value:e.dialogParams.query.oils,callback:function(a){e.$set(e.dialogParams.query,"oils",a)},expression:"dialogParams.query.oils"}},e._l(e.dialogParams.companyStationList,function(a){return r("el-radio",{key:a.oil_id,staticClass:"block",attrs:{label:a.oil_id,required:""}},[e._v("\r\n                          "+e._s(a.oil_name)+"\r\n                          "),Object.keys(e.currentCompanyBatchInfo).length>0?r("span",[e._v("\r\n                          (余额："+e._s(e.currentCompanyBatchInfo[a.oil_id])+e._s(e.currentFixPriceMode===e.FIXED_PRICE_MODE.FixedAmount?"元":"升")+")\r\n                        ")]):e._e()])}),1)]],2),e._v(" "),r("el-form-item",{attrs:{label:"划拨资金",prop:"amount"}},[r("el-input-number",{staticClass:"w-160px",attrs:{controls:!1,min:0,max:*********,precision:e.currentFixPriceMode===e.FIXED_PRICE_MODE.FixedAmount?2:4,clearable:"",placeholder:"请输入划拨资金"},model:{value:e.dialogParams.query.amount,callback:function(a){e.$set(e.dialogParams.query,"amount",a)},expression:"dialogParams.query.amount"}}),e._v(" "),r("span",{staticClass:"pl-2"},[e._v(e._s(e.currentFixPriceMode===e.FIXED_PRICE_MODE.FixedAmount?"元":"升"))])],1)]:[r("el-form-item",{attrs:{label:"划拨本金"}},[r("el-input",{staticStyle:{width:"160px"},attrs:{clearable:"",placeholder:"请输入划拨本金"},model:{value:e.PrincipalAmount,callback:function(a){e.PrincipalAmount=a},expression:"PrincipalAmount"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"划拨赠金"}},[r("el-input",{staticStyle:{width:"160px","margin-left":"4px"},attrs:{clearable:"",placeholder:"请输入划拨赠金"},model:{value:e.giveAmount,callback:function(a){e.giveAmount=a},expression:"giveAmount"}})],1)]],2)],1),e._v(" "),r("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{size:"mini"},on:{click:function(a){e.singleDialogVisible=!1}}},[e._v("取 消")]),e._v(" "),e.showLockPrice&&e.currentFixPriceMode!==e.FIXED_PRICE_MODE.No?r("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.allocationConfirm}},[e._v("确 定")]):r("el-button",{attrs:{type:"primary",size:"mini",disabled:!(e.PrincipalAmount&&e.giveAmount)},on:{click:e.transferCompanyAccount}},[e._v("确 定")])],1)]),e._v(" "),r("el-dialog",{attrs:{"close-on-click-modal":!1,title:"资金批量划拨",visible:e.moreDialogVisible,width:1==e.radioType?"720px":"940px"},on:{"update:visible":function(a){e.moreDialogVisible=a}}},[r("div",{staticStyle:{"margin-bottom":"30px"}},[r("span",[e._v("金额划拨")]),e._v(" "),r("el-radio-group",{on:{change:e.changeRadioType},model:{value:e.radioType,callback:function(a){e.radioType=a},expression:"radioType"}},[r("el-radio",{attrs:{label:"1"}},[e._v("划拨相同金额")]),e._v(" "),r("el-radio",{attrs:{label:"2"}},[e._v("划拨不同金额")])],1)],1),e._v(" "),r("p",[e._v("已选中 "),r("span",[e._v(e._s(e.multipleSelection.length))]),e._v(" 张卡")]),e._v(" "),r("el-table",{staticStyle:{width:"100%"},attrs:{data:e.multipleSelection}},[r("el-table-column",{attrs:{align:"center",prop:"CardNO",label:"卡号"}}),e._v(" "),r("el-table-column",{attrs:{align:"center",prop:"Phone",label:"手机号"}}),e._v(" "),r("el-table-column",{attrs:{align:"center",label:"卡面卡号",prop:"CardNumber",formatter:e.formatterCellval}}),e._v(" "),r("el-table-column",{attrs:{align:"center",prop:"Amount",label:"卡账余额(元)"}}),e._v(" "),2==e.radioType?r("el-table-column",{attrs:{align:"left",width:"350px",label:"划拨余额(元)"},scopedSlots:e._u([{key:"default",fn:function(a){return[r("el-input",{staticStyle:{width:"145px"},attrs:{clearable:"",placeholder:"请输入划拨本金"},model:{value:a.row.PrincipalAmount,callback:function(t){e.$set(a.row,"PrincipalAmount",t)},expression:"scope.row.PrincipalAmount"}}),e._v(" "),r("el-input",{staticStyle:{width:"145px"},attrs:{clearable:"",placeholder:"请输入划拨赠金"},model:{value:a.row.giveAmount,callback:function(t){e.$set(a.row,"giveAmount",t)},expression:"scope.row.giveAmount"}})]}}],null,!1,3442237139)}):e._e()],1),e._v(" "),1==e.radioType?r("div",{staticStyle:{"margin-top":"24px"}},[r("span",[e._v("单张卡划拨本金")]),e._v(" "),r("el-input",{staticStyle:{width:"145px"},attrs:{clearable:"",placeholder:"请输入划拨本金"},model:{value:e.PrincipalAmount,callback:function(a){e.PrincipalAmount=a},expression:"PrincipalAmount"}}),e._v("元，\r\n                "),r("span",[e._v("赠金")]),e._v(" "),r("el-input",{staticStyle:{width:"145px"},attrs:{clearable:"",placeholder:"请输入划拨赠金"},model:{value:e.giveAmount,callback:function(a){e.giveAmount=a},expression:"giveAmount"}}),e._v("元\r\n            ")],1):e._e(),e._v(" "),r("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{size:"mini"},on:{click:function(a){e.moreDialogVisible=!1}}},[e._v("取 消")]),e._v(" "),1==e.radioType?r("el-button",{attrs:{type:"primary",disabled:!(e.PrincipalAmount&&e.giveAmount),size:"mini"},on:{click:e.transferCompanyAccount}},[e._v("确 定")]):e._e(),e._v(" "),2==e.radioType?r("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.transferCompanyAccount}},[e._v("确 定")]):e._e()],1)],1),e._v(" "),r("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.dialogParams.allocationLoading,expression:"dialogParams.allocationLoading"}],attrs:{"destroy-on-close":!0,"close-on-click-modal":!1,title:"资金批量划拨",visible:e.dialogParams.showBatchAllocation,width:"940px"},on:{opened:e.allocationDialogInit,"update:visible":function(a){return e.$set(e.dialogParams,"showBatchAllocation",a)}},scopedSlots:e._u([{key:"footer",fn:function(){return[r("el-button",{attrs:{size:"mini"},on:{click:function(a){e.dialogParams.showBatchAllocation=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.allocationConfirm}},[e._v("确 定")])]},proxy:!0}])},[r("el-form",{ref:"allocationBatchForm",attrs:{model:e.dialogParams.query,rules:e.dialogParams.allocationRules}},[r("ul",{staticClass:"space-y-4 pb-4 list-none"},[r("li",[r("el-form-item",{attrs:{prop:"radioType"}},[r("span",[e._v("金额划拨")]),e._v(" "),r("el-radio-group",{on:{change:function(a){return e.changeRadioType(a)}},model:{value:e.dialogParams.radioType,callback:function(a){e.$set(e.dialogParams,"radioType",a)},expression:"dialogParams.radioType"}},[r("el-radio",{attrs:{label:1}},[e._v("划拨相同金额")]),e._v(" "),r("el-radio",{attrs:{label:2}},[e._v("划拨不同金额")])],1)],1)],1),e._v(" "),r("li",{directives:[{name:"loading",rawName:"v-loading",value:e.dialogParams.companyStationListLoading,expression:"dialogParams.companyStationListLoading"}]},[r("el-form-item",{attrs:{prop:"oils"}},[r("span",[e._v("划拨油品")]),e._v(" "),!e.dialogParams.companyStationList||e.dialogParams.companyStationList&&0===e.dialogParams.companyStationList.length?r("p",{staticClass:"m-0"},[e._v("无可用油品")]):[r("el-radio-group",{on:{change:function(a){return e.$refs.allocationBatchForm.validateField("amount")}},model:{value:e.dialogParams.query.oils,callback:function(a){e.$set(e.dialogParams.query,"oils",a)},expression:"dialogParams.query.oils"}},e._l(e.dialogParams.companyStationList,function(a){return r("el-radio",{key:a.oil_id,attrs:{label:a.oil_id,required:""}},[e._v("\r\n                      "+e._s(a.oil_name)+"\r\n                      "),Object.keys(e.currentCompanyBatchInfo).length>0?r("span",[e._v("\r\n                  (余额："+e._s(e.currentCompanyBatchInfo[a.oil_id])+e._s(e.currentFixPriceMode===e.FIXED_PRICE_MODE.FixedAmount?"元":"升")+")\r\n                ")]):e._e()])}),1)]],2)],1)]),e._v(" "),r("p",[e._v("已选中 "),r("span",[e._v(e._s(e.multipleSelection.length))]),e._v(" 张卡")]),e._v(" "),r("el-table",{staticStyle:{width:"100%"},attrs:{data:e.multipleSelection}},[r("el-table-column",{attrs:{align:"center",prop:"CardNO",label:"卡号"}}),e._v(" "),r("el-table-column",{attrs:{align:"center",prop:"Phone",label:"手机号"}}),e._v(" "),r("el-table-column",{attrs:{align:"center",prop:"CardNumber",label:"卡面卡号"}}),e._v(" "),r("el-table-column",{attrs:{align:"center",prop:"Amount",label:"账户余额 "}}),e._v(" "),2===e.dialogParams.radioType?r("el-table-column",{attrs:{align:"left",label:"划拨余额"},scopedSlots:e._u([{key:"default",fn:function(a){return[r("div",{staticClass:"flex items-center"},[r("el-input-number",{staticClass:"w-160px",attrs:{controls:!1,min:0,max:*********,precision:e.currentFixPriceMode===e.FIXED_PRICE_MODE.FixedAmount?2:4,clearable:"",placeholder:"请输入"+(e.currentFixPriceMode===e.FIXED_PRICE_MODE.FixedAmount?"金额":"升数")},model:{value:a.row.TransferUnitLiter,callback:function(t){e.$set(a.row,"TransferUnitLiter",t)},expression:"scope.row.TransferUnitLiter"}}),e._v(" "),r("span",{staticClass:"pl-2"},[e._v(e._s(e.currentFixPriceMode===e.FIXED_PRICE_MODE.FixedAmount?"元":"升"))])],1)]}}],null,!1,2418016608)}):e._e()],1),e._v(" "),1===e.dialogParams.radioType?r("div",{staticStyle:{"margin-top":"24px"}},[r("el-form-item",{attrs:{prop:"amount"}},[r("span",[e._v("每张卡划拨")]),e._v(" "),r("el-input-number",{staticClass:"w-160px",attrs:{controls:!1,min:0,max:*********,precision:e.currentFixPriceMode===e.FIXED_PRICE_MODE.FixedAmount?2:4,clearable:"",placeholder:"请输入"+(e.currentFixPriceMode===e.FIXED_PRICE_MODE.FixedAmount?"金额":"升数")},model:{value:e.dialogParams.query.amount,callback:function(a){e.$set(e.dialogParams.query,"amount",a)},expression:"dialogParams.query.amount"}}),e._v(" "),r("span",{staticClass:"pl-2"},[e._v(e._s(e.currentFixPriceMode===e.FIXED_PRICE_MODE.FixedAmount?"元":"升"))])],1)],1):e._e()],1)],1)],1),e._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:e.showCompany,expression:"showCompany"}]},[r("div",{staticStyle:{padding:"20px"}},[r("el-button",{attrs:{plain:"",size:"mini"},on:{click:e.goBack}},[e._v("返 回")]),e._v(" "),r("p",{staticStyle:{"font-size":"16px","font-weight":"bold"}},[e._v("当前车队："+e._s(e.companyName))]),e._v(" "),r("div",{staticStyle:{"margin-bottom":"20px"}},[r("el-date-picker",{staticStyle:{"margin-right":"20px"},attrs:{type:"daterange","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":{disabledDate:function(a){return a.getTime()>=e.$moment().add(1,"days").startOf("day").valueOf()}},format:"yyyy 年 MM 月 dd 日","value-format":"yyyy-MM-dd"},on:{change:e.getTransferDetailed},model:{value:e.searchDate,callback:function(a){e.searchDate=a},expression:"searchDate"}}),e._v(" "),r("span",[e._v("查询类型")]),e._v(" "),r("el-radio-group",{model:{value:e.searchType,callback:function(a){e.searchType=a},expression:"searchType"}},[r("el-radio",{attrs:{label:"1"}},[e._v("手机号")]),e._v(" "),r("el-radio",{attrs:{label:"0"}},[e._v("卡号")]),e._v(" "),r("el-radio",{attrs:{label:"2"}},[e._v("卡面卡号")])],1),e._v(" "),r("el-input",{staticStyle:{width:"210px"},attrs:{placeholder:"0"==e.searchType?"请输入卡号":"1"==e.searchType?"请输入手机号":"请输入卡面卡号"},model:{value:e.searchTxt,callback:function(a){e.searchTxt=a},expression:"searchTxt"}}),e._v(" "),r("el-button",{attrs:{type:"primary",disabled:!e.searchTxt.length>0},on:{click:e.getTransferDetailed}},[e._v("查询")])],1),e._v(" "),r("el-button",{attrs:{type:"primary",disabled:e.multipleSelectionCard.length<2},on:{click:e.showBatchRefund}},[e._v("批量返款")]),e._v(" "),r("span",[e._v("选中"+e._s(e.multipleSelectionCard.length)+"张卡")])],1),e._v(" "),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.cardLoading,expression:"cardLoading"}],ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:e.cardList},on:{"selection-change":e.handleSelectionCardChange}},[r("el-table-column",{attrs:{align:"center",type:"selection",width:"55"}}),e._v(" "),r("el-table-column",{attrs:{align:"center",prop:"CardNO",label:"卡号",formatter:e.formatterCellval}}),e._v(" "),r("el-table-column",{attrs:{align:"center",prop:"Phone",label:"手机号",formatter:e.formatterCellval}}),e._v(" "),r("el-table-column",{attrs:{align:"center",prop:"CardNumber",label:"卡面卡号",formatter:e.formatterCellval}}),e._v(" "),r("el-table-column",{attrs:{align:"center",label:"持卡人",prop:"cardholder_name",formatter:e.formatterCellval}}),e._v(" "),r("el-table-column",{attrs:{align:"center",prop:"CarNumber",label:"车牌号",formatter:e.formatterCellval}}),e._v(" "),r("el-table-column",{attrs:{align:"center",prop:"ReceiveAmount",label:"划拨金额(元)"}}),e._v(" "),r("el-table-column",{attrs:{align:"center",prop:"ReceiveGiveAmount",label:"赠送金额(元)"}}),e._v(" "),r("el-table-column",{attrs:{align:"center",label:"划拨时间"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v(e._s(e.$moment(a.row.CreateTime).format("YYYY-MM-DD HH:mm:ss")))]}}])}),e._v(" "),r("el-table-column",{attrs:{align:"center",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[r("el-button",{attrs:{type:"text"},on:{click:function(t){return e.showRefund(a.row)}}},[e._v("资金返款")])]}}])})],1),e._v(" "),r("div",{staticClass:"page_content"},[r("el-pagination",{staticClass:"page_left",attrs:{"current-page":e.currentPageTransfer,"page-size":e.pageSizeTransfer,layout:"prev, pager, next",total:e.totalTransfer},on:{"current-change":e.handleTransferCurrentChange}}),e._v(" "),r("el-pagination",{staticClass:"page_right",attrs:{"page-sizes":[10,15,20,30,50,100],"page-size":e.pageSizeTransfer,layout:"total, sizes",total:e.totalTransfer},on:{"size-change":e.handleTransferSizeChange}})],1)],1),e._v(" "),r("el-dialog",{attrs:{"close-on-click-modal":!1,title:"子卡资金返款",visible:e.dialogVisible,width:"942px"},on:{"update:visible":function(a){e.dialogVisible=a}}},[r("p",[e._v("选中"),r("span",{staticStyle:{color:"#32AF50"}},[e._v(e._s(e.multipleSelectionCard.length))]),e._v("张卡")]),e._v(" "),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.dialogParams.companyStationListLoading,expression:"dialogParams.companyStationListLoading"}],staticStyle:{width:"100%"},attrs:{data:e.multipleSelectionCard}},[r("el-table-column",{attrs:{align:"center",prop:"CardNO",label:"卡号"}}),e._v(" "),r("el-table-column",{attrs:{align:"center",prop:"Phone",label:"手机号"}}),e._v(" "),r("el-table-column",{attrs:{align:"center",prop:"CardNumber",label:"卡面卡号",formatter:e.formatterCellval}}),e._v(" "),e.showLockPrice&&e.currentFixPriceMode!==e.FIXED_PRICE_MODE.No?[r("el-table-column",{attrs:{align:"center",prop:"ChargeOil",label:"划拨油品"},scopedSlots:e._u([{key:"default",fn:function(a){var t=a.row;return[r("span",[e._v(e._s(e.getOilName(t.ChargeOil)))])]}}],null,!1,1243853230)}),e._v(" "),e.currentFixPriceMode===e.FIXED_PRICE_MODE.FixedRise?r("el-table-column",{attrs:{align:"center",prop:"UnitLiter",label:"划拨升数"}}):e.currentFixPriceMode===e.FIXED_PRICE_MODE.FixedAmount?r("el-table-column",{attrs:{align:"center",prop:"ReceiveAmount",label:"划拨金额"}}):e._e()]:r("el-table-column",{attrs:{align:"center",prop:"ReceiveAmount",label:"划拨余额(元)"}}),e._v(" "),e.showLockPrice||e.currentFixPriceMode===e.FIXED_PRICE_MODE.No?e._e():r("el-table-column",{attrs:{align:"center",prop:"ReceiveGiveAmount",label:"赠送余额(元)"}})],2),e._v(" "),r("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{size:"mini"},on:{click:function(a){e.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.refund}},[e._v("确认返款")])],1)],1),e._v(" "),e.showRechargeApprovalView?r("div",[r("div",{staticStyle:{padding:"20px"}},[r("el-button",{attrs:{plain:"",size:"mini"},on:{click:e.goBack}},[e._v("返 回")]),e._v(" "),r("span",{staticStyle:{"margin-left":"20px"}},[e._v("我提交的充值审核订单")])],1),e._v(" "),r("recharge-approval",{attrs:{includeSubmitterID:!0,hideStatusOptions:!0}})],1):e._e()],1)},staticRenderFns:[]};var L=t("VU/8")(A,k,!1,function(e){t("hMH/"),t("ebEc")},"data-v-7c50fc1d",null);a.default=L.exports},TmV0:function(e,a,t){t("fZOM"),e.exports=t("FeBl").Object.values},cMhO:function(e,a){},ebEc:function(e,a){},fZOM:function(e,a,t){var r=t("kM2E"),i=t("mbce")(!1);r(r.S,"Object",{values:function(e){return i(e)}})},gRE1:function(e,a,t){e.exports={default:t("TmV0"),__esModule:!0}},"hMH/":function(e,a){},mbce:function(e,a,t){var r=t("+E39"),i=t("lktj"),n=t("TcQ7"),o=t("NpIQ").f;e.exports=function(e){return function(a){for(var t,s=n(a),l=i(s),c=l.length,u=0,d=[];c>u;)t=l[u++],r&&!o.call(s,t)||d.push(e?[t,s[t]]:s[t]);return d}}},yMQS:function(e,a,t){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var r=t("fZjL"),i=t.n(r),n=t("woOf"),o=t.n(n),s=t("Xxa5"),l=t.n(s),c=t("exGp"),u=t.n(c),d=t("Dd8w"),m=t.n(d),p=t("gRE1"),g=t.n(p),f=t("M4fF"),v=t("NYxO"),h=t("3pLw"),_=t("mw3O"),y=t.n(_),b=t("bOzO"),C={PENDING:{value:0,label:"审核中",optionLabel:"待审核"},APPROVED:{value:1,label:"审核通过",optionLabel:"已审核"},REJECTED:{value:2,label:"审核驳回"},SUCCESS:{value:3,label:"审核通过"},FAILED:{value:4,label:"审核通过-充值失败"}},x=g()(C).filter(function(e){return e.optionLabel}),w={name:"RechargeApproval",props:{hideStatusOptions:{type:Boolean,default:!1},includeSubmitterID:{type:Boolean,default:!1}},data:function(){var e=this;return{STATUS:C,STATUS_OPTIONS:x,status:C.PENDING.value,dataList:[],stationId:[],dateRange:[],companyId:[],stationLoading:!1,companyLoading:!1,stationOptions:[],companyOptions:[],pickerOptions:{disabledDate:function(a){return a>e.$moment().endOf("day").toDate()},onPick:function(a){var t=a.maxDate,r=a.minDate;e.pickerMinDate=r,e.pickerMaxDate=t},shortcuts:[{text:"最近一周",onClick:function(a){var t=e.$moment().toDate(),r=e.$moment().subtract(7,"days").toDate();a.$emit("pick",[r,t])}},{text:"最近一个月",onClick:function(a){var t=e.$moment().toDate(),r=e.$moment().subtract(1,"months").toDate();a.$emit("pick",[r,t])}},{text:"最近三个月",onClick:function(a){var t=e.$moment().toDate(),r=e.$moment().subtract(3,"months").toDate();a.$emit("pick",[r,t])}}]},pickerMinDate:null,pickerMaxDate:null,query:{page:1,size:10,total:0},loading:!1,dialogState:{visible:!1,actionType:null,bonusLoading:!1,data:{id:"",CompanyName:"",CompanyContacts:"",ContactsPhone:"",BJ_MZ:"",ChargeOilName:"",ChargeAmount:"",ChargeLiter:"",BonusAmount:"",Submitter:"",SubmitTime:"",StationName:"",remark:"",CardLockType:0}},security:{verifyDialogVisible:!1,password:"",loading:!1},workflowDetailDialog:{visible:!1,data:[],loading:!1,columns:[{prop:"OperateTime",label:"操作时间"},{prop:"OperatorName",label:"操作人员"},{prop:"StatusChange",label:"状态变化"},{prop:"Remark",label:"备注"}]},FIXED_PRICE_MODE:b.a}},computed:m()({},Object(v.d)(["isGroup"]),Object(v.c)(["getCurrentStation"]),{currentFixPriceMode:function(){return String((this.dialogState.data?this.dialogState.data.CardLockType:"")||this.FIXED_PRICE_MODE.No)}}),created:function(){this.debounceRemoteMethod=Object(f.debounce)(this.remoteMethod,300),this.hideStatusOptions&&(this.status=this.STATUS.REJECTED.value)},mounted:function(){this.getStationOptions(),this.handleSearch()},methods:{getStationOptions:function(){var e=this;return u()(l.a.mark(function a(){var t;return l.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(!e.stationLoading){a.next=2;break}return a.abrupt("return");case 2:return e.stationLoading=!0,a.prev=3,a.next=6,e.$axios.post("/Stations/getStationList",{});case 6:200===(t=a.sent).status?e.stationOptions=t.data.data.map(function(e){return{stid:e.stid,stname:e.stname}}):e.$message.error(t.data.info||"获取油站列表失败"),a.next=14;break;case 10:a.prev=10,a.t0=a.catch(3),console.error("获取油站列表失败:",a.t0),e.$message.error("获取油站列表失败,请重试");case 14:return a.prev=14,e.stationLoading=!1,a.finish(14);case 17:case"end":return a.stop()}},a,e,[[3,10,14,17]])}))()},handleStatusChange:function(){this.query.page=1,this.handleSearch()},handleSearch:function(){var e=this;return u()(l.a.mark(function a(){var t,r,i,n,o;return l.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return e.loading=!0,a.prev=1,t={Page:e.query.page,PageSize:e.query.size,StationNoList:2==e.getCurrentStation.merchant_type?e.stationId&&e.stationId.length?e.stationId.join(","):void 0:e.getCurrentStation.merchant_id,CompanyIdList:e.companyId.join(","),QueryType:e.status},e.includeSubmitterID&&(r=JSON.parse(localStorage.getItem("__userInfo__")),t.SubmitterID=r&&r.adid?r.adid:""),e.dateRange&&2===e.dateRange.length&&(t.StartTime=e.$moment(e.dateRange[0]).format("YYYY-MM-DD 00:00:00"),t.EndTime=e.$moment(e.dateRange[1]).format("YYYY-MM-DD 23:59:59")),i=y.a.stringify(t,{arrayFormat:"repeat"}),a.next=8,Object(h.b)("/CompanyCard/getApplyRechargeList?"+i);case 8:n=a.sent,o=n.data,console.log("🚀 ~ file: RechargeApproval.vue:400 ~ handleSearch ~ res:",o,n),200===n.status?(e.dataList=o.list,e.query.total=o.page_info.TotalQty):e.$message.error(n.info||"获取数据失败"),a.next=18;break;case 14:a.prev=14,a.t0=a.catch(1),console.error("获取数据失败:",a.t0),e.$message.error("获取数据失败，请重试");case 18:return a.prev=18,e.loading=!1,a.finish(18);case 21:case"end":return a.stop()}},a,e,[[1,14,18,21]])}))()},remoteMethod:function(e){var a=this;return u()(l.a.mark(function t(){var r,i;return l.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(e){t.next=3;break}return a.companyOptions=[],t.abrupt("return");case 3:return a.companyLoading=!0,t.prev=4,t.next=7,a.$axios.post("/CompanyCard/getCompanyNameList",{input:e});case 7:200===(r=t.sent).data.status?(i=JSON.parse(r.data.data),a.companyOptions=i.map(function(e){return{CompanyID:e.CompanyID,CompanyName:e.CompanyName}})):a.$message.error(r.data.info||"搜索车队失败"),t.next=15;break;case 11:t.prev=11,t.t0=t.catch(4),console.error("搜索车队失败:",t.t0),a.$message.error("搜索车队失败,请重试");case 15:return t.prev=15,a.companyLoading=!1,t.finish(15);case 18:case"end":return t.stop()}},t,a,[[4,11,15,18]])}))()},handleSizeChange:function(e){this.query.size=e,this.query.page=1,this.handleSearch()},handleCurrentChange:function(e){this.query.page=e,this.handleSearch()},showWorkflowDetail:function(e){var a=this;return u()(l.a.mark(function t(){var r;return l.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return console.log("🚀 ~ showWorkflowDetail ~ row:",e),a.workflowDetailDialog.visible=!0,a.workflowDetailDialog.loading=!0,t.prev=3,t.next=6,a.$axios.get("/CompanyCard/getApplyRechargeInfo",{params:{id:e.ID}});case 6:200===(r=t.sent).data.status?a.workflowDetailDialog.data=r.data.data.map(function(e){return{OperateTime:e.OperateTime,OperatorName:e.OperatorName,StatusChange:e.StatusChange,Remark:e.Remark}}):a.$message.error(r.data.info||"获取工作流明细失败"),t.next=14;break;case 10:t.prev=10,t.t0=t.catch(3),console.error("获取工作流明细失败:",t.t0),a.$message.error("获取工作流明细失败，请重试");case 14:return t.prev=14,a.workflowDetailDialog.loading=!1,t.finish(14);case 17:case"end":return t.stop()}},t,a,[[3,10,14,17]])}))()},showApprovalDialog:function(e,a){if(console.log("🚀 ~ showApprovalDialog ~ this.isGroup:",this.isGroup),this.isGroup)this.$message.error("暂不支持集团");else{this.dialogState.visible=!0,this.dialogState.actionType=a;this.dialogState.data=o()({},{id:"",CompanyName:"",CompanyContacts:"",ContactsPhone:"",BJ_MZ:"",ChargeOilName:"",ChargeAmount:"",ChargeLiter:"",BonusAmount:"",Submitter:"",SubmitTime:"",StationName:"",remark:"",gift:"",CardLockType:0,ChargePrice:0},e||{},{id:e&&e.ID||"",CardLockType:e&&e.CardLockType||0,ChargePrice:e&&e.ChargePrice||0}),this.dialogState.bonusLoading=!0,this.getChargeBonus()}},handleApproval:function(){var e=this;return u()(l.a.mark(function a(){return l.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:e.security.verifyDialogVisible=!0;case 1:case"end":return a.stop()}},a,e)}))()},getStatusLabel:function(e){var a=i()(C).find(function(a){return C[a].value===e});return a&&C[a]?C[a].label:e},verifyPassword:function(){var e=this;return u()(l.a.mark(function a(){var t,r,i;return l.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(e.security.password){a.next=3;break}return e.$message.warning("请输入操作密码"),a.abrupt("return");case 3:if(!(e.security.password.length>64)){a.next=6;break}return e.$message.warning("操作密码不能超过64位"),a.abrupt("return");case 6:return a.prev=6,e.security.loading=!0,t=e.dialogState.actionType,r={id:e.dialogState.data.id,status:t,remark:e.dialogState.data.remark,password:e.security.password,company_id:e.dialogState.data.CompanyID,amount:e.dialogState.data.ChargeAmount,pay_way:1,CardLockType:e.dialogState.data.CardLockType||0,Bonus:e.dialogState.data.gift,oils:e.dialogState.data.ChargeOil,adid:function(){var e=localStorage.getItem("__userInfo__");if(e)try{return JSON.parse(e).adid||""}catch(e){return console.error("解析userInfo失败",e),""}return""}(),liters:e.dialogState.data.ChargeLiter,unit_price:e.dialogState.data.ChargePrice,other:e.dialogState.data},a.next=12,e.$axios.post("/CompanyCard/auditApplyRecharge",r);case 12:if(200!==(i=a.sent).data.status){a.next=22;break}return e.$message.success("操作成功"),e.dialogState.visible=!1,e.security.verifyDialogVisible=!1,e.security.password="",a.next=20,e.handleSearch();case 20:a.next=23;break;case 22:e.$message.error(i.data.info||"操作失败");case 23:a.next=29;break;case 25:a.prev=25,a.t0=a.catch(6),console.error("操作失败:",a.t0),e.$message.error("操作失败，请重试");case 29:return a.prev=29,e.security.loading=!1,a.finish(29);case 32:case"end":return a.stop()}},a,e,[[6,25,29,32]])}))()},getChargeBonus:function(){var e=this;if(this.dialogState.bonusLoading=!0,this.currentFixPriceMode!==b.a.No)return this.dialogState.data.gift="",void(this.dialogState.bonusLoading=!1);if(!this.dialogState.data.ChargeAmount)return this.dialogState.data.gift="",void(this.dialogState.bonusLoading=!1);var a=this.dialogState.data.CompanyID||"";this.$axios.post("/CompanyCard/getChargeAmount",{company_id:a,amount:this.dialogState.data.ChargeAmount,pay_way:1}).then(function(a){if(a&&a.data&&200===a.data.status){var t=a.data.data.donate_money?a.data.data.donate_money+"元":"",r=a.data.data.couponinfo,i="";i=r&&1==r.coupon_type?2==r.retail_type?r.price+"元油品券*"+r.count:3==r.retail_type?r.price+"元非油券*"+r.count:4==r.retail_type?r.price+"元服务券*"+r.count:r.price+"元赠金券*"+r.count:r&&3==r.coupon_type?2==r.retail_type?r.price+"折油品券*"+r.count:3==r.retail_type?r.price+"折非油券*"+r.count:4==r.retail_type?r.price+"折服务券*"+r.count:r.price+"折赠金券*"+r.count:r&&2==r.coupon_type?2==r.retail_type?"油品券*"+r.count:3==r.retail_type?"非油券*"+r.count:4==r.retail_type?"服务券*"+r.count:"赠金券*"+r.count:r&&0==r.coupon_type?"券包":"",e.dialogState.data.gift=t+(i?" + "+i:"")}else{e.dialogState.data.gift="";var n=a&&a.data&&a.data.info?"获取赠送金额失败："+a.data.info:"获取赠送金额失败";e.$message.error(n)}}).catch(function(a){console.log("请求异常:",a),e.dialogState.data.gift="",e.$message.error("获取赠送金额失败，请重试")}).finally(function(){e.$nextTick(function(){e.dialogState.bonusLoading=!1,console.log("Loading状态已设置为:",e.dialogState.bonusLoading)})})}},watch:{dateRange:function(e){if(e&&2===e.length){var a=this.$moment(e[0]),t=this.$moment(e[1]).diff(a,"months",!0);console.log("🚀 ~ dateRange ~ monthDiff:",t),t>3&&(this.dateRange=[],this.$message.error("选择的时间范围不能超过3个月"))}},getCurrentStation:function(e,a){a&&e.value===a.value||(this.companyId=[],this.companyOptions=[],this.query.page=1,this.handleSearch())}}},P={render:function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"recharge-approval py-5"},[t("div",{staticClass:"flex items-center mb-5"},[2==e.getCurrentStation.merchant_type?t("div",{staticClass:"flex items-center mr-5"},[t("span",{staticClass:"mr-2.5"},[e._v("油站名称")]),e._v(" "),t("el-select",{directives:[{name:"loading",rawName:"v-loading",value:e.stationLoading,expression:"stationLoading"}],staticClass:"w-250px",attrs:{placeholder:"请选择",multiple:"",clearable:"","collapse-tags":""},model:{value:e.stationId,callback:function(a){e.stationId=a},expression:"stationId"}},e._l(e.stationOptions,function(e,a){return t("el-option",{key:a,attrs:{label:e.stname,value:e.stid}})}),1)],1):e._e(),e._v(" "),t("div",{staticClass:"flex items-center mr-5"},[t("span",{staticClass:"mr-2.5"},[e._v("提交日期范围")]),e._v(" "),t("el-date-picker",{staticClass:"w-280px",attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,clearable:""},model:{value:e.dateRange,callback:function(a){e.dateRange=a},expression:"dateRange"}})],1),e._v(" "),t("div",{staticClass:"flex items-center mr-5"},[t("span",{staticClass:"mr-2.5"},[e._v("车队名称")]),e._v(" "),t("el-select",{staticClass:"w-[200px]",attrs:{clearable:"",filterable:"",remote:"","reserve-keyword":"",placeholder:"请输入车队名称","remote-method":e.debounceRemoteMethod,loading:e.companyLoading,multiple:"","collapse-tags":""},model:{value:e.companyId,callback:function(a){e.companyId=a},expression:"companyId"}},e._l(e.companyOptions,function(e){return t("el-option",{key:e.CompanyID,attrs:{label:e.CompanyName,value:e.CompanyID}})}),1)],1),e._v(" "),t("el-button",{attrs:{type:"primary",disabled:e.loading},on:{click:e.handleSearch}},[e._v("查询")])],1),e._v(" "),t("div",[e.hideStatusOptions?e._e():t("el-radio-group",{on:{change:e.handleStatusChange},model:{value:e.status,callback:function(a){e.status=a},expression:"status"}},e._l(e.STATUS_OPTIONS,function(a){return t("el-radio-button",{key:a.value,attrs:{label:a.value}},[e._v("\n        "+e._s(a.optionLabel)+"\n      ")])}),1),e._v(" "),t("div",{staticClass:"mt-4"},[e.status===e.STATUS.PENDING.value?t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.dataList}},[t("el-table-column",{attrs:{prop:"CompanyName",label:"车队名称"}}),e._v(" "),t("el-table-column",{attrs:{prop:"CompanyContacts",label:"车队管理员"}}),e._v(" "),t("el-table-column",{attrs:{prop:"ContactsPhone",label:"管理员手机号码"}}),e._v(" "),t("el-table-column",{attrs:{prop:"BJ_MZ",label:"母账余额（元）"}}),e._v(" "),t("el-table-column",{attrs:{label:"母账升数（升）"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v("\n            "+e._s(0===a.row.MZ_Liters||null==a.row.MZ_Liters?"--":Number(a.row.MZ_Liters).toFixed(4))+"\n          ")]}}],null,!1,2715690950)}),e._v(" "),t("el-table-column",{attrs:{label:"充值油品"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v("\n            "+e._s(e.formatterCellval(a.row.ChargeOilName))+"\n          ")]}}],null,!1,2370095318)}),e._v(" "),t("el-table-column",{attrs:{label:"本次充值（元）"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v("\n            "+e._s(e.formatterCellval(a.row.ChargeAmount))+"\n          ")]}}],null,!1,3961013783)}),e._v(" "),t("el-table-column",{attrs:{label:"本次充值（升）"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v("\n            "+e._s(0===a.row.ChargeLiter||null==a.row.ChargeLiter?"--":Number(a.row.ChargeLiter).toFixed(4))+"\n          ")]}}],null,!1,1056217799)}),e._v(" "),t("el-table-column",{attrs:{prop:"Submitter",label:"提交人员"}}),e._v(" "),t("el-table-column",{attrs:{prop:"StationName",label:"操作油站"}}),e._v(" "),t("el-table-column",{attrs:{prop:"SubmitTime",label:"提交审核时间"}}),e._v(" "),t("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.showApprovalDialog(a.row,e.STATUS.APPROVED.value)}}},[e._v("通过")]),e._v(" "),t("el-link",{attrs:{type:"danger",underline:!1},on:{click:function(t){return e.showApprovalDialog(a.row,e.STATUS.REJECTED.value)}}},[e._v("驳回")])]}}],null,!1,*********)})],1):e._e(),e._v(" "),e.status===e.STATUS.APPROVED.value||e.status===e.STATUS.REJECTED.value?t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.dataList}},[t("el-table-column",{attrs:{prop:"CompanyName",label:"车队名称"}}),e._v(" "),t("el-table-column",{attrs:{prop:"CompanyContacts",label:"车队管理员"}}),e._v(" "),t("el-table-column",{attrs:{prop:"ContactsPhone",label:"管理员手机号码"}}),e._v(" "),t("el-table-column",{attrs:{prop:"BJ_MZ",label:"母账余额（元）"}}),e._v(" "),t("el-table-column",{attrs:{label:"母账升数（升）"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v("\n            "+e._s(0===a.row.MZ_Liters||null==a.row.MZ_Liters?"--":Number(a.row.MZ_Liters).toFixed(4))+"\n          ")]}}],null,!1,2715690950)}),e._v(" "),t("el-table-column",{attrs:{label:"充值油品"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v("\n            "+e._s(e.formatterCellval(a.row.ChargeOilName))+"\n          ")]}}],null,!1,2370095318)}),e._v(" "),t("el-table-column",{attrs:{label:"本次充值（元）"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v("\n            "+e._s(e.formatterCellval(a.row.ChargeAmount))+"\n          ")]}}],null,!1,3961013783)}),e._v(" "),t("el-table-column",{attrs:{label:"本次充值（升）"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v("\n            "+e._s(0===a.row.ChargeLiter||null==a.row.ChargeLiter?"--":Number(a.row.ChargeLiter).toFixed(4))+"\n          ")]}}],null,!1,1056217799)}),e._v(" "),t("el-table-column",{attrs:{label:"本次充值赠送"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v("\n            "+e._s(e.formatterCellval(a.row.Bonus))+"\n          ")]}}],null,!1,2970561444)}),e._v(" "),t("el-table-column",{attrs:{prop:"Submitter",label:"提交人员"}}),e._v(" "),t("el-table-column",{attrs:{prop:"StationName",label:"操作油站"}}),e._v(" "),t("el-table-column",{attrs:{prop:"SubmitTime",label:"提交审核时间"}}),e._v(" "),t("el-table-column",{attrs:{label:"审核结果"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.status||a.row.Status===e.STATUS.REJECTED.value?t("span",{staticStyle:{color:"#F56C6C"}},[e._v("\n              "+e._s(e.getStatusLabel(a.row.status||a.row.Status))+"\n            ")]):t("span",[e._v("\n              "+e._s(e.getStatusLabel(a.row.status||a.row.Status))+"\n            ")])]}}],null,!1,2942292958)}),e._v(" "),t("el-table-column",{attrs:{prop:"Auditor",label:"审核人员"}}),e._v(" "),t("el-table-column",{attrs:{prop:"AuditTime",label:"审核时间"}}),e._v(" "),t("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.showWorkflowDetail(a.row)}}},[e._v("工作流明细")])]}}],null,!1,41091850)})],1):e._e(),e._v(" "),t("div",{staticClass:"mt-4 w-full flex justify-between items-center"},[t("el-pagination",{attrs:{"current-page":e.query.page,"page-size":e.query.size,layout:"prev, pager, next",total:e.query.total},on:{"current-change":e.handleCurrentChange}}),e._v(" "),t("el-pagination",{attrs:{"page-sizes":[10,20,50,100],"page-size":e.query.size,layout:"total, sizes",total:e.query.total},on:{"size-change":e.handleSizeChange}})],1)],1)],1),e._v(" "),t("el-dialog",{attrs:{title:"充值审核",visible:e.dialogState.visible,width:"600px"},on:{"update:visible":function(a){return e.$set(e.dialogState,"visible",a)}}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.dialogState.bonusLoading,expression:"dialogState.bonusLoading"}]},[t("el-form",{attrs:{"label-width":"120px"}},[t("el-row",{staticClass:"flex-wrap",attrs:{gutter:15,type:"flex"}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"车队名称："}},[t("span",[e._v(e._s(e.dialogState.data.CompanyName))])])],1),e._v(" "),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"车队管理员："}},[t("span",[e._v(e._s(e.dialogState.data.CompanyContacts))])])],1),e._v(" "),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"车队余额："}},[t("span",[e._v(e._s(e.dialogState.data.BJ_MZ))]),e._v("元\n            ")])],1),e._v(" "),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"手机号："}},[t("span",[e._v(e._s(e.dialogState.data.ContactsPhone))])])],1),e._v(" "),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"充值油品："}},[t("span",[e._v(e._s(e.formatterCellval(e.dialogState.data.ChargeOilName)))])])],1),e._v(" "),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"本次充值(元)："}},[t("span",[e._v(e._s(e.formatterCellval(e.dialogState.data.ChargeAmount))+" 元")])])],1),e._v(" "),e.currentFixPriceMode!==e.FIXED_PRICE_MODE.No?t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"本次充值(升)："}},[t("span",[e._v("\n                "+e._s(0===e.dialogState.data.ChargeLiter||null==e.dialogState.data.ChargeLiter?"--":Number(e.dialogState.data.ChargeLiter).toFixed(4))+" \n                "),0!==e.dialogState.data.ChargeLiter&&null!=e.dialogState.data.ChargeLiter?[e._v("升")]:e._e()],2)])],1):e._e(),e._v(" "),e.currentFixPriceMode!==e.FIXED_PRICE_MODE.No?t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"油品单价："}},[t("span",[e._v(e._s(e.dialogState.data.ChargePrice)+"元/升")])])],1):e._e(),e._v(" "),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"提交人员："}},[t("span",[e._v(e._s(e.dialogState.data.Submitter))])])],1),e._v(" "),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"操作油站："}},[t("span",[e._v(e._s(e.dialogState.data.StationName))])])],1),e._v(" "),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"操作时间："}},[t("span",[e._v(e._s(e.dialogState.data.SubmitTime))])])],1),e._v(" "),t("el-col",{attrs:{span:12}},[e.dialogState.data.gift?t("el-form-item",{attrs:{label:"充值赠送："}},[t("span",[e._v(e._s(e.dialogState.data.gift))])]):e._e()],1),e._v(" "),t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"备注："}},[t("el-input",{attrs:{type:"textarea",maxlength:"120",rows:3,placeholder:"请输入备注"},model:{value:e.dialogState.data.remark,callback:function(a){e.$set(e.dialogState.data,"remark",a)},expression:"dialogState.data.remark"}})],1)],1)],1)],1)],1),e._v(" "),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{disabled:e.dialogState.bonusLoading},on:{click:function(a){e.dialogState.visible=!1}}},[e._v("取 消")]),e._v(" "),e.dialogState.actionType===e.STATUS.APPROVED.value?t("el-button",{attrs:{type:"primary",disabled:e.dialogState.bonusLoading},on:{click:e.handleApproval}},[e._v("审核通过")]):t("el-button",{attrs:{type:"danger",disabled:e.dialogState.bonusLoading},on:{click:e.handleApproval}},[e._v("审核驳回")])],1)]),e._v(" "),t("el-dialog",{staticClass:"safe-dialog",attrs:{title:"安全验证","append-to-body":"",visible:e.security.verifyDialogVisible,width:"400px"},on:{"update:visible":function(a){return e.$set(e.security,"verifyDialogVisible",a)}}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.security.loading,expression:"security.loading"}]},[2==e.getCurrentStation.merchant_type?t("p",[e._v("请输入集团管理员操作密码")]):t("p",[e._v("请输入油站管理员操作密码")]),e._v(" "),t("el-input",{attrs:{type:"password",placeholder:"请输入操作密码",maxlength:"64","show-word-limit":""},model:{value:e.security.password,callback:function(a){e.$set(e.security,"password",a)},expression:"security.password"}})],1),e._v(" "),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{size:"mini",disabled:e.loading},on:{click:function(){e.security.verifyDialogVisible=!1,e.security.password=""}}},[e._v("取 消")]),e._v(" "),t("el-button",{attrs:{size:"mini",type:"primary",disabled:!e.security.password||e.security.loading||e.loading},on:{click:e.verifyPassword}},[e._v("确认")])],1)]),e._v(" "),t("el-dialog",{attrs:{title:"工作流明细",visible:e.workflowDetailDialog.visible,width:"800px"},on:{"update:visible":function(a){return e.$set(e.workflowDetailDialog,"visible",a)}}},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.workflowDetailDialog.loading,expression:"workflowDetailDialog.loading"}],attrs:{data:e.workflowDetailDialog.data,border:"",height:"400px"}},e._l(e.workflowDetailDialog.columns,function(e){return t("el-table-column",{key:e.prop,attrs:{prop:e.prop,label:e.label}})}),1),e._v(" "),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:function(a){e.workflowDetailDialog.visible=!1}}},[e._v("确 定")])],1)],1)],1)},staticRenderFns:[]};var S=t("VU/8")(w,P,!1,function(e){t("cMhO")},"data-v-5aa4380e",null);a.default=S.exports}});