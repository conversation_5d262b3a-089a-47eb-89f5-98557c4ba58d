import {DataCode, post} from "../../utils/http";
import {isEmpty, isString} from "lodash";

export const STATUS_OPEN = '100';
export const STATUS_DISABLED = '102';
export default {
  name: 'CustomerType',
  data() {
    return {
      customerTypeLoading: false,
      customerTypeOptions: [], // 客户类型
    }
  },
  computed: {
    invalidCustomerTypeList() {
      if (isEmpty(this.customerTypeOptions)) {
        return []
      }
      return this.customerTypeOptions.filter(item => item.Status === STATUS_OPEN)
    }
  },
  activated() {
    this.getCustomerType()
  },
  methods: {
    findDisabledCustomerType(CompanyTypeID){
      if(!CompanyTypeID){
        return null
      }
      return this.customerTypeOptions.find(item => item.CompanyTypeID === CompanyTypeID && item.Status === STATUS_DISABLED)
    },
    // 获取客户类型
    async getCustomerType() {
      try {
        this.customerTypeLoading = true;
        const res = await post('/CardReport/getCardCompanyCustomerType')
        if (res.status !== DataCode.SUCCESS) {
          this.$message({type: 'error', message: res.info})
          console.log("=>(fleetInvoiceReport.vue:361) 获取客户类型失败", res);
          return;
        }
        this.customerTypeOptions = []
        if (isEmpty(res.data) || !isString(res.data)) {
          return;
        }
        this.customerTypeOptions = JSON.parse(res.data)
        this.$store.commit('SET_CUSTOMER_TYPE', this.customerTypeOptions);
      } catch (e) {
        console.log("=>(fleetInvoiceReport.vue:361) 获取客户类型失败", e);
      } finally {
        this.customerTypeLoading = false;
      }
    }
  }
}
