<template>
  <div>
    <!-- 密码过期提醒对话框 -->
    <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="400px"
      center
      @close="handleDialogClose">
      <div slot="title" class="dialog-title">
        密码过期提醒
      </div>
      <div class="dialog-content">
        {{ dialogContent }}
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确认修改</el-button>
      </div>
    </el-dialog>

    <!-- 修改密码表单对话框 -->
    <el-dialog
      title="修改登录密码"
      :visible.sync="passwordDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="500px">
      <div v-loading="loading">
        <el-form :model="passwordForm" :rules="rules" ref="passwordForm" label-width="100px">
          <el-form-item label="原密码" prop="oldPassword">
            <el-input v-model="passwordForm.oldPassword" type="password" show-password maxlength="32"></el-input>
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
            <el-input v-model="passwordForm.newPassword" type="password" show-password maxlength="32"></el-input>
            <div class="form-tip">密码需要10位及以上，同时包含大写字母、小写字母和数字</div>
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input v-model="passwordForm.confirmPassword" type="password" show-password maxlength="32"></el-input>
          </el-form-item>
        </el-form>
        <div class="flex justify-end">
          <el-button @click="passwordDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitPasswordChange">确 定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import '@/assets/css/unify.css'
import ssoUrlObj from "../assets/js/url.js";
export default {
  name: 'PasswordExpireCheck',
  data() {
    const validateNewPassword = (rule, value, callback) => {
      if (value.length < 10) {
        callback(new Error('密码不少于10个字符'))
      } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^]{10,}$/.test(value)) {
        callback(new Error('密码需要同时包含大写字母、小写字母和数字'))
      } else {
        callback()
      }
    }
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }
    return {
      loading: false, // 修改密码时加载中
      dialogVisible: false,
      passwordDialogVisible: false,
      dialogContent: '',
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      rules: {
        oldPassword: [
          { required: true, message: '请输入原密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { validator: validateNewPassword, trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 检查密码状态的主函数
    async checkPasswordStatus() {
      // 如果已经显示过提醒，则不再显示
      if (localStorage.getItem('pwd_expire_reminder')) {
        return false
      }

      try {
        const res = await this.$axios.post('/Setting/getModifyPasswordConfig')
        if (res.data.status === 200 && res.data.data) {
          const { period, current_config } = res.data.data
          
          // 如果period为'0000'，表示不会过期，不做任何处理
          if (period === '0000') return false
          
          // 根据period状态设置不同的提示信息
          this.dialogContent = period === '' 
            ? `集团设置了账户初始密码过期天数，当前账号初始密码超过${current_config}天未修改，为了您的账户安全，请修改后重新登录。`
            : `温馨提示：集团设置了账户初始密码过期天数，当前账号初始密码将于${period}过期，为了您的账户安全，请修改后重新登录`

          this.dialogVisible = true
          return true
        }
        return false
      } catch (error) {
        console.error('检查密码状态失败:', error)
        this.$message.error('网络错误，请稍后重试')
        return false
      }
    },
//退出登录
logout() {
      let that = this;
      that.$axios
        .post("/ssoLogin/logout", {})
        .then(function(res) {
          if (res.data.status == 200) {
            localStorage.removeItem("__userInfo__");
            sessionStorage.removeItem("__userInfo__");
            localStorage.removeItem("options");
            localStorage.removeItem("currentStation");
            let url = window.location.href.split("?")[0];
            const lh = window.location.href;
            if (lh.indexOf("localhost:8080") != -1) {
              window.location.href =
                ssoUrlObj.ssoTest +
                "/v1/login?redirect=" +
                encodeURIComponent(url);
            } else if (lh.indexOf("test-card-admin.wcc.cn") != -1) {
              window.location.href =
                ssoUrlObj.ssoTest +
                "/v1/login?redirect=" +
                encodeURIComponent(url);
            } else if (lh.indexOf("preview-card-admin.weicheche.cn") != -1) {
              window.location.href =
                ssoUrlObj.ssoPreview +
                "/v1/login?redirect=" +
                encodeURIComponent(url);
            } else if (lh.indexOf("card-admin.zhihuiyouzhan.com") != -1) {
              window.location.href =
                ssoUrlObj.ssoProduct +
                "/v1/login?redirect=" +
                encodeURIComponent(url);
            } else {
              window.location.href =
                ssoUrlObj.ssoTest +
                "/v1/login?redirect=" +
                encodeURIComponent(url);
            }
          } else {
            that.$message({
              message: res.data.info,
              type: "error"
            });
          }
        })
        .catch(function(error) {});
    },
    // 处理确认按钮
    handleConfirm() {
      this.dialogVisible = false
      this.passwordDialogVisible = true
    },

    // 处理对话框关闭
    handleDialogClose() {
      localStorage.setItem('pwd_expire_reminder', '1')
    },

    // 提交密码修改
    submitPasswordChange() {
      this.$refs.passwordForm.validate(async (valid) => {
        if (valid) {
          try {
            this.loading = true;
            
            let userInfo = JSON.parse(localStorage.getItem("__userInfo__") || "{}");
            
            const res = await this.$axios.post('/Setting/changePwd', {
              adid: userInfo.adid,
              new_pwd: this.passwordForm.newPassword,
              orig_pwd: this.passwordForm.oldPassword
            })

            if (res.data.status === 200) {
              localStorage.removeItem('pwd_expire_reminder')
              this.$message.success('修改成功,即将退出登录,需重新登录')
              this.passwordDialogVisible = false
              setTimeout(() => {
                this.logout()
              }, 1000)
            } else {
              this.$message.error(res.data.info || '密码修改失败')
            }
          } catch (error) {
            console.error('修改密码失败:', error)
            this.$message.error('网络错误，请稍后重试')
          } finally {
            this.loading = false
          }
        }
      })
    },

    // 重置表单
    resetForm() {
      this.passwordForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
      this.$refs.passwordForm.resetFields()
    }
  }
}
</script>

<style scoped>
.dialog-title {
  text-align: center;
  font-weight: bold;
}
.dialog-content {
  text-align: center;
  padding: 20px 0;
  line-height: 1.5;
}
.dialog-footer {
  text-align: center;
}
.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  padding-top: 4px;
}
</style> 