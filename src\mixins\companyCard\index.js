import BigNumber from "bignumber.js";
import {FIXED_PRICE_MODE} from "../../utils/contants/fixedPrice";

export default {
  data() {
    return {

      FIXED_PRICE_MODE
    }
  },
  methods: {
    zero2Formater(num){
      if(!num || Number(num) === 0){
        return '-'
      }
      return num
    },
    sumUnitLiter(companyBatches) {
      if (!companyBatches || !Array.isArray(companyBatches)) {
        return 0;
      }

      return companyBatches.reduce((acc, batch) => {
        if (batch && (typeof batch.UnitLiter) === 'number') {
          return acc.plus(new BigNumber(batch.UnitLiter));
        }
        return acc;
      }, new BigNumber(0)).toFixed(4);
    },
  }
}
