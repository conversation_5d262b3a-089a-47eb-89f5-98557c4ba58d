<template>
  <div class="report">
    <div class="report-content">
      <div class="content-header">
        <div class="typeBtn">
          <el-radio-group v-model="typeValue" @change="changeTypeValue">
            <el-radio-button v-for="item in typeOptions" :key="item.value" :label="item.value">{{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </div>
        <div style="margin-top:10px">
          <span class="txt" v-if="getCurrentStation.merchant_type == 2">开卡油站</span>
          <el-select v-model="stationId" :multiple="typeValue == 1" clearable collapse-tags
            style="width:250px;margin-right:20px;" placeholder="请选择开卡油站"
            v-if="getCurrentStation.merchant_type == 2 && update" @change="changeStationValue">
            <el-option v-for="(item, index) in stationOptions" :key="index" :label="item.stname" :value="item.stid">
            </el-option>
          </el-select>
          <el-date-picker v-show="typeValue == 1" style="margin-right:15px" v-model="dateValue"
            value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']" type="datetimerange"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="clearData"
            :picker-options="{ disabledDate(time) { return time.getTime() > $moment().endOf('day').valueOf(); } }">
          </el-date-picker>
          <banci-date-time ref="banciRef" :stationValue="stationId" :dateValue="dateBanciValue"
            @searchDate="searchBanciDate" @changeDate="changeBnaciDate" v-show="typeValue == 4">
          </banci-date-time>
            <span class="txt" v-if="getCurrentStation.merchant_type == 2">充值油站</span>
            <el-select v-model="cz_station" multiple clearable collapse-tags style="width:220px;margin-right:20px;"
              placeholder="请选择充值油站" v-if="getCurrentStation.merchant_type == 2 && update" @change="changeCZStationValue">
              <el-option v-for="(item, index) in stationOptions" :key="index" :label="item.stname" :value="item.stid">
              </el-option>
            </el-select>
          <el-button v-show="typeValue == 4" type="primary" @click="page = 1; tableData = []; changeDate();
          " :disabled="!dateBanciValue">生成</el-button>

          <span class="txt" v-show="typeValue == 1">车队名称</span>
          <el-select v-show="typeValue == 1" v-model="company_id" filterable style="width:220px;margin-right:20px;"
            multiple clearable collapse-tags placeholder="请选择车队">
            <el-option v-for="(item, index) in companyOptions" :key="index" :label="item.CompanyName" :value="item.ID">
            </el-option>
          </el-select>
          <div style="margin-top:10px" v-show="typeValue == 1">
            <span class="txt">查询类型</span>
            <el-radio-group v-model="searchTypeVlaue">
              <el-radio label="1">手机号</el-radio>
              <el-radio label="0">卡号</el-radio>
              <el-radio label="2">卡面卡号</el-radio>
            </el-radio-group>
            <el-input v-model="inputTxt" style="width:210px;" :placeholder="searchTypeVlaue == '1' ? '请输入手机号': searchTypeVlaue == '2'? '请输入卡号': '请输入卡面卡号'" clearable>
            </el-input>
            <el-checkbox v-model="is_charge" style="margin:0 10px;">仅查首充卡</el-checkbox>
            <el-button type="primary" @click="page = 1; tableData = []; changeDate();" :disabled="!dateValue">生成
            </el-button>
          </div>
        </div>
      </div>
      <div div class="search-box">
        <el-button type="primary" :disabled="tableData.length == 0" @click="printContent">打印</el-button>
        <el-button type="primary" :disabled="tableData.length == 0" @click="cardChargeDownload" v-show="isTotalReportForm">下载数据</el-button>
      </div>

      <div id="myTable">
        <div class="tableData reportData">
          <div class="report_title">储值卡新开卡用户统计表</div>
          <!-- <el-table :data="sumData" border v-loading="loading" size="small" style="width:560px">
            <el-table-column align="center" prop="SUMCountMoney" label="充值总金额" width="140"
              :formatter="formatterCellval">
            </el-table-column>
            <el-table-column align="center" prop="SUMKZMoney" label="充值卡账总金额" width="140" :formatter="formatterCellval">
            </el-table-column>
            <el-table-column align="center" prop="SUMSKJMoney" label="充值赠金总金额" width="140"
              :formatter="formatterCellval">
            </el-table-column>
            <el-table-column align="center" prop="TotalQty" label="开卡总数" :formatter="formatterCellval">
            </el-table-column>
          </el-table> -->

          <div class="report_header">
            <div v-if="isGroup">集团名称：{{ getCurrentStation.label }}</div>
            <div v-else>油站名称：{{ getCurrentStation.label }}</div>
            <!-- <div v-if="typeValue == 4">日期：{{dateBanciValue}}</div> -->
            <div>开始日期：{{ dateValue ? dateValue[0] : "" }}</div>
            <div>结束日期：{{ dateValue ? dateValue[1] : "" }}</div>
            <div>单位：元</div>
          </div>
          <div class="report_sum">
            <div class="rightBoder">开卡总数 : <span>{{sumData.KHSUM}} 张</span></div>
            <div class="rightBoder">首充总数 : <span>{{sumData.CZSUM}} 张</span></div>
            <div class="rightBoder">首充总金额: <span>{{sumData.SUMCountMoney.toFixed(2)}} 元</span></div>
            <div class="rightBoder">首充本金 : <span>{{sumData.SUMKZMoney.toFixed(2)}} 元</span></div>
            <div >首充赠金 : <span>{{sumData.SUMSKJMoney.toFixed(2)}} 元</span></div>
          </div>
          <el-table :data="tableData" border v-loading="loading" size="small" align="center" ref="table">
            <el-table-column align="center" prop="KHDate" label="开卡时间" min-width="140" :formatter="formatterCellval">
            </el-table-column>
            <el-table-column align="center" prop="KHStationName" label="开卡油站" width="160" :formatter="formatterCellval">
            </el-table-column>
            <el-table-column align="center" prop="CardNo" label="卡号" width="160" :formatter="formatterCellval">
            </el-table-column>
            <el-table-column align="center" prop="CardNumber" label="卡面卡号" width="120" :formatter="formatterCellval">
            </el-table-column>
            <el-table-column align="center" prop="Phone" label="手机号" width="100" :formatter="formatterCellval">
            </el-table-column>
            <el-table-column align="center" prop="CZDate" label="首充时间" min-width="140" :formatter="formatterCellval">
            </el-table-column>
            <el-table-column align="center" prop="CZStationName" label="首充油站" width="160" :formatter="formatterCellval">
            </el-table-column>
            <el-table-column align="center" prop="CountMoney" label="首充金额" width="100" :formatter="formatterMoneyCellval">
            </el-table-column>
            <el-table-column align="center" prop="KZMoney" label="首充本金" width="100" :formatter="formatterMoneyCellval">
            </el-table-column>
            <el-table-column align="center" prop="SKJMoney" label="首充赠金" width="100" :formatter="formatterMoneyCellval">
            </el-table-column>
            <el-table-column align="center" prop="CarNumber" label="车牌号" min-width="100" :formatter="formatterCellval">
            </el-table-column>
            <el-table-column align="center" prop="CardName" label="卡名称" width="160" :formatter="formatterCellval">
            </el-table-column>
            <el-table-column align="center" prop="CompanyName" label="车队名称" width="160" :formatter="formatterCellval">
            </el-table-column>
            <el-table-column align="center" prop="CustomerGroup" label="所在卡组" width="160"
              :formatter="formatterCardCellval">
            </el-table-column>
            <template v-if="showPayNameConfig">
              <el-table-column align="center" prop="bh" label="员工码编号" width="120" :formatter="formatterCellval">
              </el-table-column>
              <el-table-column align="center" prop="pay_name" label="支付方式" width="120" :formatter="formatterCellval">
              </el-table-column>
            </template>
          </el-table>
          <!-- 页码 -->
          <div class="page_content">
            <el-pagination class="page_left" @current-change="handleCurrentChange" :current-page="page"
              :page-size="pageSize" layout="prev, pager, next" :total="TotalQty">
            </el-pagination>
            <el-pagination class="page_right" @size-change="handleSizeChange" :page-sizes="[20, 30, 40, 50]"
              :page-size="pageSize" layout="total, sizes" :total="TotalQty">
            </el-pagination>
          </div>
          <div class="des_bottom">
            <div>制表人：{{orderMaker}}</div>
            <div>制表时间：{{orderMakingTime}}</div>
            <div>签字：</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 下载中心提示 -->
    <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>
  </div>
</template>
<script>
import DownloadTips from "../DownloadTips.vue";
import BanciDateTime from "../Banci/banciDateTime.vue";
import { mapGetters } from "vuex";
// 导入 printjs 库
import printJS from 'print-js';
import { isEmpty } from "lodash";
export default {
  name: "newUserReport",
  components: {
    DownloadTips,
    BanciDateTime
  },
  data() {
    return {
      isTotalReportForm: true,
      typeOptions: [
        {
          value: 1,
          label: "按自然日期"
        },
        {
          value: 4,
          label: "按班结日期"
        }
      ],
      typeValue: 1,
      dateValue: [],
      dateBanciValue: "",
      stationId: [], //选中油站
      cz_station: [], //选中充值油站
      stationName: "", //所选油站名称
      stationOptions: [], //油站列表
      companyOptions: [
        {
          ID: "0",
          CompanyName: "全部"
        }
      ],
      company_id: [], //选中车队
      goodsOptions: [
        {
          oil_name: "全部",
          oil_id: "0"
        }
      ],
      goodsValue: "0",
      cardTypeOptions: [
        {
          value: "0",
          label: "全部"
        },
        {
          value: "1",
          label: "个人卡"
        },
        {
          value: "2",
          label: "车队卡"
        },
        {
          value: "3",
          label: "不记名卡"
        }
      ],
      cardTypeValue: "0",
      cardOptions: [
        {
          name: "全部",
          ID: "0"
        }
      ],
      cardValue: "0",
      searchTypeVlaue: "1",
      inputTxt: "",
      is_charge:false,
      tableData: [],
      sumData:
        {
          CZSUM: 0, //充值总数
          SUMCountMoney: 0, //充值总金额
          SUMKZMoney: 0, //充值卡账总金额
          SUMSKJMoney: 0, //充值赠金总金额
          KHSUM:0 //开卡总数
        },
      loading: false,
      page: 1,
      pageSize: 20,
      start_time: "",
      end_time: "",
      orderMaker: "",
      orderMakingTime: "",
      isGroup: true, //是否是集团账号
      showDownloadTips: false,

      TotalQty: 0, //开卡总数

      checkList: [], //选择班次
      classList: [], //班次列表
      update: true
    };
  },
  computed: {
    ...mapGetters({
      getCurrentStation: "getCurrentStation",
      showPayNameConfig: "showPayNameConfig"
    })
  },
  watch: {
    getCurrentStation(newValue, oldValue) {
      if (newValue.merchant_type != 0 && newValue.value != oldValue.value) {
        if (this.getCurrentStation && this.getCurrentStation.merchant_type == 2) {
          this.isGroup = true;
        } else {
          this.isGroup = false;
          this.cz_station = []
        }
        this.getStationList();
        this.getCompanyList();
        this.changeDate();
      }
    },
    //监听班次切换变化，让多选单选重新渲染，防止报错
    'typeValue'() {
      this.update = false
      setTimeout(() => {
        this.update = true
      }, 0);
    }
  },

  mounted() {
    let userInfo = localStorage.getItem("__userInfo__") || "";
    this.isTotalReportForm = JSON.parse(userInfo).group_id != 426;
    //自然日默认为前一天的数据
    let currentDateStart = this.$moment(new Date())
      .subtract(1, "days")
      .format("YYYY-MM-DD HH:mm:ss");
    let currentDateEnd = this.$moment().format("YYYY-MM-DD HH:mm:ss");
    this.dateValue = [currentDateStart, currentDateEnd];
    //班结默认为前一天的数据
    let _today = this.$moment();
    let yesterday = _today.subtract(1, "days").format("YYYY-MM-DD");
    this.dateBanciValue = yesterday;

    if (
      this.getCurrentStation.merchant_type == undefined ||
      this.getCurrentStation.merchant_type == 0
    ) {
      return false;
    }
    if (this.getCurrentStation && this.getCurrentStation.merchant_type == 2) {
      this.isGroup = true;
    } else {
      this.isGroup = false;
    }
    this.getStationList();
    this.getCompanyList()
    this.changeDate();
  },

  methods: {
    formatterCardCellval(row, column, cellValue, index) {
      // console.log('卡组', cellValue);
      if (cellValue === undefined || cellValue === "" || cellValue === null || !cellValue.length) {
        return "--";
      } else {
        return cellValue.map(item => item.GroupName).join('/')
      }
    },
    formatterMoneyCellval(row, column, cellValue, index){
      // console.log('金额', cellValue);
      if (cellValue === undefined || cellValue === "" || cellValue === null || !cellValue.length) {
        return "--";
      } else {
        return Number(cellValue).toFixed(2)
      }
    },
    //切换班结日期
    changeTypeValue(e) {
      console.log("切换选项", e);
      if(e == 4){
        this.dateBanciValue = ''
      }
    },
    //查询班次
    searchBanciDate(value) {
      console.log('dateValue', value);
      this.dateValue = value
      this.changeDate()
    },
    //切换页码
    handleCurrentChange(val) {
      this.page = val;
      this.changeDate();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.changeDate();
    },
    //选择开卡油站
    changeStationValue() { },
    //选择充值油站
    changeCZStationValue() { },
    //
    clearData() {
      this.$nextTick(() => {
        let startTime = this.$moment(this.dateValue[0]);
        let endTime = this.$moment(this.dateValue[1]);
        let diffMonths = endTime.diff(startTime, 'months', true);
        if (diffMonths > 12) {
          this.$message.error('选择的时间范围不能超过1年');
          this.dateValue = [this.$moment().subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss'), this.$moment().format('YYYY-MM-DD HH:mm:ss')];
          return;
        }
      });
    },
    changeBnaciDate() { },
    //获取表格信息
    changeDate() {
      this.loading = true;
      let params = {
        input_type: this.searchTypeVlaue, //0=按卡号查询、1=按手机号查询、2=按卡面卡号查询，传空跳过查询
        input: this.inputTxt, //手机号或卡号、卡面卡号
        start_time: this.dateValue[0], //开始时间，传空跳过查询
        end_time: this.dateValue[1], //截止时间，传空跳过查询
        kh_station: Array.isArray(this.stationId)  ? this.stationId : [this.stationId], //开户油站,传空跳过查询
        cz_station: this.cz_station, //充值油站，传空跳过查询
        company_id: this.company_id, //车队ID，传空跳过查询
        page: this.page,
        page_size: this.pageSize,
        is_charge: this.is_charge?1:0
      };
      this.$axios.post("/CardReport/getUserCardFirstRecharge", params).then(res => {
          console.log("res", res);
          if (res.data.status == 200) {
            this.tableData = res.data.data.dt
            this.TotalQty = res.data.data.TotalQty
            this.sumData.CZSUM = res.data.data.CZSUM
            this.sumData.SUMCountMoney = res.data.data.SUMCountMoney
            this.sumData.SUMKZMoney = res.data.data.SUMKZMoney
            this.sumData.SUMSKJMoney = res.data.data.SUMSKJMoney
            this.sumData.KHSUM = res.data.data.KHSUM
            let userInfo = localStorage.getItem('__userInfo__');
            if(userInfo && (userInfo !== "" || userInfo !== "undefined")){
              this.orderMaker = JSON.parse(userInfo).name;
            }
            this.orderMakingTime = this.$moment().format("YYYY-MM-DD");
          }else{
            this.tableData = []
            this.TotalQty = 0
            this.orderMakingTime = ''
            this.orderMaker = ''
            this.sumData = {
              TotalQty: 0, //充值总数
              SUMCountMoney: 0, //充值总金额
              SUMKZMoney: 0, //充值卡账总金额
              SUMSKJMoney: 0, //充值赠金总金额
              KHSUM:0 //开卡总数
            }
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //获取可用油站
    getStationList() {
      this.stationId = [];
      let that = this;
      this.$axios.post("/Stations/getStationList", {}).then(res => {
        if (res.status == 200) {
          that.stationOptions = [];
          res.data.data.forEach(item => {
            console.log("that.stationId", that.stationId);
            that.stationId.push(item.stid);
          });
          that.stationOptions = res.data.data;
        }
      });
    },
    //获取车队信息列表
    getCompanyList() {
      let that = this;
      that.$axios.post('/CompanyCard/getSimpleCompanyList', {
        page: 1,
        page_size: 1250,
        input: "",
      })
        .then(function (res) {
          if (res.data.status == 200) {
            that.companyOptions = [{
              ID: '0',
              CompanyName: '全部'
            }];
            that.companyOptions = that.companyOptions.concat(res.data.data.dt);
          } else {
            that.$message({
              message: res.data.info,
              type: 'error'
            });
          }
        })
        .catch(function (error) {
        });
    },
    //打印
    printContent() {
      let data = this.tableData;
      console.log("=>  file: newUserReport.vue:367  data:", data)
      if (isEmpty(data)) {
        this.$message.error("当前无数据，无需打印");
        return;
      }
      let html = ``;
      html = '<div><h1>储值卡新开卡用户统计表</h1><p>' + (this.isGroup ? '集团名称' : '油站名称') + '：' + this.getCurrentStation.label + '&emsp;开始日期：' + (this.dateValue.length ? this.dateValue[0] : "") + '&emsp;结束日期：' + (this.dateValue.length ? this.dateValue[1] : "")  +'&emsp;<span>单位：元</span>'+ '</p>' +
        '<div class="report_info">' +
        '<div class="rightBoder"><span>开卡总数 : </span><span>' + this.sumData.KHSUM + ' 张</span></div>' +
        '<div class="rightBoder"><span>首充总数 : </span><span>' + this.sumData.CZSUM + ' 张</span></div>' +
        '<div class="rightBoder"><span>首充总金额: </span><span>' + this.sumData.SUMCountMoney.toFixed(2) + ' 元</span></div>' +
        '<div class="rightBoder"><span>首充本金 : </span><span>' + this.sumData.SUMKZMoney.toFixed(2) + ' 元</span></div>' +
        '<div><span>首充赠金 : </span><span>' + this.sumData.SUMSKJMoney.toFixed(2) + ' 元</span></div>' +
        '</div>' +
        '<table><thead><tr class=""><th colspan="1" rowspan="1" class="el-table_13_column_193  is-center   is-leaf"><div class="cell">开卡时间</div></th><th colspan="1" rowspan="1" class="el-table_13_column_194  is-center   is-leaf"><div class="cell">开卡油站</div></th><th colspan="1" rowspan="1" class="el-table_13_column_195  is-center   is-leaf"><div class="cell">卡号</div></th><th colspan="1" rowspan="1" class="el-table_13_column_196  is-center   is-leaf"><div class="cell">卡面卡号</div></th><th colspan="1" rowspan="1" class="el-table_13_column_197  is-center   is-leaf"><div class="cell">手机号</div></th><th colspan="1" rowspan="1" class="el-table_13_column_198  is-center   is-leaf"><div class="cell">首充时间</div></th><th colspan="1" rowspan="1" class="el-table_13_column_199  is-center   is-leaf"><div class="cell">首充油站</div></th><th colspan="1" rowspan="1" class="el-table_13_column_200  is-center   is-leaf"><div class="cell">首充金额</div></th><th colspan="1" rowspan="1" class="el-table_13_column_201  is-center   is-leaf"><div class="cell">首充本金</div></th><th colspan="1" rowspan="1" class="el-table_13_column_202  is-center   is-leaf"><div class="cell">首充赠金</div></th><th colspan="1" rowspan="1" class="el-table_13_column_203  is-center   is-leaf"><div class="cell">车牌号</div></th><th colspan="1" rowspan="1" class="el-table_13_column_204  is-center   is-leaf"><div class="cell">卡名称</div></th><th colspan="1" rowspan="1" class="el-table_13_column_205  is-center   is-leaf"><div class="cell">车队名称</div></th><th colspan="1" rowspan="1" class="el-table_13_column_206  is-center   is-leaf"><div class="cell">所在卡组</div></th>' + (this.showPayNameConfig ? '<th colspan="1" rowspan="1" class="el-table_13_column_207  is-center   is-leaf"><div class="cell">员工码编号</div></th><th colspan="1" rowspan="1" class="el-table_13_column_208  is-center   is-leaf"><div class="cell">支付方式</div></th>' : '') + '</tr></thead><tbody>' + 
        data.map(info => '<tr><td>' + this.formatterCellval(null, 'KHDate', info.KHDate) + '</td><td>' + this.formatterCellval(null, 'KHStationName', info.KHStationName) + '</td><td>' + this.formatterCellval(null, 'CardNo', info.CardNo) + '</td><td>' + this.formatterCellval(null, 'CardNumber', info.CardNumber) + '</td><td>' + this.formatterCellval(null, 'Phone', info.Phone) + '</td><td>' + this.formatterCellval(null, 'CZDate', info.CZDate) + '</td><td>' + this.formatterCellval(null, 'CZStationName', info.CZStationName) + '</td><td>' + this.formatterMoneyCellval(null, 'CountMoney', info.CountMoney) + '</td><td>' + this.formatterMoneyCellval(null, 'KZMoney', info.KZMoney) + '</td><td>' + this.formatterMoneyCellval(null, 'SKJMoney', info.SKJMoney) + '</td><td>' + this.formatterCellval(null, 'CarNumber', info.CarNumber) + '</td><td>' + this.formatterCellval(null, 'CardName', info.CardName) + '</td><td>' + this.formatterCellval(null, 'CompanyName', info.CompanyName) + '</td><td>' + this.formatterCardCellval(null, 'CustomerGroup', info.CustomerGroup) + '</td>' + (this.showPayNameConfig ? '<td>' + this.formatterCellval(null, 'bh', info.bh) + '</td><td>' + this.formatterCellval(null, 'pay_name', info.pay_name) + '</td>' : '') + '</tr>').join('') + 
        '</tbody></table></div></div>'+
        `<ul class="footer">
          <li>制表人：${ this.orderMaker }</li>
          <li>制表时间：${ this.orderMakingTime }</li>
          <li style="margin-right:20pt">签字：</li>
        </ul>`

      printJS({
        style: `@media print {
      @page {
        size: auto;
        margin: 20pt;
        margin-bottom: 7pt;
        padding: 2pt;
      }

      body {
        margin: 2pt;
        padding: 2pt;
        font-size: 12pt;
        margin-left:-1pt;
      }
      #container{
        width:100vw;
      }
      h1{font-size:16pt;text-align:center; margin:0; padding:0;}
      p{font-size:10pt;}
      table {
        border-collapse: collapse;
        width: 100%;
        box-sizing: border-box;
        font-size: 10pt;
      }
      th,
      td {
        border: 1px solid #999;
        box-sizing: border-box;
        padding: 2pt;
        text-align: center;
      }
      th{font-size:9pt}
      td{font-size:8pt}
      .next{page-break-before: always;}
      .report_info {
        border:1px solid #999;
        display: flex;
        background-color: #f5f7fa;
        padding: 2pt;
        font-family: Arial, sans-serif;
        margin-bottom:5pt;
      }

      .report_info div {
        flex: 1;
        text-align: center;
        font-size: 14px;
        color: #606266;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .report_info .rightBoder {
        border-right: 1px solid #999;
      }

      .report_info span {
        display: block;
        margin: 0 5px;
        font-size: 9pt;
        color: #303133;
      }
      .report_info span:nth-child(2) {
        font-weight: bold;
        color: black;
      }
      .footer{width:100vw; list-style-type: none; padding: 0; display: flex; justify-content: flex-end; align-items: center; font-size: 10pt;}
      .footer li{padding-right: 30pt;}
    }`,
        printable: html,
        type: 'raw-html',
      })
    },
    //下载
    cardChargeDownload() {
      let data = {
        input_type:this.searchTypeVlaue,
        input: this.inputTxt,
        start_time: this.dateValue[0],
        end_time: this.dateValue[1],
        kh_station: this.stationId.length ? this.stationId : [this.stationId],
        cz_station: this.cz_station,
        company_id: this.company_id,
        is_charge: this.is_charge?1:0
      }
      this.$axios.post('/CardReport/getUserCardFirstRechargeDownload',data).then(res=>{
        console.log('res',res);
        if(res.data.status == 200){
          this.showDownloadTips = true;
        }else{
          this.$message.error(res.data.info);
        }
      })
    },
  }
};
</script>
<style scoped>
.report {
  position: relative;
  height: 100%;
  background: rgba(245, 245, 245, 1);
  margin: 0px auto;
}

.report-content {
  background: #fff;
  padding: 20px 0;
}

.tableData {
  text-align: center;
}

.tableData .report_title {
  font-size: 24px;
  font-weight: bolder;
  margin-top: 20px;
}
.tableData .report_sum{
  padding: 4px 0;
  margin-top: 10px;
  margin-bottom: 10px;
  display: flex;
  /* justify-content: space-around; */
  /* justify-content: space-evenl; */
  width: 100%;
  border: 1px solid #ebeef5;
  height:50px ;
  line-height: 50px;
  color: #909399;
}
.tableData .report_sum div{
  flex:1
}
.rightBoder{
  padding-right: 10px;
  border-right: 1px solid #ebeef5;
}
.tableData .report_sum span{
  color: #000;
}
.tableData .report_header {
  display: flex;
  margin: 10px 20px;
}

.tableData .report_header div {
  min-width: 100px;
  text-align: left;
  margin-right: 40px;
}

.page_content {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}

.search-box {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.des_bottom {
        display: flex;
        justify-content: flex-end;
        font-size: 14px;
        font-weight: 500;
        margin-top: 10px;
    }
    .des_bottom div{
        padding-right: 100px;
    }
</style>
