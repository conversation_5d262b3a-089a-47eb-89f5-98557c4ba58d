webpackJsonp([33],{"Re/x":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=a("Dd8w"),o=a.n(n),r=a("FZmr"),i=a("NYxO"),s={name:"GroupReport",components:{DownloadTips:r.a},data:function(){return{isTotalReportForm:!0,typeOptions:[{value:1,label:"按自然日期"},{value:4,label:"按开班日期"}],typeValue:1,dateValue:[],tableData:[],loading:!1,orderMaker:"",orderMakingTime:"",arr:[],showDownloadTips:!1,isGroup:!1}},mounted:function(){var t=localStorage.getItem("__userInfo__")||"";this.isTotalReportForm=426!=JSON.parse(t).group_id;var e=this.$moment().subtract(1,"days").format("YYYY-MM-DD");if(this.dateValue.push(e),this.dateValue.push(e),void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.getCurrentStation&&2==this.getCurrentStation.merchant_type?this.isGroup=!0:this.isGroup=!1,this.changeDate()},computed:o()({},Object(i.c)({getCurrentStation:"getCurrentStation"})),methods:{changeTypeValue:function(){this.dateValue="",this.orderMakingTime="",this.tableData=[]},changeDate:function(){var t=this,e=t.typeValue,a=0,n=0;t.dateValue?(a=t.$moment(t.dateValue[0]+" 00:00:00","YYYY-MM-DD HH:mm:ss").unix(),n=t.$moment(t.dateValue[1]+" 23:59:59","YYYY-MM-DD HH:mm:ss").unix(),t.loading=!0,t.$axios.post("/CardReport/groupSettleReport",{type:e,start_time:a,end_time:n}).then(function(e){if(t.tableData=[],t.loading=!1,200==e.data.status){t.tableData=e.data.data.settle_data,t.start_time=e.data.data.start_time,t.end_time=e.data.data.end_time,t.orderMakingTime=t.$moment().format("YYYY-MM-DD");var a=localStorage.getItem("__userInfo__");!a||""===a&&"undefined"===a||(t.orderMaker=JSON.parse(a).name)}else t.$message({message:e.data.info,type:"error"})}).catch(function(t){})):(t.tableData=[],t.orderMakingTime="",t.orderMaker="")},clearData:function(){this.dateValue||(this.tableData=[],this.orderMakingTime="",this.orderMaker="")},printContent:function(){var t=document.querySelector("#myTable").innerHTML,e=document.body.innerHTML;document.body.innerHTML=t,document.getElementsByClassName("el-table__header")[0].style.width="100%",document.getElementsByClassName("el-table__header")[0].style["table-layout"]="auto",document.getElementsByClassName("el-table__body")[0].style.width="100%",document.getElementsByClassName("el-table__body")[0].style["table-layout"]="auto",window.print(),history.go(0),document.body.innerHTML=e},cardChargeDownload:function(){var t=this;this.$axios.post("/CardReport/downloadGroupSettleLists",{start_time:this.start_time,end_time:this.end_time,type:this.typeValue}).then(function(e){200==e.data.status?t.showDownloadTips=!0:t.$message.error(e.data.info)})},setTable:function(t,e){var a=[],n=0;t.forEach(function(o,r){0===r?a.push(1):String(o[e])&&String(o[e])==String(t[r-1][e])?(a[n]+=1,a.push(0)):(a.push(1),n=r)});var o={};o[e]=a,this.arr=[],this.arr.push(o)},objectSpanMethod:function(t){t.row,t.column;var e=t.rowIndex,a=t.columnIndex;if(4==this.typeValue&&0===a){var n=this.arr[0].organize[e];return{rowspan:n,colspan:n>0?1:0}}}},watch:{getCurrentStation:function(t,e){0!=t.merchant_type&&t.value!=e.value&&this.changeDate()}}},l={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"left_select"},[a("div",{staticClass:"report-content"},[a("div",{staticClass:"content_header"},[a("div",{staticClass:"left"},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},on:{change:t.clearData},model:{value:t.dateValue,callback:function(e){t.dateValue=e},expression:"dateValue"}}),t._v(" "),a("el-button",{attrs:{type:"primary",disabled:!t.dateValue},on:{click:t.changeDate}},[t._v("生成")])],1),t._v(" "),a("div",{staticClass:"right"},[a("el-button",{attrs:{type:"primary",disabled:!t.dateValue},on:{click:t.printContent}},[t._v("打印")]),t._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:t.isTotalReportForm,expression:"isTotalReportForm"}],attrs:{type:"primary",disabled:!t.dateValue},on:{click:t.cardChargeDownload}},[t._v("下载数据")])],1)]),t._v(" "),a("div",{ref:"print",attrs:{id:"myTable"}},[a("div",{staticClass:"report",staticStyle:{"text-align":"center"}},[a("div",{staticClass:"report_title"},[t._v("储值卡集团清结算报表")]),t._v(" "),a("div",{staticClass:"tips"},[t.isGroup?a("div",[t._v("集团名称："+t._s(t.getCurrentStation.label))]):t._e(),t._v(" "),a("div",[t._v("开始日期："+t._s(t.dateValue?t.dateValue[0]:""))]),t._v(" "),a("div",[t._v("结束日期："+t._s(t.dateValue?t.dateValue[1]:""))]),t._v(" "),a("div",[t._v("单位：元")])]),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.tableData,"span-method":t.objectSpanMethod,size:"small",align:"center"}},[a("el-table-column",{attrs:{align:"center",prop:"stid_name",label:"油站","min-width":"72px"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"date",label:"集团会员油站交易"}},[a("el-table-column",{attrs:{align:"center",prop:"bz_pay",label:"充值到账","min-width":"72px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.bz_pay).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"bz_discount",label:"充值优惠","min-width":"72px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.bz_discount).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"bz_gift",label:"充值赠金","min-width":"72px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.bz_gift).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"consume_bj",label:"消费本金","min-width":"72px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.consume_bj).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"consume_skj",label:"消费赠金","min-width":"72px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.consume_skj).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"consume_mz",label:"消费母账","min-width":"72px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.consume_mz).toFixed(2)))]}}])})],1),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"yingshou_amt",label:"应收金额","min-width":"72px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.rec_pay).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"yingfu_amt",label:"应付金额","min-width":"72px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.act_pay).toFixed(2)))]}}])})],1),t._v(" "),t._m(0),t._v(" "),a("div",{staticClass:"des_bottom"},[a("div",[t._v("制表人："+t._s(t.orderMaker))]),t._v(" "),a("div",[t._v("制表时间："+t._s(t.orderMakingTime))]),t._v(" "),a("div",[t._v("签字：")])])],1)])]),t._v(" "),a("download-tips",{attrs:{showDownloadTips:t.showDownloadTips},on:{"update:showDownloadTips":function(e){t.showDownloadTips=e},"update:show-download-tips":function(e){t.showDownloadTips=e}}})],1)},staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"table_des"},[e("div",{staticClass:"table_des_text"},[e("p",[this._v("注：")]),this._v(" "),e("div",[e("p",[this._v("1.应收金额=集团所有充值到账金额，包含现金等自定义支付方式。")]),this._v(" "),e("p",[this._v("2.应付金额=油站消费本金+消费母账。")]),this._v(" "),e("p",[this._v("3.合计=应收金额-应付金额。")])])])])}]};var d=a("VU/8")(s,l,!1,function(t){a("wy4a")},"data-v-557bec18",null);e.default=d.exports},wy4a:function(t,e){}});