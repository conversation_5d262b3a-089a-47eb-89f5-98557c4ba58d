webpackJsonp([34],{LEK8:function(t,e){},YnmS:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a=n("Gu7T"),r=n.n(a),i=n("d7EF"),o=n.n(i),l=n("Xxa5"),s=n.n(l),d=n("exGp"),p=n.n(d),c=n("Dd8w"),u=n.n(c),m=n("3pLw"),g=n("mw3O");var b=n("NYxO"),y=n("M4fF"),f=n("PJh5"),h=n.n(f),_=n("FZmr"),x=n("38N9"),v=n.n(x),w={name:"InventorySummary",components:{DownloadTips:_.a},data:function(){return{tableMinWidth:1205,printConfig:{queryTime:"",maker:""},query:{page:"1",page_size:"10",type:"1",company_id:[],stid:[],dateRange:[h()().startOf("day").subtract(1,"day").toDate(),h()().endOf("day").subtract(1,"day").toDate()]},stationOptions:[],stationLoading:!1,companyList:[],tableData:[],loadingCompanies:!1,loadingData:!1,pickerOptions:{disabledDate:function(t){var e=h()().startOf("day").add(1,"day");return t.getTime()>=e.startOf("day").valueOf()}},downloadConfig:{loading:!1,success:!1}}},computed:u()({},Object(b.c)({getCurrentStation:"getCurrentStation"})),watch:{getCurrentStation:{handler:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return p()(s.a.mark(function a(){return s.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(console.log("=>(InventorySummary.vue:154) old",e,n),!(e.merchant_id===n.merchant_id&&"merchant_id"in n)){a.next=3;break}return a.abrupt("return");case 3:if(!t.getCurrentStation||"1"!==String(t.getCurrentStation.merchant_type)){a.next=7;break}t.query.stid=[t.getCurrentStation.merchant_id],a.next=9;break;case 7:return a.next=9,t.getStationList();case 9:return a.next=11,t.fetchCompanies();case 11:t.fetchData();case 12:case"end":return a.stop()}},a,t)}))()},immediate:!0,deep:!0}},methods:{showPrint:function(){var t='<div>\n        <h1 style="margin:0; padding:0; font-size: 1.5rem; font-weight: bold; text-align: center;margin-top:-1mm;">定升车队进销存汇总表</h1>\n        <ul style="list-style-type: none; display: flex; gap: 40px; align-items: center; font-size: 12px; padding:1mm 0;">\n          <li><span>'+("2"===String(this.getCurrentStation.merchant_type)?"集团名称":"油站名称")+"</span>："+this.getCurrentStation.label+"</li>\n          <li>开始时间："+this.$moment(this.query.dateRange[0]).format("YYYY-MM-DD")+"</li>\n          <li>结束时间："+this.$moment(this.query.dateRange[1]).format("YYYY-MM-DD")+'</li>\n        </ul>\n        <table style="width:100%; border-collapse: collapse;">\n          <thead>\n            <tr>\n              <th style="border: 1px solid #999; padding: 5px;">车队名称</th>\n              <th style="border: 1px solid #999; padding: 5px;">期初升数</th>\n              <th style="border: 1px solid #999; padding: 5px;">期初金额</th>\n              <th style="border: 1px solid #999; padding: 5px;">充值升数</th>\n              <th style="border: 1px solid #999; padding: 5px;">充值金额</th>\n              <th style="border: 1px solid #999; padding: 5px;">充值退款升数</th>\n              <th style="border: 1px solid #999; padding: 5px;">充值退款金额</th>\n              <th style="border: 1px solid #999; padding: 5px;">消费升数</th>\n              <th style="border: 1px solid #999; padding: 5px;">消费金额</th>\n              <th style="border: 1px solid #999; padding: 5px;">消费退款升数</th>\n              <th style="border: 1px solid #999; padding: 5px;">消费退款金额</th>\n              <th style="border: 1px solid #999; padding: 5px;">清零升数</th>\n              <th style="border: 1px solid #999; padding: 5px;">清零金额</th>\n              <th style="border: 1px solid #999; padding: 5px;">期末升数</th>\n              <th style="border: 1px solid #999; padding: 5px;">期末金额</th>\n            </tr>\n          </thead>\n          <tbody>\n            '+this.tableData.map(function(t){return'\n              <tr>\n                <td style="border: 1px solid #999; padding: 5px;">'+t.company_name+'</td>\n                <td style="border: 1px solid #999; padding: 5px;">'+t.qichu_litre+'</td>\n                <td style="border: 1px solid #999; padding: 5px;">'+t.qichu_amt+'</td>\n                <td style="border: 1px solid #999; padding: 5px;">'+t.recharge_litre+'</td>\n                <td style="border: 1px solid #999; padding: 5px;">'+t.recharge_amt+'</td>\n                <td style="border: 1px solid #999; padding: 5px;">'+t.recharge_refund_litre+'</td>\n                <td style="border: 1px solid #999; padding: 5px;">'+t.recharge_refund_amt+'</td>\n                <td style="border: 1px solid #999; padding: 5px;">'+t.consume_litre+'</td>\n                <td style="border: 1px solid #999; padding: 5px;">'+t.consume_amt+'</td>\n                <td style="border: 1px solid #999; padding: 5px;">'+t.consume_refund_litre+'</td>\n                <td style="border: 1px solid #999; padding: 5px;">'+t.consume_refund_amt+'</td>\n                <td style="border: 1px solid #999; padding: 5px;">'+t.clear_litre+'</td>\n                <td style="border: 1px solid #999; padding: 5px;">'+t.clear_amt+'</td>\n                <td style="border: 1px solid #999; padding: 5px;">'+t.qimo_litre+'</td>\n                <td style="border: 1px solid #999; padding: 5px;">'+t.qimo_amt+"</td>\n              </tr>\n            "}).join("")+'\n          </tbody>\n        </table>\n        <ul class="footer" style="list-style-type: none; padding: 0; display: flex; justify-content: space-between; align-items: center; font-size: 10pt;">\n          <li>制表人：'+this.printConfig.maker+"</li>\n          <li>制表时间："+this.printConfig.queryTime+'</li>\n          <li style="margin-right:100px">签字：</li>\n        </ul>\n      </div>';v()({printable:t,type:"raw-html",style:"\n          @media print { \n            @page {\n              size: auto; \n              margin:20pt; \n              margin-bottom:5pt; \n              padding:2pt;\n            } \n            body {\n              margin:2pt; \n              margin-bottom:14pt; \n              padding:2pt;\n            } \n            ul, li {\n              padding:0;\n              margin:0;\n            } \n            table {\n              width: 100%;\n              border-collapse: collapse;\n              font-size: 9pt;\n            }\n            th, td {\n              border: 1px solid #999;\n              padding: 5px;\n              text-align: center;\n            }\n            th {\n              font-weight: bold;\n            }\n            .footer {\n              margin-top: 10pt;\n            }\n          }\n        "})},download:function(){var t,e=this,n=this.getQueryParams();n&&(this.downloadConfig.loading=!0,(t=n,Object(m.c)("/CardReport/cardCompanyInvoicingLitreDownload",Object(g.stringify)(t),{"Content-Type":"application/x-www-form-urlencoded"})).then(function(t){200===t.status?e.downloadConfig.success=!0:e.$message.error(t.info||"下载失败")}).finally(function(){e.downloadConfig.loading=!1}))},getQueryParams:function(){var t=o()(this.query.dateRange,2),e=t[0],n=t[1];return Math.abs(new Date(n)-new Date(e))/864e5>180?(this.$message.error("选择日期超过180天，请重新选择"),null):{start_time:e?Math.floor(e.getTime()/1e3):0,end_time:n?Math.floor(n.getTime()/1e3):0,type:1,company_id:this.query.company_id||[],stid:this.query.stid||[]}},getStationList:function(){var t=this;return p()(s.a.mark(function e(){return s.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t.stationLoading=!0,t.query.stid=[],e.next=4,t.$axios.post("/Stations/getStationList",{}).then(function(e){200===e.status?(t.stationOptions=e.data.data,t.query.stid=e.data.data.map(function(t){return t.stid})):t.$message.error(e.info||e.msg||"获取油站信息失败")}).finally(function(){return t.stationLoading=!1});case 4:case"end":return e.stop()}},e,t)}))()},fetchCompanies:function(){var t=this;return p()(s.a.mark(function e(){var n;return s.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t.loadingCompanies=!0,e.prev=1,e.next=4,a={page:1,page_size:1250,type:3},Object(m.c)("/CompanyCard/getSimpleCompanyList",a);case 4:if(n=e.sent,console.log("=>(InventorySummary.vue:92) response",n),200!==n.status&&t.$message.error(n.info||n.msg||"获取车队信息失败"),!Object(y.isEmpty)(n.data)){e.next=9;break}return e.abrupt("return");case 9:t.companyList=n.data.dt;case 10:return e.prev=10,t.loadingCompanies=!1,e.finish(10);case 13:case"end":return e.stop()}var a},e,t,[[1,,10,13]])}))()},fetchData:function(){var t=this;return p()(s.a.mark(function e(){var n,a,i,o;return s.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.getQueryParams()){e.next=3;break}return e.abrupt("return");case 3:return e.prev=3,t.loadingData=!0,e.next=7,l=n,Object(m.c)("/CardReport/getCardCompanyInvoicingLitre",Object(g.stringify)(l),{"Content-Type":"application/x-www-form-urlencoded"});case 7:200===(a=e.sent).status?(t.tableData=[],a.data.card_invoicing&&a.data.card_invoicing.length>0&&(i=u()({},a.data.total,{company_name:"汇总"}),t.tableData=[].concat(r()(a.data.card_invoicing),[i]),!(o=localStorage.getItem("__userInfo__"))||""===o&&"undefined"===o||(t.printConfig.maker=JSON.parse(o).name,t.printConfig.queryTime=t.$moment().format("YYYY-MM-DD HH:mm:ss")))):t.$message.error(a.info||a.msg||"获取数据失败");case 9:return e.prev=9,t.loadingData=!1,e.finish(9);case 12:case"end":return e.stop()}var l},e,t,[[3,,9,12]])}))()}}},C={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"p-4"},[n("download-tips",{attrs:{showDownloadTips:t.downloadConfig.success},on:{"update:showDownloadTips":function(e){return t.$set(t.downloadConfig,"success",e)},"update:show-download-tips":function(e){return t.$set(t.downloadConfig,"success",e)}}}),t._v(" "),n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loadingData,expression:"loadingData"}]},[n("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.downloadConfig.loading||t.printConfig.loading,expression:"downloadConfig.loading || printConfig.loading "}],staticClass:"w-full mb-4",attrs:{inline:"",model:t.query,"label-width":"70px"}},["2"===String(t.getCurrentStation.merchant_type)?n("el-form-item",{attrs:{label:"开户油站"}},[n("el-select",{staticClass:"w-250px mr-20px mb-5px",attrs:{multiple:!0,filterable:"",clearable:"","collapse-tags":"",placeholder:"请选择油站"},model:{value:t.query.stid,callback:function(e){t.$set(t.query,"stid",e)},expression:"query.stid"}},t._l(t.stationOptions,function(t,e){return n("el-option",{key:e,attrs:{label:t.stname,value:t.stid}})}),1)],1):t._e(),t._v(" "),n("el-form-item",{attrs:{label:"车队名称"}},[n("el-select",{attrs:{"collapse-tags":!0,multiple:"",clearable:"",filterable:"",placeholder:"请选择车队"},model:{value:t.query.company_id,callback:function(e){t.$set(t.query,"company_id",e)},expression:"query.company_id"}},t._l(t.companyList,function(t){return n("el-option",{key:t.ID,attrs:{label:t.CompanyName,value:t.ID}})}),1)],1),t._v(" "),n("el-form-item",{attrs:{label:"时间范围"}},[n("el-date-picker",{attrs:{clearable:!1,type:"daterange","picker-options":t.pickerOptions,format:"yyyy-MM-dd","default-time":["00:00:00","23:59:59"],"start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.query.dateRange,callback:function(e){t.$set(t.query,"dateRange",e)},expression:"query.dateRange"}})],1),t._v(" "),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:t.fetchData}},[t._v("查询")]),t._v(" "),n("el-button",{attrs:{type:"primary",disabled:!(t.tableData&&t.tableData.length>0)},on:{click:t.showPrint}},[t._v("打印")]),t._v(" "),n("el-button",{attrs:{type:"primary",disabled:!(t.tableData&&t.tableData.length>0)},on:{click:t.download}},[t._v("下载数据\n        ")])],1)],1),t._v(" "),n("div",{staticClass:"p-0 m-0",attrs:{id:"dataTable",text:"#303133"}},[n("h1",{staticClass:"text-2xl font-bold mb-4 text-center"},[t._v("定升车队进销存汇总表")]),t._v(" "),n("ul",{staticClass:"list-none p-0 flex space-x-40px items-center text-14px"},[n("li",["2"===String(t.getCurrentStation.merchant_type)?n("span",[t._v("集团名称")]):n("span",[t._v("油站名称")]),t._v("："+t._s(t.getCurrentStation.label)+"\n        ")]),t._v(" "),n("li",[t._v("开始时间："+t._s(t.$moment(t.query.dateRange[0]).format("YYYY-MM-DD")))]),t._v(" "),n("li",[t._v("结束时间："+t._s(t.$moment(t.query.dateRange[1]).format("YYYY-MM-DD")))])]),t._v(" "),n("el-table",{staticClass:"w-full !border-#999999 !after:bg-#999999 !before:bg-#999999",style:"min-width: "+t.tableMinWidth+"px;",attrs:{border:"",data:t.tableData,"header-cell-class-name":"text-#303133 !border-#999999","cell-class-name":"text-#303133 !border-#999999"}},[n("el-table-column",{attrs:{prop:"company_name",label:"车队名称"}}),t._v(" "),n("el-table-column",{attrs:{prop:"qichu_litre",label:"期初升数"}}),t._v(" "),n("el-table-column",{attrs:{prop:"qichu_amt",label:"期初金额"}}),t._v(" "),n("el-table-column",{attrs:{prop:"recharge_litre",label:"充值升数"}}),t._v(" "),n("el-table-column",{attrs:{prop:"recharge_amt",label:"充值金额"}}),t._v(" "),n("el-table-column",{attrs:{prop:"recharge_refund_litre",label:"充值退款升数"}}),t._v(" "),n("el-table-column",{attrs:{prop:"recharge_refund_amt",label:"充值退款金额"}}),t._v(" "),n("el-table-column",{attrs:{prop:"consume_litre",label:"消费升数"}}),t._v(" "),n("el-table-column",{attrs:{prop:"consume_amt",label:"消费金额"}}),t._v(" "),n("el-table-column",{attrs:{prop:"consume_refund_litre",label:"消费退款升数"}}),t._v(" "),n("el-table-column",{attrs:{prop:"consume_refund_amt",label:"消费退款金额"}}),t._v(" "),n("el-table-column",{attrs:{prop:"clear_litre",label:"清零升数"}}),t._v(" "),n("el-table-column",{attrs:{prop:"clear_amt",label:"清零金额"}}),t._v(" "),n("el-table-column",{attrs:{prop:"qimo_litre",label:"期末升数"}}),t._v(" "),n("el-table-column",{attrs:{prop:"qimo_amt",label:"期末金额"}})],1),t._v(" "),n("ul",{staticClass:"list-none p-0 flex justify-between items-center text-14px"},[n("li",[t._v("制表人："+t._s(t.printConfig.maker))]),t._v(" "),n("li",[t._v("制表时间："+t._s(t.printConfig.queryTime))]),t._v(" "),n("li",{staticClass:"mr-100px"},[t._v("签字：")])])],1)],1)],1)},staticRenderFns:[]};var D=n("VU/8")(w,C,!1,function(t){n("LEK8")},"data-v-5461db3e",null);e.default=D.exports}});