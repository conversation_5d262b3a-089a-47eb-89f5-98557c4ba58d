<template>
  <div class="pt-2">
    <invoice-query :currentCompanies.sync="currentCompanies" @update:type="query.dateValue = []"
      :company_id.sync="query.company_id" :stid.sync="query.stid" :type.sync="query.type"
      :customer_type.sync="query.customer_type" :invoice_type.sync="query.invoice_type"
      :disabledDate="(time) => time.getTime() > new Date(new Date().setHours(0, 0, 0, 0) + 24*60*60*1000 - 1)"
      @update:date_value="handleDateChange"
      :date_value.sync="query.dateValue">
      <template #btns>
        <div class="flex items-center space-x-2">
          <el-button type="primary" @click="search()" :disabled="disableAll">生成</el-button>
          <el-button type="primary" @click="checkFlag = 1, sureBranch()" :disabled="disableAll">批量打印</el-button>
          <div v-loading="downloadLoading"><el-button type="primary" @click="exportData" :disabled="disableAll">批量下载</el-button></div>
        </div>
      </template>
    </invoice-query>
    <div id="myTable">
      <div class="text-center">
        <div v-loading="disableAll" element-loading-text="正在处理数据，请稍等">
          <template>
            <div class="text-24px font-bold mt-20px py-5px">客户消费变动明细表</div>
            <div class="flex py-10px px-20px">
              <div v-if="isGroup">集团名称：{{ getCurrentStation.label }}</div>
              <div v-else>油站名称：{{ getCurrentStation.label }}</div>
              <div class="mx-40px">开始时间：{{ query.dateValue.length ? query.dateValue[0] : "" }}</div>
              <div>结束时间：{{ query.dateValue.length ? query.dateValue[1] : "" }}</div>
            </div>
          </template>
          <template v-if="tableList.length === 0">
            <el-table ref="table" :data="tableList" border size="small">
              <template v-for="(item, index) in tableColumns">
                <el-table-column align="center" :label="item.name" :prop="item.field"></el-table-column>
              </template>
            </el-table>
          </template>
          <template v-else>
            <div v-for="(list, i) in tableList" :key="i" :hidden="disableAll">
              <div
                class="text-left text-14px mt-20px px-20px h-40px leading-40px border-x-1px border-t-1px border-b-0 border-solid border-#ebeef5 flex items-center justify-between">
                <span>{{ list.companyName }}</span>
                <span>车队ID:{{ list.fleetId }}</span>
                <span>客户类型：{{ getCompanyCustomerType(list.fleetId) }}</span>
              </div>
              <el-table ref="table" :data="list.tableData" border size="small">
                <template v-for="item in tableColumns">
                  <el-table-column align="center" :label="item.name" :prop="item.field"></el-table-column>
                </template>
              </el-table>
              <div v-if="i < tableList.length - 1" style="page-break-after:always"></div>
              <div class="page-break"></div>
            </div>
          </template>
        </div>
      </div>
    </div>

     <el-dialog :title="checkFlag == 1 ? '批量打印' : '批量下载'" :visible.sync="showAllChecks" :close-on-click-modal="false"
      @open="closeAll" width="800px">
      <div>
        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange" class="mb-4px">
          全选
        </el-checkbox>
        <el-checkbox-group v-model="checkedValue" @change="checkOne">
          <el-checkbox v-for="v in companyOptions" :label="v.ID" :disabled="v.disabled" :key="v.ID" class="mb-4px">
            {{ v.CompanyName }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showAllChecks = false">取 消</el-button>
        <el-button type="primary" @click="sureBranch()">确 认</el-button>
      </span>
    </el-dialog>

    <!-- 下载中心提示 -->
    <download-tips :showDownloadTips.sync="showDownloadTips" :autoClose="true"></download-tips>
  </div>
</template>

<script>
import DownloadTips from "../DownloadTips.vue";
import { mapGetters } from "vuex";
import axiosReport from "axios";
import * as XLSX from 'xlsx';
import tableQueryCondition from "../../mixins/tableQueryCondition";
import InvoiceQuery from "../InvoiceQuery/InvoiceQuery.vue";
import { cloneDeep, isEmpty } from "lodash";
import BigNumber from "bignumber.js";
import printJS from "print-js";
import { batchDownLoadCustomerOrder } from "../../api/CustomerSpendReport";

export default {
  name: "CustomerSpendReport",
  components: {
    InvoiceQuery,
    DownloadTips,
  },
  mixins: [tableQueryCondition],
  data() {
    return {
      copySon: [],
      typeValue: 1,
      disableAll: false,
      companyOptions: [],
      companyName: "",
      tableList: [],
      cardResult: [],
      tableColumns: [],
      isGroup: true,
      loading: false,
      showAllChecks: false,
      checkedValue: [],
      isIndeterminate: false,
      checkAll: false,
      checkFlag: 1,
      is_stid: 0,
      single_stid: 0,
      stids: [],
      searchFlag: 1,
      downForm: {
        index: 0,
        num: 10,
      },
      currentCompanies: [],
      orderMaker: "",
      orderMakingTime: "",
      showDownloadTips: false,
      downloadLoading: false,
    };
  },
  mounted() {
    let userInfo = localStorage.getItem("__userInfo__") || "";
    this.isTotalReportForm = JSON.parse(userInfo).group_id != 426;
    if (
      this.getCurrentStation.merchant_type == undefined ||
      this.getCurrentStation.merchant_type == 0
    ) {
      return false;
    }
    if (this.getCurrentStation && this.getCurrentStation.merchant_type == 2) {
      this.isGroup = true;
    } else {
      this.isGroup = false;
    }
    this.setTime();
    this.getStations();
    this.getCompanyList();
    this.getCardReportColumn()
  },
  computed: {
    ...mapGetters({
      getCurrentStation: "getCurrentStation",
      customerTypeObject: 'customerTypeObject',
    })
  },
  methods: {
    handleDateChange(val) {
      if (val && val.length === 2) {
        const diff = this.$moment(val[1]).diff(this.$moment(val[0]), 'months',true)
        console.log("🚀 ~ file: CustomerSpendReport.vue:160 ~ handleDateChange ~ diff:", diff)
        if (diff > 3) {
          this.$message.warning('时间范围不能超过3个月')
          this.$nextTick(() => {
            this.query.dateValue = [this.$moment().subtract(1, 'months').format('YYYY-MM-DD 00:00:00'), this.$moment().format('YYYY-MM-DD 23:59:59')]
          })
        }
      }
    },
    exportData(){
      if(!this.query.company_id.length) {
        this.$message.error('请至少选择一个车队');
        return;
      }
      this.downloadLoading = true;
      batchDownLoadCustomerOrder({
        is_stid: this.is_stid,
        single_stid: this.single_stid,
        company_type: this.query.customer_type,
        company_id:this.query.company_id,
        start_time:this.$moment(this.query.dateValue[0]).format('YYYY-MM-DD HH:mm:ss'),
        end_time:this.$moment(this.query.dateValue[1]).format('YYYY-MM-DD HH:mm:ss'),
        company_names:JSON.stringify(this.currentCompanies.filter(item => this.query.company_id.includes(item.ID)).reduce((acc, cur) => {
          acc[cur.ID] = cur.CompanyName;
          return acc;
        }, {}))
      }).then(res=>{
        this.showDownloadTips = true;
      }).finally(()=>{
        this.downloadLoading = false;
      })
    },
    getCompanyCustomerType(fleetId) {
      const company = this.currentCompanies.find(item => item.ID === fleetId);
      if (!company) {
        return '-'
      }
      return this.customerTypeObject[company.CompanyType] || '-'
    },
    // 默认为前一月的数据
    setTime() {
      this.query.dateValue = [];
      let today = this.$moment().format("YYYY-MM-DD");
      let yesterday = this.$moment()
        .subtract(1, "months")
        .format("YYYY-MM-DD");
      this.query.dateValue.push(yesterday + " 00:00:00");
      this.query.dateValue.push(today + " 23:59:59");
    },
    handleCheckAllChange(val) {
      this.checkedValue = val ? this.companyOptions.map(item => item.ID) : [];
      this.isIndeterminate = false;
    },
    checkOne(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.companyOptions.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.companyOptions.length;
    },
    closeAll() {
      this.checkAll = false;
      this.isIndeterminate = false;
      this.checkedValue = [];
    },
    batchPrint(val) {
      console.log(`批量${val == 1 ? "打印" : "下载"}`, val);
      this.checkFlag = val
      this.showAllChecks = true;
    },
    //获取油站列表
    getStations() {
      let that = this;
      that.$axios.post("/Stations/getStations", {}).then(res => {
        if (res.data.status == 200) {
          if (res.data.data.is_group == 1) {
            that.is_stid = 0;
            that.single_stid = 0;
          } else {
            that.is_stid = 1;
            that.single_stid = res.data.data.station_info[0].stid;
          }
          that.stids = res.data.data.station_info.map(v => v.stid);
          console.log("is_stid", that.is_stid, that.single_stid, that.stids);
        } else {
          that.$message({
            message: res.data.info,
            type: "error"
          });
        }
      });
    },
    //获取车队信息列表
    async getCompanyList() {
      let that = this;
      await axiosReport
        .create()
        .post(
          "/CompanyCard/getSimpleCompanyList",
          {
            page: 1,
            page_size: 1250,
            input: ""
          },
          {
            timeout: 30000
          }
        )
        .then(function (res) {
          if (res.data.status == 200) {
            that.companyOptions = res.data.data.dt
          } else {
            that.$message({
              message: res.data.info,
              type: "error"
            });
          }
        })
        .catch(function (error) {
          console.log(error);
          that.$message.error('获取车队信息失败');
        })
    },
    // 获取自定义表头数据
    async getCardReportColumn() {
      this.tableColumns = [
        {
          "cid": 2,
          "type": 10,
          "name": "变动类型",
          "sort": 2,
          "field": "type",
          "disabled": true,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 1,
          "is_checked": true,
          "children": []
        },
        {
          "cid": 4,
          "type": 10,
          "name": "卡面卡号",
          "sort": 4,
          "field": "card_number",
          "disabled": false,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 1,
          "is_checked": false,
          "children": []
        },
        {
          "cid": 5,
          "type": 10,
          "name": "车牌号",
          "sort": 5,
          "field": "car_no",
          "disabled": false,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 1,
          "is_checked": true,
          "children": []
        },
        {
          "cid": 6,
          "type": 10,
          "name": "油品名称",
          "sort": 6,
          "field": "goods_name",
          "disabled": true,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 1,
          "is_checked": true,
          "children": []
        },
        {
          "cid": 7,
          "type": 10,
          "name": "挂牌单价",
          "sort": 7,
          "field": "han_price",
          "disabled": false,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 1,
          "is_checked": false,
          "children": []
        },
        {
          "cid": 8,
          "type": 10,
          "name": "实付单价",
          "sort": 8,
          "field": "zh_dj",
          "disabled": false,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 1,
          "is_checked": false,
          "children": []
        },
        {
          "cid": 9,
          "type": 10,
          "name": "数量(升)",
          "sort": 9,
          "field": "sl",
          "disabled": false,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 1,
          "is_checked": true,
          "children": []
        },
        {
          "cid": 10,
          "type": 10,
          "name": "油品应付",
          "sort": 10,
          "field": "origin_money",
          "disabled": false,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 1,
          "is_checked": false,
          "children": []
        },
        {
          "cid": 12,
          "type": 10,
          "name": "优惠金额",
          "sort": 12,
          "field": "dis_money",
          "disabled": false,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 1,
          "is_checked": false,
          "children": []
        },
        {
          "cid": 13,
          "type": 10,
          "name": "实付金额",
          "sort": 13,
          "field": "pay_money",
          "disabled": true,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 1,
          "is_checked": true,
          "children": []
        },
        {
          "cid": 14,
          "type": 10,
          "name": "充值金额",
          "sort": 14,
          "field": "recharge_money",
          "disabled": true,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 1,
          "is_checked": true,
          "children": []
        },
        {
          "cid": 15,
          "type": 10,
          "name": "余额",
          "sort": 15,
          "field": "after_money",
          "disabled": false,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 1,
          "is_checked": false,
          "children": []
        },
        {
          "cid": 16,
          "type": 10,
          "name": "交易时间",
          "sort": 16,
          "field": "pay_time",
          "disabled": false,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 1,
          "is_checked": false,
          "children": []
        }
      ]
      this.copyMom = cloneDeep(this.tableColumns)
      this.sonList = [
        {
          "cid": 17,
          "type": 11,
          "name": "油站名称",
          "sort": 1,
          "field": "stid_name",
          "disabled": false,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 2,
          "is_checked": true,
          "children": []
        },
        {
          "cid": 18,
          "type": 11,
          "name": "变动类型",
          "sort": 2,
          "field": "type",
          "disabled": true,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 2,
          "is_checked": true,
          "children": []
        },
        {
          "cid": 19,
          "type": 11,
          "name": "油品名称",
          "sort": 3,
          "field": "goods_name",
          "disabled": true,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 2,
          "is_checked": true,
          "children": []
        },
        {
          "cid": 20,
          "type": 11,
          "name": "挂牌单价",
          "sort": 4,
          "field": "han_price",
          "disabled": false,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 2,
          "is_checked": true,
          "children": []
        },
        {
          "cid": 21,
          "type": 11,
          "name": "实付单价",
          "sort": 5,
          "field": "zh_dj",
          "disabled": false,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 2,
          "is_checked": false,
          "children": []
        },
        {
          "cid": 22,
          "type": 11,
          "name": "数量(升)",
          "sort": 6,
          "field": "sl",
          "disabled": false,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 2,
          "is_checked": false,
          "children": []
        },
        {
          "cid": 23,
          "type": 11,
          "name": "油品应付",
          "sort": 7,
          "field": "origin_money",
          "disabled": false,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 2,
          "is_checked": false,
          "children": []
        },
        {
          "cid": 24,
          "type": 11,
          "name": "非油应付",
          "sort": 8,
          "field": "goods_money",
          "disabled": false,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 2,
          "is_checked": true,
          "children": []
        },
        {
          "cid": 25,
          "type": 11,
          "name": "优惠金额",
          "sort": 9,
          "field": "dis_money",
          "disabled": false,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 2,
          "is_checked": false,
          "children": []
        },
        {
          "cid": 26,
          "type": 11,
          "name": "实付金额",
          "sort": 10,
          "field": "pay_money",
          "disabled": true,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 2,
          "is_checked": true,
          "children": []
        },
        {
          "cid": 27,
          "type": 11,
          "name": "充值金额",
          "sort": 11,
          "field": "recharge_money",
          "disabled": true,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 2,
          "is_checked": true,
          "children": []
        },
        {
          "cid": 28,
          "type": 11,
          "name": "余额",
          "sort": 12,
          "field": "after_money",
          "disabled": false,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 2,
          "is_checked": false,
          "children": []
        },
        {
          "cid": 29,
          "type": 11,
          "name": "交易时间",
          "sort": 13,
          "field": "pay_time",
          "disabled": false,
          "is_default": 0,
          "pid": 0,
          "is_deleted": 0,
          "create_time": 1685432373,
          "update_time": 1685432373,
          "crid": 2,
          "is_checked": false,
          "children": []
        }
      ]
      this.copySon = cloneDeep(this.sonList)
    },

    /*
    * 生成报表
    * num:查询次数；index:当前查询索引；flag：1->打印，2->下载
    * */
    async createReport(num, index, flag) {
      try {
        if (isEmpty(this.query.dateValue)) {
          this.$message({
            message: "请选择时间",
            type: "error"
          });
          return;
        }
        let params = {
          company_type: this.query.customer_type,
          is_stid: this.is_stid,
          single_stid: this.single_stid,
          start_time: this.query.dateValue[0],
          end_time: this.query.dateValue[1],
          stids: this.stids,
        }

        if (index >= num) {
          if (this.searchFlag == 2) {
            this.disableAll = false
            setTimeout(() => {
              if (flag == 1) {
                this.printContent()
              } else {
                this.cardChargeDownload()
              }
            }, 0)
          }
          // Sort tableList by fleetId in ascending order
          this.tableList.sort((a, b) => {
            return Number(a.fleetId) - Number(b.fleetId);
          });
          return console.log('生成截止', flag)
        }

        params.company_id = this.query.company_id[index];

        this.disableAll = true
        const res = await this.$axios.post('/CardReport/getCustomerOrderListsV2', params)
        if (res.data.status !== 200) {
          return this.$message.error(res.data.info)
        }
        console.log('查询res', num, index, flag);
        if (num > 0) {
          if (index < num) {
            if (!isEmpty(res.data.data)) {
              const company = this.companyOptions.find(item => params.company_id === item.ID)

              res.data.data.result.forEach(row => {
                row.fleet_id = params.company_id
                if (String(company.CompanyType) === '0') {
                  row.customer_type = '未分类';
                  return;
                }
                row.customer_type = this.customerTypeObject[company.CompanyType] || company.CompanyType;
              })

              function formatValues(obj) {
                const keys = ['sl', 'origin_money', 'dis_money', 'pay_money', 'recharge_money'];
                keys.forEach(key => {
                  if (obj && obj.hasOwnProperty(key)) {
                    if (obj[key] === '-') {
                      obj[key] = '0.00';
                    }
                  }
                });
                return obj;
              }

              function dataSortByType(table) {
                table.sort((a, b) => {
                  // 1. 按照 card_number 排序
                  const cardComparison = a.card_number.localeCompare(b.card_number);
                  if (cardComparison !== 0) return cardComparison;

                  // 2. 按照 car_no 排序
                  const carComparison = a.car_no.localeCompare(b.car_no);
                  if (carComparison !== 0) return carComparison;

                  // 3. 按照 type_no 升序排序
                  const typeComparison = a.type_no - b.type_no;
                  if (typeComparison !== 0) return typeComparison;

                  //4. 按照 pay_time 倒序排序
                  return new Date(a.pay_time) - new Date(b.pay_time);
                });
              }

              const tableData = res.data.data.result;
              if (!isEmpty(tableData)) {
                const last = tableData.pop();

                dataSortByType(tableData);
                calculateSubtotal(tableData);
                formatValues(last)
                last.car_no = '母账合计：';
                tableData.push(last);
              }


              function numberNaNInit(val) {
                return isNaN(Number(val)) ? 0 : val
              }

              function calculateSubtotal(data) {
                const typeMap = {};
                data.forEach((item, index) => {
                  const key = `${item.car_no}_${item.type === '消费退款' ? '消费' : item.type}`;
                  if (!typeMap[key]) {
                    typeMap[key] = {
                      sl: new BigNumber(0),
                      origin_money: new BigNumber(0),
                      dis_money: new BigNumber(0),
                      pay_money: new BigNumber(0),
                      recharge_money: new BigNumber(0)
                    };
                  }
                  typeMap[key].sl = typeMap[key].sl.plus(numberNaNInit(item.sl));
                  typeMap[key].origin_money = typeMap[key].origin_money.plus(numberNaNInit(item.origin_money));
                  typeMap[key].dis_money = typeMap[key].dis_money.plus(numberNaNInit(item.dis_money));
                  typeMap[key].pay_money = typeMap[key].pay_money.plus(numberNaNInit(item.pay_money));
                  typeMap[key].recharge_money = typeMap[key].recharge_money.plus(numberNaNInit(item.recharge_money));
                });
                console.log("=>(CustomerSpendReport.vue:1268) typeMap", typeMap);
                for (var key in typeMap) {
                  if (typeMap.hasOwnProperty(key)) {
                    const [car_no, type] = key.split('_');
                    const lastIndex = data.findLastIndex(x => {
                      if (type.includes('消费') && x.car_no === car_no && x.type.includes('消费')) {
                        return true
                      } else if (x.car_no === car_no && x.type === type) {
                        return true
                      }
                    });
                    console.log("=>(CustomerSpendReport.vue:1272) lastIndex", lastIndex, key);
                    if (lastIndex !== -1) {
                      const subtotalItem = {
                        type: `-`,
                        car_no: '小计：',
                        card_number: '-',
                        goods_name: '-',
                        han_price: '-',
                        zh_dj: '-',
                        sl: typeMap[key].sl.toFixed(2),
                        origin_money: typeMap[key].origin_money.toFixed(2),
                        dis_money: typeMap[key].dis_money.toFixed(2),
                        pay_money: typeMap[key].pay_money.toFixed(2),
                        recharge_money: typeMap[key].recharge_money.toFixed(2),
                        after_money: '-',
                        pay_time: '-',
                        order_no: '-'
                      };
                      data.splice(lastIndex + 1, 0, subtotalItem);
                    }
                  }
                }
                return data;
              }


              const {card_result: cardResult} = res.data.data
              console.log("=>(CustomerSpendReport.vue:746) cardResult", cardResult);
              if (!isEmpty(cardResult)) {
                cardResult.forEach(item => {
                  const { card_list } = cloneDeep(item);
                  const last = card_list.pop();

                  dataSortByType(card_list)
                  calculateSubtotal(card_list);

                  formatValues(last)
                  last.car_no = '子卡合计：';
                  card_list.push(last);
                  tableData.push(...card_list)
                })
              }

              function addTotalStatistics(data) {
                const total = {
                  sl: new BigNumber(0),
                  origin_money: new BigNumber(0),
                  dis_money: new BigNumber(0),
                  pay_money: new BigNumber(0),
                  recharge_money: new BigNumber(0),
                };

                data.forEach((item) => {
                  if (item.type !== '-') {
                    total.sl = total.sl.plus(new BigNumber(numberNaNInit(item.sl)));
                    total.origin_money = total.origin_money.plus(new BigNumber(numberNaNInit(item.origin_money)));
                    total.dis_money = total.dis_money.plus(new BigNumber(numberNaNInit(item.dis_money)));
                    total.pay_money = total.pay_money.plus(new BigNumber(numberNaNInit(item.pay_money)));
                    total.recharge_money = total.recharge_money.plus(new BigNumber(numberNaNInit(item.recharge_money)));
                  }
                });

                const totalRow = {
                  stid_name: '中国钓鱼岛加油站',
                  type: '-',
                  card_no: '-',
                  card_number: '-',
                  car_no: '总计：',
                  goods_name: '-',
                  han_price: '-',
                  zh_dj: '-',
                  sl: total.sl.toFixed(2),
                  origin_money: total.origin_money.toFixed(2),
                  goods_money: '-',
                  dis_money: total.dis_money.toFixed(2),
                  pay_money: total.pay_money.toFixed(2),
                  recharge_money: total.recharge_money.toFixed(2),
                  after_money: '-',
                  pay_time: '-',
                  order_no: '-',
                  fleet_id: '-',
                  customer_type: '-',
                };

                (data && data.length > 0) && data.push(totalRow);
              }

              addTotalStatistics(tableData)

              this.tableList.push({
                companyName: this.getCompanyName(params.company_id),
                fleetId: params.company_id,
                tableData
              })
            }

            if (flag == 1) {
              await this.createReport(num, index + 1, 1)
            } else {
              await this.createReport(num, index + 1, 2)
            }
          }
        }

        let userInfo = localStorage.getItem("__userInfo__");
        if (userInfo && (userInfo !== "" || userInfo !== "undefined")) {
          this.orderMaker = JSON.parse(userInfo).name;
        }
        this.orderMakingTime = this.$moment().format("YYYY-MM-DD");
      } catch (e) {
        console.log(e)
        this.$message.error('生成报表失败');
      } finally {
        this.disableAll = false
      }
    },
    search() {
      if (this.query.company_id.length === 0) return this.$message.error(`请选择车队`);
      this.tableList = []
      this.searchFlag = 1
      this.createReport(this.query.company_id.length, 0, 1)
    },
    sureBranch() {
      if (this.query.company_id.length === 0) {
        return this.$message.error(`请选择要${this.checkFlag == 1 ? "打印" : "下载"}的车队`);
      }
      this.tableList = []
      this.showAllChecks = false
      this.searchFlag = 2
      if (this.checkFlag == 1) {
        this.createReport(this.query.company_id.length, 0, 1)
      } else {
        this.createReport(this.query.company_id.length, 0, 2)
      }
    },
    //打印
    printContent() {
      let data = this.tableList;
      console.log("=>  file: CustomerSpendReport.vue:956  data:", data)
      if (data) {
        data = data.filter(item => item.tableData && item.tableData.length > 0)
      }
      if (isEmpty(data)) {
        this.$message.error("当前无数据，无需打印");
        return;
      }
      // 按照fleetId升序排序
      data.sort((a, b) => a.fleetId - b.fleetId);
      let html = ``;
      data.forEach((info, index) => {
        html += `<div class="${index > 0 ? 'next' : ''}"><h1>客户消费变动明细表</h1><p>${this.isGroup ? '集团名称' : '油站名称'}：${this.getCurrentStation.label}&emsp;开始时间：${this.query.dateValue.length ? this.query.dateValue[0] : ""}&emsp;结束时间：${this.query.dateValue.length ? this.query.dateValue[1] : ""}</p><div class="table-wrapper">
    <span>${info.companyName}</span><span>车队ID：${info.fleetId}</span><span>客户类型：${this.getCompanyCustomerType(info.fleetId)}</span>
    </div><table><thead><tr class=""><th><div class="cell">变动类型</div></th><th><div class="cell">卡面卡号</div></th><th><div class="cell">车牌号</div></th><th><div class="cell">油品名称</div></th><th><div class="cell">挂牌单价</div></th><th><div class="cell">实付单价</div></th><th><div class="cell">数量(升)</div></th><th><div class="cell">油品应付</div></th><th><div class="cell">优惠金额</div></th><th><div class="cell">实付金额</div></th><th><div class="cell">充值金额</div></th><th><div class="cell">余额</div></th><th><div class="cell">交易时间</div></th></tr></thead>
        <tbody>
        ${info.tableData.map(item => "<tr>" + "<td>" + item.type + "</td>" + "<td>" + item.card_number + "</td>" + "<td>" + item.car_no + "</td>" + "<td>" + item.goods_name + "</td>" + "<td>" + item.han_price + "</td>" + "<td>" + item.zh_dj + "</td>" + "<td>" + item.sl + "</td>" + "<td>" + item.origin_money + "</td>" + "<td>" + item.dis_money + "</td>" + "<td>" + item.pay_money + "</td>" + "<td>" + item.recharge_money + "</td>" + "<td>" + item.after_money + "</td>" + "<td>" + item.pay_time + "</td>").join('')}
        </tbody>
      </table>
    </div>
  </div>`
      })

      printJS({
        style: `@media print {
      @page {
        size: auto;
        margin: 20pt;
        margin-bottom: 10pt;
        padding: 2pt;
      }

      body {
        margin: 2pt;
        padding: 2pt;
        font-size: 12pt;
        margin-left:-1pt;
      }
      #container{
        width:100vw;
      }
      h1{font-size:16pt;text-align:center; margin:0; padding:0;}
      p{font-size:10pt;}
      table {
        border-collapse: collapse;
        width: 100%;
        box-sizing: border-box;
        font-size: 10pt;
      }
      th,
      td {
        border: 1px solid #999;
        box-sizing: border-box;
        padding: 2pt;
        text-align: center;
      }
      th{font-size:9pt}
      td{font-size:8pt}
      .next{page-break-before: always;}
      .table-wrapper {
        font-size: 9pt;
        margin-top: 1em;
        padding: 0 1em;
        height: 15pt;
        border: 1px solid #999999;
        border-bottom: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

    }`,
        printable: html,
        type: 'raw-html',
      })
    },
    pause(msec) {
      return new Promise(
        (resolve, reject) => {
          setTimeout(resolve, msec || 1000);
        }
      );
    },
    //下载数据
    async cardChargeDownload() {
      const wordA = 65;

      function arrayToExcel(list, columns, companyName) {
        const cardList = list.cardResult;
        list = list.tableData;
        const head = columns.map(item => item.name);
        const rows = list.map(item => columns.map(key => item[key.field]));
        const title = ['客户消费变动明细表'], companyNameRow = [companyName];
        const date = [`${this.isGroup ? '集团' : "油站"}名称：${this.getCurrentStation.label}`, `开始时间：${this.query.dateValue.length ? this.query.dateValue[0] : ""}`, `结束时间：${this.query.dateValue.length ? this.query.dateValue[1] : ""}`]
        rows.unshift(head)
        rows.unshift(companyNameRow)
        rows.unshift(date)
        rows.unshift(title)
        console.log("=>(CustomerSpendReport.vue:474) rows", rows, cardList);
        // 增加子账列表
        cardList && cardList.forEach(item => {
          rows.push(
            [], // 空行
            // 表格头
            [`卡号：${item.card_info.CardNo || '-'}   卡面卡号：${item.card_info.CardNumber || '-'}   车牌：${item.card_info.CarNumber || '-'}   卡名称：${item.card_info.CardName || '-'}`],
            // 表格列
            this.copySon.map(item => item.name),
            // 表格数据行
            ...item.card_list.map(row => this.copySon.map(key => row[key.field]))
          )
        })
        // rows.push()

        const ws = XLSX.utils.aoa_to_sheet(rows);
        console.log("=>(CustomerSpendReport.vue:977) rows", rows);
        ws["!merges"] = [
          XLSX.utils.decode_range(`A1:${String.fromCharCode(columns.length + wordA - 1)}1`),
          XLSX.utils.decode_range(`A3:${String.fromCharCode(columns.length + wordA - 1)}3`)
        ];
        ws["!cols"] = columns.map((item, index) => [0, 1, 2, 3, 4, 11].includes(index) ? { wch: 30 } : {});
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
        XLSX.writeFile(wb, `客户资金变动明细表-${companyNameRow}.xlsx`);
      }

      this.$nextTick(async () => {
        let count = 0
        console.log("=>  file: CustomerSpendReport.vue:1065  this.tableList:",this.tableList, this.tableList.length)
        let tableList = [];
        if(this.tableList &&  this.tableList.length > 0){
          tableList = this.tableList.filter(item => item.tableData.length > 0);
        }
        if(tableList && tableList.length === 0){
          this.$message.error('当前无数据，无需下载');
          return;
        }
        for (const item of tableList) {
          count++
          arrayToExcel.call(this, item, this.tableColumns, item.companyName)
          if (count >= 10) {
            await this.pause(2000);
            count = 0
          }
        }
      })
    },
    getCompanyName(id) {
      let name = ''
      this.companyOptions.forEach(item => {
        if (item.ID == id) {
          name = item.CompanyName
        }
      })
      return name
    }
  },
  watch: {
    async getCurrentStation(newValue, oldValue) {
      if (newValue.merchant_type != 0 && newValue.value != oldValue.value) {
        if (
          this.getCurrentStation &&
          this.getCurrentStation.merchant_type == 2
        ) {
          this.isGroup = true;
        } else {
          this.isGroup = false;
        }
        this.getStations();
        this.getCompanyList();
        this.getCardReportColumn()
      }
    },
  }
};
</script>

<style scoped>
@import url('../../assets/css/unify.css');
@media print {

  /* 在需要分页的元素之前插入分页 */
  .page-break {
    page-break-before: always;
  }
}

.table3_head {
  margin-top: 20px;
  height: 40px;
  line-height: 40px;
  text-align: left;
  padding-left: 20px;
  border-left: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
  border-top: 1px solid #EBEEF5;
}
</style>
