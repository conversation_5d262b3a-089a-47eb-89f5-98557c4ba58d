webpackJsonp([25],{"314y":function(t,e){},"4kB4":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=a("fZjL"),o=a.n(r),n=a("Dd8w"),l=a.n(n),i=a("FZmr"),s=a("NYxO"),u={name:"CapitalJXC",components:{DownloadTips:i.a},data:function(){return{isTotalReportForm:!0,groupValue:2,showGroupValue:!0,typeValue:1,dateValue:[],BCoptions1:[],BCoptions2:[],BCValue1:"",BCValue2:"",DailyTableData:[],loading:!1,start_time:"",end_time:"",orderMaker:"",orderMakingTime:"",tableStartTime:"",tableEndTime:"",oilName:"",arr:[],isGroup:!0,showDownloadTips:!1,isGroupSettle:Boolean,showAdjust:1}},mounted:function(){var t=localStorage.getItem("__userInfo__")||"";this.isTotalReportForm=426!=JSON.parse(t).group_id;var e=this.$moment().subtract(1,"days").format("YYYY-MM-DD");if(this.dateValue.push(e),this.dateValue.push(e),void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.getCurrentStation&&2==this.getCurrentStation.merchant_type?(this.isGroup=!0,this.showGroupValue=!0,this.groupValue=2):(this.isGroup=!1,this.showGroupValue=!1,this.groupValue=1),this.changeDate()},computed:l()({},Object(s.c)({getCurrentStation:"getCurrentStation"})),methods:{changeTypeValue:function(){this.dateValue="",this.DailyTableData=[],this.orderMakingTime=""},changeGroupValue:function(){2==this.groupValue&&(this.typeValue=1),this.DailyTableData=[]},changeDate:function(){var t=0,e=0;this.dateValue?(t=this.$moment(this.dateValue[0]+" 00:00:00","YYYY-MM-DD HH:mm:ss").unix(),e=this.$moment(this.dateValue[1]+" 23:59:59","YYYY-MM-DD HH:mm:ss").unix(),this.getCardInvoicingReport(t,e)):(this.DailyTableData=[],this.orderMakingTime="",this.orderMaker="",this.oilName="")},getCardInvoicingReport:function(t,e){if(1==localStorage.getItem("__isGroupSettle__")&&2!=this.getCurrentStation.merchant_type)return this.DailyTableData=[],this.orderMakingTime="",this.orderMaker="",void(this.oilName="");var a=this;a.loading=!0;var r=a.typeValue;2==a.groupValue&&(r=2),a.$axios.post("/CardReport/getCardInvoicingReport",{type:r,start_time:t,end_time:e}).then(function(t){if(a.DailyTableData=[],a.loading=!1,200==t.data.status){a.DailyTableData=t.data.data.card_invoicing,a.$nextTick(function(){a.showAdjust=t.data.data.balance_correction});var e=t.data.data.total;e&&0!=o()(e).length&&(e.date="汇总",e.stname="--",e.qichu_amt="--",e.qichu_bj_amt="--",e.qichu_skj_amt="--",e.qimo_amt="",e.qimo_bj_amt="",e.qimo_skj_amt="",e.start_time="",e.end_time="",e.banci="--",a.DailyTableData.push(e)),4==a.typeValue&&(a.setData(a.DailyTableData,"date","stid"),a.setTable(a.DailyTableData,"date","stid")),a.start_time=t.data.data.start_time,a.end_time=t.data.data.end_time,console.log(t.data.data.group_settle),a.isGroupSettle=1==t.data.data.group_settle,a.tableStartTime=a.$moment(Number(a.start_time+"000")).format("YYYY-MM-DD"),a.tableEndTime=a.$moment(Number(a.end_time+"000")).format("YYYY-MM-DD"),a.orderMakingTime=a.$moment().format("YYYY-MM-DD");var r=localStorage.getItem("__userInfo__");!r||""===r&&"undefined"===r||(a.orderMaker=JSON.parse(r).name);var n=JSON.parse(localStorage.getItem("getCurrentStation"));a.oilName=n.label}else a.$message({message:t.data.info,type:"error"})}).catch(function(t){})},clearData:function(){this.dateValue||(this.DailyTableData=[],this.orderMakingTime="",this.orderMaker="",this.oilName="")},printContent:function(){var t=document.querySelector("#myTable").innerHTML,e=document.body.innerHTML;document.body.innerHTML=t,document.getElementsByClassName("el-table__header")[0].style.width="100%",document.getElementsByClassName("el-table__header")[0].style["table-layout"]="auto",document.getElementsByClassName("el-table__body")[0].style.width="100%",document.getElementsByClassName("el-table__body")[0].style["table-layout"]="auto",window.print(),history.go(0),document.body.innerHTML=e,this.$print(this.$refs.print)},cardChargeDownload:function(){var t=this,e=this.typeValue;2==this.groupValue&&(e=2),this.$axios.get("/CardReport/cardInvoicingReportDownload",{params:{start_time:this.start_time,end_time:this.end_time,type:e,balance_correction:this.showAdjust}}).then(function(e){200==e.data.status?t.showDownloadTips=!0:t.$message.error(e.data.info)})},setTable:function(t,e,a){var r=[],o=0,n=[],l=0;t.forEach(function(i,s){0===s?(r.push(1),n.push(1)):String(i[e])&&String(i[e])==String(t[s-1][e])?(r[o]+=1,r.push(0),String(i[a])&&String(i[a])==String(t[s-1][a])?(n[l]+=1,n.push(0)):(n.push(1),l=s)):(r.push(1),o=s,n.push(1),l=s)});var i={};i[e]=r,i[a]=n,this.arr=[],this.arr.push(i)},setData:function(t,e,a){for(var r,o=t.length,n=0;n<o-1;n++)for(var l=n+1;l<o;l++)t[l][e]==t[n][e]&&t[l][a]==t[n][a]&&(r=t[n+1],t[n+1]=t[l],t[l]=r);for(n=0;n<o-1;n++)for(l=0;l<o-1-n;l++)if(t[l+1][e]==t[l][e]&&t[l+1][a]==t[l][a]&&t[l].start_time<t[l+1].start_time){var i=t[l+1];t[l+1]=t[l],t[l]=i}return t},objectSpanMethod:function(t){t.row,t.column;var e=t.rowIndex,a=t.columnIndex;if(this.arr[0]&&4==this.typeValue){if(0===a){var r=this.arr[0].date[e];return{rowspan:r,colspan:r>0?1:0}}if(1===a&&2==this.getCurrentStation.merchant_type){var o=this.arr[0].stid[e];return{rowspan:o,colspan:o>0?1:0}}}},getSummaries:function(t){var e=t.columns,a=t.data,r=[];return e.forEach(function(t,e){if(0!==e)if(1!=e&&2!=e&&3!=e&&4!=e&&22!=e&&21!=e&&20!=e){var o=a.map(function(e){return e[t.property]});o.every(function(t){return isNaN(t)})?r[e]="--":(r[e]=o.reduce(function(t,e){var a=Number(e);return isNaN(a)?t:t+e},0),r[e]=r[e].toFixed(2))}else r[e]="--";else r[e]="汇总"}),r}},watch:{getCurrentStation:function(t,e){0!=t.merchant_type&&t.value!=e.value&&(this.getCurrentStation&&2==this.getCurrentStation.merchant_type?(this.isGroup=!0,this.showGroupValue=!0,this.groupValue=2,this.typeValue=1):(this.isGroup=!1,this.showGroupValue=!1,this.groupValue=1),this.changeDate())}}},_={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"report"},[a("div",{staticClass:"left_select"},[a("div",{staticClass:"group_select"},[a("el-radio-group",{on:{change:t.changeGroupValue},model:{value:t.groupValue,callback:function(e){t.groupValue=e},expression:"groupValue"}},[t.showGroupValue?a("el-radio-button",{attrs:{label:"2"}},[t._v("集团查看")]):t._e(),t._v(" "),a("el-radio-button",{attrs:{label:"1"}},[t._v("单站查看")])],1)],1),t._v(" "),t.isGroupSettle?t._e():a("div",[a("el-radio-group",{on:{change:t.changeTypeValue},model:{value:t.typeValue,callback:function(e){t.typeValue=e},expression:"typeValue"}},[a("el-radio-button",{attrs:{label:"1"}},[t._v("按自然日期")]),t._v(" "),1==t.groupValue?a("el-radio-button",{attrs:{label:"4"}},[t._v("按开班日期")]):t._e()],1)],1),t._v(" "),a("div",{staticClass:"content_header"},[a("div",{staticClass:"left"},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},on:{change:t.clearData},model:{value:t.dateValue,callback:function(e){t.dateValue=e},expression:"dateValue"}}),t._v(" "),a("el-button",{attrs:{type:"primary",disabled:!t.dateValue},on:{click:t.changeDate}},[t._v("生成")])],1),t._v(" "),a("div",{staticClass:"right"},[a("el-button",{attrs:{type:"primary",disabled:0==t.DailyTableData.length},on:{click:t.printContent}},[t._v("打印")]),t._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:t.isTotalReportForm,expression:"isTotalReportForm"}],attrs:{type:"primary",disabled:0==t.DailyTableData.length},on:{click:t.cardChargeDownload}},[t._v("下载数据")])],1)]),t._v(" "),a("div",{attrs:{id:"myTable"}},[a("div",{staticClass:"tableData reportData"},[a("div",{staticClass:"report_title"},[t._v("储值卡资金进销存报表")]),t._v(" "),a("div",{staticClass:"report_header"},[t.isGroup?a("div",[t._v("集团名称："+t._s(t.getCurrentStation.label))]):a("div",[t._v("油站名称："+t._s(t.getCurrentStation.label))]),t._v(" "),a("div",[t._v("开始日期："+t._s(t.dateValue?t.dateValue[0]:""))]),t._v(" "),a("div",[t._v("结束日期："+t._s(t.dateValue?t.dateValue[1]:""))]),t._v(" "),a("div",[t._v("单位：元")])]),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.DailyTableData,"span-method":t.objectSpanMethod,border:"",size:"small",align:"center",fit:""}},[a("el-table-column",{attrs:{align:"center",prop:"date",label:1==t.typeValue?"日期":"开班日期",width:"90"}}),t._v(" "),2==t.getCurrentStation.merchant_type&&2!=t.groupValue?a("el-table-column",{attrs:{align:"center",prop:"stname",label:"油站",width:"150"}}):t._e(),t._v(" "),4==t.typeValue?a("el-table-column",{attrs:{align:"center",prop:"banci",label:"班次",width:"150"}}):t._e(),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"期初余额"}},[a("el-table-column",{attrs:{align:"center",label:"期初总余额",width:"100",prop:"qichu_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(""===e.row.qimo_amt?"--":Number(e.row.qichu_amt).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"期初本金",width:"100",prop:"qichu_bj_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(""===e.row.qimo_amt?"--":Number(e.row.qichu_bj_amt).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"期初赠金",prop:"qichu_skj_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(""===e.row.qimo_amt?"--":Number(e.row.qichu_skj_amt).toFixed(2)))]}}])})],1),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"期间充值"}},[a("el-table-column",{attrs:{align:"center",label:"充值金额",width:"100",prop:"recharge_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.recharge_amt).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值本金",width:"100",prop:"recharge_bj_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.recharge_bj_amt).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值赠金",prop:"recharge_skj_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.recharge_skj_amt).toFixed(2)))]}}])})],1),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"期间消费"}},[a("el-table-column",{attrs:{align:"center",label:"消费金额",width:"100",prop:"consume_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.consume_amt).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费本金",width:"100",prop:"consume_bj_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.consume_bj_amt).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费赠金",prop:"consume_skj_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.consume_skj_amt).toFixed(2)))]}}])})],1),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"期间退款"}},[a("el-table-column",{attrs:{align:"center",label:"充值退款"}},[a("el-table-column",{attrs:{align:"center",label:"充值退款金额",width:"100",prop:"recharge_refund_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.recharge_refund_amt).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值退款本金",width:"100",prop:"recharge_refund_bj_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.recharge_refund_bj_amt).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值退款赠金",width:"100",prop:"recharge_refund_skj_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.recharge_refund_skj_amt).toFixed(2)))]}}])})],1),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费退款"}},[a("el-table-column",{attrs:{align:"center",label:"消费退款金额",width:"100",prop:"consume_refund_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.consume_refund_amt).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费退款本金",width:"100",prop:"consume_refund_bj_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.consume_refund_bj_amt).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费退款赠金",width:"100",prop:"consume_refund_skj_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.consume_refund_skj_amt).toFixed(2)))]}}])})],1)],1),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"余额清零"}},[a("el-table-column",{attrs:{align:"center",label:"清零金额",width:"100",prop:"clear_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.clear_amt).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"清零本金",width:"100",prop:"clear_bj_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.clear_bj_amt).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"清零赠金",width:"100",prop:"clear_skj_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.clear_skj_amt).toFixed(2)))]}}])})],1),t._v(" "),1==t.showAdjust?a("el-table-column",{attrs:{align:"center",label:"余额冲正",width:"100",prop:"balance_correction"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.balance_correction?[t._v(t._s(Number(e.row.balance_correction).toFixed(2)))]:[t._v("--")]]}}],null,!1,1135822307)}):t._e(),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"期末余额"}},[a("el-table-column",{attrs:{align:"center",label:"期末总余额",width:"100",prop:"qimo_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(""===e.row.qimo_amt?"--":Number(e.row.qimo_amt).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"期末本金",width:"100",prop:"qimo_bj_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(""===e.row.qimo_amt?"--":Number(e.row.qimo_bj_amt).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"期末赠金",width:"100",prop:"qimo_skj_amt"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(""===e.row.qimo_amt?"--":Number(e.row.qimo_skj_amt).toFixed(2)))]}}])})],1)],1)],1),t._v(" "),a("div",{staticClass:"table_des"},[a("div",{staticClass:"table_des_text"},[a("p",[t._v("注：")]),t._v(" "),t.isGroupSettle?a("div",[a("p",[t._v("\n              1.期末总余额=期初总余额+（充值金额-充值退款金额）-（消费金额-消费退款金额）。\n            ")])]):a("div",[a("p",[t._v("\n              1.仅统计油站自有储值卡会员数据，他站会员至本站消费、充值数据不统计。\n            ")]),t._v(" "),a("p",[t._v("\n              2.期末总余额=期初总余额+（充值金额-充值退款金额）-（消费金额-消费退款金额）。\n            ")])])])]),t._v(" "),a("div",{staticClass:"des_bottom"},[a("div",[t._v("制表人："+t._s(t.orderMaker))]),t._v(" "),a("div",[t._v("制表时间："+t._s(t.orderMakingTime))]),t._v(" "),a("div",[t._v("签字：")])])])]),t._v(" "),a("download-tips",{attrs:{showDownloadTips:t.showDownloadTips},on:{"update:showDownloadTips":function(e){t.showDownloadTips=e},"update:show-download-tips":function(e){t.showDownloadTips=e}}})],1)},staticRenderFns:[]};var c=a("VU/8")(u,_,!1,function(t){a("314y"),a("xgYC")},"data-v-27c1302a",null);e.default=c.exports},xgYC:function(t,e){}});