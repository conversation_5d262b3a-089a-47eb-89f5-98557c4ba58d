webpackJsonp([37],{I30n:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=a("Xxa5"),n=a.n(l),r=a("exGp"),o=a.n(r),i=a("Dd8w"),s=a.n(i),d=a("Keoy"),c=a("FZmr"),p=a("fTGR"),m=a("NYxO"),u={name:"customerOilReport",components:{DownloadTips:c.a,BanciDateTime:p.a},data:function(){return{isTotalReportForm:!0,typeOptions:[{value:1,label:"按自然日期"},{value:4,label:"按班结日期"}],typeValue:1,dateValue:[],dateBanciValue:"",companyOptions:[],company_id:"",isGroup:!0,loading:!1,loadingDetail:!1,tableData:[],card_oil:[],tableDataDetail:[],noMore:!0,page:1,pageSize:20,total:0,showDownloadTips:!1,stationIdArr:[],stationId:"",stationName:"",stationOptions:[],orderMaker:"",orderMakingTime:"",checked:!0}},computed:s()({},Object(m.c)({getCurrentStation:"getCurrentStation"}),{companyName:function(){var t=this,e="";return this.companyOptions.forEach(function(a){a.ID==t.company_id&&(e=a.CompanyName)}),e}}),watch:{getCurrentStation:function(t,e){0!=t.merchant_type&&t.value!=e.value&&(this.stationId="",this.getCurrentStation&&2==this.getCurrentStation.merchant_type?(this.isGroup=!0,this.getStationList()):(this.isGroup=!1,this.stationId=this.getCurrentStation.merchant_id),this.getCompanyList())}},mounted:function(){var t=localStorage.getItem("__userInfo__")||"";this.isTotalReportForm=426!=JSON.parse(t).group_id;var e=this.$moment(new Date).subtract(1,"days").format("YYYY-MM-DD HH:mm:ss"),a=this.$moment().format("YYYY-MM-DD HH:mm:ss");this.dateValue=[e,a];var l=this.$moment().subtract(1,"days").format("YYYY-MM-DD");if(this.dateBanciValue=l,void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.getCurrentStation&&2==this.getCurrentStation.merchant_type?(this.isGroup=!0,this.getStationList()):(this.isGroup=!1,this.stationId=this.getCurrentStation.merchant_id),this.getCompanyList()},methods:{getStationList:function(){this.stationIdArr=[];var t=this;Object(d.h)().then(function(e){200==e.status&&(t.stationOptions=[],e.data.data.forEach(function(e){t.stationIdArr.push(e.stid)}),t.stationOptions=e.data.data)})},getCompanyList:function(){var t=this;t.$axios.post("/CompanyCard/getSimpleCompanyList",{page:1,page_size:1250,input:""}).then(function(e){200==e.data.status?(t.companyOptions=[],t.companyOptions=t.companyOptions.concat(e.data.data.dt)):t.$message({message:e.data.info,type:"error"})}).catch(function(t){})},changeTypeValue:function(t){var e=this.$moment(new Date).subtract(1,"days").format("YYYY-MM-DD HH:mm:ss"),a=this.$moment().format("YYYY-MM-DD HH:mm:ss");this.dateValue=[e,a];var l=this.$moment().subtract(1,"days").format("YYYY-MM-DD");this.dateBanciValue=l,this.orderMakingTime="",this.tableData=[],this.tableDataDetail=[],this.page=1},searchBanciDate:function(t){this.dateValue=t},changeDate:function(){var t=this;return o()(n.a.mark(function e(){var a,l,r,o,i,s,c;return n.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(a=t,!t.isGroup){e.next=5;break}if(a.stationId||4!=a.typeValue){e.next=5;break}return t.$message.error("请选择油站"),e.abrupt("return");case 5:if(a.company_id){e.next=8;break}return t.$message.error("请选择车队"),e.abrupt("return");case 8:return a.loading=!0,a.loadingDetail=!0,l=void 0,l=4==t.typeValue?[a.stationId]:a.stationIdArr,r={start_time:a.dateValue?a.dateValue[0]:"",end_time:a.dateValue?a.dateValue[1]:"",company_id:a.company_id},o={page_index:a.page,page_size:a.pageSize,start_time:a.dateValue?a.dateValue[0]:"",end_time:a.dateValue?a.dateValue[1]:"",company_id:a.company_id},t.isGroup&&(r.stid=l,o.stid=l),a.orderMakingTime=a.$moment().format("YYYY-MM-DD"),!(i=localStorage.getItem("__userInfo__"))||""===i&&"undefined"===i||(a.orderMaker=JSON.parse(i).name),e.next=20,Object(d.e)(r);case 20:return s=e.sent,a.loading=!1,200==s.data.status?t.tableData=s.data.data:t.$message.error(s.data.info),e.next=25,Object(d.f)(o);case 25:c=e.sent,a.loadingDetail=!1,200==c.data.status?(a.noMore=a.page*a.pageSize>=c.data.data.total_count,a.total=c.data.data.total_count,c.data.data.order_list.length?(a.card_oil=c.data.data.order_list[0].card_oil,a.tableDataDetail=c.data.data.order_list):(a.card_oil=[],a.tableDataDetail=[])):a.$message.error(c.data.info);case 28:case"end":return e.stop()}},e,t)}))()},handleCurrentChange:function(t){this.page=t,this.changeDate()},handleSizeChange:function(t){this.pageSize=t,this.changeDate()},printContent:function(){var t=document.querySelector("#myTable").innerHTML,e=document.body.innerHTML;document.body.innerHTML=t,document.getElementsByClassName("el-table__header")[0].style.width="100%",document.getElementsByClassName("el-table__header")[0].style["table-layout"]="auto",document.getElementsByClassName("el-table__body")[0].style.width="100%",document.getElementsByClassName("el-table__body")[0].style["table-layout"]="auto",document.getElementsByClassName("el-table__header")[1].style.width="100%",document.getElementsByClassName("el-table__header")[1].style["table-layout"]="auto",document.getElementsByClassName("el-table__body")[1].style.width="100%",document.getElementsByClassName("el-table__body")[1].style["table-layout"]="auto",window.print(),history.go(0),document.body.innerHTML=e},cardChargeDownload:function(){var t=this,e={start_time:this.dateValue[0],end_time:this.dateValue[1],company_id:this.company_id,download_cost:this.checked?1:0},a=void 0;a=4==this.typeValue?[this.stationId]:this.stationIdArr,this.isGroup&&(e.stid=a),Object(d.a)(e).then(function(e){200==e.data.status?t.showDownloadTips=!0:t.$message.error(e.data.info)})}}},_={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"report"},[a("div",{staticClass:"report-content"},[a("div",{staticClass:"content-header"},[a("div",{staticStyle:{"margin-bottom":"10px"}},[a("el-radio-group",{on:{change:t.changeTypeValue},model:{value:t.typeValue,callback:function(e){t.typeValue=e},expression:"typeValue"}},t._l(t.typeOptions,function(e){return a("el-radio-button",{key:e.value,attrs:{label:e.value}},[t._v("\n                        "+t._s(e.label)+"\n                    ")])}),1)],1),t._v(" "),2==t.getCurrentStation.merchant_type?a("span",{staticClass:"txt"},[t._v("油站名称")]):t._e(),t._v(" "),2==t.getCurrentStation.merchant_type&&1==t.typeValue?a("el-select",{staticStyle:{width:"250px","margin-right":"20px"},attrs:{multiple:"",clearable:"","collapse-tags":"",placeholder:"请选择油站"},model:{value:t.stationIdArr,callback:function(e){t.stationIdArr=e},expression:"stationIdArr"}},t._l(t.stationOptions,function(t,e){return a("el-option",{key:e,attrs:{label:t.stname,value:t.stid}})}),1):t._e(),t._v(" "),2==t.getCurrentStation.merchant_type&&4==t.typeValue?a("el-select",{staticStyle:{width:"220px","margin-right":"20px"},attrs:{clearable:"","collapse-tags":"",placeholder:"请选择油站"},model:{value:t.stationId,callback:function(e){t.stationId=e},expression:"stationId"}},t._l(t.stationOptions,function(t,e){return a("el-option",{key:e,attrs:{label:t.stname,value:t.stid}})}),1):t._e(),t._v(" "),a("el-date-picker",{directives:[{name:"show",rawName:"v-show",value:1==t.typeValue,expression:"typeValue == 1"}],staticStyle:{"margin-right":"15px"},attrs:{"value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"],type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.dateValue,callback:function(e){t.dateValue=e},expression:"dateValue"}}),t._v(" "),a("banci-date-time",{directives:[{name:"show",rawName:"v-show",value:4==t.typeValue,expression:"typeValue == 4"}],ref:"banciRef",attrs:{stationValue:4==t.typeValue?t.stationId:t.stationIdArr,dateValue:t.dateBanciValue},on:{searchDate:t.searchBanciDate}}),t._v(" "),a("span",{staticClass:"txt"},[t._v("车队名称")]),t._v(" "),a("el-select",{staticStyle:{width:"220px","margin-right":"20px"},attrs:{filterable:"",clearable:"",placeholder:"请选择车队"},model:{value:t.company_id,callback:function(e){t.company_id=e},expression:"company_id"}},t._l(t.companyOptions,function(t,e){return a("el-option",{key:e,attrs:{label:t.CompanyName,value:t.ID}})}),1),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.changeDate()}}},[t._v("生成")])],1),t._v(" "),a("div",{staticClass:"search-box",attrs:{div:""}},[a("el-button",{attrs:{type:"primary",disabled:0==t.tableData.length&&0==t.tableDataDetail.length},on:{click:t.printContent}},[t._v("打印")]),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.isTotalReportForm,expression:"isTotalReportForm"}],staticClass:"download-tips"},[a("el-button",{attrs:{type:"primary",disabled:0==t.tableData.length&&0==t.tableDataDetail.length},on:{click:t.cardChargeDownload}},[t._v("下载数据")]),t._v(" "),a("el-checkbox",{staticStyle:{"margin-top":"7px","font-size":"13px"},model:{value:t.checked,callback:function(e){t.checked=e},expression:"checked"}},[t._v("同时下载销售明细")])],1)],1),t._v(" "),a("div",{attrs:{id:"myTable"}},[a("div",{staticClass:"tableData reportData"},[a("div",{staticClass:"report_title"},[t._v("车队油品销售汇总表")]),t._v(" "),a("div",{staticClass:"report_header"},[t.isGroup?a("div",[t._v("\n                        集团名称："+t._s(t.getCurrentStation.label)+"\n                    ")]):a("div",[t._v("\n                        油站名称："+t._s(t.getCurrentStation.label)+"\n                    ")]),t._v(" "),a("div",[t._v("车队："+t._s(t.companyName))]),t._v(" "),a("div",[t._v("开始日期："+t._s(t.dateValue?t.dateValue[0]:""))]),t._v(" "),a("div",[t._v("结束日期："+t._s(t.dateValue?t.dateValue[1]:""))]),t._v(" "),a("div",[t._v("单位：元")])]),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"table",attrs:{data:t.tableData,border:"",size:"small",align:"center"}},[a("el-table-column",{attrs:{align:"center",prop:"oil_name",label:"油品",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"sale_liter",label:"销售量（升）",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"origin_price",label:"油品原价",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"dis_price",label:"优惠",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"pay_money",label:"支付金额",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"pay_bj",label:"支付本金",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"pay_gift",label:"支付赠金",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"pay_xyed",label:"信用额度",formatter:t.formatterCellval}})],1)],1),t._v(" "),a("div",{staticClass:"tableData reportData"},[a("div",{staticClass:"report_title"},[t._v("车队油品销售明细汇总表")]),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loadingDetail,expression:"loadingDetail"}],ref:"tableDetail",attrs:{data:t.tableDataDetail,border:"",size:"small",align:"center"}},[a("el-table-column",{attrs:{align:"center",prop:"card_no",label:"卡号",width:"160",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"card_number",label:"卡面卡号",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"card_name",label:"卡名称",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"phone",label:"手机号",width:"100",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"name",label:"持卡人",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"car_no",label:"车牌号",formatter:t.formatterCellval}}),t._v(" "),t._l(t.card_oil,function(e){return[a("el-table-column",{key:e.index,attrs:{align:"center",prop:String(e.prop),label:e.oil_name+"（升）",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{key:e.index,attrs:{align:"center",prop:String(e.prop_money),label:"支付金额",formatter:t.formatterCellval}})]}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"oil_total",label:"销量合计（升）",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"pay_money_total",label:"金额合计",formatter:t.formatterCellval}})],2),t._v(" "),a("div",{staticClass:"page_content"},[a("el-pagination",{staticClass:"page_left",attrs:{"current-page":t.page,"page-size":t.pageSize,layout:"prev, pager, next",total:t.total},on:{"current-change":t.handleCurrentChange}}),t._v(" "),a("el-pagination",{staticClass:"page_right",attrs:{"page-sizes":[20,30,40,50],"page-size":t.pageSize,layout:"total, sizes",total:t.total},on:{"size-change":t.handleSizeChange}})],1)],1),t._v(" "),a("div",{staticClass:"des_bottom"},[a("div",[t._v("制表人："+t._s(t.orderMaker))]),t._v(" "),a("div",[t._v("制表时间："+t._s(t.orderMakingTime))]),t._v(" "),a("div",[t._v("签字：")])])])]),t._v(" "),a("download-tips",{attrs:{showDownloadTips:t.showDownloadTips},on:{"update:showDownloadTips":function(e){t.showDownloadTips=e},"update:show-download-tips":function(e){t.showDownloadTips=e}}})],1)},staticRenderFns:[]};var v=a("VU/8")(u,_,!1,function(t){a("Pl/x")},"data-v-44b99bd2",null);e.default=v.exports},"Pl/x":function(t,e){}});