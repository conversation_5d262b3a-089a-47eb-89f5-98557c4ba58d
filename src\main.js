import Vue from 'vue'
import App from './App'
import router from './router'
import store from './store'
import ElementUI from 'new-wcc-ui';
import 'new-wcc-ui/lib/theme-chalk/index.css';
import moment from 'moment'
import './assets/css/unify.css'
import axios from "axios"

Vue.prototype.$axios = axios;  //把axios挂载到vue上，$axios这个名称可以自定义

Vue.prototype.$moment = moment;

import Print from 'vue-printjs'
Vue.use(Print) // 注册

Vue.use(ElementUI);

import echarts from 'echarts'
Vue.prototype.$echarts = echarts

Vue.config.productionTip = false

//列表空值替换符
Vue.prototype.formatterCellval = function(...args) {
    let value;
    if (args.length === 0) {
        value = undefined;
    } else if (args.length === 1) {
        // 通用调用，直接取第一个参数
        value = args[0];
    } else if (args.length >= 3) {
        // 表格模式: 按 Element UI 的格式化函数, 第三个参数为 cellValue
        value = args[2];
    } else {
        // 参数个数为2时，取第一个参数
        value = args[0];
    }
    return (value === undefined || value === "" || value === null) ? "--" : value;
};

Vue.prototype.urlObj = {
    local:"http://zhangkai.mp.wcc.cn/",
    test:"http://test.mp.wcc.cn/",
    preview:"http://preview.mp.weicheche.cn/",
    product:"https://mp.zhihuiyouzhan.com/"
}

Array.prototype.indexOf = function(val) {
    for (var i = 0; i < this.length; i++) {
        if (this[i] == val) return i;
    }
    return -1;
};
Array.prototype.remove = function(val) {
    var index = this.indexOf(val);
    if (index > -1) {
        this.splice(index, 1);
    }
};

//浮点数计算
//减法
Vue.prototype.accSubtr = function(arg1,arg2){
    var r1,r2,m,n;
    try{r1=arg1.toString().split(".")[1].length}catch(e){r1=0}
    try{r2=arg2.toString().split(".")[1].length}catch(e){r2=0}
    m=Math.pow(10,Math.max(r1,r2));
    //动态控制精度长度
    n=(r1>=r2)?r1:r2;
    return ((arg1*m-arg2*m)/m).toFixed(n);
}
Vue.prototype.getQueryVariable = function(variable){
    var query = window.location.href.substring(1);
    var vars = query.split("&");
    for(var i=0;i<vars.length;i++){
        var pair = vars[i].split("=");
        if(pair[0] == variable){
            return pair[1];
        }
    }
    return(false);
}

// 注册滚动条加载触发事件v-loadmore绑定
import directive from './directive'
Vue.use(directive)

/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
