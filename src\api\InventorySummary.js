import {post} from "../utils/http";
import {stringify} from "qs";

/**
 * 请求进销存汇总报表数据
 * @function fetchCardCompanyInvoicingLitre
 * @param {Object} data - 请求参数对象
 * @param {number} data.type - 必填，报表类型：1 按日，4 按班结
 * @param {number} data.start_time - 必填，开始时间（时间戳），类型为整数
 * @param {number} data.end_time - 必填，结束时间（时间戳），类型为整数
 * @param {Array<number>} [data.company_id] - 可选，车队ID数组
 * @param {Array<number>} data.stid - 必填，油站ID数组
 * @returns {Promise<AxiosResponse>} 返回请求结果的axios响应Promise对象
 */
export function fetchCardCompanyInvoicingLitre(data) {
  return post('/CardReport/getCardCompanyInvoicingLitre', stringify(data), {
    'Content-Type': 'application/x-www-form-urlencoded'
  });
}
/**
 * 下载进销存汇总报表数据
 * @function fetchCardCompanyInvoicingLitre
 * @param {Object} data - 请求参数对象
 * @param {number} data.type - 必填，报表类型：1 按日，4 按班结
 * @param {number} data.start_time - 必填，开始时间（时间戳），类型为整数
 * @param {number} data.end_time - 必填，结束时间（时间戳），类型为整数
 * @param {Array<number>} [data.company_id] - 可选，车队ID数组
 * @param {Array<number>} data.stid - 必填，油站ID数组
 * @returns {Promise<AxiosResponse>} 返回请求结果的axios响应Promise对象
 */
export function cardCompanyInvoicingLitreDownload(data) {
  return post('/CardReport/cardCompanyInvoicingLitreDownload', stringify(data), {
    'Content-Type': 'application/x-www-form-urlencoded'
  });
}

/**
 * 查询车队名称列表
 * @function fetchSimpleCompanyList
 * @param {Object} data - 请求参数对象
 * @param {string} data.page - 必填，页码，类型为字符串
 * @param {string} data.page_size - 必填，页大小，类型为字符串
 * @param {string} data.type - 必填，车队类型：1 通用，2 定额，3 定升
 * @returns {Promise<AxiosResponse>} 返回查询结果的axios响应Promise对象
 */
export function fetchSimpleCompanyList(data) {
  return post('/CompanyCard/getSimpleCompanyList', (data));
}
