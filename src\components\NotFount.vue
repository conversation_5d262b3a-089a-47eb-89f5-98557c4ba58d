<template>
    <div class="not-fount">
        <div class="content">
            <img class="icon" src="../assets/images/notfount.png" alt="">
            <p>似乎出了点小问题</p>
        </div>
    </div>
</template>

<script>
export default {
    name: 'NotFount',
    data () {
        return {
        }
    },
    mounted(){
    },
    methods: {
    },
}
</script>

<style scoped>
.not-fount{
    position: absolute;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
}
.content{
    text-align: center;
    color: #777;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
}
.content .icon{
    display: inline-block;
    width: 240px;
    height: 125px;
}
</style>