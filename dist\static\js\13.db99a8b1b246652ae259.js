webpackJsonp([13],{"0bXA":function(t,e){},bFN6:function(t,e){},v8fh:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=a("//Fk"),l=a.n(n),o=a("Xxa5"),s=a.n(o),r=a("exGp"),i=a.n(r),u=a("Dd8w"),c=a.n(u),d=a("FZmr"),_=a("NYxO"),m={name:"CardStationReport",components:{DownloadTips:d.a},data:function(){return{isTotalReportForm:!0,stationOptions:[],stationValue:[],dateValue:[],DailyTableData:[],isGroup:!0,loading:!0,start_time:"",end_time:"",orderMaker:"",orderMakingTime:"",stationName:"",params:{},showDownloadTips:!1,is_bp:!1,isOilRefundBounsListNotNull:!1,isOilBounsListNotNull:!1,oilBounsList:[],oilRefundBounsList:[]}},mounted:function(){var t=localStorage.getItem("__userInfo__")||"";this.isTotalReportForm=426!=JSON.parse(t).group_id;var e=this.$moment().subtract(1,"days").format("YYYY-MM-DD");if(this.dateValue.push(e),this.dateValue.push(e),void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.getStations(),this.getCurrentStation&&2==this.getCurrentStation.merchant_type?this.isGroup=!0:this.isGroup=!1},computed:c()({},Object(_.c)({getCurrentStation:"getCurrentStation"})),methods:{getStations:function(){var t=this;this.stationValue=[],this.$axios.post("/Stations/getStations",{}).then(function(e){200==e.status&&(t.stationOptions=e.data.data.station_info,t.stationOptions.forEach(function(e){t.stationValue.push(e.stid)}),t.createReport())})},getCheckedStation:function(){var t=this;this.stationName="";var e=this.stationValue.length;this.stationValue.forEach(function(a,n){t.stationOptions.forEach(function(l){l.stid==a&&(t.stationName+=n==e-1?l.stname:l.stname+"，")})})},createReport:function(){var t=this;return i()(s.a.mark(function e(){var a,n,l;return s.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t.oilRefundBounsList=[],t.oilBounsList=[],t.isOilRefundBounsListNotNull=!1,t.isOilBounsListNotNull=!1,t.loading=!0,0!=t.stationValue.length){e.next=8;break}return t.$message({message:"请选择油站",type:"error"}),e.abrupt("return");case 8:return t.params.type=2,t.params.station_ids=t.stationValue,t.params.start_time=t.dateValue[0]+" 00:00:00",t.params.end_time=t.dateValue[1]+" 23:59:59",e.next=14,t.getFundSummary();case 14:a=e.sent,t.loading=!1,200==a.data.status?(t.DailyTableData=a.data.data,a.data.data[0]&&(a.data.data[0].consume_bouns_oil_info&&(t.oilBounsList=a.data.data[0].consume_bouns_oil_info),a.data.data[0].consume_refund_bouns_oil_info&&(t.oilRefundBounsList=a.data.data[0].consume_refund_bouns_oil_info)),1374==(n=JSON.parse(window.localStorage.getItem("__userInfo__"))).new_group_id||1==n.new_group_id||1==n.isPlatform?(t.is_bp=!0,t.oilRefundBounsList.length>0&&(t.isOilRefundBounsListNotNull=!0),t.oilBounsList.length>0&&(t.isOilBounsListNotNull=!0)):t.is_bp=!1,t.start_time=t.dateValue[0],t.end_time=t.dateValue[1],!(l=localStorage.getItem("__userInfo__"))||""===l&&"undefined"===l||(t.orderMaker=JSON.parse(l).name),t.orderMakingTime=t.$moment().format("YYYY-MM-DD")):t.$message({message:a.data.info,type:"error"});case 17:case"end":return e.stop()}},e,t)}))()},getFundSummary:function(){var t=this;return new l.a(function(e,a){t.$axios.post("/CardReportForm/getFundSummary",t.params).then(function(t){e(t)}).catch(function(t){e(t)})})},printContent:function(){var t=document.querySelector("#myTable").innerHTML,e=document.body.innerHTML;document.body.innerHTML=t,document.getElementsByClassName("el-table__header")[0].style.width="100%",document.getElementsByClassName("el-table__header")[0].style["table-layout"]="auto",document.getElementsByClassName("el-table__body")[0].style.width="100%",document.getElementsByClassName("el-table__body")[0].style["table-layout"]="auto",window.print(),history.go(0),document.body.innerHTML=e,this.$print(this.$refs.print)},cardChargeDownload:function(){var t=this;this.$axios.post("/CardReportForm/exportFundSummary",this.params).then(function(e){200==e.data.status?t.showDownloadTips=!0:t.$message.error(e.data.info)})}},watch:{getCurrentStation:function(t,e){0!=t.merchant_type&&t.value!=e.value&&(this.getStations(),this.getCurrentStation&&2==this.getCurrentStation.merchant_type?this.isGroup=!0:this.isGroup=!1)}}},p={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"report"},[a("div",{staticClass:"content_header"},[a("div",{staticClass:"left"},[a("el-select",{staticStyle:{width:"250px"},attrs:{multiple:"","collapse-tags":"",placeholder:"请选择油站"},on:{change:t.getCheckedStation},model:{value:t.stationValue,callback:function(e){t.stationValue=e},expression:"stationValue"}},t._l(t.stationOptions,function(t){return a("el-option",{key:t.stid,attrs:{label:t.stname,value:t.stid}})}),1),t._v(" "),a("el-date-picker",{attrs:{clearable:!1,type:"daterange","default-time":["00:00:00","23:59:59"],"range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:t.dateValue,callback:function(e){t.dateValue=e},expression:"dateValue"}}),t._v(" "),a("el-button",{attrs:{type:"primary",disabled:!t.dateValue},on:{click:t.createReport}},[t._v("生成")])],1),t._v(" "),a("div",{staticClass:"right"},[a("el-button",{attrs:{type:"primary",disabled:0==t.DailyTableData.length},on:{click:t.printContent}},[t._v("打印")]),t._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:t.isTotalReportForm,expression:"isTotalReportForm"}],attrs:{type:"primary",disabled:0==t.DailyTableData.length},on:{click:t.cardChargeDownload}},[t._v("下载数据")])],1)]),t._v(" "),a("div",{attrs:{id:"myTable"}},[a("div",{staticClass:"tableData reportData"},[a("div",{staticClass:"report_title"},[t._v("储值卡油站对账汇总表")]),t._v(" "),a("div",{staticClass:"report_header"},[t.isGroup?a("div",[t._v("集团名称："+t._s(t.getCurrentStation.label))]):a("div",[t._v("油站名称："+t._s(t.getCurrentStation.label))]),t._v(" "),a("div",[t._v("开始日期："+t._s(t.start_time))]),t._v(" "),a("div",[t._v("结束日期："+t._s(t.end_time))]),t._v(" "),a("div",[t._v("单位：元")])]),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.DailyTableData,border:"",size:"small",align:"center",fit:""}},[a("el-table-column",{attrs:{align:"center",prop:"stname",label:"油站名称"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"期间充值"}},[a("el-table-column",{attrs:{align:"center",prop:"recharge_number",label:"充值笔数"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.recharge_amount).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值本金"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.recharge_capital).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值赠金"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.recharge_bouns).toFixed(2)))]}}])})],1),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"期间消费"}},[a("el-table-column",{attrs:{align:"center",prop:"consume_number",label:"消费笔数"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.consume_amount).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费本金"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.consume_capital).toFixed(2)))]}}])}),t._v(" "),t.is_bp&&t.isOilBounsListNotNull?a("el-table-column",{attrs:{align:"center",label:"消费赠金"}},[t._l(t.oilBounsList,function(e){return a("el-table-column",{key:e.index,attrs:{align:"center",label:e.oil_name,prop:e.prop,width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v(t._s(Number(a.row[e.prop]).toFixed(2)))]}}],null,!0)})}),t._v(" "),t.is_bp&&t.isOilBounsListNotNull?a("el-table-column",{attrs:{align:"center",label:"合计"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.consume_oil_total).toFixed(2)))]}}],null,!1,2399596939)}):t._e()],2):a("el-table-column",{attrs:{align:"center",label:"消费赠金"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.consume_bouns).toFixed(2)))]}}])})],1),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"期间退款"}},[a("el-table-column",{attrs:{align:"center",label:"充值退款"}},[a("el-table-column",{attrs:{align:"center",prop:"recharge_refund_number",label:"充值退款笔数"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值退款金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.recharge_refund_amount).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值退款赠金"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.recharge_refund_bouns).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"实退金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.recharge_refund_capital).toFixed(2)))]}}])})],1),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费退款"}},[a("el-table-column",{attrs:{align:"center",prop:"consume_refund_number",label:"消费退款笔数"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费退款金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.consume_refund_amount).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费退款本金"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.consume_refund_capital).toFixed(2)))]}}])}),t._v(" "),t.is_bp&&t.isOilRefundBounsListNotNull?a("el-table-column",{attrs:{align:"center",label:"消费退款赠金"}},[t._l(t.oilRefundBounsList,function(e){return a("el-table-column",{key:e.index,attrs:{align:"center",label:e.oil_name,prop:e.prop,width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v(t._s(Number(a.row[e.prop]).toFixed(2)))]}}],null,!0)})}),t._v(" "),t.is_bp&&t.isOilBounsListNotNull?a("el-table-column",{attrs:{align:"center",label:"合计"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.consume_refund_oil_total).toFixed(2)))]}}],null,!1,2586563194)}):t._e()],2):a("el-table-column",{attrs:{align:"center",label:"消费退款赠金"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.consume_refund_bouns).toFixed(2)))]}}])})],1)],1),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"余额清零"}},[a("el-table-column",{attrs:{align:"center",prop:"clear_number",label:"清零笔数"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"清零金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.clear_amount).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"清零本金"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.clear_capital_amount).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"清零赠金"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.clear_bouns_amount).toFixed(2)))]}}])})],1)],1)],1),t._v(" "),a("div",{staticClass:"table_des"},[a("div",{staticClass:"table_des_text"},[a("p",[t._v("注：")]),t._v(" "),a("div",[a("p",[t._v("1、统计油站时间范围内产生的储值卡数据，含跨站消费、充值、退款数据。")]),t._v(" "),t.stationValue.length!=t.stationOptions.length&&t.isGroup?a("p",{staticClass:"stations"},[t._v("2、取数油站："+t._s(t.stationName))]):t._e()])])]),t._v(" "),a("div",{staticClass:"des_bottom"},[a("div",[t._v("制表人："+t._s(t.orderMaker))]),t._v(" "),a("div",[t._v("制表时间："+t._s(t.orderMakingTime))]),t._v(" "),a("div",[t._v("签字：")])])]),t._v(" "),a("download-tips",{attrs:{showDownloadTips:t.showDownloadTips},on:{"update:showDownloadTips":function(e){t.showDownloadTips=e},"update:show-download-tips":function(e){t.showDownloadTips=e}}})],1)},staticRenderFns:[]};var b=a("VU/8")(m,p,!1,function(t){a("0bXA"),a("bFN6")},"data-v-cfd18b78",null);e.default=b.exports}});