webpackJsonp([43],{g53n:function(e,a,t){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var r=t("Xxa5"),o=t.n(r),s=t("exGp"),l=t.n(s),n=t("Dd8w"),i=t.n(n),u=(t("oj3p"),t("3pLw"));function m(e){return Object(u.c)("/CustomerGroup/updateCompanyType",e)}var d=t("FZmr"),c=t("PJh5"),p=t.n(c),g=t("NYxO"),y=t("M4fF"),f=t("iJJg"),b=[{label:"全部",value:""},{label:"启用",value:f.b},{label:"禁用",value:f.a}],v={components:{DownloadTips:d.a},computed:i()({},Object(g.d)(["currentStation"]),{currentGroupId:function(){return this.currentStation.group_id}}),data:function(){return{STATUS_DISABLED:f.a,STATUS_OPEN:f.b,query:{customerNo:"",customerType:"",createdTime:[p()().subtract(1,"year").format("YYYY-MM-DD 00:00:00"),p()().format("YYYY-MM-DD 23:59:59")],status:"",loading:!1,downloading:!1,downloadDone:!1},dialogParams:{visible:!1,form:{customerNo:"",customerTypeName:"",status:f.b,remark:""},loading:!1},tableData:{data:[],total:0,currentPage:1,pageSize:10},tableColumns:[{prop:"CompanyTypeID",label:"序号",width:"80"},{prop:"BH",label:"客户编号",width:"200"},{prop:"CompanyTypeName",label:"客户类型",width:"150"},{prop:"Status",label:"状态",width:"100",formatter:function(e){var a=b.find(function(a){return e.Status===a.value});return a&&a.label?a.label:e.Status}},{prop:"Remark",label:"备注"},{prop:"CreateTime",label:"创建时间",width:"180"},{prop:"UpdateTime",label:"更新时间",width:"180"}],rules:{customerNo:[{required:!0,message:"请输入客户编号",trigger:"blur"},{max:30,message:"客户编号不能超过30个字符",trigger:"blur"},{pattern:/^[a-zA-Z0-9]+$/,message:"仅支持大小写字母+数字，请重新输入",trigger:"blur"}],customerTypeName:[{required:!0,message:"请输入客户类型名称",trigger:"blur"},{max:30,message:"客户类型名称不能超过30个字符",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}],remark:[{max:100,message:"备注不能超过100个字符",trigger:"blur"}]},statusOptions:b}},watch:{currentGroupId:{deep:!0,immediate:!0,handler:function(e,a){e!==a&&(this.query.page=1,this.fetchData())}}},methods:{showAddDialog:function(){this.dialogParams.visible=!0,this.$refs.addFormRef.resetFields(),this.dialogParams.form={customerNo:"",customerTypeName:"",status:f.b,remark:""}},closeAddDialog:function(){this.dialogParams.visible=!1},generateCustomerNo:function(){this.dialogParams.form.customerNo=""+Date.now()+Math.floor(1e4*Math.random()),this.$refs.addFormRef.validateField("customerNo")},submitAddForm:function(){var e=this;if(this.dialogParams.form.companyTypeID){var a=this.dialogParams.form,t={bh:a.customerNo,remark:a.remark,group_id:this.currentGroupId,type_id:a.companyTypeID,type:a.customerTypeName,status:a.status};this.editCustomerType(t)}else{var r;this.$refs.addFormRef.validate((r=l()(o.a.mark(function a(t){var r,s;return o.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(!t){a.next=22;break}return a.prev=1,e.dialogParams.loading=!0,r={bh:e.dialogParams.form.customerNo,type:e.dialogParams.form.customerTypeName,group_id:e.currentGroupId,remark:e.dialogParams.form.remark,status:e.dialogParams.form.status},a.next=6,o=r,Object(u.c)("/CustomerGroup/addCompanyType",o);case 6:if(200===(s=a.sent).status){a.next=10;break}return e.$message.error(s.info||"添加客户类型失败"),a.abrupt("return");case 10:e.closeAddDialog(),e.tableData.page=1,e.fetchData(),a.next=19;break;case 15:a.prev=15,a.t0=a.catch(1),console.log("=>(CustomerType.vue:257) 添加客户类型失败 ",a.t0),e.$message.error("添加客户类型失败");case 19:return a.prev=19,e.dialogParams.loading=!1,a.finish(19);case 22:case"end":return a.stop()}var o},a,e,[[1,15,19,22]])})),function(e){return r.apply(this,arguments)}))}},fetchData:function(){var e=this;return l()(o.a.mark(function a(){var t,r;return o.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return e.query.loading=!0,a.prev=1,t={group_id:e.currentStation.group_id,page:e.tableData.currentPage,page_size:e.tableData.pageSize,bh:e.query.customerNo,type:e.query.customerType,status:e.query.status,s_time:e.query.createdTime&&e.query.createdTime[0]||"",e_time:e.query.createdTime&&e.query.createdTime[1]||""},a.next=5,o=t,Object(u.b)("/CustomerGroup/getCompanyTypeList",o);case 5:if(r=a.sent,console.log("=>(CustomerType.vue:232) res",r),r.status===u.a.SUCCESS){a.next=11;break}return e.$message({type:"error",message:r.info||"获取数据异常"}),console.log("=>(CustomerType.vue:235) 获取数据异常",r),a.abrupt("return");case 11:e.tableData.data=r.data.list,e.tableData.total=r.data.page_info.count,a.next=18;break;case 15:a.prev=15,a.t0=a.catch(1),console.log("=>(CustomerType.vue:257) 获取数据异常",a.t0);case 18:return a.prev=18,e.query.loading=!1,a.finish(18);case 21:case"end":return a.stop()}var o},a,e,[[1,15,18,21]])}))()},downloadData:function(){var e=this;return l()(o.a.mark(function a(){var t,r;return o.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.prev=0,e.query.downloading=!0,t={group_id:e.currentGroupId,bh:e.query.customerNo,type:e.query.customerType,status:e.query.status,s_time:e.query.createdTime[0],e_time:e.query.createdTime[1]},a.next=5,o=t,Object(u.c)("/CustomerGroup/downloadCompanyType",o,{responseType:"blob"});case 5:if(r=a.sent,console.log("=>(CustomerType.vue:252) res",r),e.query.downloadDone=r&&r.status===u.a.SUCCESS,r.status===u.a.SUCCESS){a.next=11;break}return e.$message.error(r.info||"下载数据异常"),a.abrupt("return");case 11:a.next=17;break;case 13:a.prev=13,a.t0=a.catch(0),console.log("=>(CustomerType.vue:237) 下载数据失败",a.t0),e.$message.error("下载数据失败，请稍后重试"+a.t0.status);case 17:return a.prev=17,e.query.downloading=!1,a.finish(17);case 20:case"end":return a.stop()}var o},a,e,[[0,13,17,20]])}))()},handlePageChange:function(e){this.tableData.currentPage=e,this.fetchData()},handleSizeChange:function(e){this.tableData.page=1,this.tableData.pageSize=e,this.fetchData()},handleEdit:function(e){this.dialogParams.visible=!0,this.dialogParams.form={customerNo:e.BH,customerTypeName:e.CompanyTypeName,status:e.Status,remark:e.Remark,companyTypeID:e.CompanyTypeID}},handleStatusChange:function(e){var a={bh:e.BH,remark:e.Remark,group_id:this.currentGroupId,type_id:e.CompanyTypeID,type:e.CompanyTypeName,status:e.Status===f.b?f.a:f.b};console.log("=>(CustomerType.vue:368) row.Status",e.Status),this.editCustomerType(a)},editCustomerType:function(e){var a=this;return l()(o.a.mark(function t(){var r;return o.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!Object(y.isEmpty)(e)){t.next=3;break}return a.$message.error("更改客户类型数据异常"),t.abrupt("return");case 3:return t.prev=3,a.query.loading=!a.dialogParams.visible,a.dialogParams.loading=!0,t.next=8,m(e);case 8:if((r=t.sent).status===u.a.SUCCESS){t.next=12;break}return a.$message.error(r.info||"更改客户类型数据异常"),t.abrupt("return");case 12:a.fetchData(),a.dialogParams.visible=!1,t.next=20;break;case 16:t.prev=16,t.t0=t.catch(3),console.log("=>(CustomerType.vue:257) 更改客户类型数据异常",t.t0),a.$message.error("更改客户类型数据异常");case 20:return t.prev=20,a.query.loading=!1,a.dialogParams.loading=!1,t.finish(20);case 24:case"end":return t.stop()}},t,a,[[3,16,20,24]])}))()}}},h={render:function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"pt-4 space-y-4"},[t("el-form",{staticClass:"demo-form-inline",attrs:{model:e.query,inline:!0}},[t("el-form-item",{attrs:{label:"客户编号"}},[t("el-input",{attrs:{placeholder:"请输入客户编号",maxlength:"30",clearable:""},model:{value:e.query.customerNo,callback:function(a){e.$set(e.query,"customerNo",a)},expression:"query.customerNo"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"客户类型"}},[t("el-input",{attrs:{placeholder:"请输入客户类型",maxlength:"30",clearable:""},model:{value:e.query.customerType,callback:function(a){e.$set(e.query,"customerType",a)},expression:"query.customerType"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"时间范围"}},[t("el-date-picker",{attrs:{type:"datetimerange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"],clearable:!1,"picker-options":{disabledDate:function(a){return a.getTime()>e.$moment().set({m:59,s:59,h:23,ms:999}).valueOf()}}},model:{value:e.query.createdTime,callback:function(a){e.$set(e.query,"createdTime",a)},expression:"query.createdTime"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"状态"}},[t("el-select",{attrs:{placeholder:"请选择状态"},model:{value:e.query.status,callback:function(a){e.$set(e.query,"status",a)},expression:"query.status"}},e._l(e.statusOptions,function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),t("el-form-item",[t("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.query.loading,expression:"query.loading"}],attrs:{type:"primary",disabled:e.query.loading},on:{click:function(a){e.tableData.currentPage=1,e.fetchData()}}},[e._v("查询\n      ")]),e._v(" "),t("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.query.downloading,expression:"query.downloading"}],key:"downloadData",attrs:{type:"primary",disabled:e.query.downloading},on:{click:e.downloadData}},[e._v("\n        下载数据\n      ")])],1)],1),e._v(" "),t("el-button",{attrs:{type:"primary"},on:{click:e.showAddDialog}},[e._v("添加客户类型")]),e._v(" "),t("el-dialog",{attrs:{visible:e.dialogParams.visible,"close-on-press-escape":!1,"close-on-click-modal":!1,title:(e.dialogParams.form.companyTypeID?"编辑":"添加")+"客户类型",width:"500px","destroy-on-close":!0},on:{"update:visible":function(a){return e.$set(e.dialogParams,"visible",a)}}},[t("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.dialogParams.loading,expression:"dialogParams.loading"}],ref:"addFormRef",attrs:{model:e.dialogParams.form,rules:e.rules,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"客户编号",prop:"customerNo"}},[t("div",{staticClass:"flex"},[t("el-input",{attrs:{disabled:e.dialogParams.form.companyTypeID,placeholder:"请输入客户编号",maxlength:"30",clearable:""},model:{value:e.dialogParams.form.customerNo,callback:function(a){e.$set(e.dialogParams.form,"customerNo",a)},expression:"dialogParams.form.customerNo"}}),e._v(" "),t("el-button",{attrs:{disabled:e.dialogParams.form.companyTypeID,type:"primary"},on:{click:e.generateCustomerNo}},[e._v("自动生成\n          ")])],1)]),e._v(" "),t("el-form-item",{attrs:{label:"客户类型名称",prop:"customerTypeName"}},[t("el-input",{attrs:{disabled:e.dialogParams.form.companyTypeID,placeholder:"请输入客户类型名称",maxlength:"30",clearable:""},model:{value:e.dialogParams.form.customerTypeName,callback:function(a){e.$set(e.dialogParams.form,"customerTypeName",a)},expression:"dialogParams.form.customerTypeName"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"状态",prop:"status"}},[t("el-radio-group",{model:{value:e.dialogParams.form.status,callback:function(a){e.$set(e.dialogParams.form,"status",a)},expression:"dialogParams.form.status"}},[t("el-radio",{attrs:{label:"100"}},[e._v("启用")]),e._v(" "),t("el-radio",{attrs:{label:"102"}},[e._v("禁用")])],1)],1),e._v(" "),t("el-form-item",{attrs:{label:"备注信息",prop:"remark"}},[t("el-input",{attrs:{type:"textarea",rows:"7",placeholder:"请输入备注信息",maxlength:"100","show-word-limit":""},model:{value:e.dialogParams.form.remark,callback:function(a){e.$set(e.dialogParams.form,"remark",a)},expression:"dialogParams.form.remark"}})],1)],1),e._v(" "),t("template",{slot:"footer"},[t("span",{staticClass:"dialog-footer"},[t("el-button",{attrs:{disabled:e.dialogParams.loading},on:{click:e.closeAddDialog}},[e._v("取消")]),e._v(" "),t("el-button",{attrs:{disabled:e.dialogParams.loading,type:"primary"},on:{click:e.submitAddForm}},[e._v(e._s(e.dialogParams.form.companyTypeID?"保存":"添加"))])],1)])],2),e._v(" "),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.query.loading,expression:"query.loading"}],attrs:{border:"",data:e.tableData.data}},[e._l(e.tableColumns,function(e){return t("el-table-column",{key:e.prop,attrs:{prop:e.prop,label:e.label,width:e.width,formatter:e.formatter}})}),e._v(" "),t("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.Status===e.STATUS_DISABLED?t("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleEdit(a.row)}}},[e._v("\n          编辑\n        ")]):e._e(),e._v(" "),t("el-button",{attrs:{type:a.row.Status===e.STATUS_OPEN?"danger":"success",size:"small"},on:{click:function(t){return e.handleStatusChange(a.row)}}},[e._v("\n          "+e._s(a.row.Status===e.STATUS_OPEN?"禁用":"启用")+"\n        ")])]}}])})],2),e._v(" "),t("el-pagination",{attrs:{"current-page":e.tableData.currentPage,"page-sizes":[5,10,20,30,40],"page-size":e.tableData.pageSize,total:e.tableData.total,layout:"total, sizes, prev, pager, next, jumper"},on:{"current-change":e.handlePageChange,"size-change":e.handleSizeChange}}),e._v(" "),t("download-tips",{attrs:{showDownloadTips:e.query.downloadDone},on:{"update:showDownloadTips":function(a){return e.$set(e.query,"downloadDone",a)},"update:show-download-tips":function(a){return e.$set(e.query,"downloadDone",a)}}})],1)},staticRenderFns:[]},T=t("VU/8")(v,h,!1,null,null,null);a.default=T.exports}});