webpackJsonp([8],{"7Otq":function(e,t){e.exports="data:image/png;base64,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"},KPWV:function(e,t){},avFL:function(e,t){},bwGm:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=n("Gu7T"),o=n.n(a),r=n("mvHQ"),i=n.n(r),s=n("Dd8w"),c=n.n(s),h=n("Xxa5"),u=n.n(h),l=n("exGp"),p=n.n(l),d=(n("txcq"),n("pDRr")),m=n("NYxO"),v=(n("oj3p"),{name:"PasswordExpireCheck",data:function(){var e=this;return{loading:!1,dialogVisible:!1,passwordDialogVisible:!1,dialogContent:"",passwordForm:{oldPassword:"",newPassword:"",confirmPassword:""},rules:{oldPassword:[{required:!0,message:"请输入原密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{validator:function(e,t,n){t.length<10?n(new Error("密码不少于10个字符")):/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^]{10,}$/.test(t)?n():n(new Error("密码需要同时包含大写字母、小写字母和数字"))},trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入新密码",trigger:"blur"},{validator:function(t,n,a){n!==e.passwordForm.newPassword?a(new Error("两次输入的密码不一致")):a()},trigger:"blur"}]}}},methods:{checkPasswordStatus:function(){var e=this;return p()(u.a.mark(function t(){var n,a,o,r;return u.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!localStorage.getItem("pwd_expire_reminder")){t.next=2;break}return t.abrupt("return",!1);case 2:return t.prev=2,t.next=5,e.$axios.post("/Setting/getModifyPasswordConfig");case 5:if(200!==(n=t.sent).data.status||!n.data.data){t.next=13;break}if(a=n.data.data,o=a.period,r=a.current_config,"0000"!==o){t.next=10;break}return t.abrupt("return",!1);case 10:return e.dialogContent=""===o?"集团设置了账户初始密码过期天数，当前账号初始密码超过"+r+"天未修改，为了您的账户安全，请修改后重新登录。":"温馨提示：集团设置了账户初始密码过期天数，当前账号初始密码将于"+o+"过期，为了您的账户安全，请修改后重新登录",e.dialogVisible=!0,t.abrupt("return",!0);case 13:return t.abrupt("return",!1);case 16:return t.prev=16,t.t0=t.catch(2),console.error("检查密码状态失败:",t.t0),e.$message.error("网络错误，请稍后重试"),t.abrupt("return",!1);case 21:case"end":return t.stop()}},t,e,[[2,16]])}))()},logout:function(){var e=this;e.$axios.post("/ssoLogin/logout",{}).then(function(t){if(200==t.data.status){localStorage.removeItem("__userInfo__"),sessionStorage.removeItem("__userInfo__"),localStorage.removeItem("options"),localStorage.removeItem("currentStation");var n=window.location.href.split("?")[0],a=window.location.href;-1!=a.indexOf("localhost:8080")?window.location.href=d.a.ssoTest+"/v1/login?redirect="+encodeURIComponent(n):-1!=a.indexOf("test-card-admin.wcc.cn")?window.location.href=d.a.ssoTest+"/v1/login?redirect="+encodeURIComponent(n):-1!=a.indexOf("preview-card-admin.weicheche.cn")?window.location.href=d.a.ssoPreview+"/v1/login?redirect="+encodeURIComponent(n):-1!=a.indexOf("card-admin.zhihuiyouzhan.com")?window.location.href=d.a.ssoProduct+"/v1/login?redirect="+encodeURIComponent(n):window.location.href=d.a.ssoTest+"/v1/login?redirect="+encodeURIComponent(n)}else e.$message({message:t.data.info,type:"error"})}).catch(function(e){})},handleConfirm:function(){this.dialogVisible=!1,this.passwordDialogVisible=!0},handleDialogClose:function(){localStorage.setItem("pwd_expire_reminder","1")},submitPasswordChange:function(){var e,t=this;this.$refs.passwordForm.validate((e=p()(u.a.mark(function e(n){var a,o;return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!n){e.next=17;break}return e.prev=1,t.loading=!0,a=JSON.parse(localStorage.getItem("__userInfo__")||"{}"),e.next=6,t.$axios.post("/Setting/changePwd",{adid:a.adid,new_pwd:t.passwordForm.newPassword,orig_pwd:t.passwordForm.oldPassword});case 6:200===(o=e.sent).data.status?(localStorage.removeItem("pwd_expire_reminder"),t.$message.success("修改成功,即将退出登录,需重新登录"),t.passwordDialogVisible=!1,setTimeout(function(){t.logout()},1e3)):t.$message.error(o.data.info||"密码修改失败"),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(1),console.error("修改密码失败:",e.t0),t.$message.error("网络错误，请稍后重试");case 14:return e.prev=14,t.loading=!1,e.finish(14);case 17:case"end":return e.stop()}},e,t,[[1,10,14,17]])})),function(t){return e.apply(this,arguments)}))},resetForm:function(){this.passwordForm={oldPassword:"",newPassword:"",confirmPassword:""},this.$refs.passwordForm.resetFields()}}}),w={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-dialog",{attrs:{visible:e.dialogVisible,"close-on-click-modal":!1,"close-on-press-escape":!1,width:"400px",center:""},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleDialogClose}},[n("div",{staticClass:"dialog-title",attrs:{slot:"title"},slot:"title"},[e._v("\n      密码过期提醒\n    ")]),e._v(" "),n("div",{staticClass:"dialog-content"},[e._v("\n      "+e._s(e.dialogContent)+"\n    ")]),e._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:e.handleConfirm}},[e._v("确认修改")])],1)]),e._v(" "),n("el-dialog",{attrs:{title:"修改登录密码",visible:e.passwordDialogVisible,"close-on-click-modal":!1,"close-on-press-escape":!1,width:"500px"},on:{"update:visible":function(t){e.passwordDialogVisible=t}}},[n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[n("el-form",{ref:"passwordForm",attrs:{model:e.passwordForm,rules:e.rules,"label-width":"100px"}},[n("el-form-item",{attrs:{label:"原密码",prop:"oldPassword"}},[n("el-input",{attrs:{type:"password","show-password":"",maxlength:"32"},model:{value:e.passwordForm.oldPassword,callback:function(t){e.$set(e.passwordForm,"oldPassword",t)},expression:"passwordForm.oldPassword"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"新密码",prop:"newPassword"}},[n("el-input",{attrs:{type:"password","show-password":"",maxlength:"32"},model:{value:e.passwordForm.newPassword,callback:function(t){e.$set(e.passwordForm,"newPassword",t)},expression:"passwordForm.newPassword"}}),e._v(" "),n("div",{staticClass:"form-tip"},[e._v("密码需要10位及以上，同时包含大写字母、小写字母和数字")])],1),e._v(" "),n("el-form-item",{attrs:{label:"确认密码",prop:"confirmPassword"}},[n("el-input",{attrs:{type:"password","show-password":"",maxlength:"32"},model:{value:e.passwordForm.confirmPassword,callback:function(t){e.$set(e.passwordForm,"confirmPassword",t)},expression:"passwordForm.confirmPassword"}})],1)],1),e._v(" "),n("div",{staticClass:"flex justify-end"},[n("el-button",{on:{click:function(t){e.passwordDialogVisible=!1}}},[e._v("取 消")]),e._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:e.submitPasswordChange}},[e._v("确 定")])],1)],1)])],1)},staticRenderFns:[]};var f={name:"Navigation",components:{PasswordExpireCheck:n("VU/8")(v,w,!1,function(e){n("xe9m")},"data-v-7d693e53",null).exports},data:function(){return{name:"",currentStation:{merchant_type:"",merchant_id:"",value:"",label:"",pid:"",group_id:""},currentStationValue:[],currentIndex:"/index",isShowInfo:!1,breadcrumb:{current:"首页",prev:[]},menuList:[{entity:{name:"首页",path:"/index",icon:"el-icon-house",prev:[],show:!0}},{entity:{name:"卡管理",path:"/CardManager",icon:"el-icon-bank-card",prev:[],show:!0}},{entity:{name:"订单管理",path:"/3",icon:"el-icon-notebook-1",prev:[],show:!1},childs:[{entity:{name:"充值管理",path:"/Refund",icon:"el-icon-menu",prev:["订单管理"],show:!1}},{entity:{name:"资金流水",path:"/CapitalRecord",icon:"el-icon-menu",prev:["订单管理"],show:!0}}]},{entity:{name:"报表管理",path:"/4",icon:"el-icon-data-line",prev:[],show:!1},childs:[{entity:{name:"车队充值开票汇总报表",path:"/FleetTopUpInvoiceReport",icon:"el-icon-menu",prev:["报表管理"],show:!1}},{entity:{name:"充值资金日报表",path:"/Report",icon:"el-icon-menu",prev:["报表管理"],show:!0}},{entity:{name:"充值收付款流水表",path:"/RechargeRecodeList",icon:"el-icon-menu",prev:["报表管理"],show:!0}},{entity:{name:"消费明细报表",path:"/SpendDetailReport",icon:"el-icon-menu",prev:["报表管理"],show:!1}},{entity:{name:"新开卡用户统计报表",path:"/newUserReport",icon:"el-icon-menu",prev:["报表管理"],show:!0}},{entity:{name:"资金进销存报表",path:"/CapitalJXC",icon:"el-icon-menu",prev:["报表管理"],show:!1}},{entity:{name:"车队进销存报表",path:"/fleetJXC",icon:"el-icon-menu",prev:["报表管理"],show:!1}},{entity:{name:"车队开票汇总报表",path:"/fleetInvoiceReport",icon:"el-icon-menu",prev:["报表管理"],show:!1}},{entity:{name:"资金对账报表",path:"/4-1",icon:"",show:!1},childs:[{entity:{name:"资金对账汇总表",path:"/CardCapitalDayReport",prev:["资金对账报表"],show:!0}},{entity:{name:"油站对账汇总表",path:"/CardStationReport",prev:["资金对账报表"],show:!0}}]},{entity:{name:"客户资金对账报表",path:"/CardCustomerReport",icon:"el-icon-menu",prev:["报表管理"],show:!0}},{entity:{name:"客户消费变动明细表",path:"/CustomerSpendReport",icon:"el-icon-menu",prev:["报表管理"],show:!1}},{entity:{name:"车队油品销售报表",path:"/customerOilReport",icon:"el-icon-menu",prev:["报表管理"],show:!1}},{entity:{name:"站间清结算报表",path:"/BetweenStationsReport",icon:"el-icon-menu",prev:["报表管理"],show:!1}},{entity:{name:"集团清结算报表",path:"/GroupReport",icon:"el-icon-menu",prev:["报表管理"],show:!1}},{entity:{name:"定升车队进销存汇总表",path:"/InventorySummary",icon:"el-icon-menu",prev:["报表管理"],show:!0}}]},{entity:{name:"车队管理",path:"/6",icon:"el-icon-office-building",prev:[],show:!1},childs:[{entity:{name:"车队信息",path:"/EnterpriseInformationManagement",icon:"el-icon-menu",prev:["车队管理"],show:!0}},{entity:{name:"车队资金管理",path:"/CardTransfer",icon:"el-icon-menu",prev:["车队管理"],show:!0}},{entity:{name:"车队资金流水",path:"/GroupCapitalRecord",icon:"el-icon-menu",prev:["车队管理"],show:!1}},{entity:{name:"车队充值审核",path:"/RechargeApproval",icon:"el-icon-menu",prev:["车队管理"],show:this.showRechargeApproval&&1==this.$store.getters.rechargeApprovalPrivilege.is_has}}]},{entity:{name:"卡组管理",path:"/CustomerGroupManagement",icon:"el-icon-money",prev:[],show:!1}},{entity:{name:"发票管理",path:"/8",icon:"el-icon-tickets",prev:[],show:!1},childs:[{entity:{name:"普通发票管理",path:"/RegularInvoice",icon:"el-icon-menu",prev:["发票管理"],show:!0}},{entity:{name:"增值税专用发票管理",path:"/TaxInvoice",icon:"el-icon-menu",prev:["发票管理"],show:!0}},{entity:{name:"客户发票信息管理",path:"/CustomerInvoices",icon:"el-icon-menu",prev:["发票管理"],show:!0}}]},{entity:{name:"规则管理",path:"/9",icon:"el-icon-document",prev:[],show:!1},childs:[{entity:{name:"充值营销规则",path:"/MarketingRules",icon:"el-icon-menu",prev:["规则管理"],show:!1}},{entity:{name:"单价锁定",path:"/unitPriceLock",icon:"el-icon-menu",prev:["充值营销规则"],show:!1}},{entity:{name:"充值规则",path:"/CardRule",icon:"el-icon-menu",prev:["规则管理"],show:!0}},{entity:{name:"制卡规则",path:"/CardTheme",icon:"el-icon-menu",prev:["规则管理"],show:!0}}]},{entity:{name:"卡系统配置",path:"/Setting",icon:"el-icon-setting",prev:[],show:!1}},{entity:{name:"下载中心",path:"/DownloadList",icon:"el-icon-download",prev:[],show:!1}},{entity:{name:"操作日志",path:"/12",icon:"el-icon-notebook-2",prev:[],show:!1},childs:[{entity:{name:"平台操作日志",path:"/CardLog",icon:"el-icon-menu",prev:["操作日志"],show:!0}},{entity:{name:"手工账务处理日志",path:"/HandMadeLog",icon:"el-icon-menu",prev:["操作日志"],show:!0}}]}],options:[],value:"",new_group_id:-1,isGroupSettle:0,groupSettleStid:-1,showMP:!0,showYZ:!0,showFJ:!0,year:2017,new_customer_report:!0,recharge_batch:!0,show_company_invoice:!1,show_customerSpendReport:!1,show_fleet_topup_invoice:!1}},created:function(){var e=this;return p()(u.a.mark(function t(){return u.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:e.year=(new Date).getFullYear();case 1:case"end":return t.stop()}},t,e)}))()},mounted:function(){var e=this;return p()(u.a.mark(function t(){var n,a,o;return u.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(n=e,!(a=localStorage.getItem("__userInfo__")||"")||""==a||"undefined"==a){t.next=6;break}return n.name=JSON.parse(a).name,t.next=6,n.getCompany();case 6:if(o=e._getRouterDetail(e.$route.path)){t.next=9;break}return t.abrupt("return");case 9:e.breadcrumb={current:o.name,prev:o.prev},e.changeEditableTabs({title:e.getRouterName(e.$router.currentRoute.path),name:e.tabIndex,router:e.$router.currentRoute.path,current:o.name,prev:o.prev}),e.changeCacheArray(e.$router.currentRoute.path.substr(1));case 12:case"end":return t.stop()}},t,e)}))()},computed:c()({myUrl:function(){var e=window.location.href;return-1!=e.indexOf("localhost:8080")?this.urlObj.local:-1!=e.indexOf("test-card-admin.wcc.cn")?this.urlObj.test:-1!=e.indexOf("preview-card-admin.weicheche.cn")?this.urlObj.preview:(e.indexOf("card-admin.zhihuiyouzhan.com"),this.urlObj.product)},currentRoute:function(){return this.$route.path},editableTabsValue:{get:function(){return this.$store.getters.getEditableTabsValue},set:function(e){this.changeEditableTabsValue(e)}},editableTabs:{get:function(){return this.$store.getters.getEditableTabs},set:function(e){this.$store.commit("SETEDITABLETABS",e)}},tabIndex:{get:function(){return this.$store.getters.getTabIndex},set:function(e){this.$store.commit("SETTABINDEX",e)}},cacheArray:{get:function(){return this.$store.getters.getCacheArray},set:function(e){this.$store.commit("SETCACHEARRAY",e)}}},Object(m.c)({getCurrentStation:"getCurrentStation"}),Object(m.c)(["showLockPrice","showRechargeApproval"])),methods:c()({},Object(m.b)({changeCurrentStation:"changeCurrentStation",changeEditableTabsValue:"changeEditableTabsValue",changeEditableTabs:"changeEditableTabs",changeCacheArray:"changeCacheArray"}),{fetchRoleJurisdiction:function(){var e=this;return p()(u.a.mark(function t(){var n,a,o;return u.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.$axios.get("/CardHome/getRoleJurisdiction");case 3:200===(n=(n=t.sent).data).status?e.$store.commit("SET_ROLE_JURISDICTION",n.data):(a="获取角色权限失败，请稍后重试",n.info?a="获取角色权限失败，"+n.info:n.message&&(a="获取角色权限失败，"+n.message),console.error("获取角色权限失败: HTTP status code is not 200",n),e.$message.error(a)),t.next=14;break;case 8:t.prev=8,t.t0=t.catch(0),o="获取角色权限失败，请稍后重试",t.t0.message&&(o=t.t0.message),console.error("获取角色权限失败:",t.t0),e.$message.error(o);case 14:case"end":return t.stop()}},t,e,[[0,8]])}))()},getMenu:function(){var e=localStorage.getItem("__userInfo__")||"",t=JSON.parse(e);this.new_group_id=e?t.new_group_id:-1,1545==t.group_id&&(this.showMP=!1),1321==t.group_id&&(this.showYZ=!1),this.menuList=[{entity:{name:"首页",path:"/index",icon:"el-icon-house",prev:[],show:!0}},{entity:{name:"卡管理",path:"/CardManager",icon:"el-icon-bank-card",prev:[],show:!0}},{entity:{name:"订单管理",path:"/3",icon:"el-icon-notebook-1",prev:[],show:this.showYZ},childs:[{entity:{name:"充值管理",path:"/Refund",icon:"el-icon-menu",prev:["订单管理"],show:this.showFJ}},{entity:{name:"资金流水",path:"/CapitalRecord",icon:"el-icon-menu",prev:["订单管理"],show:!0}}]},{entity:{name:"报表管理",path:"/4",icon:"el-icon-data-line",prev:[],show:0===t.has_mch_id&&this.showYZ&&this.showFJ},childs:[{entity:{name:"车队充值开票汇总报表",path:"/FleetTopUpInvoiceReport",icon:"el-icon-menu",prev:["报表管理"],show:this.show_fleet_topup_invoice}},{entity:{name:"充值资金日报表",path:"/Report",icon:"el-icon-menu",prev:["报表管理"],show:!0}},{entity:{name:"充值收付款流水表",path:"/RechargeRecodeList",icon:"el-icon-menu",prev:["报表管理"],show:!0}},{entity:{name:"消费明细报表",path:"/SpendDetailReport",icon:"el-icon-menu",prev:["报表管理"],show:this.showMP}},{entity:{name:"新开卡用户统计报表",path:"/newUserReport",icon:"el-icon-menu",prev:["报表管理"],show:!0}},{entity:{name:"资金进销存报表",path:"/CapitalJXC",icon:"el-icon-menu",prev:["报表管理"],show:this.showMP}},{entity:{name:"车队进销存报表",path:"/fleetJXC",icon:"el-icon-menu",prev:["报表管理"],show:this.show_company_invoice}},{entity:{name:"车队开票汇总报表",path:"/fleetInvoiceReport",icon:"el-icon-menu",prev:["报表管理"],show:1===this.$store.state.groupBaseInfo.show_company_invoice_report}},{entity:{name:"资金对账报表",path:"/4-1",icon:"",show:this.showMP},childs:[{entity:{name:"资金对账汇总表",path:"/CardCapitalDayReport",prev:["资金对账报表"],show:!0}},{entity:{name:"油站对账汇总表",path:"/CardStationReport",prev:["资金对账报表"],show:!0}}]},{entity:{name:"客户资金对账报表",path:"/CardCustomerReport",icon:"el-icon-menu",prev:["报表管理"],show:!0}},{entity:{name:"客户消费变动明细表",path:"/CustomerSpendReport",icon:"el-icon-menu",prev:["报表管理"],show:this.show_customerSpendReport}},{entity:{name:"车队油品销售报表",path:"/customerOilReport",icon:"el-icon-menu",prev:["报表管理"],show:this.new_customer_report}},{entity:{name:"站间清结算报表",path:"/BetweenStationsReport",icon:"el-icon-menu",prev:["报表管理"],show:0==this.new_group_id||0==this.isGroupSettle}},{entity:{name:"集团清结算报表",path:"/GroupReport",icon:"el-icon-menu",prev:["报表管理"],show:0==this.new_group_id||1==this.isGroupSettle}},{entity:{name:"定升车队进销存汇总表",path:"/InventorySummary",icon:"el-icon-menu",prev:["报表管理"],show:this.showLockPrice}}]},{entity:{name:"车队管理",path:"/6",icon:"el-icon-office-building",prev:[],show:this.showFJ},childs:[{entity:{name:"车队信息",path:"/EnterpriseInformationManagement",icon:"el-icon-menu",prev:["车队管理"],show:!0}},{entity:{name:"车队资金管理",path:"/CardTransfer",icon:"el-icon-menu",prev:["车队管理"],show:!0}},{entity:{name:"车队资金流水",path:"/GroupCapitalRecord",icon:"el-icon-menu",prev:["车队管理"],show:this.showYZ}},{entity:{name:"客户类型",path:"/CustomerType",icon:"el-icon-menu",prev:["车队管理"],show:!0}},{entity:{name:"车队充值审核",path:"/RechargeApproval",icon:"el-icon-menu",prev:["车队管理"],show:this.showRechargeApproval&&1==this.$store.getters.rechargeApprovalPrivilege.is_has}}]},{entity:{name:"卡组管理",path:"/CustomerGroupManagement",icon:"el-icon-money",prev:[],show:this.showFJ}},{entity:{name:"发票管理",path:"/8",icon:"el-icon-tickets",prev:[],show:this.showYZ&&this.showFJ},childs:[{entity:{name:"普通发票管理",path:"/RegularInvoice",icon:"el-icon-menu",prev:["发票管理"],show:!0}},{entity:{name:"增值税专用发票管理",path:"/TaxInvoice",icon:"el-icon-menu",prev:["发票管理"],show:!0}},{entity:{name:"客户发票信息管理",path:"/CustomerInvoices",icon:"el-icon-menu",prev:["发票管理"],show:!0}}]},{entity:{name:"规则管理",path:"/9",icon:"el-icon-document",prev:[],show:this.showFJ},childs:[{entity:{name:"充值营销规则",path:"/MarketingRules",icon:"el-icon-menu",prev:["规则管理"],show:this.recharge_batch}},{entity:{name:"单价锁定",path:"/unitPriceLock",icon:"el-icon-menu",prev:["充值营销规则"],show:!1}},{entity:{name:"充值规则",path:"/CardRule",icon:"el-icon-menu",prev:["规则管理"],show:!0}},{entity:{name:"制卡规则",path:"/CardTheme",icon:"el-icon-menu",prev:["规则管理"],show:!0}}]},{entity:{name:"卡系统配置",path:"/Setting",icon:"el-icon-setting",prev:[],show:this.showFJ}},{entity:{name:"下载中心",path:"/DownloadList",icon:"el-icon-download",prev:[],show:this.showFJ}},{entity:{name:"操作日志",path:"/12",icon:"el-icon-notebook-2",prev:[],show:this.showFJ},childs:[{entity:{name:"平台操作日志",path:"/CardLog",icon:"el-icon-menu",prev:["操作日志"],show:!0}},{entity:{name:"手工账务处理日志",path:"/HandMadeLog",icon:"el-icon-menu",prev:["操作日志"],show:!0}}]}],this.$forceUpdate()},getGroupBaseInfo:function(){var e=this;return p()(u.a.mark(function t(){var n,a,o,r;return u.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.$axios.post("/Ostn/getGroupBaseInfo");case 3:if(200==(n=t.sent).data.status){t.next=6;break}return t.abrupt("return",e.$message.error(n.data.info));case 6:return e.$store.commit("SET_GROUP_BASE_INFO",n.data.data),e.new_customer_report=1==n.data.data.company_oil_report,e.recharge_batch=0!=n.data.data.recharge_batch,e.isGroupSettle=n.data.data.is_group_settle,e.show_company_invoice=1==n.data.data.show_company_invoice,e.show_customerSpendReport=1==n.data.data.show_customerSpendReport,e.show_fleet_topup_invoice=1==n.data.data.show_fleet_topup_invoice,localStorage.setItem("__isGroupSettle__",n.data.data.is_group_settle),localStorage.setItem("__groupSettle__",n.data.data.group_settle),(a=n.data.data)&&"is_group"in a&&(o=a.is_group,r=void 0===o?"0":o,e.$store.state.isGroup=r),e.$nextTick(function(){e.$refs.passwordCheck.checkPasswordStatus()}),t.next=20,e.fetchRoleJurisdiction();case 20:e.getMenu(),e.$forceUpdate(),t.next=28;break;case 24:t.prev=24,t.t0=t.catch(0),console.log("=>(Navigation.vue:1091) e",t.t0),e.$message.error("网络错误！");case 28:case"end":return t.stop()}},t,e,[[0,24]])}))()},goToMp:function(){var e=window.location.href;-1!=e.indexOf("localhost:8080")?window.open(this.urlObj.local):-1!=e.indexOf("test-card-admin.wcc.cn")?window.open(this.urlObj.test):-1!=e.indexOf("preview-card-admin.weicheche.cn")?window.open(this.urlObj.preview):(e.indexOf("card-admin.zhihuiyouzhan.com"),window.open(this.urlObj.product))},currentNav:function(e){this.breadcrumb=e},jump:function(e){this.breadcrumb={current:e.entity.name,prev:e.entity.prev};var t=e.entity.path.substr(1);this.cacheArray.find(function(e){return e==t})||this.changeCacheArray(t);var n=++this.tabIndex+"",a=this.editableTabs.find(function(t){return t.title==e.entity.name});a?this.changeEditableTabsValue(a.name):(this.changeEditableTabs({title:e.entity.name,name:n,router:e.entity.path,current:e.entity.name,prev:e.entity.prev}),this.changeEditableTabsValue(n))},_getRouterDetail:function(e){var t=void 0;return this.menuList.forEach(function(n){n.childs&&n.childs.length>0?n.childs.forEach(function(n){e==n.entity.path?t=n.entity:n.childs&&n.childs.length>0&&n.childs.forEach(function(n){e==n.entity.path&&(t=n.entity)})}):e==n.entity.path&&(t=n.entity)}),t},showInfo:function(){this.isShowInfo=!0},hideInfo:function(){this.isShowInfo=!1},handleClick:function(e){"out"==e&&this.logout()},logout:function(){var e=this;e.$axios.post("/ssoLogin/logout",{}).then(function(t){if(200==t.data.status){localStorage.removeItem("pwd_expire_reminder"),localStorage.removeItem("__userInfo__"),sessionStorage.removeItem("__userInfo__"),localStorage.removeItem("options"),localStorage.removeItem("currentStation");var n=window.location.href.split("?")[0],a=window.location.href;-1!=a.indexOf("localhost:8080")?window.location.href=d.a.ssoTest+"/v1/login?redirect="+encodeURIComponent(n):-1!=a.indexOf("test-card-admin.wcc.cn")?window.location.href=d.a.ssoTest+"/v1/login?redirect="+encodeURIComponent(n):-1!=a.indexOf("preview-card-admin.weicheche.cn")?window.location.href=d.a.ssoPreview+"/v1/login?redirect="+encodeURIComponent(n):-1!=a.indexOf("card-admin.zhihuiyouzhan.com")?window.location.href=d.a.ssoProduct+"/v1/login?redirect="+encodeURIComponent(n):window.location.href=d.a.ssoTest+"/v1/login?redirect="+encodeURIComponent(n)}else e.$message({message:t.data.info,type:"error"})}).catch(function(e){})},getCompany:function(){var e=this;return p()(u.a.mark(function t(){var n,a;return u.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return(n=localStorage.getItem("__userInfo__")||"")&&""!=n&&"undefined"!=n&&(e.groupSettleStid=JSON.parse(n).group_settle_stid),a=e,t.next=5,a.$axios.post("/Ostn/getMerchants",{}).then(function(){var e=p()(u.a.mark(function e(t){var n,r,s,h,l;return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:200==t.data.status?(n=function(e,t){return r([],e,t),h},r=function e(t,n,a){a.some(function(a){var r=a.children||[];if(a.value===n)return h=t,!0;if(r.length>0){var i=[].concat(o()(t));return i.push(a),e(i,n,r)}})},a.options=t.data.data.merchantList,a.currentStation=c()({},t.data.data.current,{group_id:t.data.data.group_id}),localStorage.setItem("options",i()(a.options)),localStorage.setItem("currentStation",i()(a.currentStation)),a.changeCurrentStation(a.currentStation),s=n(a.currentStation.value,a.options),h=[],l=[],s.forEach(function(e){l.push(e.value)}),l.push(a.currentStation.value),a.currentStationValue=l,a.getGroupBaseInfo(),a.$forceUpdate()):a.$message({message:t.data.info,type:"error"});case 1:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()).catch(function(e){});case 5:case"end":return t.stop()}},t,e)}))()},handleSelectStation:function(){var e=this;localStorage.removeItem("options"),this.$nextTick(p()(u.a.mark(function t(){var n,a;return u.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!this.$refs.stationCascader.getCheckedNodes()[0]){t.next=5;break}return n=this.$refs.stationCascader.getCheckedNodes()[0].data,a={merchant_type:n.merchant_type,merchant_id:n.merchant_id,value:n.value,label:n.label,pid:n.pid},t.next=5,e.$axios.post("/Ostn/switchOstn",a).then(function(){var t=p()(u.a.mark(function t(o){var r;return u.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:200==o.data.status?(e.editableTabs=[],e.cacheArray=[],r=e._getRouterDetail(e.$route.path),e.changeEditableTabs({title:e.getRouterName(e.$router.currentRoute.path),name:e.tabIndex,router:e.$router.currentRoute.path,current:r.name,prev:r.prev}),e.changeCacheArray(e.$router.currentRoute.path.substr(1)),e.changeEditableTabsValue(e.tabIndex),e.changeCurrentStation(c()({},a,{group_id:n.group_id})),e.getGroupBaseInfo(),e.$refs.stationCascader.dropDownVisible=!1):e.$message({message:o.data.info,type:"error"});case 1:case"end":return t.stop()}},t,this)}));return function(e){return t.apply(this,arguments)}}()).catch(function(e){});case 5:case"end":return t.stop()}},t,this)})))},downloadTool:function(){window.location.href="https://wcc-app-public.oss-cn-shenzhen.aliyuncs.com/制卡工具.rar"},removeTab:function(e){var t=this,n=this.editableTabs;1!=n.length&&(n.forEach(function(n,a){if(n.name==e){var o=n.router.substr(1);t.cacheArray.remove(o)}}),this.editableTabsValue===e&&n.forEach(function(a,o){if(a.name===e){var r=n[o+1]||n[o-1];r&&(t.changeEditableTabsValue(r.name),t.breadcrumb={current:r.current,prev:r.prev},t.$router.push(r.router).catch(function(e){}))}}),this.editableTabs=n.filter(function(t){return t.name!==e}))},clickTab:function(e){var t=this;this.editableTabs.forEach(function(n,a){n.title===e.label&&(t.breadcrumb={current:n.current,prev:n.prev},t.$router.push(n.router).catch(function(e){}))})},getRouterName:function(e){var t=void 0;return this.menuList.forEach(function(n){n.childs&&n.childs.length>0?n.childs.forEach(function(n){e==n.entity.path?t=n.entity.name:n.childs&&n.childs.length>0&&n.childs.forEach(function(n){e==n.entity.path&&(t=n.entity.name)})}):e==n.entity.path&&(t=n.entity.name)}),t}}),watch:{getCurrentStation:function(e,t){if(0==e.merchant_type)return this.$message({type:"error",message:"暂不支持平台！"}),!1;0!=e.merchant_type&&(e.value,t.value)},currentRoute:function(e){var t=this;this.editableTabs.forEach(function(n){n.router==e&&(t.editableTabsValue=n.name)});var n=this._getRouterDetail(this.$route.path);this.breadcrumb={current:n.name,prev:n.prev};var a=e.substr(1);this.cacheArray.find(function(e){return e==a})||this.changeCacheArray(a);var o=++this.tabIndex+"";this.editableTabs.find(function(t){return t.router==e})||(this.changeEditableTabs({title:n.name,name:o,router:e,current:n.name,prev:n.prev}),this.changeEditableTabsValue(o))}}},g={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{attrs:{id:"navigation"}},[n("div",{staticClass:"wrap-top"},[e._m(0),e._v(" "),n("div",{staticClass:"wrap-header"},[n("el-cascader",{ref:"stationCascader",attrs:{placeholder:"请输入搜索的油站名称","show-all-levels":!1,options:e.options,props:{checkStrictly:!0},"collapse-tags":"",filterable:"",clearable:""},on:{change:e.handleSelectStation},model:{value:e.currentStationValue,callback:function(t){e.currentStationValue=t},expression:"currentStationValue"}})],1),e._v(" "),n("ul",{staticClass:"wrap-top-info"},[n("li",{staticClass:"downloadTool",on:{click:e.downloadTool}},[n("i",{staticClass:"el-icon-download"}),e._v("下载制卡工具\n      ")]),e._v(" "),e._m(1),e._v(" "),n("li",{directives:[{name:"show",rawName:"v-show",value:e.showMP,expression:"showMP"}]},[n("a",{on:{click:e.goToMp}},[e._v("会员营销系统后台")])]),e._v(" "),n("li",{on:{mouseenter:e.showInfo,mouseleave:e.hideInfo}},[e._v("\n        |      "),n("i",{staticClass:"el-icon-user"}),e._v(e._s(e.name?e.name:"未登录")),n("i",{staticClass:"el-icon-arrow-down el-icon--right"}),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:e.isShowInfo,expression:"isShowInfo"}],staticClass:"superHeader_superTop_right_nav"},[n("div",{staticClass:"superHeader_superTop_right_nav_triangle"}),e._v(" "),n("div",{staticClass:"superHeader_superTop_right_userinfo"},[n("div",{staticClass:"superHeader_superTop_right_logo"},[e._v("\n              "+e._s(e.currentStation.label[0])+"\n            ")]),e._v(" "),n("div",{staticClass:"superHeader_superTop_right_userinfoContent"},[n("div",{staticClass:"superHeader_superTop_right_userinfoContent_nameTop"},[n("div",{staticClass:"superHeader_superTop_right_userinfoContent_name"},[e._v("\n                  "+e._s(e.name)+"\n                ")]),e._v("\n                >\n              ")]),e._v(" "),n("div",{staticClass:"superHeader_superTop_right_userinfoContent_stationLocal"},[e._v("\n                "+e._s(e.currentStation.label)+"\n              ")])])]),e._v(" "),n("a",{staticClass:"superHeader_superTop_right_nav_block",attrs:{target:"_blank",href:e.myUrl+"Message/show"}},[n("span",[e._v("消息中心")])]),e._v(" "),n("a",{staticClass:"superHeader_superTop_right_nav_block",attrs:{target:"_blank",href:e.myUrl+"Setting/personal"}},[n("span",[e._v("系统管理")])]),e._v(" "),n("a",{staticClass:"superHeader_superTop_right_nav_block",on:{click:e.logout}},[n("span",[e._v("退出")])])])])])]),e._v(" "),n("div",[n("div",{staticClass:"menu"},[n("div",{staticStyle:{width:"100%"}},[n("el-menu",{staticClass:"el-menu-vertical-demo card-menu",attrs:{"default-active":e.$route.path,router:""}},[e._l(e.menuList,function(t){return[n("el-submenu",{directives:[{name:"show",rawName:"v-show",value:t.entity&&t.childs&&t.entity.show,expression:"item.entity && item.childs && item.entity.show"}],key:t.path,attrs:{index:t.entity.path}},[n("template",{slot:"title"},[n("i",{class:t.entity.icon}),e._v(" "),n("span",[e._v(e._s(t.entity.name))])]),e._v(" "),e._l(t.childs,function(t,a){return n("div",{key:a},[n("el-menu-item",{directives:[{name:"show",rawName:"v-show",value:t.entity.show&&!t.childs,expression:"subitem.entity.show && !subitem.childs"}],attrs:{index:t.entity.path},on:{click:function(n){return e.jump(t)}}},[n("span",[e._v(e._s(t.entity.name))])]),e._v(" "),n("el-submenu",{directives:[{name:"show",rawName:"v-show",value:t.entity.show&&t.childs,expression:"subitem.entity.show && subitem.childs"}],key:t.index,attrs:{index:t.entity.path}},[n("template",{slot:"title"},[n("span",[e._v(e._s(t.entity.name))])]),e._v(" "),e._l(t.childs,function(t,a){return n("div",{key:a},[n("el-menu-item",{directives:[{name:"show",rawName:"v-show",value:t.entity.show,expression:"sub2item.entity.show"}],attrs:{index:t.entity.path},on:{click:function(n){return e.jump(t)}}},[n("span",[e._v(e._s(t.entity.name))])])],1)})],2)],1)})],2),e._v(" "),n("el-menu-item",{directives:[{name:"show",rawName:"v-show",value:t.entity&&!t.childs&&t.entity.show,expression:"item.entity && !item.childs && item.entity.show"}],key:t.index,attrs:{index:t.entity.path},on:{click:function(n){return e.jump(t)}}},[n("i",{class:t.entity.icon}),e._v(" "),n("span",[e._v(e._s(t.entity.name))])])]})],2)],1)]),e._v(" "),n("div",{staticClass:"wrap-main"},[n("el-tabs",{staticClass:"main-tabs",attrs:{type:"card",closable:e.editableTabs&&e.editableTabs.length>1},on:{"tab-click":e.clickTab,"tab-remove":e.removeTab},model:{value:e.editableTabsValue,callback:function(t){e.editableTabsValue=t},expression:"editableTabsValue"}},e._l(e.editableTabs,function(e){return n("el-tab-pane",{key:e.name,attrs:{label:e.title,name:e.name}})}),1),e._v(" "),n("div",{staticClass:"wrap-menu"},[n("el-breadcrumb",{attrs:{"separator-class":"el-icon-arrow-right"}},[e._l(e.breadcrumb.prev,function(t,a){return n("el-breadcrumb-item",{key:a},[e._v(e._s(t))])}),e._v(" "),n("el-breadcrumb-item",[e._v(e._s(e.breadcrumb.current))])],2)],1),e._v(" "),n("div",{staticClass:"wrap-content"},[n("keep-alive",{attrs:{include:e.cacheArray}},[n("router-view",{attrs:{currentStation:e.currentStation},on:{currentNav:e.currentNav}})],1)],1)],1)]),e._v(" "),n("div",{staticClass:"sign-up"},[n("p",[e._v("©2014-"+e._s(e.year)+" zhihuiyouzhan.com 粤ICP备14049534号-1")]),e._v(" "),n("p",[e._v("Powered By Shenzhen Wecar Technology Co., Ltd.")])]),e._v(" "),n("password-expire-check",{ref:"passwordCheck"})],1)},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"menu-logo"},[t("img",{staticClass:"logo",attrs:{src:n("7Otq"),alt:""}}),this._v(" "),t("span",{staticClass:"menu-logo-test"},[this._v("加油卡系统后台")])])},function(){var e=this.$createElement,t=this._self._c||e;return t("li",{staticClass:"on"},[t("a",{attrs:{href:""}},[this._v("加油卡系统后台")])])}]};var b=n("VU/8")(f,g,!1,function(e){n("avFL"),n("KPWV")},"data-v-bac1bb52",null);t.default=b.exports},xe9m:function(e,t){}});