<template>
    <div class="tax"  v-loading="loading">
        <div class="header_input">
            <div>车队名称</div>
            <el-select class="motorcades_select" filterable clearable v-model="selectMotorcade" placeholder="请选择车队" @change="searchAction">
                <el-option
                v-for="item in motorcades"
                :key="item.ID"
                :label="item.CompanyName"
                :value="item.ID">
                </el-option>
            </el-select>
            <el-date-picker class="header_datePick"
                @change="searchAction"
                v-model="selectDate"
                type="datetimerange"
                :default-time="['00:00:00', '23:59:59']"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
            </el-date-picker>
        </div>
        <div class="header_search">
            <div class="header_search_left">
                <div class="search_title">查询客户</div>
                <div>
                    <el-radio-group v-model="radio">
                        <el-radio :label="1">手机号</el-radio>
                        <el-radio :label="2">卡号</el-radio>
                        <el-radio :label="3">卡面卡号</el-radio>
                    </el-radio-group>
                </div>
                <el-input v-model="inputValue" style="width:210px" :placeholder="radio=='2'?'请输入卡号':radio=='1'?'请输入手机号':'请输入卡面卡号'" clearable class="search_input"></el-input>
                <el-button type="primary" @click="searchAction">查询</el-button>
            </div>
            <div>
                <span class="invoice-btn" :class="{active:fpstate == 1}" @click="getDisabledInvoice" v-if="currentGroupId == bpGroupId">已开票退款订单</span>
                <el-button class="output_btn" type="primary" @click="outputAction" v-show="isTotalReportForm">下载数据</el-button>
            </div>
        </div>
        <div class="edit_box" v-if="fpstate === 0">
            <el-button type="primary" @click="multiEdit">批量编辑</el-button>
            <div class="edit_des">选中<span class="count">{{selectItems.length}}</span>条数据</div>
            <el-radio-group v-model="typeRadio" @change="searchAction">
                <el-radio :label="2">全部</el-radio>
                <el-radio :label="0">仅看未开票</el-radio>
                <el-radio :label="1">仅看已开票</el-radio>
            </el-radio-group>
            <div class="invoice_notice">仅显示车队消费订单。</div>
        </div>
        <div class="edit_box" v-else>
            <el-button size="mini" @click="getAllData">返回</el-button>
        </div>
        <!-- 表格 -->
        <div class="table_box">
            <el-table
                :cell-style="transactionCellstyle"
                :header-cell-style="headerStyle"
                @selection-change="handleSelectionChange"
                :data="tableData"
                style="width: 100%">
                <el-table-column
                    fixed="left"
                    type="selection"
                    width="55">
                </el-table-column>
                <el-table-column
                    prop="StationName"
                    label="所属油站"
                    width="160">
                </el-table-column>
                <el-table-column
                    prop="TradeID"
                    label="订单号"
                    width="180">
                </el-table-column>
                <el-table-column
                    prop="CardNO"
                    label="卡号"
                    width="180"
                    :formatter="formatterCellval">
                </el-table-column>
                <el-table-column
                    prop="Phone"
                    label="手机号"
                    width="120">
                </el-table-column>
                <el-table-column
                    prop="CardNumber"
                    label="卡面卡号"
                    width="100"
                    :formatter="formatterCellval">
                </el-table-column>
                <el-table-column
                    prop="bp_has_invoice_text"
                    label="是否开票">
                </el-table-column>
                <el-table-column
                    prop="open_time"
                    label="开票时间"
                    width="110"
                    :formatter="formatterCellval">
                </el-table-column>
                <el-table-column
                    prop="goods_name"
                    label="商品名称"
                    width="150">
                </el-table-column>
                <el-table-column
                    prop="goods_number"
                    label="数量">
                </el-table-column>
                <el-table-column
                    prop="market_price"
                    label="单价">
                </el-table-column>
                <el-table-column
                    prop="goods_amount"
                    width="120"
                    label="应付金额（元）">
                </el-table-column>
                <el-table-column
                    prop="discount_unit_price"
                    width="120"
                    label="折后单价（元）">
                </el-table-column>
                <el-table-column
                    prop="discount_amount"
                    width="120"
                    label="折后金额（元）">
                </el-table-column>
                <el-table-column
                    prop="SFBJ"
                    width="120"
                    label="实付本金（元）">
                </el-table-column>
                <el-table-column
                    prop="SKJ"
                    width="120"
                    label="实付赠金（元）">
                </el-table-column>
                <el-table-column
                    prop="yh_amount"
                    width="120"
                    label="优惠金额（元）">
                </el-table-column>
                <el-table-column
                    prop="CompanyName"
                    label="车队名称"
                    width="160">
                </el-table-column>

                <el-table-column
                    prop="TradeTime"
                    label="交易时间"
                    width="140">
                </el-table-column>

                <el-table-column
                    prop="bp_invoice_summ"
                    width="350"
                    label="备注"
                    :formatter="formatterCellval">
                </el-table-column>
                <el-table-column
                    fixed="right"
                    label="操作">
                    <template slot-scope="scope">
                        <el-button
                        @click.native.prevent="editRow(scope.row)"
                        type="text"
                        size="small">
                        编辑
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 页码 -->
        <div class="page_content">
            <el-pagination class="page_left"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-size="pageSize"
                layout="prev, pager, next"
                :total="totalCount">
            </el-pagination>
            <el-pagination class="page_right"
                @size-change="handleSizeChange"
                :page-sizes="[10, 15, 20, 30]"
                :page-size="pageSize"
                layout="total, sizes"
                :total="totalCount">
            </el-pagination>
        </div>
        <!-- 编辑 -->
        <el-dialog
            title="编辑"
            :visible.sync="editDialogVisible"
            width="500px"
            :close-on-click-modal="false">
            <div>
                <div class="edit_count" v-if="isMulti">已选中<span class="select_count">3</span>笔订单</div>
                <div class="edit_item">
                    <div class="edit_title">是否开票</div>
                    <div>
                        <el-radio-group v-model="radio1" @change="invoiceStatusChange" :disabled="fpstate==1">
                            <el-radio :label="0">未开票</el-radio>
                            <el-radio :label="1">已开票</el-radio>
                        </el-radio-group>
                    </div>
                </div>
                <div class="edit_item">
                    <div class="edit_title">
                        <img class="mark_icon" v-if="radio1 == 1" src="../assets/images/bitian.png">
                        开票时间
                    </div>
                    <div>
                        <el-date-picker
                            :disabled="fpstate==1||dateDisable"
                            v-model="edit_date"
                            :picker-options="pickerOptions"
                            type="date"
                            placeholder="请选择开票时间">
                        </el-date-picker>
                    </div>
                </div>
                <div class="edit_item">
                    <div class="edit_title">发票信息</div>
                    <div>
                        <el-select v-model="company" filterable placeholder="请选择" class="company_input" :disabled="fpstate==1">
                            <el-option
                            v-for="item in invoiceCompanys"
                            :key="item.id"
                            :label="item.showTitle"
                            :value="item.showTitle">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div class="edit_item edit_input">
                    <div class="edit_title">备注</div>
                    <div>
                        <el-input
                            :disabled="fpstate==1"
                            class="textInput"
                            type="textarea"
                            :rows="4"
                            maxlength="180"
                            show-word-limit
                            placeholder="请输入内容"
                            v-model="textarea">
                        </el-input>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" type="primary" @click="editConfirm">确 定</el-button>
                <el-button size="mini" @click="editDialogVisible = false">取 消</el-button>
            </span>
        </el-dialog>
        <!-- 修改确认 -->
        <el-dialog
            title="提示"
            :visible.sync="confirmDialogVisible"
            width="350px"
            :close-on-click-modal="false"
            center>
            <div class="confirmTips">请确认是否将"已开票"修改为"未开票？"</div>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" type="primary" @click="modifyConfirm(1)">确 定</el-button>
                <el-button size="mini" @click="modifyConfirm(2)">取 消</el-button>
            </span>
        </el-dialog>
        <!-- 下载确认 -->
        <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>
    </div>
</template>

<script>
import DownloadTips from './DownloadTips.vue';
import {mapGetters} from 'vuex'
export default {
    name:"TaxInvoice",
    components:{
        DownloadTips
    },
    data() {
        return {
            isTotalReportForm: true,
            selectStatus: "全部",
            selectDate: [],
            radio: 1,
            pageSize: 10,
            currentPage: 1,
            totalCount: 0,
            inputValue: "",
            tableData: [],

            radio1: 1,
            edit_date: null,
            textarea: "",
            carInput: "",
            dateDisable: true,
            isMulti: false,

            selectItem: null,
            selectItems: [],

            confirmDialogVisible: false,
            editDialogVisible: false,

            companys: [],
            company: '',
            invoiceCompanys: [],
            invoiceCompany: '',
            selectMotorcade: null,
            loading: false,
            motorcades: [],
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            showDownloadTips: false,
            typeRadio:2,//类型选项
            fpstate:0,//0正常发票，1已开票已退款发票
            bpGroupId:1,//
            currentGroupId:0,//当前集团id
        }
    },
    mounted() {
        let userInfo = localStorage.getItem("__userInfo__") || "";
        this.isTotalReportForm = JSON.parse(userInfo).group_id != 426;
        //默认显示前一个月数据
        const startDate = this.$moment(new Date()).subtract(1,'months').format('YYYY-MM-DD HH:mm:ss');
        const endDate = this.$moment(new Date());
        this.selectDate.push(this.$moment(startDate).format('YYYY-MM-DD')+ ' 00:00:00');
        this.selectDate.push(this.$moment(endDate).format('YYYY-MM-DD')+ ' 23:59:59');

        if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
            return false;
        }
        //判断环境，生产bp的id1374，测试892
        let url = window.location.href;
        if(url.indexOf('card-admin.zhihuiyouzhan.com') != -1){
            this.bpGroupId = 1374;
        }else{
            this.bpGroupId = 1;
        }
        this.getInvoiceList()
        this.getInvoicesCompanyList()
        this.getMotorcades()

    },
    computed:{
        ...mapGetters({
            "getCurrentStation":"getCurrentStation"
        })
    },
    methods: {
        // 开票到未开票确认
        modifyConfirm: function (val) {
            if (val == 1) {
                this.editConfirmAction()
            }
            this.confirmDialogVisible = false
            this.editDialogVisible = false
        },
        invoiceStatusChange:function (val) {
            // 未开票
            if (val == 0) {
                this.dateDisable = true
            }else {
                this.dateDisable = false
            }
        },

        editRow:function (item) {
            // if(
            //   item.RKPType == 2 ||
            //   (item.RKPType == 1 && item.Type != 'XF') ||
            //   item.RKPType == 0 && item.Type != 'CZ' && item.Type != 'JH_CZ'
            // ){
            //   return this.$message.error(`该笔订单开票规则为充值开票，不可编辑`)
            // }
            this.selectItem = item
            this.isMulti = false
            this.editDialogVisible = true

            if (item.is_bp_has_invoice) {
                this.dateDisable = false
                this.radio1 = 1
            }else {
                this.dateDisable = true
                this.radio1 = 0
            }

            if (item.bp_invoice_open_time) {
                this.edit_date = item.bp_invoice_open_time.substring(0, 10)
            }else{
                this.edit_date =  null
            }
            if (item.bp_invoice_title && item.bp_invoice_no) {
                this.company = item.bp_invoice_title + "\xa0\xa0\xa0\xa0\xa0\xa0" + item.bp_invoice_no
            }else {
                this.company = ""
            }

            this.textarea = item.bp_invoice_summ
        },
        searchAction:function () {
            this.currentPage = 1;
            this.getInvoiceList()
        },
        // 修改确认
        editConfirm: function () {
            // 选择未开票
            if (this.radio1 == 0) {
                // 是已开票的
                if (this.isMulti) {
                    if (this.selectItems[0].is_bp_has_invoice) {
                        this.confirmDialogVisible = true
                    }else {
                        this.editConfirmAction()
                    }
                }else {
                    if (this.selectItem.is_bp_has_invoice) {
                        this.confirmDialogVisible = true
                    }else {
                        this.editConfirmAction()
                    }
                }
            // 选择已开票
            }else {
                this.editConfirmAction()
            }
        },
        editConfirmAction: function () {

            var that = this

            if (!this.edit_date && !this.dateDisable) {
                that.$message.error('请选择开票时间');
                return
            }

            var codes = {}
            if (this.isMulti) {
                for (var i = 0; i < this.selectItems.length; i++ ) {
                    var item = this.selectItems[i]
                    let TradeID = item.TradeID
                    codes[TradeID] = JSON.stringify({stid:item.StationNO,pay_time:item.TradeTime})
                }
            }else {
                let TradeID = this.selectItem.TradeID
                codes[TradeID] = JSON.stringify({stid:this.selectItem.StationNO,pay_time:this.selectItem.TradeTime})
            }

            var openDate = that.edit_date ? that.$moment(that.edit_date).valueOf() / 1000 : ''
            if (this.radio1 == 0) {
                openDate = ""
            }

            var bp_invoice_info = ""
            var comItem = null
            for (var i = 0 ; i < this.invoiceCompanys.length ; i++) {
                if (this.company == this.invoiceCompanys[i].showTitle) {
                    comItem = this.invoiceCompanys[i]
                }
            }

            if (comItem) {
                bp_invoice_info = comItem.title + "\xa0\xa0\xa0\xa0\xa0\xa0"
                + comItem.snno + "\xa0\xa0\xa0\xa0\xa0\xa0"
                + comItem.addr + "\xa0\xa0\xa0\xa0\xa0\xa0"
                + comItem.phone + "\xa0\xa0\xa0\xa0\xa0\xa0"
                + comItem.bank_name + "\xa0\xa0\xa0\xa0\xa0\xa0"
                + comItem.bank_no
            }


            var exts = {
                is_bp_has_invoice: this.radio1,
                bp_invoice_open_time: openDate,
                bp_invoice_summ: this.textarea,
                bp_invoice_info: bp_invoice_info
            }
            that.editDialogVisible = false
            that.loading = true
            that.$axios.post('/bp/oinvoicesAdd', {
                codes: codes,
                exts: exts
            })
            .then(function (res) {

                if(res.data.status == 200){

                    setTimeout(function (){
                        that.loading = false
                        that.getInvoiceList()
                    },3000);
                    that.$message.success('编辑成功')
                }else{
                    that.loading = false
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
                that.loading = false
                that.$message.error('修改出错');
            });
        },
        // 获取列表
        getInvoiceList:function () {
            var that = this

            var type = '';
            if (that.selectStatus == "充值") {
                type = "CZ"
            }else if (that.selectStatus == "消费") {
                type = "XF"
            }

            var start_time = that.selectDate ? that.$moment(that.selectDate[0]).format('YYYY-MM-DD HH:mm:ss') : ''
            var end_time = that.selectDate ? that.$moment(that.selectDate[1]).format('YYYY-MM-DD HH:mm:ss') : ''

            that.loading = true
            that.$axios.post('/bp/oinvoices', {
                fptype: 1,
                qtype: that.radio,
                cztype: type,
                query: that.inputValue,
                page: that.currentPage,
                start: start_time,
                end: end_time,
                pagesize: that.pageSize,
                company_id: that.selectMotorcade,
                fpstatus: that.typeRadio,
                fpstate: that.fpstate
            })
            .then(function (res) {
                that.loading = false
                if(res.data.status == 200){
                    var list = res.data.data.dt

                    for (var i = 0; i < list.length; i++) {
                        var item = list[i]

                        if (item.Money.indexOf('-') == 0) {
                            item.money = item.Money.substr(1)
                        }else {
                            item.money = item.Money
                        }

                        if (item.items && item.items.length > 0) {
                            var des = item.items[0]
                            item.goods_name = des.goods_name
                            item.goods_number = parseFloat(des.goods_number).toFixed(2)
                            item.goods_price = parseFloat(des.goods_price).toFixed(2)
                            item.order_amount = parseFloat(item.order_amount).toFixed(2)
                            item.market_price = des.market_price
                        }

                        if (item.bp_invoice_open_time) {
                            item.open_time = item.bp_invoice_open_time.substring(0, 10)
                        }else {
                            item.open_time = ''
                        }
                    }
                    that.totalCount = res.data.data.TotalQty
                    that.currentGroupId = res.data.data.group_id;
                    that.tableData = list
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
                that.loading = false
                that.$message.error('获取列表出错');
            });
        },
        outputAction: function () {
            var that = this

            var type = '';
            if (that.selectStatus == "充值") {
                type = "CZ"
            }else if (that.selectStatus == "消费") {
                type = "XF"
            }

            var start_time = that.selectDate ? that.$moment(that.selectDate[0]).format('YYYY-MM-DD HH:mm:ss') : ''
            var end_time = that.selectDate ? that.$moment(that.selectDate[1]).format('YYYY-MM-DD HH:mm:ss') : ''

            that.loading = true;
            that.$axios.get('/bp/export', {
                params : {
                    fptype: 1,
                    qtype: that.radio,
                    cztype: type,
                    query: that.inputValue,
                    page: that.currentPage,
                    start: start_time,
                    end: end_time,
                    pagesize: that.pageSize,
                    company_id: that.selectMotorcade,
                    name: "专票",
                    fpstatus: that.typeRadio,
                    fpstate:that.fpstate
                }
            })
            .then(function (res) {
                that.loading = false;
                if(res.data.status == 200){
                    that.showDownloadTips = true
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
                that.$message({
                    message: "导出出错",
                    type: 'error'
                });
                that.loading = false;
            });
        },

        handleCurrentChange:function (val) {
            this.currentPage = val
            this.getInvoiceList()
        },
        handleSizeChange:function (val) {
            this.pageSize = val
            this.getInvoiceList()
        },
        multiEdit: function() {
            var that = this
            if (this.selectItems.length == 0) {
                that.$message.error('请至少选择一条记录');
                return
            }

            if (this.selectItems.length == 1) {
                this.editRow(this.selectItems[0])
                return
            }

            var openStatus = this.selectItems[0].is_bp_has_invoice;
            var notSame = false
            for (var i = 1; i < this.selectItems.length; i++ ) {
                var item = this.selectItems[i]

                if (item.is_bp_has_invoice != openStatus) {
                    notSame = true
                    break;
                }
            }

            if (notSame) {
                that.$message.error('选中订单开票状态不一致');
                return
            }

            this.isMulti = true
            this.selectItem = null
            this.editDialogVisible = true


            if (openStatus) {
                this.radio1 = 1
                this.dateDisable = false
            }else {
                this.radio1 = 0
                this.dateDisable = true
            }
        },
        handleSelectionChange: function (val) {
            this.selectItems = val
        },
        checkSelectSet(row, index) {
          if(
            row.RKPType == 2 ||
            (row.RKPType == 1 && row.Type != 'XF') ||
            row.RKPType == 0 && row.Type != 'CZ' && row.Type != 'JH_CZ'
          ){
            return false
          }
          else{
            return true
          }
        },

        // 获取发票抬头列表
        getInvoicesCompanyList:function () {
            var that = this;
            that.loading = true
            that.$axios.get('/bp/invoices', {
                params: {
                    page: 1,
                    pagesize: 999
                }
            })
            .then(function (res) {
                that.loading = false
                if(res.data.status == 200){

                    var list = res.data.data.data

                    for (var i = 0; i < list.length; i++) {
                        var item = list[i]
                        item.showTitle = item.title + "\xa0\xa0\xa0\xa0\xa0\xa0" + item.snno
                    }

                    that.invoiceCompanys = list
                }else{

                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
                that.loading = false
                that.$message.error('获取发票信息列表出错');
            });
        },
        // 获取车队列表
        getMotorcades:function () {
            var that = this;
            that.loading = true
            that.$axios.post('/oscard/getCompanies', {
                page: 1,
                pagesize: 999
            })
            .then(function (res) {
                that.loading = false
                if(res.data.status == 200){

                    var companys = []

                    for (var i = 0; i < res.data.data.length; i++) {
                        var item = res.data.data[i];
                        item.id = item.ID;
                        companys.push(item)
                    }

                    that.motorcades = companys

                }else{

                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
                that.loading = false
                that.$message.error('获取车队列表出错');
            });
        },
        // table style
        transactionCellstyle({row, column, rowIndex, columnIndex}){
            if (columnIndex != 17) {
                return "text-align:center";
            }
        },
        headerStyle({row, column, rowIndex, columnIndex}){
           if (columnIndex != 17) {
                return "text-align:center";
            }
        },
        //查询已开票已退款发票
        getDisabledInvoice(){
            if(this.fpstate == 1){
                this.fpstate = 0;
            }else{
                this.fpstate = 1;
            }
            this.typeRadio = 2;
            this.currentPage = 1;
            this.getInvoiceList();
        },
        //返回全部数据
        getAllData(){
            this.fpstate = 0;
            this.currentPage = 1;
            this.getInvoiceList();
        }
    },
    watch: {
        getCurrentStation(newValue,oldValue){
            if(newValue.merchant_type != 0 && newValue.value != oldValue.value){
                this.getInvoiceList()
                this.getInvoicesCompanyList()
                this.getMotorcades()
            }
        },
    },
}
</script>

<style scoped>

.tax {
    overflow: hidden;
    padding: 20px 0;
}

/* 输入栏 */
.header_input {
    display: flex;
    align-items: center;
}

.car_input {
    width: 160px;
    margin: 0 30px 0 20px;
}

.header_datePick {
    margin-left: 30px;
}

.header_search {
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header_search_left {
    display: flex;
    align-items: center;
}

.search_input {
    margin-left: 20px;
    margin-right: 5px;
}

.search_title {
    margin-right: 15px;
}

/* 批量编辑 */
.edit_box {
    margin-top: 10px;
    display: flex;
    align-items: center;
}

.edit_des {
    margin-left: 20px;
    margin-right: 66px;
}

.count {
    color: #32af50;
}

.table_box {
    margin-top: 20px;
}

/* 页码 */
.page_content {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
}

/* 编辑 */
.edit_item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.edit_title {
    width: 80px;
    text-align: right;
    margin-right: 20px;
}

.textInput {
    width: 300px;
}

.edit_input {
    align-items: flex-start;
}

.edit_count {
    margin-bottom: 20px;
}

.select_count {
    color: #32af50;
}

.date_mark {
    background: red;
    display: inline-block;
    width: 10px;
    height: 10px;
    margin-right: 5px;
}

.confirmTips {
    text-align: center;
}

.company_input {
    width: 300px;
}

.motorcades_select {
    margin-left: 20px;
}

.mark_icon {
    display: inline-block;
    width: 8px;
    height: 8px;
}
.invoice-btn{
    display: inline-block;
    padding: 9.5px 20px;
    margin-right: 10px;
    font-size: 14px;
    border: 1px solid #DCDFE6;
    color: #606266;
    cursor: pointer;
    border-radius: 4px;
}
.invoice-btn.active,.invoice-btn:hover{
    color: #32AF50;
    border-color: #c2e7cb;
    background-color: #ebf7ee;
}

.invoice_notice{
    font-size: 14px;
    color: #666;
    line-height: 1;
    margin-left: 60px; 
    border-left:2px solid #666;
    padding-left: 10px;
}

</style>

