<template>
  <div>
    <div class="pt-20px">
      <el-radio-group v-model="typeValue" @change="changeTypeValue">
        <el-radio-button v-for="item in typeOptions" :key="item.value" :label="item.value">{{ item.label }}
        </el-radio-button>
      </el-radio-group>
      <div class="mt-20px flex flex-wrap items-center">
        <span v-if="getCurrentStation.merchant_type == 2" class="mr-5px">开户油站</span>
        <el-select v-if="getCurrentStation.merchant_type == 2 && update" v-model="stationId" :multiple="typeValue == 1"
                   class="w-250px mr-20px mb-5px"
                   clearable collapse-tags placeholder="请选择油站" @change="changeStationValue">
          <el-option
            v-for="(item,index) in stationOptions"
            :key="index"
            :label="item.stname"
            :value="item.stid">
          </el-option>
        </el-select>
        <span class="mr-5px">车队名称</span>
        <el-select v-model="companyValue" class="w-250px mr-20px mb-5px"
                   filterable
                   multiple
                   collapse-tags
                   clearable
                   placeholder="请选择车队">
          <el-option
            v-for="(item,index) in companyOptions"
            :key="index"
            :label="item.CompanyName"
            :value="item.ID">
          </el-option>
        </el-select>
        <el-date-picker
          v-show="typeValue == 1"
          v-model="dateValue"
          end-placeholder="结束日期"
          range-separator="至"
          start-placeholder="开始日期"
          class="mb-5px"
          type="daterange"
          value-format="yyyy-MM-dd"
          @change="clearData">
        </el-date-picker>
        <banci-date-time
          v-show="typeValue == 4"
          class="mb-5px"
          ref="banciRef"
          :dateValue="dateBanciValue"
          :stationValue="stationId"
          @changeDate="changeBnaciDate"
          @searchDate="searchBanciDate">
        </banci-date-time>

        <el-button :disabled="loading" type="primary" class="mb-5px ml-15px" @click="changeDate()">生成</el-button>

      </div>
      <div class="flex justify-end">
        <el-button :disabled="loading || tableData.length === 0" type="primary" @click="printContent">打印</el-button>
        <el-button :disabled="loading || tableData.length === 0" type="primary" @click="cardChargeDownload">下载数据</el-button>
      </div>

      <div id="myTable">
        <div class="text-center" v-loading="loading">
          <!-- 班结日报 -->
          <div class="text-24px font-bold mt-20px py-5px border border-solid border-#EBEEF5">车队进销存汇总表</div>
          <div class="flex py-10px px-20px border-x-1px  border-x-solid border-#EBEEF5">
            <div v-if="isGroup">集团名称：{{ getCurrentStation.label }}</div>
            <div v-else>油站名称：{{ getCurrentStation.label }}</div>
            <div class="mx-40px">开始日期：{{ dateValue.length ? dateValue[0].length == 10 ? dateValue[0] + " 00:00:00": dateValue[0] : "" }}</div>
            <div>结束日期：{{ dateValue.length ? dateValue[1].length == 10 ? dateValue[1] + " 23:59:59" : dateValue[1]  : "" }}</div>
          </div>
          <el-table ref="table" :data="tableData" border size="small" :span-method="objectSpanMethod">
            <el-table-column :formatter="formatterCellval" v-if="getCurrentStation.merchant_type == 2"
                             align="center" prop="stname" label="油站" width="150">
            </el-table-column>
            <template v-for="(item, index) in tableColumn">
              <el-table-column  align="center" :label="item.label" :prop="item.prop" :width="item.width">
                <template v-if="item.childs && item.childs.length > 0">
                  <template v-for="(subItem, subIndex) in item.childs">
                    <el-table-column :key="subIndex" align="center" :width="subItem.width"
                                     :label="subItem.label" :prop="subItem.prop"
                    ></el-table-column>
                  </template>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <div class="flex justify-between py-10px text-14px border border-solid border-#EBEEF5 border-t-0">
            <div class="ml-20px">制表人：{{ orderMaker }}</div>
            <div class="mx-100px">制表时间：{{ orderMakingTime }}</div>
            <div class="mr-100px">签字：</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 下载中心提示 -->
    <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>
  </div>
</template>

<script>
import DownloadTips from '../DownloadTips.vue';
import BanciDateTime from '../Banci/banciDateTime.vue'
import {mapGetters} from 'vuex'

export default {
  name: 'fleetJXC',
  components: {
    DownloadTips,
    BanciDateTime
  },
  data() {
    return {
      typeOptions: [{
        value: 1,
        label: "按自然日期",
      }, {
        value: 4,
        label: "按班结日期",
      }],
      typeValue: 1,
      dateValue: [],
      dateBanciValue: [],
      stationId: [],//选中油站
      stationName: "",//所选油站名称
      stationOptions: [],//油站列表
      companyOptions: [],
      companyValue: [],//选中车队
      tableData: [],
      tableColumn: [
        {
          label: "车队名称",
          prop: "company_name",
        },
        {
          label: "期初余额",
          childs: [
            {
              label: "期初总余额",
              prop: "qichu_amt",
              width: 100
            },
            {
              label: "期初本金",
              prop: "qichu_bj_amt",
            },
            {
              label: "期初赠金",
              prop: "qichu_skj_amt",
            }
          ]
        },
        {
          label: "期间充值",
          childs: [
            {
              label: "充值金额",
              prop: "recharge_amt",
            },
            {
              label: "充值本金",
              prop: "recharge_bj_amt",
            },
            {
              label: "充值赠金",
              prop: "recharge_skj_amt",
            }
          ]
        },
        {
          label: "期间充值退款",
          childs: [
            {
              label: "充值退款金额",
              prop: "recharge_refund_amt",
              width: 100
            },
            {
              label: "充值退款本金",
              prop: "recharge_refund_bj_amt",
              width: 100
            },
            {
              label: "充值退款赠金",
              prop: "recharge_refund_skj_amt",
              width: 100
            }
          ]
        },
        {
          label: "期间消费",
          childs: [
            {
              label: "消费金额",
              prop: "consume_amt",
            },
            {
              label: "消费本金",
              prop: "consume_bj_amt",
            },
            {
              label: "消费赠金",
              prop: "consume_skj_amt",
            }
          ]
        },
        {
          label: "期间消费退款",
          childs: [
            {
              label: "消费退款金额",
              prop: "consume_refund_amt",
              width: 100
            },
            {
              label: "消费退款本金",
              prop: "consume_refund_bj_amt",
              width: 100
            },
            {
              label: "消费退款赠金",
              prop: "consume_refund_skj_amt",
              width: 100
            }
          ]
        },
        {
          label: "加油量(吨)",
          prop: "oil_v20_ton",
        },
        {
          label: "加油量(升)",
          prop: "oil_litre",
        },
        {
          label: "余额清零",
          childs: [
            {
              label: "清零金额",
              prop: "clear_amt",
            },
            {
              label: "清零本金",
              prop: "clear_bj_amt",
            },
            {
              label: "清零赠金",
              prop: "clear_skj_amt",
            }
          ]
        },
        {
          label: "期末余额",
          childs: [
            {
              label: "期末总余额",
              prop: "qimo_amt",
              width: 100
            },
            {
              label: "期末本金",
              prop: "qimo_bj_amt",
            },
            {
              label: "期末赠金",
              prop: "qimo_skj_amt",
            }
          ]
        },
      ],
      loading: false,
      start_time: "",
      end_time: "",
      orderMaker: "",
      orderMakingTime: "",
      isGroup: true,//是否是集团账号
      showDownloadTips: false,
      update: true,
      arr:[],
    }
  },
  async mounted() {
    let userInfo = localStorage.getItem("__userInfo__") || "";
    //自然日默认为前一天的数据
    let currentDateStart = this.$moment(new Date()).subtract(1, 'days').format('YYYY-MM-DD');
    let currentDateEnd = this.$moment().format('YYYY-MM-DD');
    this.dateValue = [currentDateStart, currentDateEnd];
    //班结默认为前一天的数据
    let _today = this.$moment();
    let yesterday = _today.subtract(1, 'days').format('YYYY-MM-DD');
    this.dateBanciValue = [yesterday,this.$moment().format('YYYY-MM-DD')];

    if (this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0) {
      return false;
    }
    if (this.getCurrentStation && this.getCurrentStation.merchant_type == 2) {
      this.isGroup = true;
    } else {
      this.isGroup = false;
    }
    await this.getStationList();
    await this.getCompanyList();
    await this.changeDate();
  },
  computed: {
    ...mapGetters({
      "getCurrentStation": "getCurrentStation"
    })
  },
  methods: {
    changeBnaciDate(value) {
      this.dateBanciValue = value
    },
    //查询班次
    searchBanciDate(value) {
      this.dateValue = value
      this.changeDate()
    },
    //改变油站，相对于商品列表和卡列表都得变化
    changeStationValue(e) {
      //获取班次
      if (this.typeValue == 4 && this.stationId) {
        this.$refs.banciRef.getBanci(e)
      }
    },
    //获取可用油站
    async getStationList() {
      this.stationId = [];
      let that = this;
      await this.$axios.post('/Stations/getStationList', {}).then((res) => {
        if (res.status == 200) {
          that.stationOptions = res.data.data;
          if (this.getCurrentStation.merchant_type == 1) {
            if (this.typeValue == 4) {
              this.stationId = this.getCurrentStation.merchant_id
            }else{
              this.stationId = [this.getCurrentStation.merchant_id]
            }
          }else{
            if (this.typeValue == 4) {
              this.stationId = ''
            }else{
              that.stationId = res.data.data.map(item => item.stid)
            }
          }
        }
      })
    },
    //获取车队信息列表
    async getCompanyList() {
      let that = this;
      await that.$axios.post('/CompanyCard/getSimpleCompanyList', {
        page: 1,
        page_size: 1250,
        input: "",
      })
        .then(function (res) {
          if (res.data.status == 200) {
            that.companyOptions = [];
            that.companyOptions = that.companyOptions.concat(res.data.data.dt);
          } else {
            that.$message({
              message: res.data.info,
              type: 'error'
            });
          }
        })
        .catch(function (error) {
          that.$message.error('网络错误')
        });
    },
    changeTypeValue(e) {
      //判断是否是单站，单站默认选中油站

      if (this.getCurrentStation.merchant_type == 1) {
        if (e == 4) {
          this.stationId = this.getCurrentStation.merchant_id
        }else{
          this.stationId = [this.getCurrentStation.merchant_id]
        }
      }else{
        if (e == 4) {
          this.stationId = ''
          this.$refs.banciRef.clearDate()
        } else {
          this.stationId = []
        }
      }
      //自然日默认为前一天的数据
      let currentDateStart = this.$moment(new Date()).subtract(1, 'days').format('YYYY-MM-DD');
      let currentDateEnd = this.$moment().format('YYYY-MM-DD');
      this.dateValue = [currentDateStart, currentDateEnd];
      //班结默认为前一天的数据
      let _today = this.$moment();
      let yesterday = _today.subtract(1, 'days').format('YYYY-MM-DD');
      this.dateBanciValue = [yesterday,this.$moment().format('YYYY-MM-DD')];
      this.companyValue = [];

      this.orderMakingTime = "";
      this.tableData = [];
      this.page = 1;
    },
    //选中时间生成报表
    async changeDate() {
      let that = this;
      if(!that.dateValue || that.dateValue.length === 0) return this.$message.error('请选择查询时间')
      if(that.typeValue == 4){
        if(!that.stationId) return this.$message.error('请选择开户油站')
      }
      that.loading = true;
      let start_time = that.dateValue[0]
      let end_time = that.dateValue[1]
      if (start_time.length == 10) start_time = start_time + ' 00:00:00'
      if (end_time.length == 10) end_time = end_time + ' 23:59:59'
      let params = {
        start_time: that.$moment(start_time).unix(),
        end_time: that.$moment(end_time).unix(),
        company_id: that.companyValue
      };

      if (that.typeValue == 1) {
        params.type = 1
        params.stid = that.stationId
      } else {
        params.type = 4
        params.stid = [that.stationId]
      }

      await that.$axios.create({timeout: 30000}).post('/CardReport/getCardCompanyInvoicingReport', params)
        .then(function (res) {
          that.loading = false;
          if (res.data.status == 200) {
            that.orderMakingTime = that.$moment().format("YYYY-MM-DD HH:mm:ss");
            that.tableData = res.data.data.card_invoicing
            var total = res.data.data.total;
            //期初期末不展示汇总
            if (that.tableData && that.tableData.length > 0) {
              that.tableData.push(total);
            }
            that.setTable(that.tableData, "stid", "stname");
            let userInfo = localStorage.getItem('__userInfo__');
            if (userInfo && (userInfo !== "" || userInfo !== "undefined")) {
              that.orderMaker = JSON.parse(userInfo).name;
            }
          } else {
            that.$message({
              message: res.data.info,
              type: 'error'
            });
          }
        }).catch(function (error) {
          that.$message.error('网络错误')
        }).finally(() => {
          that.loading = false
        })
    },
    setTable(data, key, key01) {
      let spanOneArr = [];
      let concatOne = 0;
      let subArr = [];
      let subConcatOne = 0;
      data.forEach((item, index) => {
        if (index === 0) {
          spanOneArr.push(1);
          subArr.push(1);
        } else {
          if (
            String(item[key]) &&
            String(item[key]) == String(data[index - 1][key])
          ) {
            //当前项和前一项比较
            spanOneArr[concatOne] += 1; //相同值第一个出现的位置，统计需要合并多少行
            spanOneArr.push(0); //新增一个被合并行
            if (
              String(item[key01]) &&
              String(item[key01]) == String(data[index - 1][key01])
            ) {
              //当前项和前一项比较
              subArr[subConcatOne] += 1; //相同值第一个出现的位置，统计需要合并多少行
              subArr.push(0); //新增一个被合并行
            } else {
              subArr.push(1);
              subConcatOne = index;
            }
          } else {
            spanOneArr.push(1); //否则不合并
            concatOne = index; //指向位移
            subArr.push(1);
            subConcatOne = index;
          }
        }
      });
      var obj = {};
      obj[key] = spanOneArr;
      obj[key01] = subArr;
      this.arr = [];
      this.arr.push(obj);
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if(this.getCurrentStation.merchant_type != 2) return
      if (this.arr[0]) {
        if (columnIndex === 0) {
          const _row = this.arr[0].stid[rowIndex]; //因为rowIndex出现会从1到结尾
          const _col = _row > 0 ? 1 : 0;
          return {
            rowspan: _row,
            colspan: _col,
          };
        }
      }
    },
    clearData(e) {
      if (!this.dateValue) {
        this.tableData = [];
        this.orderMakingTime = "";
        this.orderMaker = "";
      }
    },
    //打印
    printContent() {
      let wpt = document.querySelector('#myTable');
      let newContent = wpt.innerHTML;
      let oldContent = document.body.innerHTML;
      document.body.innerHTML = newContent;
      document.getElementsByClassName("el-table__header")[0].style.width = "100%";
      document.getElementsByClassName("el-table__header")[0].style["table-layout"] = "auto";
      document.getElementsByClassName("el-table__body")[0].style.width = "100%";
      document.getElementsByClassName("el-table__body")[0].style["table-layout"] = "auto";
      window.print(); //打印方法
      history.go(0);
      document.body.innerHTML = oldContent;
    },
    //切换页码
    handleCurrentChange(val) {
      this.page = val;
      this.changeDate();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.changeDate();
    },
    //导出
    cardChargeDownload() {
      let that = this;
      let start_time = that.dateValue[0]
      let end_time = that.dateValue[1]
      if (start_time.length == 10) start_time = start_time + ' 00:00:00'
      if (end_time.length == 10) end_time = end_time + ' 23:59:59'
      let params = {
        start_time: that.$moment(start_time).unix(),
        end_time: that.$moment(end_time).unix(),
        company_id: that.companyValue
      };

      if (that.typeValue == 1) {
        params.type = 1
        params.stid = that.stationId
      } else {
        params.type = 4
        params.stid = [that.stationId]
      }

      this.$axios.get('/CardReport/cardCompanyInvoicingReportDownload', {params: params}).then((res) => {
        if (res.data.status == 200) {
          this.showDownloadTips = true;
        } else {
          this.$message({
            message: res.data.info,
            type: 'error'
          });
        }
      })
    },
  },
  watch: {
    async getCurrentStation(newValue, oldValue) {
      if (newValue.merchant_type != 0 && newValue.value != oldValue.value) {
        if (this.getCurrentStation && this.getCurrentStation.merchant_type == 2) {
          this.isGroup = true;
        } else {
          this.isGroup = false;
        }
        await this.getStationList();
        await this.getCompanyList();
        await this.changeDate();
      }
    },
    //监听班次切换变化，让多选单选重新渲染，防止报错
    'typeValue'() {
      this.update = false
      setTimeout(() => {
        this.update = true
      }, 0);
    }
  },
}
</script>

<style scoped>
</style>
