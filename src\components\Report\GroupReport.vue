<template>
    <div class="left_select">
        <div class="report-content">
            <!-- <div>
                <el-radio-group v-model="typeValue" @change="changeTypeValue">
                    <el-radio-button v-for="item in typeOptions" :key="item.value" :label="item.value">{{item.label}}</el-radio-button>
                </el-radio-group>
            </div> -->
            <div class="content_header">
                <div class="left">
                    <el-date-picker
                        v-model="dateValue"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd"
                        @change="clearData">
                    </el-date-picker>
                    <el-button type="primary" @click="changeDate" :disabled="!dateValue">生成</el-button>
                </div>
                <div class="right">
                    <el-button type="primary" :disabled="!dateValue" @click="printContent">打印</el-button>
                    <el-button type="primary" :disabled="!dateValue" @click="cardChargeDownload" v-show="isTotalReportForm">下载数据</el-button>
                </div>
            </div>
            <!-- 站间清算 -->
            <div id="myTable" ref="print" >
                <div class="report" style="text-align: center;">
                <div class="report_title">储值卡集团清结算报表</div>
                <div class="tips">
                    <div v-if="isGroup">集团名称：{{getCurrentStation.label}}</div>
                    <div>开始日期：{{dateValue?dateValue[0]:""}}</div>
                    <div>结束日期：{{dateValue?dateValue[1]:""}}</div>
                    <div>单位：元</div>
                </div>
                <el-table :data="tableData" :span-method="objectSpanMethod" v-loading="loading" size="small" align="center">
                    <!-- <el-table-column align="center" label="班次" min-width="72px" v-if="typeValue == 4">
                        <template slot-scope="scope">
                            {{$moment(scope.row.start_time*1000).format("YYYY-MM-DD HH:mm:ss") + "至" + $moment(scope.row.end_time*1000).format("YYYY-MM-DD HH:mm:ss")}}
                        </template>
                    </el-table-column> -->
                    <el-table-column align="center" prop="stid_name" label="油站" min-width="72px"> </el-table-column>
                    <el-table-column align="center" prop="date" label="集团会员油站交易">
                        <el-table-column align="center" prop="bz_pay" label="充值到账" min-width="72px">
                            <template slot-scope="scope">{{Number(scope.row.bz_pay).toFixed(2)}}</template>
                        </el-table-column>
                        <el-table-column align="center" prop="bz_discount" label="充值优惠" min-width="72px">
                            <template slot-scope="scope">{{Number(scope.row.bz_discount).toFixed(2)}}</template>
                        </el-table-column>
                        <el-table-column align="center" prop="bz_gift" label="充值赠金" min-width="72px">
                            <template slot-scope="scope">{{Number(scope.row.bz_gift).toFixed(2)}}</template>
                        </el-table-column>
                        <el-table-column align="center" prop="consume_bj" label="消费本金" min-width="72px">
                            <template slot-scope="scope">{{Number(scope.row.consume_bj).toFixed(2)}}</template>
                        </el-table-column>
                        <el-table-column align="center" prop="consume_skj" label="消费赠金" min-width="72px">
                            <template slot-scope="scope">{{Number(scope.row.consume_skj).toFixed(2)}}</template>
                        </el-table-column>
                        <el-table-column align="center" prop="consume_mz" label="消费母账" min-width="72px">
                            <template slot-scope="scope">{{Number(scope.row.consume_mz).toFixed(2)}}</template>
                        </el-table-column>
                    </el-table-column>
                    <!-- <el-table-column align="center" prop="name" label="油站会员集团交易">
                        <el-table-column align="center" prop="df_pay" label="充值实付" min-width="72px">
                            <template slot-scope="scope">{{Number(scope.row.df_pay).toFixed(2)}}</template>
                        </el-table-column>
                        <el-table-column align="center" prop="df_discount" label="充值优惠" min-width="72px">
                            <template slot-scope="scope">{{Number(scope.row.df_discount).toFixed(2)}}</template>
                        </el-table-column>
                        <el-table-column align="center" prop="df_gift" label="充值赠金" min-width="72px">
                            <template slot-scope="scope">{{Number(scope.row.df_gift).toFixed(2)}}</template>
                        </el-table-column>
                        <el-table-column align="center" prop="df_consume_bj" label="消费本金" min-width="72px">
                            <template slot-scope="scope">{{Number(scope.row.df_consume_bj).toFixed(2)}}</template>
                        </el-table-column>
                        <el-table-column align="center" prop="df_consume_skj" label="消费赠金" min-width="72px">
                            <template slot-scope="scope">{{Number(scope.row.df_consume_skj).toFixed(2)}}</template>
                        </el-table-column>
                    </el-table-column> -->
                    <el-table-column align="center" prop="yingshou_amt" label="应收金额" min-width="72px">
                        <template slot-scope="scope">{{Number(scope.row.rec_pay).toFixed(2)}}</template>
                    </el-table-column>
                    <el-table-column align="center" prop="yingfu_amt" label="应付金额" min-width="72px">
                        <template slot-scope="scope">{{Number(scope.row.act_pay).toFixed(2)}}</template>
                    </el-table-column>
                </el-table>
                <div class="table_des">
                    <div class="table_des_text">
                        <p>注：</p>
                        <div>
                            <!-- <p>1."应收金额"包含集团会员在油站充值实付金额及油站会员在集团消费金额(消费本金+消费赠金)。</p>
                            <p>2."应付金额"包含油站会员在集团充值实付金额及集团会员在油站消费金额(消费本金+消费赠金)。</p> -->
                            <p>1.应收金额=集团所有充值到账金额，包含现金等自定义支付方式。</p>
                            <p>2.应付金额=油站消费本金+消费母账。</p>
                            <p>3.合计=应收金额-应付金额。</p>
                        </div>
                    </div>
                </div>
                <div class="des_bottom">
                    <div>制表人：{{orderMaker}}</div>
                    <div>制表时间：{{orderMakingTime}}</div>
                    <div>签字：</div>
                </div>
                </div>
            </div>
        </div>

        <!-- 下载中心提示 -->
        <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>
    </div>
</template>

<script>
import DownloadTips from '../DownloadTips.vue';
import {mapGetters} from 'vuex'
export default {
    name:"GroupReport",
    components:{
        DownloadTips
    },
    data() {
        return {
            isTotalReportForm: true,
            typeOptions:[{
                value:1,
                label:"按自然日期",
            },{
                value:4,
                label:"按开班日期",
            }],
            typeValue:1,
            dateValue: [],
            tableData: [],
            loading:false,
            orderMaker:"",
            orderMakingTime:"",
            arr:[],
            showDownloadTips:false,
            isGroup: false,
        }
    },
    mounted(){
        let userInfo = localStorage.getItem("__userInfo__") || "";
        this.isTotalReportForm = JSON.parse(userInfo).group_id != 426;
        let _today = this.$moment();
        //默认为前一天的数据
        let yesterday = _today.subtract(1, 'days').format('YYYY-MM-DD');
        this.dateValue.push(yesterday);
        this.dateValue.push(yesterday);

        if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
            return false;
        }
        if(this.getCurrentStation && this.getCurrentStation.merchant_type == 2){
            this.isGroup = true;
        }else{
            this.isGroup = false;
        }
        this.changeDate();
    },
    computed:{
        ...mapGetters({
            "getCurrentStation":"getCurrentStation"
        })
    },
    methods:{
        //changeTypeValue
        changeTypeValue(){
            this.dateValue = "";
            this.orderMakingTime = "";
            this.tableData = [];
        },
        changeDate(){
            let that = this;
            let type = that.typeValue;
            let start_time = 0;
            let end_time = 0;
            if(that.dateValue){
                start_time = that.$moment(that.dateValue[0]+' 00:00:00', 'YYYY-MM-DD HH:mm:ss').unix();
                end_time = that.$moment(that.dateValue[1]+' 23:59:59', 'YYYY-MM-DD HH:mm:ss').unix();
                that.loading = true;
                that.$axios.post('/CardReport/groupSettleReport', {
                    type: type,
                    start_time: start_time,
                    end_time: end_time,
                })
                .then(function (res) {
                    that.tableData = [];
                    that.loading = false;
                    if(res.data.status == 200){
                        that.tableData = res.data.data.settle_data;
                        // if(that.typeValue == 4){
                        //     that.setTable(that.tableData,"organize");
                        // }
                        that.start_time = res.data.data.start_time;
                        that.end_time = res.data.data.end_time;
                        that.orderMakingTime = that.$moment().format("YYYY-MM-DD");
                        let userInfo = localStorage.getItem('__userInfo__');
                        if(userInfo && (userInfo !== "" || userInfo !== "undefined")){
                            that.orderMaker = JSON.parse(userInfo).name;
                        }
                    }else{
                        that.$message({
                            message: res.data.info,
                            type: 'error'
                        });
                    }
                })
                .catch(function (error) {
                });
            }else{
                that.tableData = [];
                that.orderMakingTime = "";
                that.orderMaker = "";
            }

        },
        clearData(){
            if(!this.dateValue){
                this.tableData = [];
                this.orderMakingTime = "";
                this.orderMaker = "";
            }
        },
        //打印
        printContent(){
            let wpt = document.querySelector('#myTable');
            let newContent = wpt.innerHTML;
            let oldContent = document.body.innerHTML;

            document.body.innerHTML = newContent;
            document.getElementsByClassName("el-table__header")[0].style.width = "100%"
            document.getElementsByClassName("el-table__header")[0].style["table-layout"] = "auto"
            document.getElementsByClassName("el-table__body")[0].style.width = "100%"
            document.getElementsByClassName("el-table__body")[0].style["table-layout"] = "auto"
            window.print(); //打印方法
            history.go(0)
            document.body.innerHTML = oldContent;
        },
        //下载数据
        cardChargeDownload(){
            this.$axios.post('/CardReport/downloadGroupSettleLists',{

                start_time:this.start_time,
                end_time:this.end_time,
                type:this.typeValue,
            }).then((res)=>{
                if(res.data.status == 200){
                    this.showDownloadTips = true;
                }else{
                    this.$message.error(res.data.info);
                }
            })
            //window.location.href = this.baseURL + "/CardReport/cleanAndSettleDownload?start_time="+this.start_time+"&end_time="+this.end_time+"&type="+this.typeValue;
        },

        setTable(data, key) {
            let spanOneArr = [];
            let concatOne = 0;
            data.forEach((item, index) => {
                if (index === 0) {
                    spanOneArr.push(1)
                } else {
                    if (String(item[key]) && String(item[key]) == String(data[index - 1][key])) { //当前项和前一项比较
                        spanOneArr[concatOne] += 1; //相同值第一个出现的位置，统计需要合并多少行
                        spanOneArr.push(0);//新增一个被合并行
                    } else {
                        spanOneArr.push(1); //否则不合并
                        concatOne = index;//指向位移
                    }
                }
            })
            var obj = {};
            obj[key] = spanOneArr;
            this.arr = [];
            this.arr.push(obj);
        },
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            //只有类型是班结才进行合并
            if(this.typeValue == 4){
                if (columnIndex === 0) {
                    const _row = this.arr[0].organize[rowIndex] //因为rowIndex出现会从1到结尾
                    const _col = _row > 0 ? 1 : 0
                    return {
                        rowspan: _row,
                        colspan: _col
                    }
                }
            }
        }
    },
    watch:{
        getCurrentStation(newValue,oldValue){
            if(newValue.merchant_type != 0 && newValue.value != oldValue.value){
                this.changeDate();
            }
        }
    }
}
</script>

<style scoped>
    .left_select{
        position: relative;
        height: 100%;
        background:rgba(245,245,245,1);
        /* width: 1070px; */
        margin: 0px auto;
    }
    .report-content{
        background: #fff;
        padding:20px 0;

    }
    .report-content .content_header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 20px 0;
    }
    .report .report_title {
        font-size: 24px;
        font-weight: bolder;
    }
    .report .header_table {
        width: 100%;
        border-right:1px solid #EBEEF5;
        border-bottom:1px solid #EBEEF5;
        margin-top: 20px;
    }
    .report .header_table td {
        border-left:1px solid #EBEEF5;
        border-top:1px solid #EBEEF5;
    }
    .report .header_table_row {
        height: 40px;
    }
    .report .table_des {
        margin: 20px 0;
    }
    .report .table_des_text {
        font-size: 14px;
        display: flex;
        text-align: left;
    }
    .report .table_des_text p{
        margin: 0
    }
    .des_bottom {
        display: flex;
        justify-content: flex-end;
        font-size: 14px;
        font-weight: 500;
        margin-top: 10px;
    }
    .des_bottom div{
        padding-right: 100px;
    }
    .tips{
        display: flex;
        align-items: center;
        margin: 20px 0;
    }
    .tips div{
        min-width: 100px;
        text-align: left;
        margin-right: 40px;
    }
</style>
