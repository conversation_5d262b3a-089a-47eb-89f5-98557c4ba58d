webpackJsonp([24],{"6/nJ":function(e,t){},T3zP:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a("//Fk"),l=a.n(n),r=a("Xxa5"),o=a.n(r),s=a("exGp"),u=a.n(s),c=a("Dd8w"),i=a.n(c),_=a("FZmr"),d=a("NYxO"),m=a("mtWM"),p=a.n(m),b={name:"oldCardCustomerReport",components:{DownloadTips:_.a},data:function(){return{isTotalReportForm:!0,checkAll:!1,checkedReport:[],reports:[{name:"客户充值明细表",value:1},{name:"客户消费明细表",value:2},{name:"客户充值退款明细表",value:3},{name:"客户消费退款明细表",value:4},{name:"客户资金分配明细表",value:5}],isIndeterminate:!1,stationOptions:[],stationValue:[],dateValue:[],companyOptions:[],companyValue:"",searchTypeVlaue:"1",inputTxt:"",companyName:"",customerName:"",start_time:"",end_time:"",stationName:"",tableData:[],tableData1:[],tableData2:[],tableData3:[],tableData4:[],tableData5:[],isGroup:!0,showTable01:!1,showTable02:!1,showTable03:!1,showTable04:!1,showTable05:!1,loading:!1,orderMaker:"",orderMakingTime:"",params:{type:"",start_time:"",end_time:"",station_ids:[],company_id:"",query_type:"",content:""},showDownloadTips:!1}},mounted:function(){var e=localStorage.getItem("__userInfo__")||"";if(this.isTotalReportForm=426!=JSON.parse(e).group_id,void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.getCurrentStation&&2==this.getCurrentStation.merchant_type?this.isGroup=!0:this.isGroup=!1,this.getStations(),this.getCompanyList()},computed:i()({},Object(d.c)({getCurrentStation:"getCurrentStation"})),methods:{handleCheckAllChange:function(e){var t=[];this.reports.forEach(function(e){t.push(e.value)}),this.checkedReport=e?t:[],this.isIndeterminate=!1},handleCheckedReportChange:function(e){var t=e.length;this.checkAll=t===this.reports.length,this.isIndeterminate=t>0&&t<this.reports.length},getStations:function(){var e=this;e.stationValue=[],e.$axios.post("/Stations/getStations",{}).then(function(t){200==t.data.status?(e.stationOptions=t.data.data.station_info,e.stationOptions.forEach(function(t){e.stationValue.push(t.stid)})):e.$message({message:t.data.info,type:"error"})})},getCompanyName:function(e){var t=this;this.companyOptions.forEach(function(a){e==a.ID&&(t.companyName=a.CompanyName)})},getCompanyList:function(){var e=this;e.$axios.post("/CompanyCard/getSimpleCompanyList",{page:1,page_size:800,input:""}).then(function(t){200==t.data.status?e.companyOptions=t.data.data.dt:e.$message({message:t.data.info,type:"error"})})},getCheckedStation:function(){var e=this;this.stationName="";var t=this.stationValue.length;this.stationValue.forEach(function(a,n){e.stationOptions.forEach(function(l){l.stid==a&&(e.stationName+=n==t-1?l.stname:l.stname+"，")})})},changeDateValue:function(e){if(this.dateValue){var t=this.$moment(this.dateValue[0],"YYYY-MM-DD HH:mm:ss").unix();(this.$moment(this.dateValue[1],"YYYY-MM-DD HH:mm:ss").unix()-t)/86400>31&&(this.$message({message:"只能获取一个月内的数据",type:"warning"}),this.dateValue[1]=this.$moment(1e3*(t+2678400)).format("YYYY-MM-DD HH:mm:ss"))}},changeCompanyValue:function(){this.companyName=""},createReport:function(){var e=this;return u()(o.a.mark(function t(){var a,n,l,r,s,u,c,i,_,d,m,p,b;return o.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(0!=e.stationValue.length){t.next=3;break}return e.$message({message:"请选择油站",type:"error"}),t.abrupt("return");case 3:if(e.dateValue&&0!=e.dateValue.length){t.next=6;break}return e.$message({message:"请选择时间",type:"error"}),t.abrupt("return");case 6:if(e.companyValue||e.inputTxt){t.next=9;break}return e.$message({message:"请选择车队或输入查询类型",type:"error"}),t.abrupt("return");case 9:return e.loading=!0,e.params.type=6,e.params.start_time=e.dateValue[0],e.params.end_time=e.dateValue[1],e.params.station_ids=e.stationValue,e.params.company_id=e.companyValue?e.companyValue:0,e.params.query_type=Number(e.searchTypeVlaue),e.params.content=e.inputTxt,t.next=19,e.getCapitalDetails();case 19:if(a=t.sent,n=[],200==a.data.status?(n.push(a.data.data),e.tableData=n):e.$message({message:a.data.info,type:"error"}),!e.checkedReport.includes(1)){t.next=28;break}return e.params.type=1,t.next=26,e.getCapitalDetails();case 26:200==(l=t.sent).data.status?(r=l.data.data.list.sort(function(e,t){return e.pay_date>t.pay_date?1:-1}),e.tableData1=r):e.$message({message:l.data.info,type:"error"});case 28:if(!e.checkedReport.includes(2)){t.next=34;break}return e.params.type=2,t.next=32,e.getCapitalDetails();case 32:200==(s=t.sent).data.status?(u=s.data.data.list.sort(function(e,t){return e.pay_date>t.pay_date?1:-1}),e.tableData2=u):e.$message({message:s.data.info,type:"error"});case 34:if(!e.checkedReport.includes(3)){t.next=40;break}return e.params.type=3,t.next=38,e.getCapitalDetails();case 38:200==(c=t.sent).data.status?(i=c.data.data.list.sort(function(e,t){return e.trade_time>t.trade_time?1:-1}),e.tableData3=i):e.$message({message:c.data.info,type:"error"});case 40:if(!e.checkedReport.includes(4)){t.next=46;break}return e.params.type=4,t.next=44,e.getCapitalDetails();case 44:200==(_=t.sent).data.status?(d=_.data.data.list.sort(function(e,t){return e.trade_time>t.trade_time?1:-1}),e.tableData4=d):e.$message({message:_.data.info,type:"error"});case 46:if(!e.checkedReport.includes(5)){t.next=52;break}return e.params.type=5,t.next=50,e.getCapitalDetails();case 50:200==(m=t.sent).data.status?(p=m.data.data.list.sort(function(e,t){return e.trade_time>t.trade_time?1:-1}),e.tableData5=p):e.$message({message:m.data.info,type:"error"});case 52:e.loading=!1,e.getCompanyName(e.companyValue),e.customerName=e.inputTxt,e.start_time=e.dateValue[0],e.end_time=e.dateValue[1],!(b=localStorage.getItem("__userInfo__"))||""===b&&"undefined"===b||(e.orderMaker=JSON.parse(b).name),e.orderMakingTime=e.$moment().format("YYYY-MM-DD");case 60:case"end":return t.stop()}},t,e)}))()},getCapitalDetails:function(){var e=this,t=p.a.create();return new l.a(function(a,n){t.post("/CardReportForm/getCapitalDetails",e.params,{timeout:3e4}).then(function(e){a(e)}).catch(function(e){a(e)})})},printContent:function(){var e=document.querySelector("#myTable").innerHTML,t=document.body.innerHTML;document.body.innerHTML=e;for(var a=document.getElementsByClassName("el-table__header"),n=document.getElementsByClassName("el-table__body"),l=document.getElementsByClassName("el-table__body-wrapper"),r=document.getElementsByClassName("el-table__empty-block"),o=0;o<a.length;o++)a[o].style.width="100%",a[o].style["table-layout"]="auto";for(var s=0;s<n.length;s++)n[s].style.width="100%",n[s].style["table-layout"]="auto";for(var u=0;u<l.length;u++)l[u].style["overflow-x"]="hidden";for(var c=0;c<r.length;c++)r[c]&&(r[c].style.width="100%");window.print(),history.go(0),document.body.innerHTML=t},cardChargeDownload:function(){var e=this;return u()(o.a.mark(function t(){return o.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e.params.type=e.checkedReport,t.next=3,e.exportCapitalDetails();case 3:e.showDownloadTips=!0;case 4:case"end":return t.stop()}},t,e)}))()},exportCapitalDetails:function(){var e=this,t=this;return new l.a(function(a,n){e.$axios.post("/CardReport/newCapitalDetailsDownload",e.params).then(function(e){if(200!=e.data.status)return t.$message({message:e.data.info,type:"error"}),void n(e);a(e)}).catch(function(e){a(e)})})}},watch:{getCurrentStation:function(e,t){0!=e.merchant_type&&e.value!=t.value&&(this.getCurrentStation&&2==this.getCurrentStation.merchant_type?this.isGroup=!0:this.isGroup=!1,this.getStations(),this.getCompanyList(),this.$parent.getOldReport())},checkedReport:function(e){e.includes(1)?this.showTable01=!0:this.showTable01=!1,e.includes(2)?this.showTable02=!0:this.showTable02=!1,e.includes(3)?this.showTable03=!0:this.showTable03=!1,e.includes(4)?this.showTable04=!0:this.showTable04=!1,e.includes(5)?this.showTable05=!0:this.showTable05=!1}}},f={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"report"},[a("div",{staticClass:"report-content"},[a("div",{staticClass:"checkbox-box"},[a("span",[e._v("勾选后生成报表")]),e._v(" "),a("el-checkbox",{staticClass:"checkAll",attrs:{indeterminate:e.isIndeterminate},on:{change:e.handleCheckAllChange},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[e._v("全选")]),e._v(" "),a("el-checkbox-group",{on:{change:e.handleCheckedReportChange},model:{value:e.checkedReport,callback:function(t){e.checkedReport=t},expression:"checkedReport"}},e._l(e.reports,function(t,n){return a("el-checkbox",{key:n,attrs:{label:t.value}},[e._v(e._s(t.name))])}),1)],1),e._v(" "),a("el-select",{staticStyle:{width:"220px"},attrs:{multiple:"","collapse-tags":"",placeholder:"请选择油站"},on:{change:e.getCheckedStation},model:{value:e.stationValue,callback:function(t){e.stationValue=t},expression:"stationValue"}},e._l(e.stationOptions,function(e,t){return a("el-option",{key:t,attrs:{label:e.stname,value:e.stid}})}),1),e._v(" "),a("el-date-picker",{attrs:{clearable:!1,"value-format":"yyyy-MM-dd HH:mm:ss",type:"datetimerange","range-separator":"至","default-time":["00:00:00","23:59:59"],"start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:e.changeDateValue},model:{value:e.dateValue,callback:function(t){e.dateValue=t},expression:"dateValue"}}),e._v(" "),a("el-select",{staticStyle:{width:"220px"},attrs:{filterable:"",placeholder:"请选择车队",clearable:""},on:{change:e.changeCompanyValue},model:{value:e.companyValue,callback:function(t){e.companyValue=t},expression:"companyValue"}},e._l(e.companyOptions,function(e,t){return a("el-option",{key:t,attrs:{label:e.CompanyName,value:e.ID}})}),1),e._v(" "),a("div",{staticClass:"search-box"},[a("span",{staticClass:"txt"},[e._v("查询类型")]),e._v(" "),a("el-radio-group",{model:{value:e.searchTypeVlaue,callback:function(t){e.searchTypeVlaue=t},expression:"searchTypeVlaue"}},[a("el-radio",{attrs:{label:"1"}},[e._v("手机号")]),e._v(" "),a("el-radio",{attrs:{label:"2"}},[e._v("卡号")]),e._v(" "),a("el-radio",{attrs:{label:"3"}},[e._v("卡面卡号")])],1),e._v(" "),a("el-input",{staticStyle:{width:"210px"},attrs:{placeholder:"0"==e.searchTypeVlaue?"请输入卡号":"1"==e.searchTypeVlaue?"请输入手机号":"请输入卡面卡号",clearable:""},model:{value:e.inputTxt,callback:function(t){e.inputTxt=t},expression:"inputTxt"}})],1),e._v(" "),a("div",{staticClass:"content_header"},[a("el-button",{attrs:{type:"primary"},on:{click:e.createReport}},[e._v("生成")]),e._v(" "),a("el-button",{attrs:{type:"primary",disabled:0==e.tableData.length},on:{click:e.printContent}},[e._v("打印")]),e._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:e.isTotalReportForm,expression:"isTotalReportForm"}],attrs:{type:"primary",disabled:0==e.tableData.length},on:{click:e.cardChargeDownload}},[e._v("下载数据")])],1),e._v(" "),a("div",{attrs:{id:"myTable"}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"tableData reportData"},[a("div",{staticClass:"report_title"},[e._v("储值卡客户资金对账汇总表")]),e._v(" "),a("div",{staticClass:"report_header"},[e.isGroup?a("div",[e._v("集团名称："+e._s(e.getCurrentStation.label))]):a("div",[e._v("油站名称："+e._s(e.getCurrentStation.label))]),e._v(" "),e.companyValue?a("div",[e._v("车队："+e._s(e.companyName))]):e._e(),e._v(" "),e.inputTxt?a("div",[e._v("客户："+e._s(e.customerName))]):e._e(),e._v(" "),a("div",[e._v("开始日期："+e._s(e.start_time))]),e._v(" "),a("div",[e._v("结束日期："+e._s(e.end_time))]),e._v(" "),a("div",[e._v("单位：元")])]),e._v(" "),a("el-table",{attrs:{data:e.tableData,border:"",size:"small",align:"center"}},[a("el-table-column",{attrs:{align:"center",label:"期初余额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.StartBalance).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"期间充值"}},[a("el-table-column",{attrs:{align:"center",prop:"RechargeNumber",label:"充值笔数"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.RechargeAmount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.RechargeCapital).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.RechargeBouns).toFixed(2)))]}}])})],1),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"期间消费"}},[a("el-table-column",{attrs:{align:"center",prop:"ConsumeNumber",label:"消费笔数"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ConsumeAmount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ConsumeCapital).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ConsumeBouns).toFixed(2)))]}}])})],1),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"期间退款"}},[a("el-table-column",{attrs:{align:"center",label:"充值退款"}},[a("el-table-column",{attrs:{align:"center",prop:"RechargeRefundNumber",label:"充值退款笔数"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值退款金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.RechargeRefundAmount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值退款赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.RechargeRefundBouns).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"实退金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.RechargeRefundCapital).toFixed(2)))]}}])})],1),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"recharge_refund_order_cnt",label:"消费退款"}},[a("el-table-column",{attrs:{align:"center",prop:"ConsumeRefundNumber",label:"消费退款笔数"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费退款金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ConsumeRefundAmount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费退款本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ConsumeRefundCapital).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费退款赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ConsumeRefundBouns).toFixed(2)))]}}])})],1)],1),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"期间资金分配"}},[a("el-table-column",{attrs:{align:"center",label:"母账划拨"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.DivedeAmount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"子卡卡账返款"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.RebateAmount).toFixed(2)))]}}])})],1),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"余额清零"}},[a("el-table-column",{attrs:{align:"center",prop:"ClearNumber",label:"清零笔数"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"清零金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ClearAmount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"清零本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ClearCapital).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"清零赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ClearBouns).toFixed(2)))]}}])})],1),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"期末余额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.EndBalance).toFixed(2)))]}}])})],1),e._v(" "),e.stationValue.length!=e.stationOptions.length&&e.isGroup?a("p",{staticClass:"stations"},[a("span",[e._v("*")]),e._v("取数油站："+e._s(e.stationName))]):e._e(),e._v(" "),a("p",{staticClass:"stations"},[e._v("注：客户资金变动计入储值卡开户油站")]),e._v(" "),a("div",{staticClass:"des_bottom"},[a("div",[e._v("制表人："+e._s(e.orderMaker))]),e._v(" "),a("div",[e._v("制表时间:"+e._s(e.orderMakingTime))]),e._v(" "),a("div",[e._v("签字:")])]),e._v(" "),e.showTable01?a("div",{staticClass:"report_title"},[e._v("客户充值明细表")]):e._e(),e._v(" "),e.showTable01?a("el-table",{attrs:{data:e.tableData1,border:"",size:"small",align:"center"}},[a("el-table-column",{attrs:{align:"center",prop:"stname",label:"充值油站"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_no?t.row.card_no:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡面卡号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_number?t.row.card_number:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_name?t.row.card_name:"-"))]}}],null,!1,1916413525)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"手机号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.phone?t.row.phone:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"车队名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.company_name?t.row.company_name:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"card_type",label:"卡类型"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"account_type",label:"到账类型"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"payment_name",label:"付款方式"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"付款金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.pay_money).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s((Number(t.row.recharge_money)+Number(t.row.give_money)).toFixed(2)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.recharge_money).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"赠送金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.give_money).toFixed(2)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"platform",label:"充值方式"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"pay_date",label:"交易时间"}})],1):e._e(),e._v(" "),e.showTable02?a("div",{staticClass:"report_title"},[e._v("客户消费明细表")]):e._e(),e._v(" "),e.showTable02?a("el-table",{attrs:{data:e.tableData2,border:"",size:"small",align:"center"}},[a("el-table-column",{attrs:{align:"center",prop:"stname",label:"付款油站"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_no?t.row.card_no:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡面卡号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_number?t.row.card_number:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_name?t.row.card_name:"-"))]}}],null,!1,1916413525)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"车牌号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.car_number?t.row.car_number:"-"))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"手机号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.phone?t.row.phone:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"车队名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.company_name?t.row.company_name:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"goods_name",label:"商品名称"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"数量(升)"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.goods_number).toFixed(2)))]}}],null,!1,1776219139)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"订单原价"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.goods_amount).toFixed(2)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"支付金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.pay_money).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"支付本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.account_money).toFixed(2)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"支付赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.give_money).toFixed(2)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"pay_date",label:"交易时间"}})],1):e._e(),e._v(" "),e.showTable03?a("div",{staticClass:"report_title"},[e._v("客户充值退款明细表")]):e._e(),e._v(" "),e.showTable03?a("el-table",{attrs:{data:e.tableData3,border:"",size:"small",align:"center"}},[a("el-table-column",{attrs:{align:"center",label:"卡号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_no?t.row.card_no:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡面卡号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_number?t.row.card_number:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_name?t.row.card_name:"-"))]}}],null,!1,1916413525)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"手机号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.phone?t.row.phone:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"车队名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.company_name?t.row.company_name:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"order_code",label:"交易单号"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"付款金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.pay_money).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"account_type",label:"退款账户"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"payment_name",label:"退款方式"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"实退金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.pay_money).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"退款金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.recharge_money).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"退款赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.give_money).toFixed(2)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"trade_time",label:"退款时间"}})],1):e._e(),e._v(" "),e.showTable04?a("div",{staticClass:"report_title"},[e._v("客户消费退款明细表")]):e._e(),e._v(" "),e.showTable04?a("el-table",{attrs:{data:e.tableData4,border:"",size:"small",align:"center"}},[a("el-table-column",{attrs:{align:"center",label:"卡号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_no?t.row.card_no:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡面卡号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_number?t.row.card_number:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_name?t.row.card_name:"-"))]}}],null,!1,1916413525)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"车牌号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.car_number?t.row.car_number:"-"))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"手机号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.phone?t.row.phone:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"车队名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.company_name?t.row.company_name:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"order_code",label:"交易单号"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"goods_name",label:"商品名称"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"数量(升)"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.goods_number).toFixed(2)))]}}],null,!1,1776219139)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"订单原价"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.goods_amount).toFixed(2)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"付款金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.pay_money).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"退回金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.pay_money).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"退回本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.account_money).toFixed(2)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"退回赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.give_money).toFixed(2)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"trade_time",label:"退款时间"}})],1):e._e(),e._v(" "),e.showTable05?a("div",{staticClass:"report_title"},[e._v("客户资金分配明细表")]):e._e(),e._v(" "),e.showTable05?a("el-table",{attrs:{data:e.tableData5,border:"",size:"small",align:"center"}},[a("el-table-column",{attrs:{align:"center",prop:"transfer_type",label:"分配类型"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_no?t.row.card_no:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡面卡号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_number?t.row.card_number:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_name?t.row.card_name:"-"))]}}],null,!1,1916413525)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"手机号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.phone?t.row.phone:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"车队名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.company_name?t.row.company_name:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"stname",label:"操作油站"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"account_type",label:"操作对象"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"变动前金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.change_front_balance).toFixed(2)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"操作金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.change_money).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"变动后金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.change_after_balance).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"trade_time",label:"操作时间"}})],1):e._e()],1)])],1),e._v(" "),a("download-tips",{attrs:{showDownloadTips:e.showDownloadTips},on:{"update:showDownloadTips":function(t){e.showDownloadTips=t},"update:show-download-tips":function(t){e.showDownloadTips=t}}})],1)},staticRenderFns:[]};var v=a("VU/8")(b,f,!1,function(e){a("6/nJ"),a("tyvY")},"data-v-6efaaf69",null);t.default=v.exports},tyvY:function(e,t){}});