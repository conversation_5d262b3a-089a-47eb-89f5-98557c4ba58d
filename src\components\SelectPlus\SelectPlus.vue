<template>
  <el-select
    :disabled="disabled"
    :clearable="clearable"
    v-model="dataValue"
    :multiple="multiple"
    :collapse-tags="collapseTags"
    :filterable="filterable"
    key="SelectPlus"
    @change="$emit('input', $event),listChangeAllChecked()"
  >
    <el-checkbox v-if="checkAll" class=" all_checked el-select-dropdown__item" v-model="allChecked" @change="allCheckChange"><p>全选</p>
    </el-checkbox>
    <slot name="default">
      <el-option v-for="(item,index) of list" :name="item[attr.value]" :value="item[attr.value]"
                 :key="`${item[attr.value]}${index}`"
                 :label="attr.getLabel?attr.getLabel(item):item[attr.label]"></el-option>
    </slot>
  </el-select>
</template>
<!--

对el-select增加全选/反全选功能

-->
<script>
import {cloneDeep} from "lodash";

export default {
  name: "SelectPlus",
  props: {
    checkAll: {
      type: Boolean,
      default: true
    },
    value: {
      type: [Array,String,Number],
      default: () => ([])
    },
    list: {
      type: Array,
      default: () => ([])
    },
    multiple: {
      type: Boolean,
      default: false
    },
    collapseTags: {
      type: Boolean,
      default: false
    },
    filterable: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: false
    },
    attr: {
      type: Object,
      default: () => ({label: 'name', value: 'id'})
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dataValue: [], // 当前选中
      allChecked: false, // 全选
    }
  },
  computed: {
    listValue() {
      return this.list.map(item => item[this.attr.value]);
    }
  },
  methods: {
    allCheckChange() {
      if (this.allChecked) {
        this.$emit('input', this.dataValue = cloneDeep(this.listValue))
      } else {
        this.$emit('input', this.dataValue = [])
      }
    },
    // 列表选中发生改变后，是否全选
    listChangeAllChecked() {
      // 处理当前选中的数据
      let dataList = cloneDeep(this.dataValue);
      // 两个数据长度相同
      if (dataList.length === this.listValue.length && dataList.length > 0) {
        let compareResult = false;
        for (let item of dataList) {
          compareResult = this.listValue.includes(item);
          if (!compareResult) {
            console.log('不存在的数据', item, this.listValue, dataList)
            this.allChecked = false;
            return;
          }
        }
        this.allChecked = true;
      } else {
        this.allChecked = false; // 不是全选
      }
    }
  },
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler(news) {
        this.dataValue = news;
        this.listChangeAllChecked();
      }
    }
  }
}
</script>

<style scoped>
.all_checked {
  display: block;
}
</style>
