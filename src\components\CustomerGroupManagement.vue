<template>
    <div class="modify">
        <div class="modify-header">
            <el-button type="primary" @click="showAddCustomerGroup">创建卡组</el-button>
            <el-button type="text" @click="download">下载导入模板</el-button>
        </div>
        <!-- 表数据 -->
        <el-table
            class="tableData"
            v-loading="loading"
            :data="tableData"
            style="width: 100%;">
            <el-table-column
                align="left"
                label="名称"
                min-width="200">
                <template slot-scope="scope">
                    <el-badge v-if="scope.row.IsDefault == 1" value="默认" class="item">
                        <span>{{ scope.row.CustomerGroupName }}</span>
                    </el-badge>
                    <span v-else>{{ scope.row.CustomerGroupName }}</span>
                </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="状态"
                min-width="120">
                <template slot-scope="scope">
                    <span>{{ scope.row.State == 100 ? "启用" : "禁用" }}</span>
                </template>
            </el-table-column>
            <el-table-column
                align="center"
                prop="CardCount"
                label="卡总数"
                min-width="120">
            </el-table-column>
            <el-table-column
                align="center"
                label="备注"
                prop="Remark"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                fixed="right"
                label="操作"
                min-width="250">
                <template slot-scope="scope">
                <el-button @click="showAddCard(scope.row)" type="text" size="small">添加</el-button>
                <el-upload
                    class="upload" style="display:inline"
                    action=""
                    :multiple="false"
                    :show-file-list="false"
                    accept="csv, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    :http-request="httpRequest">
                    <el-button size="small" type="text" @click="addMore(scope.row)">导入</el-button>
                </el-upload>
                <el-button @click="handleModifyClick(scope.row)" type="text" size="small">修改</el-button>
                <el-button style="margin-left:0" @click="handleCardListClick(scope.row)" type="text" size="small">查看</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 页码 -->
        <div class="page_content">
            <el-pagination class="page_left"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-size="pageSize"
                layout="prev, pager, next"
                :total="total">
            </el-pagination>
            <el-pagination class="page_right"
                @size-change="currentPage=1,handleSizeChange($event)"
                :page-sizes="[10, 15, 20, 30]"
                :page-size="pageSize"
                layout="total, sizes"
                :total="total">
            </el-pagination>
        </div>

        <!-- 添加/修改弹窗 -->
        <el-dialog
        :close-on-click-modal="false"
        class="card-dialog"
        :title='isAdd?"创 建":"修改卡组"'
        :visible.sync="modifyDialogVisible"
        width="600px">
        <el-form ref="modifyForm" :model="modifyForm" :rules="modifyFormRules" label-width="100px">
            <el-form-item label="名称" prop="CustomerGroupName">
                <el-input v-model="modifyForm.CustomerGroupName"></el-input>
            </el-form-item>
            <el-form-item label="是否默认组">
                <el-switch v-model="modifyForm.IsDefault" :disabled="isDefault"></el-switch>
            </el-form-item>
            <el-form-item label="是否启用">
                <el-switch v-model="modifyForm.State"></el-switch>
            </el-form-item>
            <el-form-item label="备注">
                <el-input type="textarea" v-model="modifyForm.Remark" maxlength="110" show-word-limit></el-input>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button size="mini" @click="modifyDialogVisible = false">取 消</el-button>
            <el-button size="mini" type="primary" @click="setCustomerGroup('modifyForm')">{{isAdd?"创 建":"确 定"}}</el-button>
        </span>
        </el-dialog>

        <!-- 查看卡列表 -->
        <el-dialog
          @closed="detailCurrentPage=1,pageSizeDetail=10"
          append-to-body
        :close-on-click-modal="false"
        class="card-dialog"
        title="查看卡组列表"
        :visible.sync="cardListDialogVisible"
        width="90%">
        <span>查询类型</span>
        <el-radio-group v-model="checkSearchType">
            <el-radio label="1">手机号</el-radio>
            <el-radio label="2">卡号</el-radio>
            <el-radio label="3">卡面卡号</el-radio>
        </el-radio-group>
        <el-input
            style="width:210px"
            :placeholder="checkSearchType=='2'?'请输入卡号':checkSearchType=='1'?'请输入手机号':'请输入卡面卡号'"
            v-model="checkSearchTxtTemp">
        </el-input>
        <el-button type="primary" @click="detailCurrentPage=1,checkSearchTxt=checkSearchTxtTemp,getRelevanceInfoQuery(customerGroupId)" >查询</el-button>
        <el-table
            v-loading="detailLoading"
            ref="multipleTable" @selection-change="handleSelectionChange"
            :data="detailTableData"
            style="width: 100%;margin:30px 0;">
            <el-table-column
                type="selection"
                width="55"></el-table-column>
            <el-table-column
                align="center"
                label="手机号"
                min-width="110"
                prop="Phone"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                label="持卡人"
                min-width="120"
                prop="cardholder_name"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                prop="CardNO"
                label="卡号"
                min-width="200"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                label="卡面卡号"
                prop="CardNumber"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                prop="CarNumber"
                min-width="180"
                label="车牌号"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                prop="CardType"
                label="卡类型">
                <template slot-scope="scope">{{ getCardType(scope.row.CardType) }}</template>
            </el-table-column>
            <el-table-column
                align="center"
                prop="CompanyName"
                min-width="210"
                label="车队名称"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="left"
                prop="CardName"
                min-width="210"
                label="卡名称">
            </el-table-column>
            <el-table-column
                prop="Remark"
                min-width="210"
                label="优惠信息"
                :width="setColumnWidth()"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                prop="State"
                label="卡状态">
                <template slot-scope="scope">{{ getCardState(scope.row.State) }}</template>
            </el-table-column>
        </el-table>

        <!-- 页码 -->
        <div class="page_content">
            <el-pagination class="page_left"
                @current-change="handleDetailCurrentChange"
                :current-page="detailCurrentPage"
                :page-size="pageSizeDetail"
                layout="prev, pager, next"
                :total="detailTotal">
            </el-pagination>
            <el-pagination class="page_right"
                @size-change="detailCurrentPage=1,handleDetailSizeChange($event)"
                :page-sizes="[10, 15, 20, 30]"
                :page-size="pageSizeDetail"
                layout="total, sizes"
                :total="detailTotal">
            </el-pagination>
        </div>

        <span slot="footer" class="dialog-footer">
            <el-button @click="cardListDialogVisible = false" size="small">取 消</el-button>
            <el-button type="danger" :disabled="multipleSelection.length < 1"  @click="delect" size="mini">删除</el-button>
            <el-button type="primary" :disabled="detailTableData.length<1" @click="downloadCustomerGroup" size="mini">导出</el-button>
            <el-button type="primary" :disabled="detailTableData.length<1" @click="downloadAllCustomerGroup" size="mini">导出全部</el-button>
        </span>
        </el-dialog>

        <!-- 添加卡弹窗 -->
        <el-dialog
        :close-on-click-modal="false"
        class="card-dialog"
        title="添加卡"
        :visible.sync="addDialogVisible"
        width="90%">
        <span>查询类型</span>
        <el-radio-group v-model="searchType">
            <el-radio label="1">手机号</el-radio>
            <el-radio label="0">卡号</el-radio>
            <el-radio label="2">卡面卡号</el-radio>
        </el-radio-group>
        <el-input
            style="width:210px"
            :placeholder="searchType=='0'?'请输入卡号':searchType=='1'?'请输入手机号':'请输入卡面卡号'"
            v-model="searchTxt">
        </el-input>
        <el-button type="primary" @click="getUserCardList" :disabled="!searchTxt.length>0">查询</el-button>
        <el-table
            v-show="tableShow"
            v-loading="cardListLoading"
            :data="cardListTableData"
            style="width: 100%;margin:30px 0;"
            @selection-change="handleCardSelectionChange">
            <el-table-column
                type="selection"
                width="55">
            </el-table-column>
            <el-table-column
                align="left"
                label="类型">
                <template slot-scope="scope">{{ getCardType(scope.row.CardType) }}</template>
            </el-table-column>
            <el-table-column
                align="center"
                prop="Phone"
                label="手机号">
            </el-table-column>
            <el-table-column
                align="center"
                label="持卡人"
                min-width="120"
                prop="cardholder_name"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="left"
                prop="CardName"
                label="卡名称"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                prop="CardNO"
                min-width="200"
                label="加油卡号"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                prop="CardNumber"
                label="卡面卡号"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                prop="CarNumber"
                label="车牌号"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                label="状态">
                <template slot-scope="scope">
                    <span>{{ getCardState(scope.row.State) }}</span>
                </template>
            </el-table-column>
        </el-table>

        <!-- 页码 -->
        <div class="page_content" v-show="tableShow">
            <el-pagination class="page_left"
                @current-change="handlecardListCurrentChange"
                :current-page="cardListCurrentPage"
                :page-size="pageSizeCardList"
                layout="prev, pager, next"
                :total="cardListTotal">
            </el-pagination>
            <el-pagination class="page_right"
                @size-change="handleCardListSizeChange"
                :page-sizes="[10, 15, 20, 30]"
                :page-size="pageSizeCardList"
                layout="total, sizes"
                :total="cardListTotal">
            </el-pagination>
        </div>

        <span slot="footer" class="dialog-footer">
            <el-button @click="addDialogVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="relevanceCustomerGroup" :disabled="multipleCardSelection.length<1" size="mini">添加卡</el-button>
        </span>
        </el-dialog>

      <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>
    </div>
</template>

<script>
import setColumnWidth from './formatTabel.js'
import {mapGetters} from 'vuex'
import XLSX from 'xlsx'
import DownloadTips from "./DownloadTips.vue";
export default {
    name: 'CustomerGroupManagement',
  components: {DownloadTips},
    data () {
        return {
            showDownloadTips:false,
            tableData: [],
            multipleSelection:[],
            loading:true,
            currentPage:1,
            pageSize:10,
            total:0,
            modifyDialogVisible:false,//修改弹窗，默认隐藏
            modifyForm:{
                ID:"",
                CustomerGroupName:"",
                CustomerGroupType:"1",
                IsDefault:true,
                State:true,
                Remark:"",
            },//修改的信息
            modifyFormRules:{
                CustomerGroupName:[ { required: true, message: '请输入客户组名称', trigger: 'blur' } ]
            },
            cardListDialogVisible:false,//查看卡列表，默认隐藏
            addDialogVisible:false,//添加用户/卡
            searchTxt:"",//搜索内容
            checkSearchTxt:"",//搜索内容[搜索的]
            checkSearchTxtTemp:"",//搜索内容[当前输入框的]
            isAdd:true,//判断添加/修改
            cardListTableData:[],//客户卡列表
            multipleCardSelection:[],//选中的客户卡
            cardListLoading:true,//客户卡列表loading
            cardListCurrentPage:1,
            pageSizeCardList:10,
            cardListTotal:0,
            phone:"",//客户手机号
            customerGroupId:"",//当前选择的客户组id
            customerGroupName:"",//当前选择的客户组名称
            customerGroupType:"",//当前选择的客户组类型
            detailTableData:[],//查看客户组关联明细
            detailCurrentPage:1,
            detailTotal:0,
            pageSizeDetail:10,
            detailLoading:true,
            tableShow:false,
            searchType:"1",//查询类型
            checkSearchType:"1",//查询类型
            cardStateOptions:[{
                value: '0',
                label: '全部'
                },{
                value: '10',
                label: '待启用'
                },{
                value: '20',
                label: '已制卡'
                },{
                value: '100',
                label: '已激活'
                },{
                value: '101',
                label: '已冻结'
                },{
                value: '110',
                label: '挂失'
                },{
                value: '111',
                label: '坏卡'
            }],
            cardTypeOptions:[{
                value: '0',
                label: '全部'
                },{
                value: '1',
                label: '个人卡'
                },{
                value: '2',
                label: '车队卡'
                },{
                value: '3',
                label: '不记名卡'
                },{
                value: '4',
                label: '员工卡'
                },{
                value: '5',
                label: '第三方卡'
            }],
            isDefault:false//判断是否为默认值的变量
        }
    },
    mounted(){
        if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
            return false;
        }
        this.getCustomerGroupList();
    },
    computed:{
        ...mapGetters({
            "getCurrentStation":"getCurrentStation"
        })
    },
    methods:{
        setColumnWidth(){
            return setColumnWidth(this.detailTableData,4)
        },
        //获取客户组数据列表
        getCustomerGroupList(){
            let that = this;
            that.loading = true;
            that.$axios.post('/CustomerGroup/getCustomerGroupList', {
                page: that.currentPage,
                page_size: that.pageSize,
            })
            .then(function (res) {
                that.loading = false;
                if(res.data.status == 200){
                    that.tableData = res.data.data.dt;
                    that.total = res.data.data.TotalQty;
                    //存在默认卡组，再次添加卡组时，默认卡组选项关闭
                    if(that.total > 1){
                        that.modifyForm.IsDefault = false;
                    }
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
            });
        },
        //打开添加客户组弹窗
        showAddCustomerGroup(){
            this.isAdd = true;
            this.modifyDialogVisible = true;
            this.modifyForm.CustomerGroupName = "";
            this.modifyForm.CustomerGroupType = "1";
            this.modifyForm.State = true;
            this.modifyForm.Remark = "";
            this.isDefault = false;
            if(this.total > 1){
                this.modifyForm.IsDefault = false;
            }
        },

        //打开修改弹窗
        handleModifyClick(val){
            this.isAdd = false;
            this.modifyDialogVisible = true;
            this.modifyForm.ID = val.ID;
            this.modifyForm.CustomerGroupName = val.CustomerGroupName;
            this.modifyForm.CustomerGroupType = String(val.CustomerGroupType);
            this.modifyForm.IsDefault = val.IsDefault==1?true:false;
            this.modifyForm.State = val.State==100?true:false;
            this.modifyForm.Remark = val.Remark;
            if(val.IsDefault == 1){
                this.isDefault = true;
            }else{
                this.isDefault = false;
            }
        },
        //打开查看卡列表弹窗
        handleCardListClick(val){
            let that = this;
            that.checkSearchTxt = "";
            that.checkSearchTxtTemp = "";
            that.checkSearchType = "1";
            that.detailLoading = true;
            that.cardListDialogVisible = true;
            that.customerGroupId = val.ID;
            that.customerGroupType = val.CustomerGroupType;
            that.customerGroupName = val.CustomerGroupName;
            that.getRelevanceInfoQuery(val.ID);
        },
        //获取卡列表
        getRelevanceInfoQuery(id){
            let that = this;
            that.$axios.post('/CustomerGroup/getRelevanceInfoQuery', {
                page: that.detailCurrentPage,
                page_size: that.pageSizeDetail,
                id:id,
                type: that.checkSearchType,
                info: that.checkSearchTxt,
            })
            .then(function (res) {
                that.detailLoading = false;
                if(res.data.status == 200){
                    that.detailTableData = res.data.data.dt.RelevanceDetailList;
                    that.detailTotal = res.data.data.TotalQty;
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
            });
        },
        handleDetailCurrentChange(val){
            this.detailCurrentPage = val;
            this.getRelevanceInfoQuery(this.customerGroupId);
        },
        handleDetailSizeChange(val){
            this.pageSizeDetail = val;
            this.getRelevanceInfoQuery(this.customerGroupId);
        },
        //主页面数据切换页码
        handleCurrentChange(val){
            this.currentPage = val;
            this.getCustomerGroupList();
        },
        handleSizeChange(val){
            this.pageSize = val;
            this.getCustomerGroupList();
        },
        //显示添加卡
        showAddCard(val){
            this.addDialogVisible = true;
            this.customerGroupId = val.ID;
            this.customerGroupType = val.CustomerGroupType;
            this.searchTxt = "";
            this.searchType = "1";
            this.tableShow = false;
        },
        //获取客户卡列表
        getUserCardList(){
            let that = this;
            that.tableShow = true;
            that.cardListLoading = true;
            that.$axios.post('/Card/getUserCardList', {
                page: that.cardListCurrentPage,
                page_size: that.pageSizeCardList,
                input_type: that.searchType,
                input: that.searchTxt,
            })
            .then(function (res) {
                that.cardListLoading = false;
                if(res.data.status == 200){
                    that.cardListTableData = res.data.data.dt;
                    that.cardListTotal = res.data.data.TotalQty;
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
            });
        },
        //获取选中数据
        handleSelectionChange(val){
            this.multipleSelection = val;
        },
        //handlecardListCurrentChange
        handlecardListCurrentChange(val){
            this.cardListCurrentPage = val;
            this.getUserCardList();
        },
        handleCardListSizeChange(val){
            this.pageSizeCardList = val;
            this.getUserCardList();
        },
        //获取选择的卡
        handleCardSelectionChange(val){
            this.multipleCardSelection = val;
        },
        //添加卡
        relevanceCustomerGroup(){
            let that = this;
            let RelevanceList=[];
            this.multipleCardSelection.forEach((element)=>{
                RelevanceList.push({
                    CustomerGroupID: that.customerGroupId,
                    RelevanceID: element.ID,
                    CustomerGroupType: that.customerGroupType,
                })
            })
            let params = {
                RelevanceList: RelevanceList
            }
            that.$axios.post('/CustomerGroup/relevanceCustomerGroup', params)
            .then(function (res) {
                if(res.data.status == 200){
                    that.$message({
                        message: "添加成功",
                        type: 'success'
                    });
                    that.addDialogVisible = false;
                    that.getCustomerGroupList();
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
            });
        },
        //添加/修改客户组
        setCustomerGroup(formName){
            let that = this;
            that.$refs[formName].validate((valid) => {
            if (valid) {
                let params = {
                    CustomerGroupName:that.modifyForm.CustomerGroupName,
                    CustomerGroupType:1,
                    IsDefault:that.modifyForm.IsDefault?1:0,
                    State:that.modifyForm.State?100:101,
                    Remark:that.modifyForm.Remark
                };
                if(!that.isAdd){
                    params.ID = that.modifyForm.ID;
                }
                that.$axios.post('/CustomerGroup/setCustomerGroup', params)
                .then(function (res) {
                    if(res.data.status == 200){
                        if(that.isAdd){
                            that.$message({
                                message: '添加成功',
                                type: 'success'
                            });
                        }else{
                            that.$message({
                                message: '修改成功',
                                type: 'success'
                            });
                        }
                        that.getCustomerGroupList();
                    }else{
                        that.$message({
                            message: res.data.info,
                            type: 'error'
                        });
                    }
                    that.modifyDialogVisible = false;
                })
                .catch(function (error) {
                    that.modifyDialogVisible = false;
                    if(that.isAdd){
                        that.$message({
                            message: '添加失败',
                            type: 'error'
                        });
                    }else{
                        that.$message({
                            message: '修改失败',
                            type: 'error'
                        });
                    }
                });
            }
            });
        },
        //
        addMore(val){
            this.customerGroupId = val.ID;
            this.customerGroupType = val.CustomerGroupType;
            this.customerGroupName = val.CustomerGroupName;
        },
        //批量添加
        httpRequest (e) {
            let file = e.file // 文件信息
            let that = this;

            if (!file) {
                // 没有文件
                return false
            } else if (!/\.(xls|xlsx)$/.test(file.name.toLowerCase())) {
                // 格式根据自己需求定义
                that.$message.error('上传格式不正确，请上传xls或者xlsx格式')
                return false
            }

            const fileReader = new FileReader()
            fileReader.onload = (ev) => {
            try {
                const data = ev.target.result
                const workbook = XLSX.read(data, {
                type: 'binary' // 以字符编码的方式解析
                })
                const exlname = workbook.SheetNames[0] // 取第一张表
                const exl = XLSX.utils.sheet_to_json(workbook.Sheets[exlname]) // 生成json表格内容

                let params = {
                    CustomerGroupID: that.customerGroupId,
                    CustomerGroupType: that.customerGroupType,
                    CustomerGroupName: that.customerGroupName,
                    json: exl
                }
                that.$axios.post('/CustomerGroup/relevanceBigCustomerGroup', params)
                .then(function (res) {
                    if(res.data.status == 200){
                        that.$message({
                            message: "添加成功",
                            type: 'success'
                        });
                        that.getCustomerGroupList();
                    }else{
                        that.$message({
                            message: res.data.info,
                            type: 'error'
                        });
                    }
                })
                .catch(function (error) {
                });

                // 将 JSON 数据挂到 data 里

                // this.tableData = exl
                // document.getElementsByName('file')[0].value = '' // 根据自己需求，可重置上传value为空，允许重复上传同一文件
            } catch (e) {
                console.log('出错了：：')
                return false
            }
            }
            fileReader.readAsBinaryString(file)
        },
        //删除卡
        delect(val){
            let that = this;
            this.$confirm('确定删除?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
            }).then(() => {
                let RelevanceIDArray = [];
                let cardIdArray = [];
                console.log(that.customerGroupName);
                that.multipleSelection.forEach(element => {
                    RelevanceIDArray.push(element.RelevanceID);
                    cardIdArray.push(element.CardNO);
                })
                let params = {
                    CustomerGroupID: that.customerGroupId,
                    RelevanceID: RelevanceIDArray,
                    CustomerGroupType: that.customerGroupType,
                    CustomerGroupName: that.customerGroupName,
                    CardNO: cardIdArray,
                    State: 101,
                }
                that.$axios.post('/CustomerGroup/changeCustomerGroupMemberState', params)
                .then(function (res) {
                    if(res.data.status == 200){
                        that.$message({
                            message: "删除成功",
                            type: 'success'
                        });
                        that.cardListDialogVisible = false;
                        that.getCustomerGroupList();
                    }else{
                        that.$message({
                            message: res.data.info,
                            type: 'error'
                        });
                    }
                })
                .catch(function (error) {
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        //导出
        downloadCustomerGroup(){
            window.location.href = this.baseURL + "/CustomerGroup/downloadCustomerGroup?CustomerGroupID="+this.customerGroupId+"&CustomerGroupType="+this.customerGroupType+"&page="+this.detailCurrentPage+"&page_size="+this.pageSizeDetail;
        },
        //导出全部
        async downloadAllCustomerGroup(){
          try {
            const res = await this.$axios.get('/CustomerGroup/downloadCustomerGroup', {
              params:{
                CustomerGroupID: this.customerGroupId,
                CustomerGroupType: this.customerGroupType,
              }
            })
            console.log('导出',res)
            if(res.data.status !== 200) return this.$message.error(res.data.info)
            this.cardListDialogVisible = false
            this.showDownloadTips = true
          }catch (e) {
            this.$message.error('网路错误')
          }
            // window.location.href = this.baseURL + "/CustomerGroup/downloadCustomerGroup?CustomerGroupID="+this.customerGroupId+"&CustomerGroupType="+this.customerGroupType;
        },
        //获取卡状态
        getCardState(val){
            let typeName = "";
            this.cardStateOptions.forEach((element)=>{
                if(element.value == val){
                    typeName =  element.label;
                }
            })
            return typeName;
        },
        //获取卡类型
        getCardType(val){
            let typeName = "";
            this.cardTypeOptions.forEach((element)=>{
                if(element.value == val){
                    typeName =  element.label;
                }
            })
            return typeName;
        },
        //下载模板
        download(){
            window.location.href = this.baseURL + "/CustomerGroup/downloadInputTem";
        },
    },
    watch: {
        getCurrentStation(newValue,oldValue){
            if(newValue.merchant_type != 0 && newValue.value != oldValue.value){
                this.getCustomerGroupList();
            }
        },
    },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.modify-header{
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* 页码 */
.page_content {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
}
</style>
<style>
.card-dialog .el-dialog{
    padding: 0 20px;
}
.tableData .cell:first-child{
    overflow: initial;
}
.tableData .el-badge__content.is-fixed{
    right: -4px;
    top: 10px;
}
</style>
