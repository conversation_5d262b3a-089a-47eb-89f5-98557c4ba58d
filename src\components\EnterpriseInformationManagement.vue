<template>
    <div class="wrap">
        <div class="header">
            <div><el-button type="primary" @click="add">添加</el-button></div>
            <div>
                <!--<el-input
                    placeholder="请输入车队名称"
                    style="width:200px;margin:0;"
                    prefix-icon="el-icon-search"
                    v-model="searchTxt" clearable>
                </el-input>-->
              <el-select
                v-model="searchTxt"
                clearable
                @clear="search"
                filterable
                remote
                reserve-keyword
                placeholder="请输入车队名称"
                :remote-method="remoteMethod">
                <el-option
                  v-for="item in companyOptions"
                  :key="item.CompanyID"
                  :label="item.CompanyName"
                  :value="item.CompanyID">
                </el-option>
              </el-select>
                <el-button type="primary" @click="search" :disabled="!searchTxt">查询</el-button>
                <el-button type="primary" @click="carInfoDownload" :disabled="!tableData.length>0" v-show="isTotalReportForm">下载数据</el-button>
            </div>
        </div>
        <el-table
            v-loading="loading"
            @filter-change="filterHandler"
            class="table_out" style="width:100%;margin-bottom:20px"  min-height="580"
            :data="tableData">
            <el-table-column
              align="center"
              prop="ID"
              min-width="80"
              label="车队ID"
              :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                prop="CompanyName"
                min-width="180"
                label="车队名称"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                min-width="180"
                label="开户油站"
                v-if="!isGroupSettle"
                >
                <template slot-scope="scope" >
                    <span v-if="getStationName(scope.row.StationNO) !== ''">{{ getStationName(scope.row.StationNO) }}</span>
                    <span v-else>-</span>
                </template>
            </el-table-column>
            <el-table-column
                align="center"
                prop="CompanyCardStockNum"
                min-width="80"
                label="卡库存"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                prop="CompanyCardNum"
                min-width="80"
                label="领卡数"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                v-if="showLockPrice"
                align="left"
                width="120"
                label="母账升数">
                <template slot-scope="scope">{{ zero2Formater(sumUnitLiter(scope.row.companyBatches))}}</template>
            </el-table-column>
            <el-table-column
                align="left"
                width="120"
                label="母账余额（元）">
                <template slot-scope="scope">{{ Number(scope.row.CompanySumAccount).toFixed(2) }}</template>
            </el-table-column>
            <el-table-column
                align="left"
                width="120"
                label="母账本金（元）">
                <template slot-scope="scope">{{ Number(scope.row.CompanyUsableAccount).toFixed(2) }}</template>
            </el-table-column>
            <el-table-column
                align="left"
                width="120"
                label="母账赠金（元）">
                <template slot-scope="scope">{{ Number(scope.row.CompanyGiveAccount).toFixed(2) }}</template>
            </el-table-column>
            <el-table-column
                v-if="showLockPrice"
                align="left"
                width="120"
                label="子卡升数">
                <template slot-scope="scope">{{ zero2Formater(Number(scope.row.CardSumLiter||'0').toFixed(4)) }}</template>
            </el-table-column>
            <el-table-column
                align="left"
                width="120"
                label="子卡余额（元）">
                <template slot-scope="scope">{{ Number(scope.row.CardSumAccount).toFixed(2) }}</template>
            </el-table-column>
            <el-table-column
                align="left"
                width="120"
                label="子卡本金（元）">
                <template slot-scope="scope">{{ Number(scope.row.CardKZSum).toFixed(2) }}</template>
            </el-table-column>
            <el-table-column
                align="left"
                width="120"
                label="子卡赠金（元）">
                <template slot-scope="scope">{{ Number(scope.row.CardSKJSum).toFixed(2) }}</template>
            </el-table-column>
            <el-table-column
                align="center"
                prop="CompanyContacts"
                min-width="120"
                label="管理员"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                prop="ContactsPhone"
                min-width="120"
                label="管理员手机号"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                min-width="140"
                label="扣款方式">
                <template slot-scope="scope"> {{ scope.row.PayMethod == 0 ? "仅允许卡账扣款" : scope.row.PayMethod == 1 ? "母账优先" : "卡账优先" }}</template>
            </el-table-column>
            <el-table-column
                align="center"
                min-width="120"
                label="信用功能"
                prop="CompanyCreditType"
                :formatter="formatterCellval">
                <template slot-scope="scope"> {{ scope.row.CompanyCreditType == 0 ? "不启用" : scope.row.CompanyCreditType == 1 ? "信贷" : "保证金" }}</template>
            </el-table-column>
            <el-table-column
              align="center"
              width="120"
              label="信用金额（元）">
              <template slot-scope="scope">
                <template v-if="scope.row.CompanyCreditType == 1">{{ Number(scope.row.CompanyCreditSum).toFixed(2) }}</template>
                <template v-else-if="scope.row.CompanyCreditType == 0">-</template>
                <template v-else>{{ Number(scope.row.CompanyCreditAccount).toFixed(2) }}</template>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              width="120"
              label="已用额度（元）">
              <template slot-scope="scope">
                <template v-if="scope.row.CompanyCreditType == 1">{{ Number(scope.row.CompanyCreditUsageLimit).toFixed(2) }}</template>
                <template v-else>-</template>
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                min-width="120"
                column-key="State"
                :filters="[{text: '启用', value: '100'},{text: '禁用', value: '101'}]"
                label="状态">
                <template slot-scope="scope"><span>{{ scope.row.State == 100 ? "启用" : "禁用" }}</span></template>
            </el-table-column>
            <el-table-column
                align="center"
                width="200"
                fixed="right"
                label="操作">
                <template slot-scope="scope">
                    <el-button @click="showCompanyInfo(scope.row)" type="text" size="small">查看</el-button>
                    <el-button @click="handleCardDetail(scope.row.ID)" type="text" size="small">子卡管理</el-button>
                    <el-button @click="handleModifyClick(scope.row)" type="text" size="small">车队设置</el-button>
                    <!-- <el-button @click="handleModifyClick(scope.row)" type="text" size="small">编辑</el-button>
                    <el-button @click="handleCardDetail(scope.row.ID)" type="text" size="small">查询</el-button>
                    <el-button @click="handleDetailClick(scope.row)" type="text" size="small">管理员</el-button> -->
                </template>
            </el-table-column>
        </el-table>

        <!-- 页码 -->
        <div class="page_content">
            <el-pagination class="page_left"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-size="pageSize"
                layout="prev, pager, next"
                :total="total">
            </el-pagination>
            <el-pagination class="page_right"
                @size-change="handleSizeChange"
                :page-sizes="[10, 15, 20, 30]"
                :page-size="pageSize"
                layout="total, sizes"
                :total="total">
            </el-pagination>
        </div>

        <!-- 添加/修改弹窗 -->
        <el-dialog
        class="card-dialog"
        :title='isAdd?"添加车队":"车队设置"'
        :visible.sync="modifyDialogVisible"
        :close-on-click-modal="false"
        width="950px">
        <el-form ref="modifyForm" :model="modifyForm" :rules="modifyFormRules" label-width="120px">
            <el-form-item label="开户油站" prop="station_id" v-if="isAdd && !isGroupSettle">
                <el-select v-model="modifyForm.station_id" placeholder="请选择开户油站">
                    <el-option v-for="item in stationList" :key="item.index" :label="item.stname" :value="item.stid"></el-option>
                </el-select>
                <i class="el-icon-warning" style="color: #F56C6C;"></i>确认添加后，无法更改
            </el-form-item>
            <el-form-item label="车队名称" prop="company_name">
                <el-input style="width:300px" v-model="modifyForm.company_name" placeholder="请输入车队名称"></el-input>
            </el-form-item>
            <el-form-item label="车队地址" prop="company_address">
                <el-input style="width:300px" v-model="modifyForm.company_address" placeholder="请输入车队地址"></el-input>
            </el-form-item>
            <el-form-item label="锁价车队" v-if="showLockPrice">
                <el-radio-group :disabled="!isAdd" v-model="modifyForm.fixedPrice" @change="changeFixedPrice">
                    <el-radio v-for="(option, index) in fixedPriceRadioOptions" :key="index" :label="option.value">{{ option.label }}</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="扣款方式" prop="pay_method">
                <el-radio-group v-model="modifyForm.pay_method">
                    <el-radio label="2">卡账优先</el-radio>
                    <el-radio label="1">母账优先</el-radio>
                    <el-radio label="0">仅允许卡账扣款</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="子卡充值" prop="isAllowRecharge">
                <el-radio-group v-model="modifyForm.isAllowRecharge" :disabled="modifyForm.fixedPrice !== fixedPriceRadioOptions[0].value">
                    <el-radio label="1">允许</el-radio>
                    <el-radio label="0">不允许</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="信用功能" prop="credit_type">
                <el-radio-group v-model="modifyForm.credit_type">
                    <el-radio label="0">不启用</el-radio>
                    <el-radio label="1">信贷</el-radio>
                    <el-radio label="2">保证金</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item prop="credit_num" :label="modifyForm.credit_type==1?'信贷额度':'保证金额度'" v-if="modifyForm.credit_type != 0">
                <el-input style="width:160px" v-model="modifyForm.credit_num"></el-input>
                <span>元</span>
            </el-form-item>
            <el-form-item :label="index == 0 ? '管理员信息' : '管理员信息'+(index+1)" v-for="(item,index) in ContactList" :key="index" required>
                <el-input style="width:180px" v-model="item.ContactName" placeholder="请输入管理员"></el-input>
                <el-input style="width:180px" v-model="item.ContactPhone" maxlength = '11' placeholder="请输入管理员手机号"></el-input>
                <el-input style="width:180px" v-model="item.Remark" maxlength = '30' placeholder="请输入备注"></el-input>
                <el-button v-if="index == 0" type="text" style="margin:0 15px 0 20px" @click="addContacts">添加</el-button>
                <el-button v-else type="text" style="margin:0 15px 0 20px; color:#f56c6c" @click="delectContacts(index)">删除</el-button>
                <span v-if="index == 0" style="">最多设置3位管理员</span>
            </el-form-item>
            <el-form-item label="开票方式" prop="invoice_open_type">
              <el-radio-group v-model="modifyForm.invoice_open_type">
                <el-radio label="1" >消费开票</el-radio>
                <el-radio label="0" >充值开票</el-radio>
                <el-radio label="2" >不开票</el-radio>
              </el-radio-group>
              <span style="margin-left: 20px;color: #999;">优先级大于制卡规则</span>
            </el-form-item>
          <!--灰度开启则显示-->
          <el-form-item label="客户类型" v-if="showInvoiceReport">
            <select-plus
              clearable
              v-loading="customerTypeLoading"
              v-model="modifyForm.CompanyType"
              :list="invalidCustomerTypeList"
              :multiple="false"
              :check-all="false">
              <el-option disabled v-if="findDisabledCustomerType(modifyForm.CompanyType)" :value="modifyForm.CompanyType" :label="findDisabledCustomerType(modifyForm.CompanyType).CompanyTypeName"></el-option>
              <el-option v-for="(item,index) of invalidCustomerTypeList" :name="item.CompanyTypeID" :value="item.CompanyTypeID"
                         :key="`${item.CompanyTypeID}${index}`"
                         :label="item.CompanyTypeName"></el-option>
            </select-plus>
          </el-form-item>
            <el-form-item label="状态" prop="state">
                 <el-radio-group v-model="modifyForm.state">
                    <el-radio label="100">启用</el-radio>
                    <el-radio label="101">禁用</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
                <el-input :rows="7" style="width:400px" type="textarea" v-model="modifyForm.remark" maxlength="110" show-word-limit placeholder="请输入备注"></el-input>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="modifyDialogVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="setCompany('modifyForm')" size="mini" :disabled="!handleabled">{{isAdd?"添 加":"确 定"}}</el-button>
        </span>
        </el-dialog>

        <!-- 查看联系人详情 -->
        <el-dialog
        class="card-dialog"
        title="管理员详情"
        :visible.sync="detailDialogVisible"
        :close-on-click-modal="false"
        width="1000px">
        <el-button type="primary" :disabled="detailTableData.length>=3" @click="showAddAdmin">添加管理员</el-button>
        <span>还可添加<span style="color:#32AF50">{{3-detailTableData.length}}</span>位管理员</span>
        <el-table
            v-loading="adminLoading"
            :data="detailTableData"
            style="width: 100%;margin:30px 0;">
            <el-table-column
                align="center"
                prop="ContactName"
                label="名称"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                prop="ContactPhone"
                label="联系电话"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                prop="Remark"
                label="备注"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                label="操作">
                <template slot-scope="scope">
                    <el-button type="text" @click="showModifyAdmin(scope.row)">编 辑</el-button>
                    <el-button type="text" v-if="!scope.row.DefaultContact" @click="deleteCompanyContact(scope.row)">删 除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
            <el-button @click="detailDialogVisible = false" size="mini">关 闭</el-button>
        </span>
        </el-dialog>
        <!-- 添加管理员 -->
        <el-dialog
        class="card-dialog"
        :title="isAddAdmin?'添加管理员':'编辑管理员'"
        :visible.sync="adminDialogVisible"
        :close-on-click-modal="false"
        width="440px">
        <el-form ref="adminForm" :model="adminForm" label-width="120px" :rules="adminFormRules">
            <el-form-item label="名称" prop="ContactName">
                <el-input v-model="adminForm.ContactName" style="width:188px"></el-input>
            </el-form-item>
            <el-form-item label="手机号" prop="ContactPhone">
                <el-input v-model="adminForm.ContactPhone" style="width:188px"></el-input>
            </el-form-item>
            <el-form-item label="备注">
                <el-input type="textarea" v-model="adminForm.Remark" style="width:188px;heigth:84px"  maxlength="110" show-word-limit></el-input>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="adminDialogVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="setCompanyContact('adminForm')" size="mini">确 定</el-button>
        </span>
        </el-dialog>
        <!-- 查看车队详情 -->
        <el-dialog
        :close-on-click-modal="false"
        class="dialog companyDetail-dialog"
        title="查看车队详情"
        append-to-body
        :visible.sync="companyDetailDialogVisible"
        width="600px">
        <div class="main">
            <div class="check-box">
                <div class="left">
                    <p><span class="txt">车队名称：</span><span>{{Company.CompanyName}}</span></p>
                    <p v-if="getStationName(Company.StationNO) !== ''"><span class="txt" >开户油站：</span><span>{{getStationName(Company.StationNO)}}</span></p>

                    <p v-if="showLockPrice"><span class="txt">锁价车队：</span><span>{{getPriceType(Company.CardLockType)}}</span></p>
                    <p><span class="txt">扣款方式：</span><span>{{Company.PayMethod == 0 ? "仅允许卡账扣款" : Company.PayMethod == 1 ? "母账优先" : "卡账优先"}}</span></p>
                    <p><span class="txt">信用功能：</span><span>{{Company.CompanyCreditType == 0 ? "不启用" : Company.CompanyCreditType == 1 ? "信贷" : "保证金"}}</span></p>
                    <p><span class="txt">开票方式：</span><span>{{Company.KPType == 0 ? "充值开票" : Company.KPType == 1 ? "消费开票" : Company.KPType == 2 ? "不开票" : "-"}}</span></p>
                    <p v-show="Company.CompanyCreditType!='0'"><span class="txt">{{Company.CompanyCreditType=='2'?'保证金额：':Company.CompanyCreditType=='1'?'信贷额度：':''}}</span><span>{{Company.CompanyCreditAccount}}元</span></p>
                    <div v-for="(item,index) in detailTableData"  :key="index">
                        <p><span class="txt">管理员{{index+1}}：</span><span>{{item.ContactName}}</span></p>
                        <p><span class="txt">手机号：</span><span>{{item.ContactPhone}}</span></p>
                    </div>
                    <p><span class="txt">状态：</span><span class="state" :class="{active:true}">{{Company.State==100?"启用":"禁用"}}</span></p>
                    <p v-if="showInvoiceReport"><span class="txt">客户类型：</span><span>{{Company.customerType && Company.customerType.CompanyTypeName ? Company.customerType.CompanyTypeName:Company.CompanyType}}</span></p>
                </div>
                <div class="right">
                    <p><span class="txt">卡库存：</span><span>{{Company.CompanyCardStockNum}}张</span></p>
                    <p><span class="txt">领卡数：</span><span>{{Company.CompanyCardNum}}张</span></p>
                    <p><span class="txt">母账余额：</span><span>{{Company.CompanySumAccount}}元</span></p>
                    <p v-if="showLockPrice && currentFixPriceMode === FIXED_PRICE_MODE.FixedRise"><span class="txt">母账升数：</span><span>{{sumUnitLiter(Company.companyBatches)}}升</span></p>
                    <p><span class="txt">母账本金：</span><span>{{Company.CompanyUsableAccount}}元</span></p>
                    <p><span class="txt">母账赠金：</span><span>{{Company.CompanyGiveAccount}}元</span></p>
                    <p v-show="Company.CompanyCreditType=='1'"><span class="txt">已用额度：</span><span>{{Company.CompanyCreditUsageLimit}}元</span></p>
                    <p class="cartxt"><span class="txt">金额操作：</span><el-button type="text" :disabled="Company.CompanyUsableAccount<=0" @click="showEmptyDialogVisible">点击母账余额清零</el-button></p>
                    <p><span class="txt">子卡余额：</span><span>{{Company.SubCardSumAccount}}元</span></p>
                    <p v-if="showLockPrice && currentFixPriceMode === FIXED_PRICE_MODE.FixedRise"><span class="txt">子卡升数：</span><span>{{Company.CardSumLiter}}升</span></p>
                    <p><span class="txt">子卡充值：</span><span>{{AllowRechargeText[Company.IsAllowRecharge] || Company.IsAllowRecharge}}</span></p>
                    <p><span class="txt">子卡本金：</span><span>{{Company.SubCardAccount}}元</span></p>
                    <p><span class="txt">子卡赠金：</span><span>{{Company.SubCardAccoGiveunt}}元</span></p>
                </div>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button size="mini" @click="companyDetailDialogVisible = false">关 闭</el-button>
        </span>
        </el-dialog>
        <!-- 车队账户余额清零 -->
        <el-dialog
            :close-on-click-modal="false"
            class="dialog empty-dialog"
            title="车队账户余额清零"
            append-to-body
            :visible.sync="emptyDialogVisible"
            width="500px">
            <div class="main">
                <p class="item"><span class="txt">车队名称：</span><span>{{Company.CompanyName}}</span></p>
                <p class="item"><span class="txt">开户油站：</span><span>{{getStationName(Company.StationNO)}}</span></p>
                <p class="item"><span class="txt">母账余额：</span><span style="color:#333;font-weight: bold;">{{Company.CompanySumAccount}}</span>元</p>
                <p class="item"><span class="txt">母账本金：</span><span style="color:#32AF50;font-weight: bold;">{{Company.CompanyUsableAccount}}</span>元 -现金退回</p>
                <p class="item"><span class="txt">母账赠金：</span><span style="color:#333;font-weight: bold;">{{Company.CompanyGiveAccount}}</span>元 -自动扣除</p>
                <p class="item" v-show="Company.CompanyCreditType=='2'"><span class="txt">保证金额：</span><span style="color:#32AF50;font-weight: bold;">{{Company.CompanyCreditAccount}}</span>元 -现金退回</p>
                <p class="tips">退回<span style="color:#32AF50">现金{{clearMoney}}</span>元，赠金扣除<span style="color:#32AF50">{{Company.CompanyGiveAccount}}</span>元</p>
                <div class="tips-item"><i class="el-icon-warning"></i>余额清零后无法退回，请务必谨慎操作！</div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" type="primary" @click="emptyTipsDialogVisible=true;emptyDialogVisible=false">余额清零</el-button>
                <el-button size="mini" @click="emptyDialogVisible=false">关 闭</el-button>
            </span>
        </el-dialog>
        <!-- 余额清零 -->
        <el-dialog
            :close-on-click-modal="false"
            class="dialog emptyTips-dialog"
            :show-close="false"
            append-to-body
            :visible.sync="emptyTipsDialogVisible"
            width="400px">
            <div class="emptyTips">
                <i class="el-icon-warning" style="font-size:64px;color:#FA6400"></i>
                <p>清零金额退回不涉及线上退款</p>
                <p style="color:#FA6400">实退：{{clearMoney}}元 — 现金方式退回管理员</p>
                <p>赠金：{{Company.CompanyGiveAccount}}元 — 自动扣除</p>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" type="primary" :disabled="!canClear" @click="showEmptyWarning">确 认</el-button>
                <el-button size="mini" @click="emptyTipsDialogVisible=false;emptyDialogVisible=false">关 闭</el-button>
            </span>
        </el-dialog>

      <!-- 下载中心提示 -->
      <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>

    </div>
</template>

<script>
import DownloadTips from './DownloadTips.vue';
import {mapGetters, mapActions, mapState} from 'vuex'
import customerTypeMixins from '../mixins/customerType'
import SelectPlus from "./SelectPlus/SelectPlus.vue";
import {cloneDeep} from "lodash";
import {FIXED_PRICE_MODE, fixedPriceRadioOptions} from "../utils/contants/fixedPrice";
import companyCard from "../mixins/companyCard";

export default {
    name: 'EnterpriseInformationManagement',
    components:{
      SelectPlus,
      DownloadTips
    },
  mixins:[customerTypeMixins,companyCard],
  created(){
    this.getCustomerType();
  },
    data () {
        var checkPhone = (rule, value, callback) => {
            const reg = /^1[3|4|5|6|7|8|9]\d{9}$/;
            if (value === '') {
                callback(new Error('请输入手机号'));
            } else if (!reg.test(value)) {
                return callback(new Error('请填写正确的手机号码！'))
            }else{
                callback()
            }
        };
        return {
            isTotalReportForm: true,
            isAdd:true,//判断是添加/修改
            searchTxt:"",//搜索内容
            tableData:[],
            loading:true,
            currentPage:1,//当前页码
            pageSize:10,
            total:0,//总数据条数
            stationList:[],//油站列表
            detailTableData:[],//联系人数据
            modifyDialogVisible:false,//修改弹窗，默认隐藏
            modifyForm:{
              fixedPrice:fixedPriceRadioOptions[0].value,
              CompanyType:'',
                id:"",
                station_id:"",
                company_name:"",
                company_contacts_level:"",
                remark:"",
                pay_method:"0",
                state:"100",
                company_address:"",
                credit_type:"0",
                isAllowRecharge:"1",
                credit_num:"0",
                invoice_open_type: "", //开票方式
            },//修改的信息
            ContactList:[{
                ContactName:"",
                ContactPhone:"",
                Remark:"",
                State:100,
                DefaultContact:0
            }],//管理员列表
            levelList:[],//等级列表
            detailDialogVisible:false,//详情弹窗
            modifyFormRules:{
                station_id:[ { required: true, message: '请选择油站', trigger: 'blur' } ],
                invoice_open_type:[ { required: true, message: '请选择开票方式', trigger: 'blur' } ],
                company_name:[ { required: true, message: '请输入车队名称', trigger: 'blur' },
                                { max: 24, message: '车队名称长度不能超过24个字符', trigger: 'blur' } ]
            },
            company_state:0,//默认为0
            adminDialogVisible:false,//添加修改管理员弹窗
            adminForm:{
                ContactPhone:"",
                ContactName:"",
                Remark:""
            },
            adminFormRules:{
                ContactName:[ { required: true, message: '请输入管理员名称', trigger: 'blur' } ],
                ContactPhone:[ { required: true, validator: checkPhone, trigger: 'blur' } ],
            },
            adminLoading:false,
            CompanyID:"",//车队ID
            ContactID:"",//管理员ID
            isAddAdmin:false,
            old_credit_type:"",
            old_credit_num:"",
            companyDetailDialogVisible:false, //车队详情
            emptyDialogVisible:false, //余额清零
            emptyTipsDialogVisible:false, //余额清零
            Company:{},//车队详情
            clearMoney:0.00,
            canClear:true,//是否能执行清零
            handleabled:true,//能否操作
            isGroupSettle: false,// 是否集团清结算
            groupSettleStid: "", //虚拟站id
            showDownloadTips:false, // 下载中心提示
            companyOptions: [], // 车队列表
        }
    },
    mounted(){
        let userInfo = localStorage.getItem("__userInfo__") || "";
        this.isTotalReportForm = JSON.parse(userInfo).group_id != 426;
        if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
            return false;
        }
        this.getStationList();//初始化
        this.getNowLevelConfig();//初始化
        this.getCompanyList();//初始化
    },
    computed:{
      currentFixPriceMode(){
        if(!this.Company){
          return String(FIXED_PRICE_MODE.No)
        }
        return String(this.Company.CardLockType);
      },
      AllowRechargeText(){
        return {
          "0":'不允许',
          "1":'允许'
        }
      },
      fixedPriceRadioOptions:() =>(fixedPriceRadioOptions),
      showInvoiceReport(){
        return this.groupBaseInfo && this.groupBaseInfo.show_company_invoice_report === 1;
      },
      ...mapState(['groupBaseInfo']),
        editableTabsValue:{
            get(){
                return this.$store.getters.getEditableTabsValue
            },
            set(newValue){
            }
        },
        editableTabs:{
            get(){
                return this.$store.getters.getEditableTabs
            },
            set(newValue){
                this.$store.commit("SETEDITABLETABS",newValue)
            }
        },
        tabsIndex:{
            get(){
                return this.$store.getters.getTabIndex
            },
            set(newValue){
                this.$store.commit("SETTABINDEX",newValue)
            }
        },
        cacheArray:{
            get(){
                return this.$store.getters.getCacheArray
            },
            set(newValue){
                this.$store.commit("SETCACHEARRAY",newValue)
            }
        },
        ...mapGetters(['showLockPrice']),
        ...mapGetters({
            "getCurrentStation":"getCurrentStation",
        })
    },
    methods:{
      changeFixedPrice(){
        if (this.modifyForm.fixedPrice !== this.fixedPriceRadioOptions[0].value) {
          this.modifyForm.isAllowRecharge = '0';
        }
      },
      getPriceType(type){
        type = String(type);
        const result = fixedPriceRadioOptions.find(option => option.value === type);
        return result ? result.label : type;
      },
      getFixedPriceLabelByValue(value) {
        const option = this.fixedPriceRadioOptions.find(option => option.value === value);
        return option ? option.label : value;
      },
        ...mapActions({
            "changeEditableTabsValue":"changeEditableTabsValue",
            "changeEditableTabs":"changeEditableTabs",
            "changeCacheArray":"changeCacheArray",
        }),
      async remoteMethod(query) {
        try {
          if (!query) {
            return this.companyOptions = [];
          }
          const res = await this.$axios.post('/CompanyCard/getCompanyNameList', {
            input: query
          })
          console.log('搜索结果', res)
          if(res.data.status != 200) return this.$message.error(res.data.info)
          this.companyOptions = JSON.parse(res.data.data)
        }catch (e) {
          console.log(e)
          this.$message.error('查询车队名称异常')
        }
      },
        //获取油站信息
        getStationList(){
            let that = this;
            that.$axios.post('/Stations/getStationList', {})
            .then(function (res) {
                if(res.status == 200){
                    that.stationList = res.data.data;
                }
            })
            .catch(function (error) {
                that.$message({
                    message: '获取油站信息失败',
                    type: 'error'
                });
            });
        },
        //根据油站id获取油站名称
        getStationName(id){
            let name = "";
            this.stationList.forEach((element)=>{
                if(element.stid == id){
                    name = element.stname;
                }
            })
            return name
        },
        //获取油站等级
        getNowLevelConfig(){
            let that = this;
            that.$axios.post('/LevelConfig/getNowLevelConfig', {})
            .then(function (res) {
                if(res.status == 200){
                    that.levelList = res.data.data.level;
                }
            })
            .catch(function (error) {
                that.$message({
                    message: '获取油站信息失败',
                    type: 'error'
                });
            });
        },
        //获取车队信息列表
        getCompanyList(){
            let that = this;
            that.loading = true;
            that.$axios.post('/CompanyCard/getCompanyList', {
                page: that.currentPage,
                page_size: that.pageSize,
                input: that.searchTxt,
                state:that.company_state,
            })
            .then(function (res) {
                that.loading = false;
                if(res.data.status == 200){
                    that.tableData = res.data.data.dt;
                    that.total = res.data.data.TotalQty;
                    that.isGroupSettle = res.data.data.group_settle == 1 ? true : false;
                    if(res.data.data.hasOwnProperty('group_settle_stid')){
                      that.groupSettleStid = res.data.data.group_settle_stid
                    }

                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
            });
        },
        //切换页面
        handleCurrentChange(val){
            this.currentPage = val;
            this.getCompanyList();
        },
        handleSizeChange(val){
            this.pageSize = val;
            this.getCompanyList();
        },
        //搜索
        search(){
            this.currentPage = 1;
            this.getCompanyList();
        },
        //搜索全部
        searchAll(){
            this.currentPage = 1;
            this.searchTxt = "";
            this.getCompanyList();
        },
        //获取信用功能数值
        getCompanyInfo(id){
            let that = this;
            that.$axios.post('/CompanyCard/getCompanyInfo', {
                id: id
            })
            .then(function (res) {
                if(res.data.status == 200){
                    that.modifyForm.credit_num = res.data.data.credit_num;
                    that.modifyForm.credit_type = String(res.data.data.credit_type);
                    that.old_credit_type = String(res.data.data.credit_type);
                    that.old_credit_num = String(res.data.data.credit_num);
                    that.ContactList = res.data.data.ContactList;
                    if(that.ContactList.length == 0){
                        that.ContactList = [{
                            ContactName:"",
                            ContactPhone:"",
                            Remark:"",
                            State:100,
                            DefaultContact:1
                        }];
                    }
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
        },
        //显示修改弹窗
        handleModifyClick(item){
            this.isAdd = false;
            this.modifyDialogVisible = true;
            this.modifyForm.id = item.ID;
            if(this.showLockPrice){
              this.modifyForm.fixedPrice = String(item.CardLockType);
            }
            this.modifyForm.CompanyType = String(item.CompanyType || item.customer_type || '');
            this.modifyForm.station_id = Number(item.StationNO);
            this.modifyForm.invoice_open_type = item.KPType==99?'': String(item.KPType)
            this.modifyForm.company_name = item.CompanyName;
            if(item.company_contacts_level){
                this.modifyForm.company_contacts_level = Number(item.company_contacts_level);
            }else{
                this.modifyForm.company_contacts_level = "";
            }
            this.modifyForm.remark = item.Remark;
            this.modifyForm.pay_method = String(item.PayMethod);

            this.modifyForm.state = String(item.State);

            this.modifyForm.company_address = item.CompanyAddress;
            this.modifyForm.ContactList = item.ContactList;
            this.modifyForm.isAllowRecharge = String(item.IsAllowRecharge);
            this.getCompanyInfo(item.ID);
        },
        //显示管理员详情
        handleDetailClick(val){
            this.detailTableData = [];
            this.detailDialogVisible = true;
            this.CompanyID = val.ID;
            this.getCompanyContact(this.CompanyID);
        },
        //获取管理员列表
        getCompanyContact(ID){
            let that = this;
            that.adminLoading = true;
            that.$axios.post('/CompanyCard/getCompanyContact', {
                CompanyID:ID
            })
            .then(function (res) {
                that.adminLoading = false;
                if(res.data.status == 200){
                    that.detailTableData = res.data.data;
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
            });
        },
        //显示添加弹窗
        add(){
            this.isAdd = true;
            this.modifyDialogVisible = true;
            this.modifyForm = {
                fixedPrice:fixedPriceRadioOptions[0].value,
                id:"",
                station_id:"",
                company_name:"",
                company_contacts_level:"",
                remark:"",
                pay_method:"0",
                state:"100",
                company_address:"",
                credit_type:"0",
                isAllowRecharge:"1",
                credit_num:"0",
                invoice_open_type: '',
              CompanyType:''
            }
            this.ContactList = [{
                ContactName:"",
                ContactPhone:"",
                Remark:"",
                State:100,
                DefaultContact:0
            }];
            if(this.levelList.length){
                this.modifyForm.company_contacts_level = this.levelList[0].id;
            }
        },
        //添加/修改
        setCompany(formName){
            let that = this;
            if(that.isGroupSettle){
              that.modifyForm.station_id = Number(that.groupSettleStid)
            }
            that.$refs[formName].validate((valid) => {
            if (valid) {
                that.ContactList[0].DefaultContact = 1;//字段必传，第一条管理员值只能为1，其他管理员只能为0
                let params = {
                    id:that.modifyForm.id,
                    station_id:Number(that.modifyForm.station_id),
                    company_name:that.modifyForm.company_name,
                    remark:that.modifyForm.remark,
                    pay_method:that.modifyForm.pay_method,
                    company_contacts_level:that.modifyForm.company_contacts_level,
                    state:that.modifyForm.state,
                    invoice_open_type:that.modifyForm.invoice_open_type,
                    company_address:that.modifyForm.company_address,
                    isAllowRecharge:that.modifyForm.isAllowRecharge,
                    ContactList:that.ContactList,
                };
                params.lock_price = that.modifyForm.fixedPrice;
                params.customer_type = ''
                // 灰度开启则需要加入客户类型参数 或者 判断当前用户是否已经有了客户类型
                if(this.showInvoiceReport || that.modifyForm.CompanyType){
                  params.customer_type = that.modifyForm.CompanyType
                }
                //检测联系人是否填写完整
                for(let i=0; i<that.ContactList.length; i++){
                  const reg = /^1[3|4|5|6|7|8|9]\d{9}$/;
                    if(!(that.ContactList[i].ContactName && that.ContactList[i].ContactPhone)){
                        that.$message({
                            message: '请完善管理员信息',
                            type: 'error'
                        });
                        return false;
                    } else if (!reg.test(that.ContactList[i].ContactPhone)) {
                      return that.$message.error('请填写正确的手机号码')
                    }
                }
                that.handleabled = false;

                if(!that.isAdd && that.old_credit_type == that.modifyForm.credit_type && that.old_credit_num == that.modifyForm.credit_num){
                }else{
                    params.credit_type = that.modifyForm.credit_type;
                    params.credit_num = that.modifyForm.credit_num;
                }
                that.$axios.post('/CompanyCard/setCompany', params)
                .then(function (res) {
                    if(res.data.status == 200){
                        if(that.isAdd){
                            that.$message({
                                message: '添加成功',
                                type: 'success'
                            });
                        }else{
                            that.$message({
                                message: '修改成功',
                                type: 'success'
                            });
                        }
                        that.getCompanyList();
                    }else{
                        that.$message({
                            message: res.data.info,
                            type: 'error'
                        });
                    }
                    that.handleabled = true;
                    that.modifyDialogVisible = false;
                })
                .catch(function (error) {
                    that.handleabled = true;
                    that.modifyDialogVisible = false;
                    if(that.isAdd){
                        that.$message({
                            message: '添加失败',
                            type: 'error'
                        });
                    }else{
                        that.$message({
                            message: '修改失败',
                            type: 'error'
                        });
                    }
                });
            }
            });
        },
        //显示车队卡明细
        handleCardDetail(id){
            this.$router.push({
                name: 'CardManager',
                params: {
                    id: id
                }
            })
            this.$emit("currentNav",{
                    current:"卡管理",
                    prev:[]
            })

            //判断缓存数组是否存在组件
            let currentName = this.cacheArray.find((element) => (element == "卡管理"));
            if(!currentName){
                this.changeCacheArray("卡管理");
            }

            let newTabName = ++this.tabsIndex + '';
            //判断点击的菜单是否存在editableTabs中
            let currentTab = this.editableTabs.find((element) => (element.title == "卡管理"));
            if(currentTab){
                this.changeEditableTabsValue(currentTab.name);
                return;
            }else{
                this.changeEditableTabs({
                    title: "卡管理",
                    name: newTabName,
                    router:"/CardManager",
                    current:"卡管理",
                    prev:[]
                })
                this.changeEditableTabsValue(newTabName);
            }

        },
        //状态筛选
        filterHandler(key) {
            if(key.State.length == 1){
                this.company_state = key.State[0];
            }else{
                this.company_state = 0;
            }
            this.getCompanyList();
        },
        //管理员模块-start
        showAddAdmin(){
            this.isAddAdmin = true;
            this.adminDialogVisible = true;
            this.$refs.adminForm.resetFields();
            this.adminForm.Remark = "";
        },
        showModifyAdmin(val){
            this.isAddAdmin = false;
            this.adminDialogVisible = true;
            this.ContactID = val.ID;
            this.adminForm.ContactPhone = val.ContactPhone;
            this.adminForm.ContactName = val.ContactName;
            this.adminForm.Remark = val.Remark;
        },
        //添加管理员
        setCompanyContact(formName){
            let that = this;
            that.$refs[formName].validate((valid) => {
                if (valid) {
                    let params = {};
                    params.CompanyID = that.CompanyID;
                    params.ContactPhone = that.adminForm.ContactPhone;
                    params.ContactName = that.adminForm.ContactName;
                    params.Remark = that.adminForm.Remark;
                    if(!that.isAddAdmin){
                        params.ID = that.ContactID;
                    }
                    that.$axios.post('/CompanyCard/setCompanyContact', params)
                    .then(function (res) {
                        if(res.data.status == 200){
                            that.$message({
                                message: "操作成功",
                                type: 'success'
                            });
                            that.adminDialogVisible = false;
                            that.getCompanyContact(that.CompanyID);
                        }else{
                            that.$message({
                                message: res.data.info,
                                type: 'error'
                            });
                        }
                    })
                    .catch(function (error) {
                    });
                }
            })
        },
        //删除管理员
        deleteCompanyContact(val){
            let that = this;
            this.$confirm('此操作将删除该管理员, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
            }).then(() => {

                that.$axios.post('/CompanyCard/deleteCompanyContact', {
                    ID:val.ID,
                    CompanyID:that.CompanyID,
                    State:101
                })
                .then(function (res) {
                    if(res.data.status == 200){
                        that.$message({
                            message: "删除成功",
                            type: 'success'
                        });
                        that.getCompanyContact(that.CompanyID);
                    }else{
                        that.$message({
                            message: res.data.info,
                            type: 'error'
                        });
                    }
                })
                .catch(function (error) {
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        //管理员模块-end

        //添加管理员
        addContacts(){
            if(this.ContactList.length == 3){
                this.$message({
                    type: 'info',
                    message: '最多设置三个管理员'
                });
                return;
            }
            this.ContactList.push({
                ContactName:"",
                ContactPhone:"",
                Remark:"",
                State:100,
                DefaultContact:0,
                CompanyID:this.modifyForm.id
            });
        },
        //删除管理员
        delectContacts(index){
            this.ContactList.splice(index, 1);
        },
        //查看车队信息
        showCompanyInfo(val){
            this.companyDetailDialogVisible=true;
            let company = cloneDeep(val);
            company.customerType = this.customerTypeOptions.find(item => String(val.CompanyType) === String(item.CompanyTypeID))
            this.Company= company;
            this.getCompanyContact(val.ID);
            this.getCompanyCardBalanceData(val.ID);
        },
        //显示余额清零弹窗
        showEmptyDialogVisible(){
            this.companyDetailDialogVisible = true;
            this.emptyDialogVisible = true;
            this.getMoney(this.Company.CompanyCreditType,this.Company.CompanyCreditAccount,this.Company.CompanyUsableAccount);
        },
        //获取退回金额
        getMoney(CompanyCreditType,CompanyCreditAccount,CompanyUsableAccount){
            if(CompanyCreditType=='2'){
                this.clearMoney = (Number(CompanyCreditAccount)+Number(CompanyUsableAccount)).toFixed(2);
            }else{
                this.clearMoney = CompanyUsableAccount;
            }
        },
        //余额清零
        showEmptyWarning(){
            let params = {
                CompanyID:this.Company.ID,
                StationNO:this.Company.StationNO,
                BalanceClearAmount:this.clearMoney,
                GiveBalanceClearAmount:this.Company.CompanyGiveAccount
            };
            this.canClear = false;
            this.$axios.post("/Card/cardBalanceClear",params).then((res)=>{
                this.canClear = true;
                if(res.data.status == 200){
                    this.cardRuleDialogVisible = false;
                    this.emptyDialogVisible = false;
                    this.emptyTipsCompanyDialogVisible = false;
                    this.emptyTipsDialogVisible = false;
                    this.cardCheckDialogVisible = false;
                    this.companyDetailDialogVisible = false;
                    this.$message.success("操作成功！");
                    this.getCompanyList();
                }else{
                    this.$message.error(res.data.info);
                }
            })
        },
        //查询子卡信息
        getCompanyCardBalanceData(id){
            this.$axios.post("/CompanyCard/getCompanyCardBalanceData",{
                company_id:id
            }).then((res)=>{
                if(res.data.status == 200){
                    this.Company.SubCardAccount = res.data.data.SubCardAccount.toFixed(2);
                    this.Company.SubCardAccoGiveunt = res.data.data.SubCardAccoGiveunt.toFixed(2);
                    this.Company.SubCardSumAccount = res.data.data.SubCardSumAccount.toFixed(2);
                    this.$forceUpdate();
                }else{
                    this.$message.error(res.data.info);
                }
            })
        },
        // 下载
        async carInfoDownload() {
          let params = {
            page: this.currentPage,
            page_size: this.pageSize,
            input: this.searchTxt,
            state:this.company_state,
          }
          const res = await this.$axios.post('/CompanyCard/downloadCompanyList',params)
          if(res.data.status != 200) return this.$message.error(res.data.info)
          // 发送请求
          this.showDownloadTips = true
        }
    },
    watch: {
        getCurrentStation(newValue,oldValue){
            if(newValue.merchant_type != 0 && newValue.value != oldValue.value){
                this.getStationList();
                this.getNowLevelConfig();
                this.getCompanyList();
            }
        },
    },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.header{
    padding: 20px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.table_out{
    margin-bottom: 20px;
}
.card-dialog .el-form-item{
    margin-bottom: 18px;
}
.check-box{
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}
.check-box .left{
    width: 35%;
}
.check-box .right{
    width: 50%;
}
.check-box p{
    text-align: left;
    margin-bottom: 20px;
    color: #333;
    display: flex;
}
.check-box .right p {
    text-align: left;
}
.check-box .cartxt .el-button--text{
    padding: 0;
}
.empty-dialog .item{
    margin-left: 65px;
    margin-bottom: 25px;
}
.empty-dialog .tips{
    width: 428px;
    margin: 0 auto 27px;
    font-size: 18px;
    background: #F5F5F5;
    padding: 15px 0;
    text-align: center;
    font-weight: bold;
    color: #333;
}
.empty-dialog .tips-item{
    position: absolute;
    width: 100%;
    left: 0;
    padding:7px 0;
    text-align: center;
    background: #FAE1D7;
    color: #FA5A00;
}

/* 页码 */
.page_content {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
}
</style>
<style>
.companyDetail-dialog .el-dialog__body{
    padding: 30px 40px;
}
.emptyTips-dialog .el-dialog__header{
    border-bottom:0;
    padding:0
}
.emptyTips-dialog .el-dialog__body{
    padding-top: 40px;
    padding-bottom: 22px;
}
.emptyTips-dialog .emptyTips{
    font-size: 16px;
    text-align: center;
    font-weight: bold;
}
.emptyTips-dialog .emptyTips p{
    margin: 5px 0;
}
</style>
