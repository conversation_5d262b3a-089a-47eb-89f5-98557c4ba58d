webpackJsonp([18],{"5tt7":function(e,t){},XZyV:function(e,t){},ejvu:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=i("Gu7T"),a=i.n(r),n=i("Dd8w"),s=i.n(n),l=i("NYxO"),c={name:"CardRule",data:function(){var e=this;return{isCreate:!0,cardId:"",state:0,stateList:[{value:0,label:"全部"},{value:100,label:"启用"},{value:101,label:"禁用"}],station_id:0,currentPage:1,showTableData:!0,pageSize:10,totalNumber:1,tableData:[],loading:!0,stepActive:0,showBaseForm:!0,showConditionForm:!1,showtimeRuleForm:!1,showgivenForm:!1,public_config:{rule_name:"",date:[],description:"",stationList:[],priority:"10"},public_config_rules:{rule_name:[{required:!0,message:"请输入规则名称",trigger:"blur"},{max:50,message:"名称不能超过50个字符",trigger:"blur"}],date:[{required:!0,message:"请选择日期",trigger:"blur"}],stationList:[{required:!0,message:"请选择油站",trigger:"change"}],description:[{max:100,message:"规则不能超过100个字符",trigger:"blur"}]},stationList:[],stationListProps:{multiple:!0,label:"stname",value:"stid"},oilList:[],oilListProps:{multiple:!0,label:"name",value:"oil_id",children:"parent_arr"},extend_rule:{card_theme:[],customer_group_id:[],card_type:[],customer_id_type:"L",customer_id:"",pay_way:[],is_birthday:"0",first_recharge:"0",account_type:"1",company_id:[],GradeStr:[],market_type:"2"},extendRules:{customer_id_type:[{required:!0,message:"请选择",trigger:"blur"}]},cardThemeRuleList:[],allCardThemeRuleList:[],customerGroupList:[],companyList:[],classList:[],payWayList:[],options:[],couponsOptions:[],couponsProps:{multiple:!0,label:"coupon_name",value:"coupon_id"},timeRuleForm:{},time_rule:{type:0,selectedTime:[]},isCheckTime:!1,charge_give_rule:{type:"0",guding:{max_price:"",min_price:"",give:""},bili:{max_price:"",min_price:"",give:""},coupons:{max_price:"",min_price:"",give:""},gudingCouponsData:[],addCouponsData:[],couponsData:[]},chargeGiveRules:{guding_max_price:[{validator:function(t,i,r){0==e.charge_give_rule.type&&(""!=String(e.charge_give_rule.guding.max_price)?Number(e.charge_give_rule.guding.max_price)>Number(e.charge_give_rule.guding.min_price)?r():r(new Error("最小值必须小于最大值")):r(new Error("请输入数值")))},trigger:"blur"}],guding_min_price:[{validator:function(t,i,r){0==e.charge_give_rule.type&&(""!=String(e.charge_give_rule.guding.min_price)?r():r(new Error("请输入数值")))},trigger:"blur"}],guding_give_money:[{validator:function(t,i,r){0==e.charge_give_rule.type&&(""!=String(e.charge_give_rule.guding.give)?r():r(new Error("请输入数值")))},trigger:"blur"}],bili_max_price:[{validator:function(t,i,r){1==e.charge_give_rule.type&&(""!=String(e.charge_give_rule.bili.max_price)?Number(e.charge_give_rule.bili.max_price)>Number(e.charge_give_rule.bili.min_price)?r():r(new Error("最小值必须小于最大值")):r(new Error("请输入数值")))},trigger:"blur"}],bili_min_price:[{validator:function(t,i,r){1==e.charge_give_rule.type&&(""!=String(e.charge_give_rule.bili.min_price)?r():r(new Error("请输入数值")))},trigger:"blur"}],bili_give_money:[{validator:function(t,i,r){1==e.charge_give_rule.type&&(""!=String(e.charge_give_rule.bili.give)?r():r(new Error("请输入数值")))},trigger:"blur"}],coupons_max_price:[{validator:function(t,i,r){1==e.charge_give_rule.type&&(""!=String(e.charge_give_rule.coupons.max_price)?Number(e.charge_give_rule.coupons.max_price)>Number(e.charge_give_rule.coupons.min_price)?r():r(new Error("最小值必须小于最大值")):r(new Error("请输入数值")))},trigger:"blur"}],coupons_min_price:[{validator:function(t,i,r){1==e.charge_give_rule.type&&(""!=String(e.charge_give_rule.coupons.min_price)?r():r(new Error("请输入数值")))},trigger:"blur"}],couponsData:[{validator:function(t,i,r){2==e.charge_give_rule.type&&(i.length>0?r():r(new Error("请选择营销券")))},trigger:"change"}]},couponsTableData:[],gudingCouponsTableData:[],addCouponsTableData:[],btnDisabled:!1,isPayWayIndeterminate:!1,checkPayWayAll:!1,isThemeIndeterminate:!1,checkThemeAll:!1,isCompanyIndeterminate:!1,checkCompanyAll:!1,isCustomerGroupIndeterminate:!1,checkCustomerGroupAll:!1,isClassIndeterminate:!1,checkClassAll:!1,showGudingCoupons:!1,searchTxt:"",recharge:{num:"0",type:"month",count:"0",total:"0"},rechargeRules:{num:[{required:!0,message:"输入用户个数",trigger:"blur"},{pattern:/^(0|[1-9]\d?|1000)$/,message:"范围在0-1000",trigger:"blur"}],type:[{required:!0,message:"请选择类型",trigger:"blur"}],count:[{required:!0,message:"请输入充值赠送次数",trigger:"blur"},{pattern:/^(0|[1-9]\d?|1000)$/,message:"范围在0-1000",trigger:"blur"}],total:[{required:!0,message:"请输入最多赠送次数",trigger:"blur"},{pattern:/^(0|[1-9]\d?|1000)$/,message:"范围在0-1000",trigger:"blur"}]},couponDeliveryOptions:[{value:"month",label:"自然月"},{value:"week",label:"自然周"},{value:"day",label:"自然日"}],isLockPrice:!0,sellPrice:0,giveNumber:0,SumPrice:0,UnitPrice:0}},activated:function(){if(void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.getStationList(),this.getCardRuleLists(),this.getChargeRuleTem(),this.getCardThemeRuleList(),this.getCustomerGroupList(),this.getCompanyList();var e=this.$route.query.id;console.log("id",e),e?this.modifyRule(e):this.clearFormData()},mounted:function(){if(void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.getStationList(),this.getCardRuleLists(),this.getChargeRuleTem(),this.getCardThemeRuleList(),this.getCustomerGroupList(),this.getCompanyList();var e=this.$route.query.id;console.log("id",e),e&&this.modifyRule(e)},computed:s()({},Object(l.c)({getCurrentStation:"getCurrentStation"}),{showLockRule:function(){return 1==JSON.parse(window.localStorage.__userInfo__).group_id||352==JSON.parse(window.localStorage.__userInfo__).group_id||0==JSON.parse(window.localStorage.__userInfo__).group_id},salePrice:function(){return Number(this.sellPrice)+Number(this.giveNumber)==0?0:(+this.SumPrice/(+this.sellPrice+ +this.giveNumber)).toFixed(2)},givePrice:function(){return 0==+this.sellPrice||this.SumPrice/this.sellPrice==0?0:(this.giveNumber*(this.SumPrice/this.sellPrice).toFixed(2)).toFixed(2)},cacheArray:{get:function(){return this.$store.getters.getCacheArray},set:function(e){this.$store.commit("SETCACHEARRAY",e)}}}),methods:{getCardRuleLists:function(){var e=this;this.loading=!0,this.$axios.post("/CardRule/getCardChargeRuleList",{state:this.state,station_id:this.station_id,page:this.currentPage,page_size:this.pageSize,ruleName:this.searchTxt}).then(function(t){e.loading=!1,200==t.data.status?(e.tableData=t.data.data.dt,e.totalNumber=t.data.data.TotalQty):e.$message({message:t.data.info,type:"error"})})},modifyRule:function(e){var t=this;this.isCreate=!1,this.cardId=e,this.$axios.post("/CardRule/getCardChargeRuleInfo",{id:e}).then(function(e){if(200==e.data.status){var i=e.data.data;t.public_config.rule_name=i.public_config.rule_name,t.public_config.description=i.public_config.description,t.public_config.priority=i.public_config.priority,t.public_config.date=[i.public_config.start_time,i.public_config.end_time],t.public_config.stationList=[],i.public_config.use_station_list&&i.public_config.use_station_list.length>0?i.public_config.use_station_list.forEach(function(e){t.public_config.stationList.push([e])}):t.public_config.stationList=[],i.extend_rule.card_theme.length==t.cardThemeRuleList.length?(t.checkThemeAll=!0,t.isThemeIndeterminate=!1):(t.isThemeIndeterminate=!0,t.checkThemeAll=!1),t.extend_rule.card_theme=i.extend_rule.card_theme;var r=[];i.extend_rule.customer_group_id&&i.extend_rule.customer_group_id.map(function(e){r.push(Number(e))}),r.length==t.customerGroupList.length?(t.checkCustomerGroupAll=!0,t.isCustomerGroupIndeterminate=!1):(t.isCustomerGroupIndeterminate=!0,t.checkCustomerGroupAll=!1),t.extend_rule.customer_group_id=r;var a=[];i.extend_rule.company_id&&i.extend_rule.company_id.map(function(e){a.push(Number(e))}),a.length==t.companyList.length?(t.checkCompanyAll=!0,t.isCompanyIndeterminate=!1):(t.isCompanyIndeterminate=!0,t.checkCompanyAll=!1),t.extend_rule.company_id=a;var n=[];if(i.extend_rule.gradeStr&&i.extend_rule.gradeStr.map(function(e){n.push(Number(e))}),n.length==t.classList.length?(t.checkClassAll=!0,t.isClassIndeterminate=!1):(t.isClassIndeterminate=!0,t.checkClassAll=!1),t.extend_rule.GradeStr=n,t.extend_rule.card_type=i.extend_rule.card_type,t.extend_rule.customer_id=i.extend_rule.customer_id,t.extend_rule.customer_id_type=i.extend_rule.customer_id_type,t.extend_rule.is_birthday=String(i.extend_rule.is_birthday),void 0!==i.extend_rule.first_recharge&&(t.extend_rule.first_recharge=String(i.extend_rule.first_recharge)),t.extend_rule.account_type=String(i.extend_rule.account_type),i.extend_rule.pay_way?(i.extend_rule.pay_way.length==t.payWayList.length?(t.checkPayWayAll=!0,t.isPayWayIndeterminate=!1):(t.checkPayWayAll=!1,t.isPayWayIndeterminate=!0),t.extend_rule.pay_way=i.extend_rule.pay_way):t.extend_rule.pay_way=[],t.time_rule.selectedTime=[],1==i.time_rule.type&&(t.time_rule.type=0,t.time_rule.rule=[]),2==i.time_rule.type&&(t.time_rule.type=1,i.time_rule.rule.forEach(function(e){t.time_rule.selectedTime.push({time:[e.start_time,e.end_time]})})),3==i.time_rule.type&&(t.time_rule.type=2,i.time_rule.rule.forEach(function(e){t.time_rule.selectedTime.push({date:e.date,time:[e.start_time,e.end_time]})})),4==i.time_rule.type&&(t.time_rule.type=3,i.time_rule.rule.forEach(function(e){t.time_rule.selectedTime.push({date:e.date,time:[e.start_time,e.end_time]})})),t.charge_give_rule.type=String(i.charge_give_rule.type),1!=i.charge_give_rule.data.market_type&&(t.isLockPrice=!0),1==i.charge_give_rule.data.market_type){var s=t.allCardThemeRuleList;t.cardThemeRuleList=s,t.isLockPrice=!1}0==i.charge_give_rule.type&&(t.charge_give_rule.guding.max_price=i.charge_give_rule.data.max_price,t.charge_give_rule.guding.min_price=i.charge_give_rule.data.min_price,t.charge_give_rule.guding.give=i.charge_give_rule.data.give.number,t.isLockPrice&&(t.sellPrice=t.charge_give_rule.guding.max_price,t.SumPrice=i.charge_give_rule.data.SumPrice,t.UnitPrice=(+i.charge_give_rule.data.UnitPrice).toFixed(2),t.extend_rule.market_type=i.charge_give_rule.data.market_type,t.giveNumber=i.charge_give_rule.data.give.number,t.handleCheckedMarketType())),1==i.charge_give_rule.type&&(t.charge_give_rule.bili.max_price=i.charge_give_rule.data.max_price,t.charge_give_rule.bili.min_price=i.charge_give_rule.data.min_price,t.charge_give_rule.bili.give=i.charge_give_rule.data.give.number),2==i.charge_give_rule.type&&(t.charge_give_rule.coupons.max_price=i.charge_give_rule.data.max_price,t.charge_give_rule.coupons.min_price=i.charge_give_rule.data.min_price,t.charge_give_rule.coupons.give=i.charge_give_rule.data.give.number),t.handelCouponlist(i.charge_give_rule.data.give.couponlist,i.charge_give_rule.type),void 0!==i.charge_give_rule.recharge&&(t.recharge.num=i.charge_give_rule.recharge.num,t.recharge.type=i.charge_give_rule.recharge.type,t.recharge.count=i.charge_give_rule.recharge.count,t.recharge.total=i.charge_give_rule.recharge.total),t.showTableData=!1,t.showBaseForm=!0}else t.$message({message:e.data.info,type:"error"})})},handelCouponlist:function(e,t){var i=this,r=[],a=[];this.charge_give_rule.gudingCouponsData=[],this.charge_give_rule.addCouponsData=[],this.charge_give_rule.couponsData=[],e.forEach(function(e){r.push(e.coupon_id),a.push([e.coupon_id]),i.couponsOptions.forEach(function(t){t.coupon_id==e.coupon_id&&(t.num=e.coupon_num)})}),0==t?this.charge_give_rule.gudingCouponsData=r:1==t?this.charge_give_rule.addCouponsData=r:this.charge_give_rule.couponsData=r},goToCreateRule:function(){this.showTableData=!1,this.showBaseForm=!0,this.isCreate=!0,this.isLockPrice=!1,this.clearFormData(),this.extend_rule.card_type=["1","2","3"],this.cardThemeRuleList=[].concat(a()(this.allCardThemeRuleList)),this.cardThemeRuleList=this.cardThemeRuleList.filter(function(e){return 1==e.Type}),this.$forceUpdate()},goToCreateLockRule:function(){this.showTableData=!1,this.showBaseForm=!0,this.isCreate=!0,this.isLockPrice=!0,console.log(this.extend_rule.card_type),this.clearFormData(),this.extend_rule.card_type=["1"];var e=this.allCardThemeRuleList;this.cardThemeRuleList=e,this.cardThemeRuleList=this.cardThemeRuleList.filter(function(e){return 1!==e.Type}),console.log(this.cardThemeRuleList),this.$forceUpdate()},resetForm:function(){var e=this;this.$confirm("是否退出单价锁定","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.clearFormData(),e.$router.push("/MarketingRules")})},baseInfoNext:function(e){var t=this;this.$refs[e].validate(function(e){e&&(t.stepActive=1,t.showBaseForm=!1,t.showConditionForm=!0)})},getStationList:function(){var e=this;this.$axios.post("/Stations/getStationList",{}).then(function(t){200==t.status&&(e.stationList=t.data.data,e.stationList.forEach(function(t){e.public_config.stationList.push([t.stid])}))})},getStationIdList:function(e){var t=[];e.forEach(function(e){t.push(e[0])})},getCardThemeRuleList:function(){var e=this,t=this;t.$axios.post("/CardRule/getCardThemeRuleList",{state:100,station_id:0,page:1,page_size:500}).then(function(i){200==i.data.status?(t.cardThemeRuleList=i.data.data.dt,t.allCardThemeRuleList=i.data.data.dt,e.cardThemeRuleList=e.cardThemeRuleList.filter(function(t){return t.Type==e.extend_rule.market_type})):t.$message({message:i.data.info,type:"error"})})},getCustomerGroupList:function(){var e=this;e.$axios.post("/CustomerGroup/getCustomerGroupList",{page:1,page_size:500}).then(function(t){200==t.data.status?e.customerGroupList=t.data.data.dt:e.$message({message:t.data.info,type:"error"})})},getCompanyList:function(){var e=this;e.$axios.post("/CompanyCard/getCompanyList",{page:1,page_size:1e3,input:"",state:100}).then(function(t){200==t.data.status?e.companyList=t.data.data.dt:e.$message({message:t.data.info,type:"error"})}).catch(function(e){})},getChargeRuleTem:function(){var e=this;this.$axios.post("/CardRule/getChargeRuleTem",{}).then(function(t){200==t.data.status?(e.payWayList=t.data.data.pay_way,e.classList=t.data.data.level):e.$message({message:t.data.info,type:"error"})})},customerIdTypeChange:function(e){this.getCustomerIdList(e)},getCustomerIdList:function(e){},conditionBefore:function(){this.stepActive=0,this.showBaseForm=!0,this.showConditionForm=!1},conditionNext:function(){if(3!=this.extend_rule.account_type||0!=this.extend_rule.GradeStr.length)if(0!=this.extend_rule.account_type||0!=this.extend_rule.company_id.length){if(1==this.extend_rule.account_type){if(0==this.extend_rule.card_theme.length)return void this.$message.error("请勾选规则对应的卡！");if(0==this.extend_rule.card_type.length)return void this.$message.error("请勾选卡类型！")}2!=this.extend_rule.account_type||0!=this.extend_rule.customer_group_id.length?this.payWayList.length>0&&0==this.extend_rule.pay_way.length?this.$message.error("请勾选付款方式！"):(this.stepActive=2,this.showConditionForm=!1,this.showtimeRuleForm=!0):this.$message.error("请勾选规则对应的卡组！")}else this.$message.error("请勾选规则对应的车队！");else this.$message.error("请勾选规则对应的会员等级！")},checkTime:function(e){this.isCheckTime=e},timeRuleBefore:function(){this.stepActive=1,this.showConditionForm=!0,this.showtimeRuleForm=!1},timeRuleNext:function(){this.isCheckTime?(this.stepActive=3,this.showtimeRuleForm=!1,this.showgivenForm=!0):this.$message.error("请选择完整时间和日期！")},givenBefore:function(){this.stepActive=2,this.showtimeRuleForm=!0,this.showgivenForm=!1},changeNumber:function(e){this.couponsOptions.forEach(function(t){t.coupon_id==e.coupon_id&&(t.num=e.num)})},handleClick:function(e,t){var i=this,r=e.coupon_id;2==t?(this.charge_give_rule.couponsData=[],this.couponsTableData.forEach(function(e,t){r==e.coupon_id&&i.couponsTableData.splice(t,1)}),this.couponsTableData.forEach(function(e){i.charge_give_rule.couponsData.push([e.coupon_id])})):1==t?(this.charge_give_rule.addCouponsData=[],this.addCouponsTableData.forEach(function(e,t){r==e.coupon_id&&i.addCouponsTableData.splice(t,1)}),this.addCouponsTableData.forEach(function(e){i.charge_give_rule.addCouponsData.push([e.coupon_id])})):(this.charge_give_rule.gudingCouponsData=[],this.gudingCouponsTableData.forEach(function(e,t){r==e.coupon_id&&i.gudingCouponsTableData.splice(t,1)}),this.gudingCouponsTableData.forEach(function(e){i.charge_give_rule.gudingCouponsData.push([e.coupon_id])}))},create:function(){var e=this,t="";0==this.charge_give_rule.type?t="chargeGive":1==this.charge_give_rule.type?t="chargeGive1":2==this.charge_give_rule.type&&(t="chargeGive2"),this.$refs[t].validate(function(t){if(t&&t){e.btnDisabled=!0;var i={public_config:{id:"",rule_name:"",start_time:"",end_time:"",description:"",state:"",use_station_list:[],priority:""},extend_rule:{card_theme:[],customer_group_id:[],card_type:[],customer_id_type:"",customer_id:"",pay_way:[],is_birthday:"",first_recharge:"",account_type:"",company_id:[],GradeStr:[]},time_rule:{rule:[],type:""},charge_give_rule:{type:"",data:""},recharge:{num:"",type:"month",count:"",total:""}};e.isCreate||(i.public_config.id=e.cardId),i.public_config.rule_name=e.public_config.rule_name,i.public_config.start_time=e.public_config.date[0],i.public_config.end_time=e.public_config.date[1],i.public_config.description=e.public_config.description,i.public_config.state=100,i.public_config.priority=Number(e.public_config.priority);var r=[];if(e.public_config.stationList.forEach(function(e){r.push(e[0])}),i.public_config.use_station_list=r,i.extend_rule.card_theme=e.extend_rule.card_theme,i.extend_rule.customer_group_id=e.extend_rule.customer_group_id,i.extend_rule.company_id=e.extend_rule.company_id,i.extend_rule.GradeStr=e.extend_rule.GradeStr,i.extend_rule.card_type=e.extend_rule.card_type,i.extend_rule.customer_id_type=e.extend_rule.customer_id_type,i.extend_rule.customer_id=e.extend_rule.customer_id,i.extend_rule.pay_way=e.extend_rule.pay_way,i.extend_rule.is_birthday=Number(e.extend_rule.is_birthday),i.extend_rule.first_recharge=Number(e.extend_rule.first_recharge),i.extend_rule.account_type=Number(e.extend_rule.account_type),"0"==e.extend_rule.account_type?(i.extend_rule.card_theme=[],i.extend_rule.card_type=[],i.extend_rule.customer_group_id=[],i.extend_rule.GradeStr=[]):"1"==e.extend_rule.account_type?(i.extend_rule.company_id=[],i.extend_rule.customer_group_id=[],i.extend_rule.GradeStr=[]):"2"==e.extend_rule.account_type?(i.extend_rule.card_theme=[],i.extend_rule.card_type=[],i.extend_rule.company_id=[],i.extend_rule.GradeStr=[]):(i.extend_rule.card_theme=[],i.extend_rule.card_type=[],i.extend_rule.company_id=[],i.extend_rule.customer_group_id=[]),0==e.time_rule.type&&(i.time_rule.type=1,i.time_rule.rule=""),1==e.time_rule.type&&(i.time_rule.type=2,e.time_rule.selectedTime.forEach(function(e){i.time_rule.rule.push({start_time:e.time[0],end_time:e.time[1]})})),2==e.time_rule.type&&(i.time_rule.type=3,e.time_rule.selectedTime.forEach(function(e){i.time_rule.rule.push({date:e.date,start_time:e.time[0],end_time:e.time[1]})})),3==e.time_rule.type&&(i.time_rule.type=4,e.time_rule.selectedTime.forEach(function(e){i.time_rule.rule.push({date:e.date,start_time:e.time[0],end_time:e.time[1]})})),i.charge_give_rule.type=e.charge_give_rule.type,0==e.charge_give_rule.type){var a=[];e.gudingCouponsTableData.forEach(function(e){a.push({coupon_id:e.coupon_id,coupon_num:e.num})}),i.charge_give_rule.data={max_price:e.charge_give_rule.guding.max_price,min_price:e.charge_give_rule.guding.min_price,SumPrice:0,UnitPrice:0,market_type:1,give:{number:e.charge_give_rule.guding.give,couponlist:a}},e.isLockPrice&&(i.charge_give_rule.data={max_price:e.sellPrice,min_price:e.sellPrice,SumPrice:e.SumPrice,UnitPrice:e.UnitPrice,market_type:e.extend_rule.market_type,give:{number:e.giveNumber,couponlist:a}})}if(1==e.charge_give_rule.type){var n=[];e.addCouponsTableData.forEach(function(e){n.push({coupon_id:e.coupon_id,coupon_num:e.num})}),i.charge_give_rule.data={max_price:e.charge_give_rule.bili.max_price,min_price:e.charge_give_rule.bili.min_price,SumPrice:0,UnitPrice:0,market_type:1,give:{number:e.charge_give_rule.bili.give,couponlist:n}}}if(2==e.charge_give_rule.type){var s=[];e.couponsTableData.forEach(function(e){s.push({coupon_id:e.coupon_id,coupon_num:e.num})}),i.charge_give_rule.data={max_price:e.charge_give_rule.coupons.max_price,min_price:e.charge_give_rule.coupons.min_price,SumPrice:0,UnitPrice:0,market_type:1,give:{number:"",couponlist:s}}}i.charge_give_rule.recharge={num:e.recharge.num,type:e.recharge.type,count:e.recharge.count,total:e.recharge.total},e.$axios.post("/CardRule/setCardChargeRule",i).then(function(t){200==t.data.status?(e.isCreate?e.$message.success("创建成功"):e.$message.success("修改成功"),e.cacheArray.remove("ruleCreation"),e.$router.push("/MarketingRules"),e.showTableData=!0,e.showBaseForm=!0,e.showConditionForm=!1,e.showtimeRuleForm=!1,e.showgivenForm=!1,e.btnDisabled=!1,e.currentPage=1):(e.btnDisabled=!1,e.$message.error(t.data.info))})}})},save:function(){var e=this;this.$confirm("保存成功后，充值次数限制将重新计算","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var t="";0==e.charge_give_rule.type?t="chargeGive":1==e.charge_give_rule.type?t="chargeGive1":2==e.charge_give_rule.type&&(t="chargeGive2"),e.$refs[t].validate(function(t){t&&e.$refs.recharge.validate(function(t){if(t){e.btnDisabled=!0;var i={public_config:{id:"",rule_name:"",start_time:"",end_time:"",description:"",state:"",use_station_list:[],priority:""},extend_rule:{card_theme:[],customer_group_id:[],card_type:[],customer_id_type:"",customer_id:"",pay_way:[],is_birthday:"",first_recharge:"",account_type:"",company_id:[],GradeStr:[]},time_rule:{rule:[],type:""},charge_give_rule:{type:"",data:""},recharge:{num:"",type:"month",count:"",total:""}};e.isCreate||(i.public_config.id=e.cardId),i.public_config.rule_name=e.public_config.rule_name,i.public_config.start_time=e.public_config.date[0],i.public_config.end_time=e.public_config.date[1],i.public_config.description=e.public_config.description,i.public_config.state=100,i.public_config.priority=Number(e.public_config.priority);var r=[];if(e.public_config.stationList.forEach(function(e){r.push(e[0])}),i.public_config.use_station_list=r,i.extend_rule.card_theme=e.extend_rule.card_theme,i.extend_rule.customer_group_id=e.extend_rule.customer_group_id,i.extend_rule.company_id=e.extend_rule.company_id,i.extend_rule.GradeStr=e.extend_rule.GradeStr,i.extend_rule.card_type=e.extend_rule.card_type,i.extend_rule.customer_id_type=e.extend_rule.customer_id_type,i.extend_rule.customer_id=e.extend_rule.customer_id,i.extend_rule.pay_way=e.extend_rule.pay_way,i.extend_rule.is_birthday=Number(e.extend_rule.is_birthday),i.extend_rule.first_recharge=Number(e.extend_rule.first_recharge),i.extend_rule.account_type=Number(e.extend_rule.account_type),"0"==e.extend_rule.account_type?(i.extend_rule.card_theme=[],i.extend_rule.card_type=[],i.extend_rule.customer_group_id=[],i.extend_rule.GradeStr=[]):"1"==e.extend_rule.account_type?(i.extend_rule.company_id=[],i.extend_rule.customer_group_id=[],i.extend_rule.GradeStr=[]):"2"==e.extend_rule.account_type?(i.extend_rule.card_theme=[],i.extend_rule.card_type=[],i.extend_rule.company_id=[],i.extend_rule.GradeStr=[]):(i.extend_rule.card_theme=[],i.extend_rule.card_type=[],i.extend_rule.company_id=[],i.extend_rule.customer_group_id=[]),0==e.time_rule.type&&(i.time_rule.type=1,i.time_rule.rule=""),1==e.time_rule.type&&(i.time_rule.type=2,e.time_rule.selectedTime.forEach(function(e){i.time_rule.rule.push({start_time:e.time[0],end_time:e.time[1]})})),2==e.time_rule.type&&(i.time_rule.type=3,e.time_rule.selectedTime.forEach(function(e){i.time_rule.rule.push({date:e.date,start_time:e.time[0],end_time:e.time[1]})})),3==e.time_rule.type&&(i.time_rule.type=4,e.time_rule.selectedTime.forEach(function(e){i.time_rule.rule.push({date:e.date,start_time:e.time[0],end_time:e.time[1]})})),i.charge_give_rule.type=e.charge_give_rule.type,0==e.charge_give_rule.type){var a=[];e.gudingCouponsTableData.forEach(function(e){a.push({coupon_id:e.coupon_id,coupon_num:e.num})}),i.charge_give_rule.data={max_price:e.charge_give_rule.guding.max_price,min_price:e.charge_give_rule.guding.min_price,SumPrice:0,UnitPrice:0,market_type:1,give:{number:e.charge_give_rule.guding.give,couponlist:a}},e.isLockPrice&&(i.charge_give_rule.data={max_price:e.sellPrice,min_price:e.sellPrice,SumPrice:e.SumPrice,UnitPrice:e.UnitPrice,market_type:e.extend_rule.market_type,give:{number:e.giveNumber,couponlist:a}})}if(1==e.charge_give_rule.type){var n=[];e.addCouponsTableData.forEach(function(e){n.push({coupon_id:e.coupon_id,coupon_num:e.num})}),i.charge_give_rule.data={max_price:e.charge_give_rule.bili.max_price,min_price:e.charge_give_rule.bili.min_price,SumPrice:0,UnitPrice:0,market_type:1,give:{number:e.charge_give_rule.bili.give,couponlist:n}}}if(2==e.charge_give_rule.type){var s=[];e.couponsTableData.forEach(function(e){s.push({coupon_id:e.coupon_id,coupon_num:e.num})}),i.charge_give_rule.data={max_price:e.charge_give_rule.coupons.max_price,min_price:e.charge_give_rule.coupons.min_price,SumPrice:0,UnitPrice:0,market_type:1,give:{number:"",couponlist:s}}}i.charge_give_rule.recharge={num:e.recharge.num,type:e.recharge.type,count:e.recharge.count,total:e.recharge.total},e.$axios.post("/CardRule/setCardChargeRule",i).then(function(t){200==t.data.status?(e.isCreate?e.$message.success("创建成功"):e.$message.success("修改成功"),e.showTableData=!0,e.showBaseForm=!1,e.showConditionForm=!1,e.showtimeRuleForm=!1,e.showgivenForm=!1,e.btnDisabled=!1,e.currentPage=1,e.getCardRuleLists()):(e.btnDisabled=!1,e.$message.error(t.data.info))})}})})}).catch(function(){e.$message({type:"info",message:"已取消保存"})})},clearFormData:function(){var e=this;this.public_config.rule_name="",this.public_config.date=[],this.public_config.description="",this.public_config.stationList=[],this.stationList.forEach(function(t){e.public_config.stationList.push([t.stid])}),this.public_config.priority="10",this.extend_rule.card_theme=[],this.extend_rule.customer_group_id=[],this.extend_rule.company_id=[],this.extend_rule.GradeStr=[],this.extend_rule.card_type=[],this.extend_rule.customer_id_type="L",this.extend_rule.pay_way=[],this.extend_rule.is_birthday="0",this.extend_rule.first_recharge="0",this.extend_rule.account_type="1",this.time_rule.type=0,this.time_rule.selectedTime=[],this.charge_give_rule.type="0",this.charge_give_rule.coupons.max_price="",this.charge_give_rule.coupons.min_price="",this.charge_give_rule.couponsData=[],this.charge_give_rule.guding.max_price="",this.charge_give_rule.guding.min_price="",this.charge_give_rule.guding.give="",this.charge_give_rule.bili.max_price="",this.charge_give_rule.bili.min_price="",this.charge_give_rule.bili.give="",this.recharge.num="0",this.recharge.type="month",this.recharge.count="0",this.recharge.total="0",this.couponsTableData=[],this.addCouponsTableData=[],this.gudingCouponsTableData=[],this.charge_give_rule.gudingCouponsData=[],this.charge_give_rule.addCouponsData=[],this.showGudingCoupons=!1,this.couponsOptions.forEach(function(e){e.num=1})},changeAccountType:function(e){0==e&&2==this.charge_give_rule.type&&(this.charge_give_rule.type="0")},handleCheckThemeAllChange:function(e){var t=[];this.cardThemeRuleList.forEach(function(e){t.push(e.ID)}),this.extend_rule.card_theme=e?t:[],this.isThemeIndeterminate=!1},handleCheckedThemeChange:function(e){var t=e.length;this.checkThemeAll=t===this.cardThemeRuleList.length,this.isThemeIndeterminate=t>0&&t<this.cardThemeRuleList.length},handleCheckClassAllChange:function(e){var t=[];this.classList.forEach(function(e){t.push(e.id)}),this.extend_rule.GradeStr=e?t:[],this.isClassIndeterminate=!1},handleCheckedClassChange:function(e){var t=e.length;this.checkClassAll=t===this.classList.length,this.isClassIndeterminate=t>0&&t<this.classList.length},handleCheckCompanyAllChange:function(e){var t=[];this.companyList.forEach(function(e){t.push(e.ID)}),this.extend_rule.company_id=e?t:[],this.isCompanyIndeterminate=!1},handleCheckedCompanyChange:function(e){var t=e.length;this.checkCompanyAll=t===this.companyList.length,this.isCompanyIndeterminate=t>0&&t<this.companyList.length},handleCheckCustomerGroupAllChange:function(e){var t=[];this.customerGroupList.forEach(function(e){t.push(e.ID)}),this.extend_rule.customer_group_id=e?t:[],this.isCustomerGroupIndeterminate=!1},handleCheckedCustomerGroupChange:function(e){var t=e.length;this.checkCustomerGroupAll=t===this.customerGroupList.length,this.isCustomerGroupIndeterminate=t>0&&t<this.customerGroupList.length},handleCheckPayWayAllChange:function(e){var t=[];this.payWayList.forEach(function(e){t.push(e.BH)}),this.extend_rule.pay_way=e?t:[],this.isPayWayIndeterminate=!1},handleCheckedPayWayChange:function(e){var t=e.length;this.checkPayWayAll=t===this.payWayList.length,this.isPayWayIndeterminate=t>0&&t<this.payWayList.length},search:function(){this.currentPage=1,this.getCardRuleLists()},changeRadioValue:function(e){this.couponsOptions.forEach(function(e){e.num=1}),"0"==e?(this.charge_give_rule.bili.max_price="",this.charge_give_rule.bili.min_price="",this.charge_give_rule.bili.give="",this.charge_give_rule.coupons.max_price="",this.charge_give_rule.coupons.min_price="",this.charge_give_rule.couponsData=[],this.charge_give_rule.addCouponsData=[],this.couponsTableData=[],this.addCouponsTableData=[],this.showGudingCoupons=!1):"1"==e?(this.charge_give_rule.coupons.max_price="",this.charge_give_rule.coupons.min_price="",this.charge_give_rule.couponsData=[],this.charge_give_rule.gudingCouponsData=[],this.couponsTableData=[],this.gudingCouponsTableData=[],this.charge_give_rule.guding.max_price="",this.charge_give_rule.guding.min_price="",this.charge_give_rule.guding.give="",this.showGudingCoupons=!1):"2"==e&&(this.charge_give_rule.guding.max_price="",this.charge_give_rule.guding.min_price="",this.charge_give_rule.guding.give="",this.charge_give_rule.bili.max_price="",this.charge_give_rule.bili.min_price="",this.charge_give_rule.bili.give="",this.showGudingCoupons=!1,this.gudingCouponsTableData=[],this.addCouponsTableData=[],this.charge_give_rule.gudingCouponsData=[],this.charge_give_rule.addCouponsData=[])},calculationRules:function(){if(2==this.extend_rule.market_type){var e=(Number(this.SumPrice)+Number(this.giveNumber))/Number(this.sellPrice);console.log(e),0==e||NaN==e||0==+this.SumPrice?this.UnitPrice:this.UnitPrice=(Number(this.SumPrice)/e).toFixed(2)}3==this.extend_rule.market_type&&(this.UnitPrice=(Number(this.SumPrice)/(Number(this.sellPrice)+Number(this.giveNumber))).toFixed(2))},handleCheckedMarketType:function(){var e=this,t=this.allCardThemeRuleList;this.cardThemeRuleList=t,this.cardThemeRuleList=this.cardThemeRuleList.filter(function(t){return t.Type==e.extend_rule.market_type}),console.log(this.cardThemeRuleList)}},watch:{getCurrentStation:function(e,t){0!=e.merchant_type&&e.value!=t.value&&(this.getStationList(),this.getCouponsList(),this.getCardRuleLists(),this.getChargeRuleTem(),this.getCardThemeRuleList(),this.getCustomerGroupList(),this.getCompanyList())}}},u={render:function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"cardRule",attrs:{id:"cardRule"}},[i("div",[i("el-steps",{staticClass:"card-step",staticStyle:{"margin-top":"20px"},attrs:{active:e.stepActive,"finish-status":"success",simple:""}},[i("el-step",{attrs:{title:"基本信息"}}),e._v(" "),i("el-step",{attrs:{title:"使用条件"}}),e._v(" "),i("el-step",{attrs:{title:"时间规则"}}),e._v(" "),i("el-step",{attrs:{title:"充值赠送"}})],1),e._v(" "),i("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showBaseForm,expression:"showBaseForm"}],ref:"baseForm",staticClass:"demo-ruleForm card-form base-form",attrs:{model:e.public_config,rules:e.public_config_rules,"label-width":"108px"}},[i("el-form-item",{attrs:{label:"充值规则名称",prop:"rule_name"}},[i("el-input",{staticStyle:{width:"250px"},attrs:{placeholder:"请输入规则名称"},model:{value:e.public_config.rule_name,callback:function(t){e.$set(e.public_config,"rule_name",t)},expression:"public_config.rule_name"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"起止时间",prop:"date"}},[i("el-date-picker",{staticStyle:{width:"360px"},attrs:{"default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd HH:mm:ss",type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.public_config.date,callback:function(t){e.$set(e.public_config,"date",t)},expression:"public_config.date"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"规则说明",prop:"description"}},[i("el-input",{staticStyle:{width:"360px"},attrs:{type:"textarea",placeholder:"请输入规则说明",rows:5,maxlength:"100","show-word-limit":""},model:{value:e.public_config.description,callback:function(t){e.$set(e.public_config,"description",t)},expression:"public_config.description"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"可用油站",prop:"stationList"}},[i("el-cascader",{staticStyle:{width:"250px"},attrs:{options:e.stationList,props:e.stationListProps,"collapse-tags":""},on:{change:e.getStationIdList},model:{value:e.public_config.stationList,callback:function(t){e.$set(e.public_config,"stationList",t)},expression:"public_config.stationList"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"优先级",required:""}},[i("el-select",{staticStyle:{width:"250px"},attrs:{placeholder:"请选择"},model:{value:e.public_config.priority,callback:function(t){e.$set(e.public_config,"priority",t)},expression:"public_config.priority"}},e._l(10,function(e){return i("el-option",{key:e.index,attrs:{label:e,value:e}})}),1),e._v(" "),i("i",{staticStyle:{display:"inline-block","line-height":"20px",position:"relative",top:"10px"}},[e._v("优先级：决定规则执行的顺序。选项为1-10，数值越小优先级越高；同等优先级的情况下，按照ID越小越优先执行。"),i("br"),e._v("建议：设置规则时从10优先级设置，方便以后增加新的高优先级规则。")])],1),e._v(" "),i("el-form-item",[i("el-button",{on:{click:function(t){return e.resetForm()}}},[e._v("取消")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.baseInfoNext("baseForm")}}},[e._v("下一步")])],1)],1),e._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:e.showConditionForm,expression:"showConditionForm"}],staticClass:"card-form recharge-form"},[i("p",{staticClass:"title"},[e._v("使用类型")]),e._v(" "),i("el-form",{staticClass:"card-form rule-form",attrs:{model:e.extend_rule}},[i("el-form-item",{attrs:{label:"客户类型:",required:""}},[i("el-radio-group",{on:{change:e.changeAccountType},model:{value:e.extend_rule.account_type,callback:function(t){e.$set(e.extend_rule,"account_type",t)},expression:"extend_rule.account_type"}},[i("el-radio",{attrs:{label:"3",disabled:e.isLockPrice}},[e._v("按会员等级")]),e._v(" "),i("el-radio",{attrs:{label:"1"}},[e._v("按制卡规则")]),e._v(" "),i("el-radio",{attrs:{label:"0",disabled:e.isLockPrice}},[e._v("按车队客户")]),e._v(" "),i("el-radio",{attrs:{label:"2",disabled:e.isLockPrice}},[e._v("按卡组")])],1)],1)],1),e._v(" "),i("p",{directives:[{name:"show",rawName:"v-show",value:3==e.extend_rule.account_type,expression:"extend_rule.account_type==3"}],staticClass:"title"},[e._v("勾选规则对应的会员等级")]),e._v(" "),i("el-form",{directives:[{name:"show",rawName:"v-show",value:3==e.extend_rule.account_type,expression:"extend_rule.account_type==3"}],staticClass:"card-form rule-form",attrs:{model:e.extend_rule}},[i("el-form-item",{staticClass:"last-item",attrs:{label:"会员等级:",required:""}},[i("el-checkbox",{attrs:{indeterminate:e.isClassIndeterminate},on:{change:e.handleCheckClassAllChange},model:{value:e.checkClassAll,callback:function(t){e.checkClassAll=t},expression:"checkClassAll"}},[e._v("全选")]),e._v(" "),i("el-checkbox-group",{staticStyle:{"margin-left":"81px"},on:{change:e.handleCheckedClassChange},model:{value:e.extend_rule.GradeStr,callback:function(t){e.$set(e.extend_rule,"GradeStr",t)},expression:"extend_rule.GradeStr"}},e._l(e.classList,function(t){return i("el-checkbox",{key:t.id,attrs:{label:t.id}},[e._v(e._s(t.level_name))])}),1)],1)],1),e._v(" "),i("p",{directives:[{name:"show",rawName:"v-show",value:0==e.extend_rule.account_type,expression:"extend_rule.account_type==0"}],staticClass:"title"},[e._v("勾选规则对应的车队")]),e._v(" "),i("el-form",{directives:[{name:"show",rawName:"v-show",value:0==e.extend_rule.account_type,expression:"extend_rule.account_type==0"}],staticClass:"card-form rule-form",attrs:{model:e.extend_rule}},[i("el-form-item",{staticClass:"last-item",attrs:{label:"车队名称:",required:""}},[i("el-checkbox",{attrs:{indeterminate:e.isCompanyIndeterminate},on:{change:e.handleCheckCompanyAllChange},model:{value:e.checkCompanyAll,callback:function(t){e.checkCompanyAll=t},expression:"checkCompanyAll"}},[e._v("全选")]),e._v(" "),i("el-checkbox-group",{staticStyle:{"margin-left":"81px"},on:{change:e.handleCheckedCompanyChange},model:{value:e.extend_rule.company_id,callback:function(t){e.$set(e.extend_rule,"company_id",t)},expression:"extend_rule.company_id"}},e._l(e.companyList,function(t){return i("el-checkbox",{key:t.ID,attrs:{label:t.ID}},[e._v(e._s(t.CompanyName))])}),1)],1)],1),e._v(" "),i("p",{directives:[{name:"show",rawName:"v-show",value:1==e.extend_rule.account_type,expression:"extend_rule.account_type==1"}],staticClass:"title"},[e._v("勾选规则对应的卡")]),e._v(" "),i("el-form",{directives:[{name:"show",rawName:"v-show",value:1==e.extend_rule.account_type,expression:"extend_rule.account_type==1"}],staticClass:"card-form rule-form",attrs:{model:e.extend_rule}},[e.isLockPrice?i("el-form-item",{staticClass:"last-item",attrs:{label:"锁价卡类型:",required:""}},[i("el-radio-group",{on:{change:e.handleCheckedMarketType},model:{value:e.extend_rule.market_type,callback:function(t){e.$set(e.extend_rule,"market_type",t)},expression:"extend_rule.market_type"}},[i("el-radio",{attrs:{label:"2"}},[e._v("定额卡")]),e._v(" "),i("el-radio",{attrs:{label:"3"}},[e._v("定升卡")])],1)],1):e._e(),e._v(" "),e.isLockPrice?e._e():i("el-form-item",{staticClass:"last-item",attrs:{label:"卡名称:",required:""}},[i("el-checkbox",{attrs:{indeterminate:e.isThemeIndeterminate},on:{change:e.handleCheckThemeAllChange},model:{value:e.checkThemeAll,callback:function(t){e.checkThemeAll=t},expression:"checkThemeAll"}},[e._v("全选")]),e._v(" "),i("el-checkbox-group",{staticStyle:{"margin-left":"110px"},on:{change:e.handleCheckedThemeChange},model:{value:e.extend_rule.card_theme,callback:function(t){e.$set(e.extend_rule,"card_theme",t)},expression:"extend_rule.card_theme"}},e._l(e.cardThemeRuleList,function(t){return i("el-checkbox",{key:t.ID,attrs:{label:t.ID}},[e._v(e._s(t.Name))])}),1)],1),e._v(" "),e.isLockPrice?i("el-form-item",{staticClass:"last-item",attrs:{label:"卡名称:",required:""}},[i("el-checkbox",{attrs:{indeterminate:e.isThemeIndeterminate},on:{change:e.handleCheckThemeAllChange},model:{value:e.checkThemeAll,callback:function(t){e.checkThemeAll=t},expression:"checkThemeAll"}},[e._v("全选")]),e._v(" "),i("el-checkbox-group",{staticStyle:{"margin-left":"110px"},on:{change:e.handleCheckedThemeChange},model:{value:e.extend_rule.card_theme,callback:function(t){e.$set(e.extend_rule,"card_theme",t)},expression:"extend_rule.card_theme"}},e._l(e.cardThemeRuleList,function(t){return i("el-checkbox",{key:t.ID,attrs:{label:t.ID}},[e._v(e._s(t.Name))])}),1)],1):e._e()],1),e._v(" "),i("p",{directives:[{name:"show",rawName:"v-show",value:2==e.extend_rule.account_type,expression:"extend_rule.account_type==2"}],staticClass:"title"},[e._v("勾选规则对应的卡组")]),e._v(" "),i("el-form",{directives:[{name:"show",rawName:"v-show",value:2==e.extend_rule.account_type,expression:"extend_rule.account_type==2"}],staticClass:"card-form rule-form",attrs:{model:e.extend_rule}},[i("el-form-item",{staticClass:"last-item",attrs:{label:"卡组名称:",required:""}},[i("el-checkbox",{attrs:{indeterminate:e.isCustomerGroupIndeterminate},on:{change:e.handleCheckCustomerGroupAllChange},model:{value:e.checkCustomerGroupAll,callback:function(t){e.checkCustomerGroupAll=t},expression:"checkCustomerGroupAll"}},[e._v("全选")]),e._v(" "),i("el-checkbox-group",{staticStyle:{"margin-left":"100px"},on:{change:e.handleCheckedCustomerGroupChange},model:{value:e.extend_rule.customer_group_id,callback:function(t){e.$set(e.extend_rule,"customer_group_id",t)},expression:"extend_rule.customer_group_id"}},e._l(e.customerGroupList,function(t){return i("el-checkbox",{key:t.ID,attrs:{label:t.ID}},[e._v(e._s(t.CustomerGroupName))])}),1)],1)],1),e._v(" "),i("p",{directives:[{name:"show",rawName:"v-show",value:1==e.extend_rule.account_type,expression:"extend_rule.account_type==1"}],staticClass:"title"},[e._v("卡使用范围")]),e._v(" "),i("el-form",{directives:[{name:"show",rawName:"v-show",value:1==e.extend_rule.account_type,expression:"extend_rule.account_type==1"}],staticClass:"card-form rule-form",attrs:{model:e.extend_rule}},[i("el-form-item",{attrs:{label:"卡类型:",prop:"card_type",required:""}},[i("el-checkbox-group",{model:{value:e.extend_rule.card_type,callback:function(t){e.$set(e.extend_rule,"card_type",t)},expression:"extend_rule.card_type"}},[i("el-checkbox",{attrs:{label:"1"}},[e._v("个人卡")]),e._v(" "),i("el-checkbox",{attrs:{label:"2",disabled:e.isLockPrice}},[e._v("车队卡")]),e._v(" "),i("el-checkbox",{attrs:{label:"3",disabled:e.isLockPrice}},[e._v("不记名卡")])],1)],1)],1),e._v(" "),i("p",{staticClass:"title"},[e._v("高级设置")]),e._v(" "),i("el-form",{staticClass:"card-form rule-form",attrs:{model:e.extend_rule}},[i("el-form-item",{attrs:{label:"付款方式:",required:""}},[i("el-checkbox",{attrs:{indeterminate:e.isPayWayIndeterminate},on:{change:e.handleCheckPayWayAllChange},model:{value:e.checkPayWayAll,callback:function(t){e.checkPayWayAll=t},expression:"checkPayWayAll"}},[e._v("全选")]),e._v(" "),i("el-checkbox-group",{staticStyle:{"margin-left":"110px"},on:{change:e.handleCheckedPayWayChange},model:{value:e.extend_rule.pay_way,callback:function(t){e.$set(e.extend_rule,"pay_way",t)},expression:"extend_rule.pay_way"}},e._l(e.payWayList,function(t){return i("el-checkbox",{key:t.key,attrs:{label:t.BH}},[e._v(e._s(t.MC))])}),1)],1),e._v(" "),i("el-form-item",{attrs:{label:"专享设置:"}},[i("el-radio-group",{model:{value:e.extend_rule.is_birthday,callback:function(t){e.$set(e.extend_rule,"is_birthday",t)},expression:"extend_rule.is_birthday"}},[i("el-radio",{attrs:{label:"0"}},[e._v("无")]),e._v(" "),i("el-radio",{attrs:{label:"1"}},[e._v("生日专享")])],1),e._v(" "),i("el-tooltip",{attrs:{placement:"right",effect:"light","popper-class":"card-tooltip"}},[i("div",{attrs:{slot:"content"},slot:"content"},[e._v("生日信息来源于公众号个人中心的个人资料设置。若勾选，则"),i("br"),e._v("用户生日当天享受本条规则。")]),e._v(" "),i("i",{staticClass:"el-icon-question",staticStyle:{"font-size":"18px"}})])],1),e._v(" "),i("el-form-item",{attrs:{label:"充值专享:"}},[i("el-radio-group",{model:{value:e.extend_rule.first_recharge,callback:function(t){e.$set(e.extend_rule,"first_recharge",t)},expression:"extend_rule.first_recharge"}},[i("el-radio",{attrs:{label:"0"}},[e._v("无")]),e._v(" "),i("el-radio",{attrs:{label:"1"}},[e._v("首次充值专享")])],1)],1)],1),e._v(" "),i("div",{staticClass:"btn-box"},[i("el-button",{on:{click:function(t){return e.conditionBefore()}}},[e._v("上一步")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.conditionNext()}}},[e._v("下一步")])],1)],1),e._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:e.showtimeRuleForm,expression:"showtimeRuleForm"}],staticClass:"card-form spend-form"},[i("el-form",{ref:"form",staticClass:"time-form",attrs:{model:e.time_rule,"label-width":"80px"}},[i("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"时间类型"}},[i("el-radio-group",{model:{value:e.time_rule.type,callback:function(t){e.$set(e.time_rule,"type",t)},expression:"time_rule.type"}},[i("el-radio",{staticStyle:{"min-width":"80px"},attrs:{label:0}},[e._v("无限制")]),e._v(" "),i("el-radio",{staticStyle:{"min-width":"80px"},attrs:{label:1}},[e._v("每日")]),e._v(" "),i("el-radio",{staticStyle:{"min-width":"80px"},attrs:{label:2}},[e._v("每周")]),e._v(" "),i("el-radio",{staticStyle:{"min-width":"80px"},attrs:{label:3}},[e._v("每月")])],1)],1)],1),e._v(" "),i("el-repeat-time-picker",{staticStyle:{"margin-left":"128px"},attrs:{xstyle:"width: 280px;","value-format":"HH:mm:ss","max-length":5,type:e.time_rule.type},on:{change:e.checkTime},model:{value:e.time_rule.selectedTime,callback:function(t){e.$set(e.time_rule,"selectedTime",t)},expression:"time_rule.selectedTime"}}),e._v(" "),i("div",{staticClass:"time-btn-box"},[i("el-button",{on:{click:function(t){return e.timeRuleBefore()}}},[e._v("上一步")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.timeRuleNext("timeRuleForm")}}},[e._v("下一步")])],1)],1),e._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:e.showgivenForm,expression:"showgivenForm"}],staticClass:"card-form rule-form given-form"},[i("el-form",{ref:"chargeGive",staticClass:"card-form",attrs:{model:e.charge_give_rule,rules:e.chargeGiveRules}},[i("el-form-item",{staticClass:"item03",staticStyle:{"margin-left":"0"},attrs:{label:"充值赠送:"}},[2==e.extend_rule.market_type?i("span",[e._v("销售单价")]):e._e(),e._v(" "),3==e.extend_rule.market_type?i("span",[e._v("销售油量")]):e._e(),e._v(" "),i("el-form-item",{staticClass:"item05"},[2==e.extend_rule.market_type?i("el-input",{staticStyle:{width:"88px"},on:{input:e.calculationRules},model:{value:e.UnitPrice,callback:function(t){e.UnitPrice=t},expression:"UnitPrice"}}):e._e(),e._v(" "),3==e.extend_rule.market_type?i("el-input",{staticStyle:{width:"88px"},on:{input:e.calculationRules},model:{value:e.sellPrice,callback:function(t){e.sellPrice=t},expression:"sellPrice"}}):e._e()],1),e._v(" "),2==e.extend_rule.market_type?i("span",[e._v("元/升，")]):e._e(),e._v(" "),3==e.extend_rule.market_type?i("span",[e._v("升，")]):e._e(),e._v(" "),i("span",[e._v("销售价格")]),e._v(" "),i("el-form-item",{staticClass:"item05"},[i("el-input",{staticStyle:{width:"88px"},on:{input:e.calculationRules},model:{value:e.SumPrice,callback:function(t){e.SumPrice=t},expression:"SumPrice"}})],1),e._v(" "),i("span",[e._v("元，")]),e._v(" "),2==e.extend_rule.market_type?i("span",[e._v("赠送金额")]):e._e(),e._v(" "),3==e.extend_rule.market_type?i("span",[e._v("赠送油量")]):e._e(),e._v(" "),i("el-form-item",{staticClass:"item05"},[i("el-input",{staticStyle:{width:"88px"},on:{input:e.calculationRules},model:{value:e.giveNumber,callback:function(t){e.giveNumber=t},expression:"giveNumber"}})],1),e._v(" "),2==e.extend_rule.market_type?i("span",[e._v("元，")]):e._e(),e._v(" "),3==e.extend_rule.market_type?i("span",[e._v("升")]):e._e(),e._v(" "),2==e.extend_rule.market_type?i("span",[e._v("折后单价")]):e._e(),e._v(" "),2==e.extend_rule.market_type?i("el-form-item",{staticClass:"item05"},[i("el-input",{staticStyle:{width:"88px",color:"#32af50"},attrs:{disabled:!0},model:{value:e.salePrice,callback:function(t){e.salePrice=t},expression:"salePrice"}})],1):e._e(),e._v(" "),2==e.extend_rule.market_type?i("span",[e._v("元/升")]):e._e()],1),e._v(" "),3==e.extend_rule.market_type?i("p",{staticClass:"ruleItem"},[e._v("\n                    油量 "),i("span",{staticClass:"dataBase"},[e._v(e._s(+e.sellPrice+ +e.giveNumber))]),e._v(" 升，\n                    本金 "),i("span",{staticClass:"dataBase"},[e._v(e._s(e.SumPrice))]),e._v(" 元，\n                    赠金 "),i("span",{staticClass:"dataBase"},[e._v(e._s(e.givePrice))]),e._v(" 元，\n                    销售单价："),i("span",{staticClass:"dataBase"},[e._v(e._s(0==e.sellPrice||0==e.SumPrice?0:(e.SumPrice/e.sellPrice).toFixed(2)))]),e._v(" 元/升，\n                    折后单价 "),i("span",{staticClass:"dataBase"},[e._v(e._s(e.salePrice))]),e._v(" 元/升\n                    ")]):e._e(),e._v(" "),i("el-form-item",{staticClass:"last-form-item"},[i("el-button",{on:{click:e.givenBefore}},[e._v("上一步")]),e._v(" "),e.isCreate?i("el-button",{attrs:{disabled:e.btnDisabled,type:"primary"},on:{click:e.create}},[e._v("创建")]):i("el-button",{attrs:{disabled:e.btnDisabled,type:"primary"},on:{click:e.save}},[e._v("确认")])],1)],1)],1)],1)])},staticRenderFns:[]};var o=i("VU/8")(c,u,!1,function(e){i("XZyV"),i("5tt7")},"data-v-872994de",null);t.default=o.exports}});