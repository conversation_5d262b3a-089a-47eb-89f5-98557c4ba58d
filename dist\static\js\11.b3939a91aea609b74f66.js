webpackJsonp([11],{"3TpU":function(t,e,a){"use strict";var o=a("uotZ"),i=a.n(o),n=a("bOzO");e.a={data:function(){return{FIXED_PRICE_MODE:n.a}},methods:{zero2Formater:function(t){return t&&0!==Number(t)?t:"-"},sumUnitLiter:function(t){return t&&Array.isArray(t)?t.reduce(function(t,e){return e&&"number"==typeof e.UnitLiter?t.plus(new i.a(e.UnitLiter)):t},new i.a(0)).toFixed(4):0}}}},UCuc:function(t,e){},wnEo:function(t,e){},x608:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=a("Xxa5"),i=a.n(o),n=a("exGp"),s=a.n(n),r=a("Dd8w"),l=a.n(r),c=a("FZmr"),m=a("NYxO"),p=a("iJJg"),d=a("V2BW"),u=a("M4fF"),y=a("bOzO"),_=a("3TpU"),C={name:"EnterpriseInformationManagement",components:{SelectPlus:d.a,DownloadTips:c.a},mixins:[p.c,_.a],created:function(){this.getCustomerType()},data:function(){return{isTotalReportForm:!0,isAdd:!0,searchTxt:"",tableData:[],loading:!0,currentPage:1,pageSize:10,total:0,stationList:[],detailTableData:[],modifyDialogVisible:!1,modifyForm:{fixedPrice:y.b[0].value,CompanyType:"",id:"",station_id:"",company_name:"",company_contacts_level:"",remark:"",pay_method:"0",state:"100",company_address:"",credit_type:"0",isAllowRecharge:"1",credit_num:"0",invoice_open_type:""},ContactList:[{ContactName:"",ContactPhone:"",Remark:"",State:100,DefaultContact:0}],levelList:[],detailDialogVisible:!1,modifyFormRules:{station_id:[{required:!0,message:"请选择油站",trigger:"blur"}],invoice_open_type:[{required:!0,message:"请选择开票方式",trigger:"blur"}],company_name:[{required:!0,message:"请输入车队名称",trigger:"blur"},{max:24,message:"车队名称长度不能超过24个字符",trigger:"blur"}]},company_state:0,adminDialogVisible:!1,adminForm:{ContactPhone:"",ContactName:"",Remark:""},adminFormRules:{ContactName:[{required:!0,message:"请输入管理员名称",trigger:"blur"}],ContactPhone:[{required:!0,validator:function(t,e,a){if(""===e)a(new Error("请输入手机号"));else{if(!/^1[3|4|5|6|7|8|9]\d{9}$/.test(e))return a(new Error("请填写正确的手机号码！"));a()}},trigger:"blur"}]},adminLoading:!1,CompanyID:"",ContactID:"",isAddAdmin:!1,old_credit_type:"",old_credit_num:"",companyDetailDialogVisible:!1,emptyDialogVisible:!1,emptyTipsDialogVisible:!1,Company:{},clearMoney:0,canClear:!0,handleabled:!0,isGroupSettle:!1,groupSettleStid:"",showDownloadTips:!1,companyOptions:[]}},mounted:function(){var t=localStorage.getItem("__userInfo__")||"";if(this.isTotalReportForm=426!=JSON.parse(t).group_id,void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.getStationList(),this.getNowLevelConfig(),this.getCompanyList()},computed:l()({currentFixPriceMode:function(){return this.Company?String(this.Company.CardLockType):String(y.a.No)},AllowRechargeText:function(){return{0:"不允许",1:"允许"}},fixedPriceRadioOptions:function(){return y.b},showInvoiceReport:function(){return this.groupBaseInfo&&1===this.groupBaseInfo.show_company_invoice_report}},Object(m.d)(["groupBaseInfo"]),{editableTabsValue:{get:function(){return this.$store.getters.getEditableTabsValue},set:function(t){}},editableTabs:{get:function(){return this.$store.getters.getEditableTabs},set:function(t){this.$store.commit("SETEDITABLETABS",t)}},tabsIndex:{get:function(){return this.$store.getters.getTabIndex},set:function(t){this.$store.commit("SETTABINDEX",t)}},cacheArray:{get:function(){return this.$store.getters.getCacheArray},set:function(t){this.$store.commit("SETCACHEARRAY",t)}}},Object(m.c)(["showLockPrice"]),Object(m.c)({getCurrentStation:"getCurrentStation"})),methods:l()({changeFixedPrice:function(){this.modifyForm.fixedPrice!==this.fixedPriceRadioOptions[0].value&&(this.modifyForm.isAllowRecharge="0")},getPriceType:function(t){t=String(t);var e=y.b.find(function(e){return e.value===t});return e?e.label:t},getFixedPriceLabelByValue:function(t){var e=this.fixedPriceRadioOptions.find(function(e){return e.value===t});return e?e.label:t}},Object(m.b)({changeEditableTabsValue:"changeEditableTabsValue",changeEditableTabs:"changeEditableTabs",changeCacheArray:"changeCacheArray"}),{remoteMethod:function(t){var e=this;return s()(i.a.mark(function a(){var o;return i.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(a.prev=0,t){a.next=3;break}return a.abrupt("return",e.companyOptions=[]);case 3:return a.next=5,e.$axios.post("/CompanyCard/getCompanyNameList",{input:t});case 5:if(o=a.sent,console.log("搜索结果",o),200==o.data.status){a.next=9;break}return a.abrupt("return",e.$message.error(o.data.info));case 9:e.companyOptions=JSON.parse(o.data.data),a.next=16;break;case 12:a.prev=12,a.t0=a.catch(0),console.log(a.t0),e.$message.error("查询车队名称异常");case 16:case"end":return a.stop()}},a,e,[[0,12]])}))()},getStationList:function(){var t=this;t.$axios.post("/Stations/getStationList",{}).then(function(e){200==e.status&&(t.stationList=e.data.data)}).catch(function(e){t.$message({message:"获取油站信息失败",type:"error"})})},getStationName:function(t){var e="";return this.stationList.forEach(function(a){a.stid==t&&(e=a.stname)}),e},getNowLevelConfig:function(){var t=this;t.$axios.post("/LevelConfig/getNowLevelConfig",{}).then(function(e){200==e.status&&(t.levelList=e.data.data.level)}).catch(function(e){t.$message({message:"获取油站信息失败",type:"error"})})},getCompanyList:function(){var t=this;t.loading=!0,t.$axios.post("/CompanyCard/getCompanyList",{page:t.currentPage,page_size:t.pageSize,input:t.searchTxt,state:t.company_state}).then(function(e){t.loading=!1,200==e.data.status?(t.tableData=e.data.data.dt,t.total=e.data.data.TotalQty,t.isGroupSettle=1==e.data.data.group_settle,e.data.data.hasOwnProperty("group_settle_stid")&&(t.groupSettleStid=e.data.data.group_settle_stid)):t.$message({message:e.data.info,type:"error"})}).catch(function(t){})},handleCurrentChange:function(t){this.currentPage=t,this.getCompanyList()},handleSizeChange:function(t){this.pageSize=t,this.getCompanyList()},search:function(){this.currentPage=1,this.getCompanyList()},searchAll:function(){this.currentPage=1,this.searchTxt="",this.getCompanyList()},getCompanyInfo:function(t){var e=this;e.$axios.post("/CompanyCard/getCompanyInfo",{id:t}).then(function(t){200==t.data.status?(e.modifyForm.credit_num=t.data.data.credit_num,e.modifyForm.credit_type=String(t.data.data.credit_type),e.old_credit_type=String(t.data.data.credit_type),e.old_credit_num=String(t.data.data.credit_num),e.ContactList=t.data.data.ContactList,0==e.ContactList.length&&(e.ContactList=[{ContactName:"",ContactPhone:"",Remark:"",State:100,DefaultContact:1}])):e.$message({message:t.data.info,type:"error"})})},handleModifyClick:function(t){this.isAdd=!1,this.modifyDialogVisible=!0,this.modifyForm.id=t.ID,this.showLockPrice&&(this.modifyForm.fixedPrice=String(t.CardLockType)),this.modifyForm.CompanyType=String(t.CompanyType||t.customer_type||""),this.modifyForm.station_id=Number(t.StationNO),this.modifyForm.invoice_open_type=99==t.KPType?"":String(t.KPType),this.modifyForm.company_name=t.CompanyName,t.company_contacts_level?this.modifyForm.company_contacts_level=Number(t.company_contacts_level):this.modifyForm.company_contacts_level="",this.modifyForm.remark=t.Remark,this.modifyForm.pay_method=String(t.PayMethod),this.modifyForm.state=String(t.State),this.modifyForm.company_address=t.CompanyAddress,this.modifyForm.ContactList=t.ContactList,this.modifyForm.isAllowRecharge=String(t.IsAllowRecharge),this.getCompanyInfo(t.ID)},handleDetailClick:function(t){this.detailTableData=[],this.detailDialogVisible=!0,this.CompanyID=t.ID,this.getCompanyContact(this.CompanyID)},getCompanyContact:function(t){var e=this;e.adminLoading=!0,e.$axios.post("/CompanyCard/getCompanyContact",{CompanyID:t}).then(function(t){e.adminLoading=!1,200==t.data.status?e.detailTableData=t.data.data:e.$message({message:t.data.info,type:"error"})}).catch(function(t){})},add:function(){this.isAdd=!0,this.modifyDialogVisible=!0,this.modifyForm={fixedPrice:y.b[0].value,id:"",station_id:"",company_name:"",company_contacts_level:"",remark:"",pay_method:"0",state:"100",company_address:"",credit_type:"0",isAllowRecharge:"1",credit_num:"0",invoice_open_type:"",CompanyType:""},this.ContactList=[{ContactName:"",ContactPhone:"",Remark:"",State:100,DefaultContact:0}],this.levelList.length&&(this.modifyForm.company_contacts_level=this.levelList[0].id)},setCompany:function(t){var e=this,a=this;a.isGroupSettle&&(a.modifyForm.station_id=Number(a.groupSettleStid)),a.$refs[t].validate(function(t){if(t){a.ContactList[0].DefaultContact=1;var o={id:a.modifyForm.id,station_id:Number(a.modifyForm.station_id),company_name:a.modifyForm.company_name,remark:a.modifyForm.remark,pay_method:a.modifyForm.pay_method,company_contacts_level:a.modifyForm.company_contacts_level,state:a.modifyForm.state,invoice_open_type:a.modifyForm.invoice_open_type,company_address:a.modifyForm.company_address,isAllowRecharge:a.modifyForm.isAllowRecharge,ContactList:a.ContactList};o.lock_price=a.modifyForm.fixedPrice,o.customer_type="",(e.showInvoiceReport||a.modifyForm.CompanyType)&&(o.customer_type=a.modifyForm.CompanyType);for(var i=0;i<a.ContactList.length;i++){if(!a.ContactList[i].ContactName||!a.ContactList[i].ContactPhone)return a.$message({message:"请完善管理员信息",type:"error"}),!1;if(!/^1[3|4|5|6|7|8|9]\d{9}$/.test(a.ContactList[i].ContactPhone))return a.$message.error("请填写正确的手机号码")}a.handleabled=!1,(a.isAdd||a.old_credit_type!=a.modifyForm.credit_type||a.old_credit_num!=a.modifyForm.credit_num)&&(o.credit_type=a.modifyForm.credit_type,o.credit_num=a.modifyForm.credit_num),a.$axios.post("/CompanyCard/setCompany",o).then(function(t){200==t.data.status?(a.isAdd?a.$message({message:"添加成功",type:"success"}):a.$message({message:"修改成功",type:"success"}),a.getCompanyList()):a.$message({message:t.data.info,type:"error"}),a.handleabled=!0,a.modifyDialogVisible=!1}).catch(function(t){a.handleabled=!0,a.modifyDialogVisible=!1,a.isAdd?a.$message({message:"添加失败",type:"error"}):a.$message({message:"修改失败",type:"error"})})}})},handleCardDetail:function(t){this.$router.push({name:"CardManager",params:{id:t}}),this.$emit("currentNav",{current:"卡管理",prev:[]}),this.cacheArray.find(function(t){return"卡管理"==t})||this.changeCacheArray("卡管理");var e=++this.tabsIndex+"",a=this.editableTabs.find(function(t){return"卡管理"==t.title});a?this.changeEditableTabsValue(a.name):(this.changeEditableTabs({title:"卡管理",name:e,router:"/CardManager",current:"卡管理",prev:[]}),this.changeEditableTabsValue(e))},filterHandler:function(t){1==t.State.length?this.company_state=t.State[0]:this.company_state=0,this.getCompanyList()},showAddAdmin:function(){this.isAddAdmin=!0,this.adminDialogVisible=!0,this.$refs.adminForm.resetFields(),this.adminForm.Remark=""},showModifyAdmin:function(t){this.isAddAdmin=!1,this.adminDialogVisible=!0,this.ContactID=t.ID,this.adminForm.ContactPhone=t.ContactPhone,this.adminForm.ContactName=t.ContactName,this.adminForm.Remark=t.Remark},setCompanyContact:function(t){var e=this;e.$refs[t].validate(function(t){if(t){var a={};a.CompanyID=e.CompanyID,a.ContactPhone=e.adminForm.ContactPhone,a.ContactName=e.adminForm.ContactName,a.Remark=e.adminForm.Remark,e.isAddAdmin||(a.ID=e.ContactID),e.$axios.post("/CompanyCard/setCompanyContact",a).then(function(t){200==t.data.status?(e.$message({message:"操作成功",type:"success"}),e.adminDialogVisible=!1,e.getCompanyContact(e.CompanyID)):e.$message({message:t.data.info,type:"error"})}).catch(function(t){})}})},deleteCompanyContact:function(t){var e=this,a=this;this.$confirm("此操作将删除该管理员, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){a.$axios.post("/CompanyCard/deleteCompanyContact",{ID:t.ID,CompanyID:a.CompanyID,State:101}).then(function(t){200==t.data.status?(a.$message({message:"删除成功",type:"success"}),a.getCompanyContact(a.CompanyID)):a.$message({message:t.data.info,type:"error"})}).catch(function(t){})}).catch(function(){e.$message({type:"info",message:"已取消删除"})})},addContacts:function(){3!=this.ContactList.length?this.ContactList.push({ContactName:"",ContactPhone:"",Remark:"",State:100,DefaultContact:0,CompanyID:this.modifyForm.id}):this.$message({type:"info",message:"最多设置三个管理员"})},delectContacts:function(t){this.ContactList.splice(t,1)},showCompanyInfo:function(t){this.companyDetailDialogVisible=!0;var e=Object(u.cloneDeep)(t);e.customerType=this.customerTypeOptions.find(function(e){return String(t.CompanyType)===String(e.CompanyTypeID)}),this.Company=e,this.getCompanyContact(t.ID),this.getCompanyCardBalanceData(t.ID)},showEmptyDialogVisible:function(){this.companyDetailDialogVisible=!0,this.emptyDialogVisible=!0,this.getMoney(this.Company.CompanyCreditType,this.Company.CompanyCreditAccount,this.Company.CompanyUsableAccount)},getMoney:function(t,e,a){this.clearMoney="2"==t?(Number(e)+Number(a)).toFixed(2):a},showEmptyWarning:function(){var t=this,e={CompanyID:this.Company.ID,StationNO:this.Company.StationNO,BalanceClearAmount:this.clearMoney,GiveBalanceClearAmount:this.Company.CompanyGiveAccount};this.canClear=!1,this.$axios.post("/Card/cardBalanceClear",e).then(function(e){t.canClear=!0,200==e.data.status?(t.cardRuleDialogVisible=!1,t.emptyDialogVisible=!1,t.emptyTipsCompanyDialogVisible=!1,t.emptyTipsDialogVisible=!1,t.cardCheckDialogVisible=!1,t.companyDetailDialogVisible=!1,t.$message.success("操作成功！"),t.getCompanyList()):t.$message.error(e.data.info)})},getCompanyCardBalanceData:function(t){var e=this;this.$axios.post("/CompanyCard/getCompanyCardBalanceData",{company_id:t}).then(function(t){200==t.data.status?(e.Company.SubCardAccount=t.data.data.SubCardAccount.toFixed(2),e.Company.SubCardAccoGiveunt=t.data.data.SubCardAccoGiveunt.toFixed(2),e.Company.SubCardSumAccount=t.data.data.SubCardSumAccount.toFixed(2),e.$forceUpdate()):e.$message.error(t.data.info)})},carInfoDownload:function(){var t=this;return s()(i.a.mark(function e(){var a,o;return i.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a={page:t.currentPage,page_size:t.pageSize,input:t.searchTxt,state:t.company_state},e.next=3,t.$axios.post("/CompanyCard/downloadCompanyList",a);case 3:if(200==(o=e.sent).data.status){e.next=6;break}return e.abrupt("return",t.$message.error(o.data.info));case 6:t.showDownloadTips=!0;case 7:case"end":return e.stop()}},e,t)}))()}}),watch:{getCurrentStation:function(t,e){0!=t.merchant_type&&t.value!=e.value&&(this.getStationList(),this.getNowLevelConfig(),this.getCompanyList())}}},f={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"wrap"},[a("div",{staticClass:"header"},[a("div",[a("el-button",{attrs:{type:"primary"},on:{click:t.add}},[t._v("添加")])],1),t._v(" "),a("div",[a("el-select",{attrs:{clearable:"",filterable:"",remote:"","reserve-keyword":"",placeholder:"请输入车队名称","remote-method":t.remoteMethod},on:{clear:t.search},model:{value:t.searchTxt,callback:function(e){t.searchTxt=e},expression:"searchTxt"}},t._l(t.companyOptions,function(t){return a("el-option",{key:t.CompanyID,attrs:{label:t.CompanyName,value:t.CompanyID}})}),1),t._v(" "),a("el-button",{attrs:{type:"primary",disabled:!t.searchTxt},on:{click:t.search}},[t._v("查询")]),t._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:t.isTotalReportForm,expression:"isTotalReportForm"}],attrs:{type:"primary",disabled:!t.tableData.length>0},on:{click:t.carInfoDownload}},[t._v("下载数据")])],1)]),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"table_out",staticStyle:{width:"100%","margin-bottom":"20px"},attrs:{"min-height":"580",data:t.tableData},on:{"filter-change":t.filterHandler}},[a("el-table-column",{attrs:{align:"center",prop:"ID","min-width":"80",label:"车队ID",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"CompanyName","min-width":"180",label:"车队名称",formatter:t.formatterCellval}}),t._v(" "),t.isGroupSettle?t._e():a("el-table-column",{attrs:{align:"center","min-width":"180",label:"开户油站"},scopedSlots:t._u([{key:"default",fn:function(e){return[""!==t.getStationName(e.row.StationNO)?a("span",[t._v(t._s(t.getStationName(e.row.StationNO)))]):a("span",[t._v("-")])]}}],null,!1,1400192061)}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"CompanyCardStockNum","min-width":"80",label:"卡库存",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"CompanyCardNum","min-width":"80",label:"领卡数",formatter:t.formatterCellval}}),t._v(" "),t.showLockPrice?a("el-table-column",{attrs:{align:"left",width:"120",label:"母账升数"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t.zero2Formater(t.sumUnitLiter(e.row.companyBatches))))]}}],null,!1,**********)}):t._e(),t._v(" "),a("el-table-column",{attrs:{align:"left",width:"120",label:"母账余额（元）"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.CompanySumAccount).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"left",width:"120",label:"母账本金（元）"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.CompanyUsableAccount).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"left",width:"120",label:"母账赠金（元）"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.CompanyGiveAccount).toFixed(2)))]}}])}),t._v(" "),t.showLockPrice?a("el-table-column",{attrs:{align:"left",width:"120",label:"子卡升数"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t.zero2Formater(Number(e.row.CardSumLiter||"0").toFixed(4))))]}}],null,!1,**********)}):t._e(),t._v(" "),a("el-table-column",{attrs:{align:"left",width:"120",label:"子卡余额（元）"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.CardSumAccount).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"left",width:"120",label:"子卡本金（元）"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.CardKZSum).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"left",width:"120",label:"子卡赠金（元）"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.CardSKJSum).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"CompanyContacts","min-width":"120",label:"管理员",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"ContactsPhone","min-width":"120",label:"管理员手机号",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center","min-width":"140",label:"扣款方式"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(0==e.row.PayMethod?"仅允许卡账扣款":1==e.row.PayMethod?"母账优先":"卡账优先"))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center","min-width":"120",label:"信用功能",prop:"CompanyCreditType",formatter:t.formatterCellval},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(0==e.row.CompanyCreditType?"不启用":1==e.row.CompanyCreditType?"信贷":"保证金"))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",width:"120",label:"信用金额（元）"},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.CompanyCreditType?[t._v(t._s(Number(e.row.CompanyCreditSum).toFixed(2)))]:0==e.row.CompanyCreditType?[t._v("-")]:[t._v(t._s(Number(e.row.CompanyCreditAccount).toFixed(2)))]]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",width:"120",label:"已用额度（元）"},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.CompanyCreditType?[t._v(t._s(Number(e.row.CompanyCreditUsageLimit).toFixed(2)))]:[t._v("-")]]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center","min-width":"120","column-key":"State",filters:[{text:"启用",value:"100"},{text:"禁用",value:"101"}],label:"状态"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(100==e.row.State?"启用":"禁用"))])]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",width:"200",fixed:"right",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.showCompanyInfo(e.row)}}},[t._v("查看")]),t._v(" "),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleCardDetail(e.row.ID)}}},[t._v("子卡管理")]),t._v(" "),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleModifyClick(e.row)}}},[t._v("车队设置")])]}}])})],1),t._v(" "),a("div",{staticClass:"page_content"},[a("el-pagination",{staticClass:"page_left",attrs:{"current-page":t.currentPage,"page-size":t.pageSize,layout:"prev, pager, next",total:t.total},on:{"current-change":t.handleCurrentChange}}),t._v(" "),a("el-pagination",{staticClass:"page_right",attrs:{"page-sizes":[10,15,20,30],"page-size":t.pageSize,layout:"total, sizes",total:t.total},on:{"size-change":t.handleSizeChange}})],1),t._v(" "),a("el-dialog",{staticClass:"card-dialog",attrs:{title:t.isAdd?"添加车队":"车队设置",visible:t.modifyDialogVisible,"close-on-click-modal":!1,width:"950px"},on:{"update:visible":function(e){t.modifyDialogVisible=e}}},[a("el-form",{ref:"modifyForm",attrs:{model:t.modifyForm,rules:t.modifyFormRules,"label-width":"120px"}},[t.isAdd&&!t.isGroupSettle?a("el-form-item",{attrs:{label:"开户油站",prop:"station_id"}},[a("el-select",{attrs:{placeholder:"请选择开户油站"},model:{value:t.modifyForm.station_id,callback:function(e){t.$set(t.modifyForm,"station_id",e)},expression:"modifyForm.station_id"}},t._l(t.stationList,function(t){return a("el-option",{key:t.index,attrs:{label:t.stname,value:t.stid}})}),1),t._v(" "),a("i",{staticClass:"el-icon-warning",staticStyle:{color:"#F56C6C"}}),t._v("确认添加后，无法更改\n        ")],1):t._e(),t._v(" "),a("el-form-item",{attrs:{label:"车队名称",prop:"company_name"}},[a("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:"请输入车队名称"},model:{value:t.modifyForm.company_name,callback:function(e){t.$set(t.modifyForm,"company_name",e)},expression:"modifyForm.company_name"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"车队地址",prop:"company_address"}},[a("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:"请输入车队地址"},model:{value:t.modifyForm.company_address,callback:function(e){t.$set(t.modifyForm,"company_address",e)},expression:"modifyForm.company_address"}})],1),t._v(" "),t.showLockPrice?a("el-form-item",{attrs:{label:"锁价车队"}},[a("el-radio-group",{attrs:{disabled:!t.isAdd},on:{change:t.changeFixedPrice},model:{value:t.modifyForm.fixedPrice,callback:function(e){t.$set(t.modifyForm,"fixedPrice",e)},expression:"modifyForm.fixedPrice"}},t._l(t.fixedPriceRadioOptions,function(e,o){return a("el-radio",{key:o,attrs:{label:e.value}},[t._v(t._s(e.label))])}),1)],1):t._e(),t._v(" "),a("el-form-item",{attrs:{label:"扣款方式",prop:"pay_method"}},[a("el-radio-group",{model:{value:t.modifyForm.pay_method,callback:function(e){t.$set(t.modifyForm,"pay_method",e)},expression:"modifyForm.pay_method"}},[a("el-radio",{attrs:{label:"2"}},[t._v("卡账优先")]),t._v(" "),a("el-radio",{attrs:{label:"1"}},[t._v("母账优先")]),t._v(" "),a("el-radio",{attrs:{label:"0"}},[t._v("仅允许卡账扣款")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"子卡充值",prop:"isAllowRecharge"}},[a("el-radio-group",{attrs:{disabled:t.modifyForm.fixedPrice!==t.fixedPriceRadioOptions[0].value},model:{value:t.modifyForm.isAllowRecharge,callback:function(e){t.$set(t.modifyForm,"isAllowRecharge",e)},expression:"modifyForm.isAllowRecharge"}},[a("el-radio",{attrs:{label:"1"}},[t._v("允许")]),t._v(" "),a("el-radio",{attrs:{label:"0"}},[t._v("不允许")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"信用功能",prop:"credit_type"}},[a("el-radio-group",{model:{value:t.modifyForm.credit_type,callback:function(e){t.$set(t.modifyForm,"credit_type",e)},expression:"modifyForm.credit_type"}},[a("el-radio",{attrs:{label:"0"}},[t._v("不启用")]),t._v(" "),a("el-radio",{attrs:{label:"1"}},[t._v("信贷")]),t._v(" "),a("el-radio",{attrs:{label:"2"}},[t._v("保证金")])],1)],1),t._v(" "),0!=t.modifyForm.credit_type?a("el-form-item",{attrs:{prop:"credit_num",label:1==t.modifyForm.credit_type?"信贷额度":"保证金额度"}},[a("el-input",{staticStyle:{width:"160px"},model:{value:t.modifyForm.credit_num,callback:function(e){t.$set(t.modifyForm,"credit_num",e)},expression:"modifyForm.credit_num"}}),t._v(" "),a("span",[t._v("元")])],1):t._e(),t._v(" "),t._l(t.ContactList,function(e,o){return a("el-form-item",{key:o,attrs:{label:0==o?"管理员信息":"管理员信息"+(o+1),required:""}},[a("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"请输入管理员"},model:{value:e.ContactName,callback:function(a){t.$set(e,"ContactName",a)},expression:"item.ContactName"}}),t._v(" "),a("el-input",{staticStyle:{width:"180px"},attrs:{maxlength:"11",placeholder:"请输入管理员手机号"},model:{value:e.ContactPhone,callback:function(a){t.$set(e,"ContactPhone",a)},expression:"item.ContactPhone"}}),t._v(" "),a("el-input",{staticStyle:{width:"180px"},attrs:{maxlength:"30",placeholder:"请输入备注"},model:{value:e.Remark,callback:function(a){t.$set(e,"Remark",a)},expression:"item.Remark"}}),t._v(" "),0==o?a("el-button",{staticStyle:{margin:"0 15px 0 20px"},attrs:{type:"text"},on:{click:t.addContacts}},[t._v("添加")]):a("el-button",{staticStyle:{margin:"0 15px 0 20px",color:"#f56c6c"},attrs:{type:"text"},on:{click:function(e){return t.delectContacts(o)}}},[t._v("删除")]),t._v(" "),0==o?a("span",{},[t._v("最多设置3位管理员")]):t._e()],1)}),t._v(" "),a("el-form-item",{attrs:{label:"开票方式",prop:"invoice_open_type"}},[a("el-radio-group",{model:{value:t.modifyForm.invoice_open_type,callback:function(e){t.$set(t.modifyForm,"invoice_open_type",e)},expression:"modifyForm.invoice_open_type"}},[a("el-radio",{attrs:{label:"1"}},[t._v("消费开票")]),t._v(" "),a("el-radio",{attrs:{label:"0"}},[t._v("充值开票")]),t._v(" "),a("el-radio",{attrs:{label:"2"}},[t._v("不开票")])],1),t._v(" "),a("span",{staticStyle:{"margin-left":"20px",color:"#999"}},[t._v("优先级大于制卡规则")])],1),t._v(" "),t.showInvoiceReport?a("el-form-item",{attrs:{label:"客户类型"}},[a("select-plus",{directives:[{name:"loading",rawName:"v-loading",value:t.customerTypeLoading,expression:"customerTypeLoading"}],attrs:{clearable:"",list:t.invalidCustomerTypeList,multiple:!1,"check-all":!1},model:{value:t.modifyForm.CompanyType,callback:function(e){t.$set(t.modifyForm,"CompanyType",e)},expression:"modifyForm.CompanyType"}},[t.findDisabledCustomerType(t.modifyForm.CompanyType)?a("el-option",{attrs:{disabled:"",value:t.modifyForm.CompanyType,label:t.findDisabledCustomerType(t.modifyForm.CompanyType).CompanyTypeName}}):t._e(),t._v(" "),t._l(t.invalidCustomerTypeList,function(t,e){return a("el-option",{key:""+t.CompanyTypeID+e,attrs:{name:t.CompanyTypeID,value:t.CompanyTypeID,label:t.CompanyTypeName}})})],2)],1):t._e(),t._v(" "),a("el-form-item",{attrs:{label:"状态",prop:"state"}},[a("el-radio-group",{model:{value:t.modifyForm.state,callback:function(e){t.$set(t.modifyForm,"state",e)},expression:"modifyForm.state"}},[a("el-radio",{attrs:{label:"100"}},[t._v("启用")]),t._v(" "),a("el-radio",{attrs:{label:"101"}},[t._v("禁用")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{staticStyle:{width:"400px"},attrs:{rows:7,type:"textarea",maxlength:"110","show-word-limit":"",placeholder:"请输入备注"},model:{value:t.modifyForm.remark,callback:function(e){t.$set(t.modifyForm,"remark",e)},expression:"modifyForm.remark"}})],1)],2),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:function(e){t.modifyDialogVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary",size:"mini",disabled:!t.handleabled},on:{click:function(e){return t.setCompany("modifyForm")}}},[t._v(t._s(t.isAdd?"添 加":"确 定"))])],1)],1),t._v(" "),a("el-dialog",{staticClass:"card-dialog",attrs:{title:"管理员详情",visible:t.detailDialogVisible,"close-on-click-modal":!1,width:"1000px"},on:{"update:visible":function(e){t.detailDialogVisible=e}}},[a("el-button",{attrs:{type:"primary",disabled:t.detailTableData.length>=3},on:{click:t.showAddAdmin}},[t._v("添加管理员")]),t._v(" "),a("span",[t._v("还可添加"),a("span",{staticStyle:{color:"#32AF50"}},[t._v(t._s(3-t.detailTableData.length))]),t._v("位管理员")]),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.adminLoading,expression:"adminLoading"}],staticStyle:{width:"100%",margin:"30px 0"},attrs:{data:t.detailTableData}},[a("el-table-column",{attrs:{align:"center",prop:"ContactName",label:"名称",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"ContactPhone",label:"联系电话",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"Remark",label:"备注",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.showModifyAdmin(e.row)}}},[t._v("编 辑")]),t._v(" "),e.row.DefaultContact?t._e():a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.deleteCompanyContact(e.row)}}},[t._v("删 除")])]}}])})],1),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:function(e){t.detailDialogVisible=!1}}},[t._v("关 闭")])],1)],1),t._v(" "),a("el-dialog",{staticClass:"card-dialog",attrs:{title:t.isAddAdmin?"添加管理员":"编辑管理员",visible:t.adminDialogVisible,"close-on-click-modal":!1,width:"440px"},on:{"update:visible":function(e){t.adminDialogVisible=e}}},[a("el-form",{ref:"adminForm",attrs:{model:t.adminForm,"label-width":"120px",rules:t.adminFormRules}},[a("el-form-item",{attrs:{label:"名称",prop:"ContactName"}},[a("el-input",{staticStyle:{width:"188px"},model:{value:t.adminForm.ContactName,callback:function(e){t.$set(t.adminForm,"ContactName",e)},expression:"adminForm.ContactName"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"手机号",prop:"ContactPhone"}},[a("el-input",{staticStyle:{width:"188px"},model:{value:t.adminForm.ContactPhone,callback:function(e){t.$set(t.adminForm,"ContactPhone",e)},expression:"adminForm.ContactPhone"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{staticStyle:{width:"188px",heigth:"84px"},attrs:{type:"textarea",maxlength:"110","show-word-limit":""},model:{value:t.adminForm.Remark,callback:function(e){t.$set(t.adminForm,"Remark",e)},expression:"adminForm.Remark"}})],1)],1),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:function(e){t.adminDialogVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.setCompanyContact("adminForm")}}},[t._v("确 定")])],1)],1),t._v(" "),a("el-dialog",{staticClass:"dialog companyDetail-dialog",attrs:{"close-on-click-modal":!1,title:"查看车队详情","append-to-body":"",visible:t.companyDetailDialogVisible,width:"600px"},on:{"update:visible":function(e){t.companyDetailDialogVisible=e}}},[a("div",{staticClass:"main"},[a("div",{staticClass:"check-box"},[a("div",{staticClass:"left"},[a("p",[a("span",{staticClass:"txt"},[t._v("车队名称：")]),a("span",[t._v(t._s(t.Company.CompanyName))])]),t._v(" "),""!==t.getStationName(t.Company.StationNO)?a("p",[a("span",{staticClass:"txt"},[t._v("开户油站：")]),a("span",[t._v(t._s(t.getStationName(t.Company.StationNO)))])]):t._e(),t._v(" "),t.showLockPrice?a("p",[a("span",{staticClass:"txt"},[t._v("锁价车队：")]),a("span",[t._v(t._s(t.getPriceType(t.Company.CardLockType)))])]):t._e(),t._v(" "),a("p",[a("span",{staticClass:"txt"},[t._v("扣款方式：")]),a("span",[t._v(t._s(0==t.Company.PayMethod?"仅允许卡账扣款":1==t.Company.PayMethod?"母账优先":"卡账优先"))])]),t._v(" "),a("p",[a("span",{staticClass:"txt"},[t._v("信用功能：")]),a("span",[t._v(t._s(0==t.Company.CompanyCreditType?"不启用":1==t.Company.CompanyCreditType?"信贷":"保证金"))])]),t._v(" "),a("p",[a("span",{staticClass:"txt"},[t._v("开票方式：")]),a("span",[t._v(t._s(0==t.Company.KPType?"充值开票":1==t.Company.KPType?"消费开票":2==t.Company.KPType?"不开票":"-"))])]),t._v(" "),a("p",{directives:[{name:"show",rawName:"v-show",value:"0"!=t.Company.CompanyCreditType,expression:"Company.CompanyCreditType!='0'"}]},[a("span",{staticClass:"txt"},[t._v(t._s("2"==t.Company.CompanyCreditType?"保证金额：":"1"==t.Company.CompanyCreditType?"信贷额度：":""))]),a("span",[t._v(t._s(t.Company.CompanyCreditAccount)+"元")])]),t._v(" "),t._l(t.detailTableData,function(e,o){return a("div",{key:o},[a("p",[a("span",{staticClass:"txt"},[t._v("管理员"+t._s(o+1)+"：")]),a("span",[t._v(t._s(e.ContactName))])]),t._v(" "),a("p",[a("span",{staticClass:"txt"},[t._v("手机号：")]),a("span",[t._v(t._s(e.ContactPhone))])])])}),t._v(" "),a("p",[a("span",{staticClass:"txt"},[t._v("状态：")]),a("span",{staticClass:"state",class:{active:!0}},[t._v(t._s(100==t.Company.State?"启用":"禁用"))])]),t._v(" "),t.showInvoiceReport?a("p",[a("span",{staticClass:"txt"},[t._v("客户类型：")]),a("span",[t._v(t._s(t.Company.customerType&&t.Company.customerType.CompanyTypeName?t.Company.customerType.CompanyTypeName:t.Company.CompanyType))])]):t._e()],2),t._v(" "),a("div",{staticClass:"right"},[a("p",[a("span",{staticClass:"txt"},[t._v("卡库存：")]),a("span",[t._v(t._s(t.Company.CompanyCardStockNum)+"张")])]),t._v(" "),a("p",[a("span",{staticClass:"txt"},[t._v("领卡数：")]),a("span",[t._v(t._s(t.Company.CompanyCardNum)+"张")])]),t._v(" "),a("p",[a("span",{staticClass:"txt"},[t._v("母账余额：")]),a("span",[t._v(t._s(t.Company.CompanySumAccount)+"元")])]),t._v(" "),t.showLockPrice&&t.currentFixPriceMode===t.FIXED_PRICE_MODE.FixedRise?a("p",[a("span",{staticClass:"txt"},[t._v("母账升数：")]),a("span",[t._v(t._s(t.sumUnitLiter(t.Company.companyBatches))+"升")])]):t._e(),t._v(" "),a("p",[a("span",{staticClass:"txt"},[t._v("母账本金：")]),a("span",[t._v(t._s(t.Company.CompanyUsableAccount)+"元")])]),t._v(" "),a("p",[a("span",{staticClass:"txt"},[t._v("母账赠金：")]),a("span",[t._v(t._s(t.Company.CompanyGiveAccount)+"元")])]),t._v(" "),a("p",{directives:[{name:"show",rawName:"v-show",value:"1"==t.Company.CompanyCreditType,expression:"Company.CompanyCreditType=='1'"}]},[a("span",{staticClass:"txt"},[t._v("已用额度：")]),a("span",[t._v(t._s(t.Company.CompanyCreditUsageLimit)+"元")])]),t._v(" "),a("p",{staticClass:"cartxt"},[a("span",{staticClass:"txt"},[t._v("金额操作：")]),a("el-button",{attrs:{type:"text",disabled:t.Company.CompanyUsableAccount<=0},on:{click:t.showEmptyDialogVisible}},[t._v("点击母账余额清零")])],1),t._v(" "),a("p",[a("span",{staticClass:"txt"},[t._v("子卡余额：")]),a("span",[t._v(t._s(t.Company.SubCardSumAccount)+"元")])]),t._v(" "),t.showLockPrice&&t.currentFixPriceMode===t.FIXED_PRICE_MODE.FixedRise?a("p",[a("span",{staticClass:"txt"},[t._v("子卡升数：")]),a("span",[t._v(t._s(t.Company.CardSumLiter)+"升")])]):t._e(),t._v(" "),a("p",[a("span",{staticClass:"txt"},[t._v("子卡充值：")]),a("span",[t._v(t._s(t.AllowRechargeText[t.Company.IsAllowRecharge]||t.Company.IsAllowRecharge))])]),t._v(" "),a("p",[a("span",{staticClass:"txt"},[t._v("子卡本金：")]),a("span",[t._v(t._s(t.Company.SubCardAccount)+"元")])]),t._v(" "),a("p",[a("span",{staticClass:"txt"},[t._v("子卡赠金：")]),a("span",[t._v(t._s(t.Company.SubCardAccoGiveunt)+"元")])])])])]),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:function(e){t.companyDetailDialogVisible=!1}}},[t._v("关 闭")])],1)]),t._v(" "),a("el-dialog",{staticClass:"dialog empty-dialog",attrs:{"close-on-click-modal":!1,title:"车队账户余额清零","append-to-body":"",visible:t.emptyDialogVisible,width:"500px"},on:{"update:visible":function(e){t.emptyDialogVisible=e}}},[a("div",{staticClass:"main"},[a("p",{staticClass:"item"},[a("span",{staticClass:"txt"},[t._v("车队名称：")]),a("span",[t._v(t._s(t.Company.CompanyName))])]),t._v(" "),a("p",{staticClass:"item"},[a("span",{staticClass:"txt"},[t._v("开户油站：")]),a("span",[t._v(t._s(t.getStationName(t.Company.StationNO)))])]),t._v(" "),a("p",{staticClass:"item"},[a("span",{staticClass:"txt"},[t._v("母账余额：")]),a("span",{staticStyle:{color:"#333","font-weight":"bold"}},[t._v(t._s(t.Company.CompanySumAccount))]),t._v("元")]),t._v(" "),a("p",{staticClass:"item"},[a("span",{staticClass:"txt"},[t._v("母账本金：")]),a("span",{staticStyle:{color:"#32AF50","font-weight":"bold"}},[t._v(t._s(t.Company.CompanyUsableAccount))]),t._v("元 -现金退回")]),t._v(" "),a("p",{staticClass:"item"},[a("span",{staticClass:"txt"},[t._v("母账赠金：")]),a("span",{staticStyle:{color:"#333","font-weight":"bold"}},[t._v(t._s(t.Company.CompanyGiveAccount))]),t._v("元 -自动扣除")]),t._v(" "),a("p",{directives:[{name:"show",rawName:"v-show",value:"2"==t.Company.CompanyCreditType,expression:"Company.CompanyCreditType=='2'"}],staticClass:"item"},[a("span",{staticClass:"txt"},[t._v("保证金额：")]),a("span",{staticStyle:{color:"#32AF50","font-weight":"bold"}},[t._v(t._s(t.Company.CompanyCreditAccount))]),t._v("元 -现金退回")]),t._v(" "),a("p",{staticClass:"tips"},[t._v("退回"),a("span",{staticStyle:{color:"#32AF50"}},[t._v("现金"+t._s(t.clearMoney))]),t._v("元，赠金扣除"),a("span",{staticStyle:{color:"#32AF50"}},[t._v(t._s(t.Company.CompanyGiveAccount))]),t._v("元")]),t._v(" "),a("div",{staticClass:"tips-item"},[a("i",{staticClass:"el-icon-warning"}),t._v("余额清零后无法退回，请务必谨慎操作！")])]),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){t.emptyTipsDialogVisible=!0,t.emptyDialogVisible=!1}}},[t._v("余额清零")]),t._v(" "),a("el-button",{attrs:{size:"mini"},on:{click:function(e){t.emptyDialogVisible=!1}}},[t._v("关 闭")])],1)]),t._v(" "),a("el-dialog",{staticClass:"dialog emptyTips-dialog",attrs:{"close-on-click-modal":!1,"show-close":!1,"append-to-body":"",visible:t.emptyTipsDialogVisible,width:"400px"},on:{"update:visible":function(e){t.emptyTipsDialogVisible=e}}},[a("div",{staticClass:"emptyTips"},[a("i",{staticClass:"el-icon-warning",staticStyle:{"font-size":"64px",color:"#FA6400"}}),t._v(" "),a("p",[t._v("清零金额退回不涉及线上退款")]),t._v(" "),a("p",{staticStyle:{color:"#FA6400"}},[t._v("实退："+t._s(t.clearMoney)+"元 — 现金方式退回管理员")]),t._v(" "),a("p",[t._v("赠金："+t._s(t.Company.CompanyGiveAccount)+"元 — 自动扣除")])]),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini",type:"primary",disabled:!t.canClear},on:{click:t.showEmptyWarning}},[t._v("确 认")]),t._v(" "),a("el-button",{attrs:{size:"mini"},on:{click:function(e){t.emptyTipsDialogVisible=!1,t.emptyDialogVisible=!1}}},[t._v("关 闭")])],1)]),t._v(" "),a("download-tips",{attrs:{showDownloadTips:t.showDownloadTips},on:{"update:showDownloadTips":function(e){t.showDownloadTips=e},"update:show-download-tips":function(e){t.showDownloadTips=e}}})],1)},staticRenderFns:[]};var v=a("VU/8")(C,f,!1,function(t){a("wnEo"),a("UCuc")},"data-v-547980d0",null);e.default=v.exports}});