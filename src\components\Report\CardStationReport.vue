<template>
    <div class="report">
        <div class="content_header">
            <div class="left">
                <el-select v-model="stationValue" multiple collapse-tags placeholder="请选择油站" style="width:250px" @change="getCheckedStation">
                    <el-option
                        v-for="item in stationOptions"
                        :key="item.stid"
                        :label="item.stname"
                        :value="item.stid">
                    </el-option>
                </el-select>
                <el-date-picker
                    v-model="dateValue"
                    :clearable="false"
                    type="daterange"
                    :default-time="['00:00:00', '23:59:59']"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd">
                </el-date-picker>
                <el-button type="primary" @click="createReport" :disabled="!dateValue">生成</el-button>
            </div>
            <div class="right">
                <el-button type="primary" :disabled="DailyTableData.length == 0" @click="printContent">打印</el-button>
                <el-button type="primary" :disabled="DailyTableData.length == 0" @click="cardChargeDownload" v-show="isTotalReportForm">下载数据</el-button>
            </div>
        </div>

        <div id="myTable">
            <div class="tableData reportData">
                <div class="report_title">储值卡油站对账汇总表</div>
                <div class="report_header">
                    <div v-if="isGroup">集团名称：{{getCurrentStation.label}}</div>
                    <div v-else>油站名称：{{getCurrentStation.label}}</div>
                    <div>开始日期：{{start_time}}</div>
                    <div>结束日期：{{end_time}}</div>
                    <div>单位：元</div>
                </div>
                <el-table :data="DailyTableData" border v-loading="loading" size="small" align="center" fit>
                    <el-table-column align="center" prop="stname" label="油站名称"></el-table-column>
                    <el-table-column align="center" label="期间充值">
                        <el-table-column align="center" prop="recharge_number" label="充值笔数"></el-table-column>
                        <el-table-column align="center" label="充值金额">
                            <template slot-scope="scope">{{Number(scope.row.recharge_amount).toFixed(2)}}</template>
                        </el-table-column>
                        <el-table-column align="center" label="充值本金">
                            <template slot-scope="scope">{{Number(scope.row.recharge_capital).toFixed(2)}}</template>
                        </el-table-column>
                        <el-table-column align="center" label="充值赠金">
                            <template slot-scope="scope">{{Number(scope.row.recharge_bouns).toFixed(2)}}</template>
                        </el-table-column>
                    </el-table-column>
                    <el-table-column align="center" label="期间消费">
                        <el-table-column align="center" prop="consume_number" label="消费笔数"></el-table-column>
                        <el-table-column align="center" label="消费金额">
                            <template slot-scope="scope">{{Number(scope.row.consume_amount).toFixed(2)}}</template>
                        </el-table-column>
                        <el-table-column align="center" label="消费本金">
                            <template slot-scope="scope">{{Number(scope.row.consume_capital).toFixed(2)}}</template>
                        </el-table-column>
                        <el-table-column align="center" label="消费赠金" v-if="is_bp && isOilBounsListNotNull">
                          <template>
                            <el-table-column v-for="item in oilBounsList" :key="item.index" align="center" :label="item.oil_name" :prop="item.prop" width="120">
                              <template slot-scope="scope">{{Number(scope.row[item.prop]).toFixed(2)}}</template>
                            </el-table-column>
                          </template>
                          <el-table-column align="center" label="合计" v-if="is_bp && isOilBounsListNotNull">
                            <template slot-scope="scope">{{ Number(scope.row.consume_oil_total).toFixed(2) }}</template>
                          </el-table-column>
                          </el-table-column>
                          <el-table-column align="center" label="消费赠金" v-else>
                            <template slot-scope="scope">{{Number(scope.row.consume_bouns).toFixed(2)}}</template>
                          </el-table-column>
                        </el-table-column>
                    <el-table-column align="center" label="期间退款">
                        <el-table-column align="center" label="充值退款">
                            <el-table-column align="center" prop="recharge_refund_number" label="充值退款笔数"></el-table-column>
                            <el-table-column align="center" label="充值退款金额">
                                <template slot-scope="scope">{{Number(scope.row.recharge_refund_amount).toFixed(2)}}</template>
                            </el-table-column>
                            <el-table-column align="center" label="充值退款赠金">
                                <template slot-scope="scope">{{Number(scope.row.recharge_refund_bouns).toFixed(2)}}</template>
                            </el-table-column>
                            <el-table-column align="center" label="实退金额">
                                <template slot-scope="scope">{{Number(scope.row.recharge_refund_capital).toFixed(2)}}</template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column align="center" label="消费退款">
                            <el-table-column align="center" prop="consume_refund_number" label="消费退款笔数"></el-table-column>
                            <el-table-column align="center" label="消费退款金额">
                                <template slot-scope="scope">{{Number(scope.row.consume_refund_amount).toFixed(2)}}</template>
                            </el-table-column>
                            <el-table-column align="center" label="消费退款本金">
                                <template slot-scope="scope">{{Number(scope.row.consume_refund_capital).toFixed(2)}}</template>
                            </el-table-column>
                            <el-table-column align="center" label="消费退款赠金" v-if="is_bp && isOilRefundBounsListNotNull">
                                  <template>
                                    <el-table-column v-for="item in oilRefundBounsList" :key="item.index" align="center" :label="item.oil_name" :prop="item.prop" width="120">
                                      <template slot-scope="scope">{{Number(scope.row[item.prop]).toFixed(2)}}</template>
                                    </el-table-column>
                                  </template>
                                  <el-table-column align="center" label="合计" v-if="is_bp && isOilBounsListNotNull">
                                    <template slot-scope="scope">{{ Number(scope.row.consume_refund_oil_total).toFixed(2) }}</template>
                                  </el-table-column>
                                </el-table-column>
                                <el-table-column align="center" label="消费退款赠金" v-else>
                                   <template slot-scope="scope">{{Number(scope.row.consume_refund_bouns).toFixed(2)}}</template>
                                </el-table-column>
                        </el-table-column>
                    </el-table-column>
                    <el-table-column align="center" label="余额清零">
                        <el-table-column align="center" prop="clear_number" label="清零笔数"></el-table-column>
                        <el-table-column align="center" label="清零金额">
                            <template slot-scope="scope">{{Number(scope.row.clear_amount).toFixed(2)}}</template>
                        </el-table-column>
                        <el-table-column align="center" label="清零本金">
                            <template slot-scope="scope">{{Number(scope.row.clear_capital_amount).toFixed(2)}}</template>
                        </el-table-column>
                        <el-table-column align="center" label="清零赠金">
                            <template slot-scope="scope">{{Number(scope.row.clear_bouns_amount).toFixed(2)}}</template>
                        </el-table-column>
                    </el-table-column>
                </el-table>
            </div>
            <div class="table_des">
                <div class="table_des_text">
                    <p>注：</p>
                    <div>
                        <p>1、统计油站时间范围内产生的储值卡数据，含跨站消费、充值、退款数据。</p>
                        <p v-if="stationValue.length != stationOptions.length && isGroup" class="stations">2、取数油站：{{stationName}}</p>
                    </div>
                </div>
            </div>
            <div class="des_bottom">
                <div>制表人：{{orderMaker}}</div>
                <div>制表时间：{{orderMakingTime}}</div>
                <div>签字：</div>
            </div>
        </div>

        <!-- 下载中心提示 -->
        <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>
    </div>
</template>

<script>
import DownloadTips from '../DownloadTips.vue';
import {mapGetters} from 'vuex'
export default {
    name: 'CardStationReport',
    components:{
        DownloadTips
    },
    data () {
        return {
            isTotalReportForm: true,
            stationOptions:[],//油站数据
            stationValue:[],//选中油站
            dateValue:[],//时间

            DailyTableData: [],//表单数据
            isGroup:true,//是否是集团账号
            loading: true,
            start_time:"",//开始日期
            end_time:"",//结束日期

            orderMaker:"",//制单人
            orderMakingTime:"",//制单时间
            stationName:"",//所选油站名称

            params:{

            },//接口参数
            showDownloadTips:false,
            is_bp: false, //判断是否是bp集团
            isOilRefundBounsListNotNull: false, //判断消费退款赠金是否非空
            isOilBounsListNotNull: false, //判断消费赠金是否非空
            //消费明细油品
            oilBounsList: [],
            //消费退款明细油品
            oilRefundBounsList: [], // 消费赠金油品
        }
    },
    mounted(){
        let userInfo = localStorage.getItem("__userInfo__") || "";
        this.isTotalReportForm = JSON.parse(userInfo).group_id != 426;
        let _today = this.$moment();
        //默认为前一天的0点到24点
        let yesterday = _today.subtract(1, 'days').format('YYYY-MM-DD');
        this.dateValue.push(yesterday);
        this.dateValue.push(yesterday);

        if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
            return false;
        }
        this.getStations();
        if(this.getCurrentStation && this.getCurrentStation.merchant_type == 2){
            this.isGroup = true;
        }else{
            this.isGroup = false;
        }
    },
    computed:{
        ...mapGetters({
            "getCurrentStation":"getCurrentStation"
        })
    },
    methods: {

        //获取油站列表
        getStations(){
            this.stationValue = [];
            this.$axios.post('/Stations/getStations',{}).then((res)=>{
                if(res.status == 200){
                    this.stationOptions = res.data.data.station_info;
                    this.stationOptions.forEach((item)=>{
                        this.stationValue.push(item.stid);
                    })
                    this.createReport();
                }
            })
        },

        //获取选择的油站名称
        getCheckedStation(){
            this.stationName = "";
            let len = this.stationValue.length;
            this.stationValue.forEach((item,index)=>{
                this.stationOptions.forEach((subitem)=>{
                    if(subitem.stid == item){
                        if(index == len-1){
                            this.stationName += subitem.stname;

                        }else{
                            this.stationName += subitem.stname + "，";
                        }
                    }
                })
            })
        },

        //选中时间生成报表
        async createReport(){
            this.oilRefundBounsList = [];
            this.oilBounsList = [];
            this.isOilRefundBounsListNotNull = false;
            this.isOilBounsListNotNull = false;

            this.loading = true;
            if(this.stationValue.length == 0){
                this.$message({
                    message: "请选择油站",
                    type: 'error'
                });
                return;
            }

            this.params.type = 2;
            this.params.station_ids = this.stationValue;
            this.params.start_time = this.dateValue[0] + ' 00:00:00';
            this.params.end_time = this.dateValue[1] + ' 23:59:59';

            let res = await this.getFundSummary();
            this.loading = false;
            if(res.data.status == 200){

                this.DailyTableData = res.data.data;
                if(res.data.data[0]){
                  if(res.data.data[0].consume_bouns_oil_info){
                    this.oilBounsList = res.data.data[0].consume_bouns_oil_info
                  }
                  if(res.data.data[0].consume_refund_bouns_oil_info){
                    this.oilRefundBounsList = res.data.data[0].consume_refund_bouns_oil_info
                  }
                }

                //根据特定的账号显示消费赠金的分类
                const userInfo = JSON.parse(window.localStorage.getItem("__userInfo__"));
                //
                if(userInfo.new_group_id == 1374 || userInfo.new_group_id == 1 || userInfo.isPlatform == 1){
                  this.is_bp = true;
                  if(this.oilRefundBounsList.length > 0){
                    this.isOilRefundBounsListNotNull = true;
                  }
                  if(this.oilBounsList.length > 0){
                    this.isOilBounsListNotNull = true;
                  }
                } else {
                  this.is_bp = false;
                }

                this.start_time = this.dateValue[0];
                this.end_time = this.dateValue[1];
                let UserInfo = localStorage.getItem('__userInfo__');
                if(UserInfo && (UserInfo !== "" || UserInfo !== "undefined")){
                    this.orderMaker = JSON.parse(UserInfo).name;
                }
                this.orderMakingTime = this.$moment().format("YYYY-MM-DD");

            }else{
                this.$message({
                    message: res.data.info,
                    type: 'error'
                });
            }
        },

        //生成报表
        getFundSummary(){
            return new Promise((resolve, reject)=>{
                this.$axios.post('/CardReportForm/getFundSummary', this.params).then(function (res) {
                    resolve(res);
                }).catch((err)=>{
                    resolve(err);
                })
            })
        },

        //打印
        printContent(){
            let wpt = document.querySelector('#myTable');
            let newContent = wpt.innerHTML;
            let oldContent = document.body.innerHTML;
            document.body.innerHTML = newContent;
            document.getElementsByClassName("el-table__header")[0].style.width = "100%"
            document.getElementsByClassName("el-table__header")[0].style["table-layout"] = "auto"
            document.getElementsByClassName("el-table__body")[0].style.width = "100%"
            document.getElementsByClassName("el-table__body")[0].style["table-layout"] = "auto"
            window.print(); //打印方法
            history.go(0)
            document.body.innerHTML = oldContent;
            this.$print(this.$refs.print);
        },
        //下载数据
        cardChargeDownload(){
            this.$axios.post('/CardReportForm/exportFundSummary',this.params).then((res)=>{
                if(res.data.status == 200){
                    this.showDownloadTips = true;
                }else{
                    this.$message.error(res.data.info);
                }
            })
        },
    },
    watch: {
        getCurrentStation(newValue,oldValue){
            if(newValue.merchant_type != 0 && newValue.value != oldValue.value){
                this.getStations();
                if(this.getCurrentStation && this.getCurrentStation.merchant_type == 2){
                    this.isGroup = true;
                }else{
                    this.isGroup = false;
                }
            }
        },
    },
}
</script>

<style scoped>
    .report{
        position: relative;
        height: 100%;
        margin: 0px auto;
        padding: 20px 0;
    }
    .report .segmentation_view {
        background-color: #e4e4e4;
        position: fixed;
        left: 27%;
        top: 62px;
        width: 10px;
        height: 100%;
    }
    .report .content_header {
        display: flex;
        justify-content: space-between;
        margin: 0 0 20px 0 ;
    }
    .report .content_header1 {
        display: flex;
        margin-top: 10px;
        margin-left: 10px;
    }
    .report .content_header2 {
        display: flex;
        margin-left: 10px;
        margin-top: 10px;
    }
    .tableData{
        text-align: center;
    }
    .tableData .report_title {
        font-size: 24px;
        font-weight: bolder;
        margin-top: 20px;
    }
    .tableData .report_header {
        display: flex;
        margin:10px 20px;
    }
    .tableData .report_header div{
        min-width: 100px;
        text-align: left;
        margin-right: 40px;
    }
    .tableData .header_table {
        width: 100%;
        border-right:1px solid #EBEEF5;
        border-bottom:1px solid #EBEEF5;
        margin-top: 20px;
    }
    .tableData .header_table td {
        border-left:1px solid #EBEEF5;
        border-top:1px solid #EBEEF5;
    }
    .tableData .header_table_row {
        height: 40px;
    }
    .report .table_des {
        margin: 20px 0;
    }
    .report .table_des_text {
        font-size: 14px;
        display: flex;
        text-align: left;
    }
    .report .table_des_text p{
        margin: 0
    }
    .des_bottom {
        display: flex;
        justify-content: flex-end;
        font-size: 14px;
        font-weight: 500;
        margin-top: 10px;
    }
    .des_bottom div{
        padding-right: 100px;
    }
    .stations{
        font-size: 14px;
        text-align: left;
    }
    .stations span{
        color: #32AF50;
        margin-right: 10px;
    }
</style>
<style>
    .reportData td:first-child, .reportData th:first-child{
        padding-left: 0 !important;
    }
</style>
