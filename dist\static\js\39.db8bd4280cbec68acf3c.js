webpackJsonp([39],{TL33:function(t,e){},dAjm:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=a("Xxa5"),s=a.n(n),i=a("exGp"),r=a.n(i),o=a("Dd8w"),m=a.n(o),c=a("NYxO"),u=JSON.parse(localStorage.getItem("__userInfo__")),h={name:"index",data:function(){return{hide_time_type:1,userInfo:u,activeName:"today",selectDateInput:"",tabIndex:"0",menuList:[{name:"卡管理",url:"CardManager",path:"/CardManager",prev:[]},{name:"充值管理",url:"Refund",path:"/Refund",prev:["订单管理"]},{name:"充值日报表",url:"Report",path:"/Report",prev:["报表管理"]},{name:"站间清结算报表",url:"BetweenStationsReport",path:"/BetweenStationsReport",prev:["报表管理"]}],tabList:[{name:"领卡会员",num:"-"},{name:"充值金额",num:"-"},{name:"消费金额",num:"-"}],moneyList:[{name:"总余额（元）",num:"-"},{name:"本金（元）",num:"-"},{name:"赠金（元）",num:"-"}],startDate:"2020-02-19",endDate:"2020-03-21",dateTpye:1,fullscreenLoading:!0,showMoney:!0,showYZ:!0,showFJ:!0,echartsDomInit:{}}},mounted:function(){var t=localStorage.getItem("__userInfo__")||"";if(1545==JSON.parse(t).group_id&&this.tabList.splice(1,2),1321==JSON.parse(t).group_id&&(this.showYZ=!1),383==JSON.parse(t).group_id&&(this.showFJ=!1),this.echartsDomInit=this.$echarts.init(document.getElementById("myChart")),this.getGroupBaseInfo(),void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.handleClick({name:this.activeName}),this.getBalanceSummary()},computed:m()({editableTabsValue:{get:function(){return this.$store.getters.getEditableTabsValue},set:function(t){}},editableTabs:{get:function(){return this.$store.getters.getEditableTabs},set:function(t){this.$store.commit("SETEDITABLETABS",t)}},tabsIndex:{get:function(){return this.$store.getters.getTabIndex},set:function(t){this.$store.commit("SETTABINDEX",t)}},cacheArray:{get:function(){return this.$store.getters.getCacheArray},set:function(t){this.$store.commit("SETCACHEARRAY",t)}}},Object(c.c)({getCurrentStation:"getCurrentStation"})),methods:m()({},Object(c.b)({changeEditableTabsValue:"changeEditableTabsValue",changeEditableTabs:"changeEditableTabs",changeCacheArray:"changeCacheArray"}),{getGroupBaseInfo:function(){var t=this;return r()(s.a.mark(function e(){var a;return s.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("获取info"),e.prev=1,e.next=4,t.$axios.post("/Ostn/getGroupBaseInfo");case 4:if(200==(a=e.sent).data.status){e.next=7;break}return e.abrupt("return",t.$message.error(a.data.info));case 7:t.hide_time_type=a.data.data.hide_time_type||0,e.next=13;break;case 10:e.prev=10,e.t0=e.catch(1),t.$message.error("网络错误！");case 13:case"end":return e.stop()}},e,t,[[1,10]])}))()},toMenu:function(t){this.$emit("currentNav",{current:t.name,prev:t.prev}),this.$router.push({name:t.url});var e=t.path.substr(1);this.cacheArray.find(function(t){return t==e})||this.changeCacheArray(e);var a=++this.tabsIndex+"",n=this.editableTabs.find(function(e){return e.title==t.name});n?this.changeEditableTabsValue(n.name):(this.changeEditableTabs({title:t.name,name:a,router:t.path,current:t.name,prev:t.prev}),this.changeEditableTabsValue(a))},handleClick:function(t,e){this.selectDateInput="","today"==t.name?(this.startDate=this.$moment().format("YYYY-MM-DD"),this.endDate=this.$moment().subtract(-1,"days").format("YYYY-MM-DD"),this.dateTpye=0,this.getData(this.tabIndex)):"currentMonth"==t.name?(this.startDate=this.$moment().startOf("month").format("YYYY-MM-DD"),this.endDate=this.$moment().endOf("month").subtract(-1,"days").format("YYYY-MM-DD"),this.dateTpye=1,this.getData(this.tabIndex)):"prevMonth"==t.name?(this.startDate=this.$moment().month(this.$moment().month()-1).startOf("month").format("YYYY-MM-DD"),this.endDate=this.$moment().month(this.$moment().month()-1).endOf("month").subtract(-1,"days").format("YYYY-MM-DD"),this.dateTpye=1,this.getData(this.tabIndex)):t.name},changeDate:function(){this.startDate=this.$moment(this.selectDateInput[0]).format("YYYY-MM-DD"),this.endDate=this.$moment(this.selectDateInput[1]).subtract(-1,"days").format("YYYY-MM-DD"),this.selectDateInput[0].toString()==this.selectDateInput[1].toString()?this.dateTpye=0:this.dateTpye=1,this.getData(this.tabIndex)},getData:function(t){this.tabIndex=t,this.getCollarCardSumNum(this.startDate,this.endDate),this.getCollarCardLinear(this.startDate,this.endDate),this.getVipCardRchg(this.startDate,this.endDate)},drawLine:function(t){var e=this;if(0==this.dateTpye){var a=["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23"],n=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];t.forEach(function(t){n[Number(t.CollarTime)]=t.CollarNum})}else{a=[],n=[];for(var s in t){var i=t[s];a.push(s),n.push(i.CollarNum),name="领卡会员"}}this.$nextTick(function(){e.echartsDomInit.setOption({title:{text:""},tooltip:{},xAxis:{type:"category",data:a,name:"日期"},yAxis:{type:"value",name:"领卡数"},series:[{name:"",type:"line",data:n,smooth:!0}]})})},drawLine2:function(t,e){var a=this,n="";if(0==this.dateTpye){var s=["0","1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20","21","22","23"],i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];t.forEach(function(t){2==e?(i[Number(t.hour)]=t.consume_num,n="消费金额"):1==e&&(i[Number(t.hour)]=t.deposit_sum,n="储值金额")})}else{s=[],i=[];for(var r in t){var o=t[r];s.push(o.batch_date),2==e?(i.push(o.consume_num),n="消费金额"):1==e&&(i.push(o.deposit_sum),n="储值金额")}}this.$nextTick(function(){a.$nextTick(function(){a.echartsDomInit.setOption({title:{text:""},tooltip:{},xAxis:{type:"category",data:s,name:"日期"},yAxis:{type:"value",name:n},series:[{name:"",type:"line",data:i,smooth:!0}]})})})},getCollarCardSumNum:function(t,e){var a=this;a.$axios.post("/CardHome/getCollarCardSumNum",{start_time:a.$moment(t,"YYYY-MM-DD HH:mm:ss").valueOf()/1e3,end_time:a.$moment(e,"YYYY-MM-DD HH:mm:ss").valueOf()/1e3,mch_arr:this.userInfo.mch_arr}).then(function(t){200==t.data.status?a.tabList[0].num=t.data.data:a.$message({message:t.data.info,type:"error"})}).catch(function(t){})},getCollarCardLinear:function(t,e){var a=this;a.$axios.post("/CardHome/getCollarCardLinear",{start_time:a.$moment(t,"YYYY-MM-DD HH:mm:ss").valueOf()/1e3,end_time:a.$moment(e,"YYYY-MM-DD HH:mm:ss").valueOf()/1e3,query_type:a.dateTpye,mch_arr:this.userInfo.mch_arr}).then(function(t){200==t.data.status?0==a.tabIndex&&a.drawLine(t.data.data):a.$message({message:t.data.info,type:"error"})}).catch(function(t){})},getBalanceSummary:function(){var t=this;t.fullscreenLoading=!0,t.$axios.post("/CardHome/getBalanceSummary",{mch_arr:this.userInfo.mch_arr}).then(function(e){if(t.fullscreenLoading=!1,200==e.data.status){for(var a=JSON.parse(e.data.data),n=[],s=0;s<a.length;s++)"SKJ"==a[s].AmountType?n[2]=a[s].Balance:"BJ_KZ"==a[s].AmountType?n[1]=a[s].Balance:"BJ_MZ"==a[s].AmountType&&(n[0]=a[s].Balance);n[0]?(t.moneyList[0].num=(n[0]+n[1]+n[2]).toFixed(2),t.moneyList[1].num=(n[0]+n[1]).toFixed(2)):(t.moneyList[0].num=(n[1]+n[2]).toFixed(2),t.moneyList[1].num=n[1].toFixed(2)),t.moneyList[2].num=n[2].toFixed(2)}else t.$message({message:e.data.info,type:"error"})}).catch(function(t){})},getVipCardRchg:function(t,e){var a=this;a.$axios.post("/CardHome/getVipStatistics",{type:a.dateTpye,start_date:a.$moment(t,"YYYY-MM-DD HH:mm:ss").valueOf()/1e3,end_date:a.$moment(e,"YYYY-MM-DD HH:mm:ss").valueOf()/1e3}).then(function(t){200==t.data.status?(a.tabList[2].num=t.data.data.total_consume_num,a.tabList[1].num=t.data.data.total_deposit_sum,1==a.tabIndex?a.drawLine2(t.data.data.list,a.tabIndex):2==a.tabIndex&&a.drawLine2(t.data.data.list,a.tabIndex)):a.$message({message:t.data.info,type:"error"})}).catch(function(t){})},changeTimeDate:function(){this.activeName="",this.changeDate()}}),watch:{getCurrentStation:function(t,e){0!=t.merchant_type&&t.value!=e.value&&(this.handleClick({name:this.activeName}),this.getBalanceSummary())}}},d={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.fullscreenLoading,expression:"fullscreenLoading"}],staticClass:"index",attrs:{id:"index"}},[a("div",{staticClass:"content"},[0===t.userInfo.has_mch_id?a("div",{staticClass:"dataBox"},[a("ul",{directives:[{name:"show",rawName:"v-show",value:t.showMoney,expression:"showMoney"}],staticClass:"dataList"},t._l(t.moneyList,function(e,n){return a("li",{key:n},[a("span",{staticClass:"data-name"},[t._v(t._s(e.name))]),t._v(" "),a("span",{staticClass:"data-money"},[t._v(t._s(e.num))])])}),0)]):t._e(),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.showYZ,expression:"showYZ"}],staticStyle:{position:"relative"}},[a("el-tabs",{attrs:{type:"card"},on:{"tab-click":t.handleClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"今日",name:"today"}}),t._v(" "),1!=t.hide_time_type?a("el-tab-pane",{attrs:{label:"本月",name:"currentMonth"}}):t._e(),t._v(" "),0==t.hide_time_type||3==t.hide_time_type?a("el-tab-pane",{attrs:{label:"上月",name:"prevMonth"}}):t._e()],1),t._v(" "),a("el-date-picker",{directives:[{name:"show",rawName:"v-show",value:0==t.hide_time_type,expression:"hide_time_type == 0"}],staticClass:"indextime",attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:t.changeTimeDate},model:{value:t.selectDateInput,callback:function(e){t.selectDateInput=e},expression:"selectDateInput"}})],1),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.showYZ,expression:"showYZ"}],staticClass:"tab"},[a("ul",{staticClass:"tabList"},t._l(t.tabList,function(e,n){return a("li",{key:n,class:t.tabIndex==n?"on":"",on:{click:function(e){return t.getData(n)}}},[a("span",{staticClass:"select-name"},[t._v(t._s(e.name))]),t._v(" "),a("span",{staticClass:"select-money"},[t._v(t._s(e.num))])])}),0)]),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.showYZ,expression:"showYZ"}],staticClass:"chart",attrs:{id:"myChart"}}),t._v(" "),0===t.userInfo.has_mch_id&&t.showYZ&&t.showFJ?[a("p",{staticClass:"quick-way"},[t._v("快捷入口")]),t._v(" "),a("ul",{staticClass:"menu-list"},t._l(t.menuList,function(e,n){return a("li",{key:n,on:{click:function(a){return t.toMenu(e)}}},[t._v("\n          "+t._s(e.name)+"\n        ")])}),0)]:t._e()],2)])},staticRenderFns:[]};var l=a("VU/8")(h,d,!1,function(t){a("TL33")},"data-v-37266508",null);e.default=l.exports}});