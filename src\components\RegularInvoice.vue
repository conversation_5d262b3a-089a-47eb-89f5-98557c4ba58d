<template>
    <div class="regular"  v-loading="loading">
        <div class="header_kind">
            <el-radio-group v-model="selectStatus" @change="searchAction">
                <el-radio-button label="全部"></el-radio-button>
                <el-radio-button label="充值"></el-radio-button>
                <el-radio-button label="消费"></el-radio-button>
            </el-radio-group>
            <el-date-picker class="header_datePick"
                @change="searchAction"
                v-model="selectDate"
                type="datetimerange"
                :default-time="['00:00:00', '23:59:59']"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
            </el-date-picker>
            <span class="comany_label">车队名称</span>
            <select-plus
                class="comany_select"
                :checkAll="false"
                placeholder="请选择车队"
                v-model="selectedCompanies"
                v-loading="companyListLoading"
                :list="companyArray"
                multiple
                filterable
                clearable
                collapse-tags
                @input="debouncedSearchAction"
                :attr="{label:'CompanyName',value:'ID',getLabel:item => `ID ${item.ID} ${item.CompanyName}`}"/>
        </div>
        <p class="invoice_notice">显示个人卡充值订单、消费订单和车队充值订单。</p>
        <div class="header_search">
            <div class="header_search_left">
                <div class="search_title">查询客户</div>
                <div>
                    <el-radio-group v-model="radio">
                        <el-radio :label="1">手机号</el-radio>
                        <el-radio :label="2">卡号</el-radio>
                        <el-radio :label="3">卡面卡号</el-radio>
                    </el-radio-group>
                </div>
                <el-input v-model="inputValue" style="width:210px" :placeholder="radio=='2'?'请输入卡号':radio=='1'?'请输入手机号':'请输入卡面卡号'" clearable class="search_input"></el-input>
                <el-button type="primary" @click="searchAction">查询</el-button>
            </div>
            <div>
                <span class="invoice-btn" :class="{active:fpstate == 1}" @click="getDisabledInvoice" v-if="currentGroupId == bpGroupId">已开票退款订单</span>
                <el-button class="output_btn" type="primary" @click="outputAction" v-show="isTotalReportForm">下载数据</el-button>
            </div>
        </div>
        <div class="edit_box" v-if="fpstate===0">
            <el-button type="primary" @click="invoiceText='批量开票';multiEdit()">批量开票</el-button>
            <div class="edit_des">选中<span class="count">{{selectItems.length}}</span>条数据</div>
            <el-radio-group v-model="typeRadio" @change="searchAction">
                <el-radio :label="2">全部</el-radio>
                <el-radio :label="0">仅看未开票</el-radio>
                <el-radio :label="1">仅看已开票</el-radio>
                <el-radio :label="3">仅看部分开票</el-radio>
            </el-radio-group>
        </div>
        <div class="edit_box" v-else>
            <el-button size="mini" @click="getAllData">返回</el-button>
        </div>
        <!-- 表格 -->
        <div class="table_box">
            <el-table
                @selection-change="handleSelectionChange"
                :data="tableData"
                :cell-style="transactionCellstyle"
                :header-cell-style="headerStyle"
                style="width: 100%">
                <el-table-column
                    fixed="left"
                    type="selection"
                    width="55">
                </el-table-column>
                <el-table-column
                    prop="StationName"
                    label="所属油站"
                    width="160">
                </el-table-column>
                <el-table-column
                    prop="TradeID"
                    label="订单号"
                    width="200">
                </el-table-column>
                <el-table-column
                    prop="CardNO"
                    label="卡号"
                    width="200">
                </el-table-column>
                <el-table-column
                    prop="Phone"
                    label="手机号"
                    width="120"
                    :formatter="formatterCellval">
                </el-table-column>
                <el-table-column
                    prop="CompanyName"
                    label="车队名称"
                    width="160">
                </el-table-column>
                <el-table-column
                    prop="CardNumber"
                    label="卡面卡号"
                    width="100"
                    :formatter="formatterCellval">
                </el-table-column>
                <el-table-column
                    prop="address"
                    label="操作类型">
                    <div slot-scope="scope">
                        <span>{{scope.row.Type == "CZ" ? "充值":"消费"}}</span>
                    </div>
                </el-table-column>
                <el-table-column
                    prop="bp_has_invoice_text"
                    label="是否开票">
                </el-table-column>
                <el-table-column
                    prop="open_time"
                    label="开票时间"
                    width="110"
                    :formatter="formatterCellval">
                </el-table-column>
                <el-table-column
                    prop="pay_money"
                    width="120"
                    label="实付金额（元）">
                </el-table-column>
                <el-table-column
                    prop="SFBJ"
                    width="120"
                    label="实付本金（元）">
                </el-table-column>
                <el-table-column
                    prop="SKJ"
                    width="120"
                    label="实付赠金（元）">
                </el-table-column>
                <el-table-column
                    prop="TradeTime"
                    label="交易时间"
                    width="160">
                </el-table-column>
                <el-table-column
                    prop="bp_invoice_summ"
                    label="备注"
                    width="350"
                    :formatter="formatterCellval">
                </el-table-column>
                <el-table-column
                    fixed="right"
                    :width="selectStatus=='充值'?120:80"
                    label="操作">
                    <template slot-scope="scope">
                        <el-button v-if="selectStatus != '充值'"  @click.native.prevent="invoiceText='开票';editRow(scope.row)"
                                   type="text" size="small"
                        >开票
                        </el-button>
                        <template v-else>
                          <el-button @click.native.prevent="invoiceRow(scope.row)" type="text" size="small">开票
                          </el-button>
                          <el-button @click.native.prevent="infoRow(scope.row)" type="text" size="small">查看</el-button>
                        </template>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 页码 -->
        <div class="page_content">
            <el-pagination class="page_left"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-size="pageSize"
                layout="prev, pager, next"
                :total="totalCount">
            </el-pagination>
            <el-pagination class="page_right"
                @size-change="handleSizeChange"
                :page-sizes="[10, 15, 20, 30]"
                :page-size="pageSize"
                layout="total, sizes"
                :total="totalCount">
            </el-pagination>
        </div>
        <!-- 编辑 -->
        <el-dialog
            :title="invoiceText"
            :visible.sync="editDialogVisible"
            width="500px"
            :close-on-click-modal="false">
            <div>
                <div class="edit_count" v-if="isMulti">已选中<span class="select_count">{{selectItems.length}}</span>笔订单</div>
                <div class="edit_item">
                    <div class="edit_title">是否开票</div>
                    <div>
                        <el-radio-group v-model="radio1" @change="invoiceStatusChange" :disabled="fpstate==1">
                            <el-radio :label="0">未开票</el-radio>
                            <el-radio :label="1">已开票</el-radio>
                        </el-radio-group>
                    </div>
                </div>
                <div class="edit_item">
                    <div class="edit_title">
                        <img class="mark_icon" v-if="radio1 == 1" src="../assets/images/bitian.png">
                        开票时间
                    </div>
                    <div>
                        <el-date-picker
                            :disabled="fpstate==1||dateDisable"
                            v-model="edit_date"
                            :picker-options="pickerOptions"
                            type="date"
                            placeholder="请选择开票时间">
                        </el-date-picker>
                    </div>
                </div>
                <div class="edit_item edit_input">
                    <div class="edit_title">备注</div>
                    <div>
                        <el-input
                            :disabled="fpstate==1"
                            class="textInput"
                            type="textarea"
                            :rows="4"
                            maxlength="180"
                            show-word-limit
                            placeholder="请输入内容"
                            v-model="textarea">
                        </el-input>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" type="primary" @click="editConfirm">确 定</el-button>
                <el-button size="mini" @click="editDialogVisible = false">取 消</el-button>
            </span>
        </el-dialog>
        <!-- 修改确认 -->
        <el-dialog
            title="提示"
            :visible.sync="confirmDialogVisible"
            width="350px"
            :close-on-click-modal="false"
            center>
            <div class="confirmTips">请确认是否将"已开票"修改为"未开票？"</div>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" type="primary" @click="modifyConfirm(1)">确 定</el-button>
                <el-button size="mini" @click="modifyConfirm(2)">取 消</el-button>
            </span>
        </el-dialog>
        <!-- 下载确认 -->
        <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>

        <!--  开票  -->
        <el-dialog title="开票" :visible.sync="invoiceDialogVisible" @close="closeInvoice" width="500px" modal-append-to-body :close-on-click-modal="false">
          <el-form :model="invoiceRuleform" :rules="rules" ref="invoiceRuleform" label-width="120px">
            <el-form-item label="订单号:" prop="TradeID">
              <span>{{ invoiceRuleform.TradeID }}</span>
            </el-form-item>
            <el-form-item label="当前可开票金额:" prop="totalAmt">
              <span>{{ invoiceRuleform.totalAmt }}</span>
            </el-form-item>
            <el-form-item label="已开票金额:">
              <span>{{ selectRow.UninvoicedMoney }}</span>
            </el-form-item>
            <el-form-item label="开票金额" prop="amt">
              <el-input v-model="invoiceRuleform.amt" placeholder="请输入开票金额" style="width: 220px"></el-input>
            </el-form-item>
            <el-form-item label="开票标记" prop="sign" required>
              <el-radio v-model="invoiceRuleform.sign" label="1">已开票</el-radio>
              <el-radio v-model="invoiceRuleform.sign" label="0" :disabled="selectRow.UninvoicedMoney == 0">未开票</el-radio>
            </el-form-item>
            <el-form-item label="开票时间" prop="time">
              <el-date-picker
                v-model="invoiceRuleform.time"
                :picker-options="pickerOptions"
                default-time="23:59:59"
                type="datetime"
                placeholder="请选择标记时间">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input v-model="invoiceRuleform.remark" type="textarea" placeholder="请输入内容"
                        show-word-limit maxlength="110"
                        :rows="2" style="width: 220px">
              </el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button size="mini" type="primary" :disabled="disabledInvoice" @click="sureInvoice">确 定</el-button>
            <el-button size="mini" @click="invoiceDialogVisible = false">取 消</el-button>
          </span>
        </el-dialog>

      <!-- 查看 -->
      <el-dialog title="历史记录查看" :visible.sync="infoDialogVisible" width="1000px" modal-append-to-body :close-on-click-modal="false">
        <div v-loading="infoLoading">
          <div class="infoOut">
            <div class="infoInner">实付金额：￥{{ selectRow.pay_money }}</div>
            <div class="infoInner">卡号：{{ selectRow.CardNO }}</div>
          </div>
          <div class="infoOut">
            <div class="infoInner">已开票金额：￥{{ selectRow.UninvoicedMoney }}</div>
            <div class="infoInner">剩余可开票金额：￥{{ invoiceRuleform.totalAmt }}</div>
          </div>
          <el-table :data="infoData" border style="width: 100%"
                    :cell-style="{ textAlign: 'center' }"
                    :header-cell-style="{ textAlign: 'center' }">
            <el-table-column type="index" label="序号" width="50">
              <template slot-scope="scope">
                <span>{{ (infoPage-1)*infoSize + scope.$index+1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="FPTime" label="开票时间"></el-table-column>
            <el-table-column prop="FPTag" label="开票标记">
              <template slot-scope="scope">
                <span>{{ scope.row.FPTag == 1 ? "已开票" : "未开票" }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="FPMoney" label="开票金额"></el-table-column>
            <el-table-column prop="FPRemainderMoney" label="剩余开票金额"></el-table-column>
            <el-table-column prop="Remark" label="备注" :formatter="formatterCellval"></el-table-column>
          </el-table>
        </div>
        <div style="display: flex;justify-content: space-between;margin-top: 20px;">
          <el-pagination
            @size-change="infoSizeChange"
            @current-change="infoCurrentChange"
            :current-page="infoPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="infoSize"
            layout="prev, pager, next"
            :total="TotalQty">
          </el-pagination>
          <el-pagination
            @size-change="infoSizeChange"
            @current-change="infoCurrentChange"
            :current-page="infoPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="infoSize"
            layout="total, sizes"
            :total="TotalQty">
          </el-pagination>
        </div>
      </el-dialog>
    </div>
</template>

<script>
import DownloadTips from './DownloadTips.vue';
import SelectPlus from './SelectPlus/SelectPlus.vue';
import {mapGetters, mapActions, mapState} from 'vuex';
import { debounce } from 'lodash';

export default {
    name:"RegularInvoice",
    components:{
        DownloadTips,
        SelectPlus
    },
    data() {
        let validateMoney = (rule, value, callback) => {
          let regex = /^\d+(\.\d{1,2})?$/
          if (regex.test(value) && value != 0) {
            callback();
          }else{
            callback(new Error('请输入正确的开票金额，最多保留两位小数'));
          }
        }
        return {
            selectRow:{},
            invoiceText: '开票',
            invoiceRuleform: {
              TradeID: '',
              totalAmt: '',
              amt: '',
              sign: '1',
              time: '',
              remark: ''
            },
            rules: {
              amt: [
                { required: true, validator: validateMoney, trigger: 'blur' },
              ],
              time: [
                { required: true, message: '请选择标记时间', trigger: ['blur', 'change'] },
              ]
            },
            disabledInvoice: false,
            infoData: [],
            infoLoading: false,
            infoPage: 1,
            infoSize: 10,
            TotalQty: 0,
            isTotalReportForm: true,
            loading: false,
            selectStatus: "全部",
            selectDate: [],
            radio: 1,
            pageSize: 10,
            currentPage: 1,
            totalCount: 0,
            inputValue: "",
            tableData: [],

            radio1: 0,
            edit_date: null,
            textarea: "",
            dateDisable: true,
            isMulti: false,

            selectItem: null,
            selectItems: [],

            confirmDialogVisible: false,
            showDownloadTips: false,
            editDialogVisible: false,
            invoiceDialogVisible: false,
            infoDialogVisible: false,
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            typeRadio:2,//类型选项
            fpstate:0,//0正常发票，1已开票已退款发票
            bpGroupId:1,//
            currentGroupId:0,//当前集团id
            selectedCompanies: [], // 选中的车队ID数组
        }
    },
    computed:{
        ...mapState(['companyListLoading']),
        ...mapGetters({
            "getCurrentStation":"getCurrentStation",
            "companyArray":"companyArray"
        })
    },
    mounted() {
        let userInfo = localStorage.getItem("__userInfo__") || "";
        this.isTotalReportForm = JSON.parse(userInfo).group_id != 426;
        //默认显示前一个月数据
        const startDate = this.$moment(new Date()).subtract(1,'months').format('YYYY-MM-DD HH:mm:ss');
        const endDate = this.$moment(new Date());
        this.selectDate.push(this.$moment(startDate).format('YYYY-MM-DD')+ ' 00:00:00');
        this.selectDate.push(this.$moment(endDate).format('YYYY-MM-DD')+ ' 23:59:59');

        if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
            return false;
        }
        //判断环境，生产bp的id1374，测试892
        let url = window.location.href;
        if(url.indexOf('card-admin.zhihuiyouzhan.com') != -1){
            this.bpGroupId = 1374;
        }else{
            this.bpGroupId = 1;
        }
        this.getInvoiceList({actionType:1});
        this.getSimpleCompanyList(); // 获取车队列表
        this.debouncedSearchAction = debounce(this.searchAction, 300);
    },
    methods: {
        ...mapActions(['getSimpleCompanyList']),
        // 开票到未开票确认
        modifyConfirm: function (val) {
            if (val == 1) {
                this.editConfirmAction()
            }
            this.confirmDialogVisible = false
            this.editDialogVisible = false
        },
        // 开票状态改变
        invoiceStatusChange:function (val) {
            // 未开票
            if (val == 0) {
                this.dateDisable = true
            }else {
                this.dateDisable = false
            }
        },
        invoiceRow(row) {
          console.log('开票',row)
          // if(
          //   row.RKPType == 2 ||
          //   (row.RKPType == 1 && row.Type != 'XF') ||
          //   row.RKPType == 0 && row.Type != 'CZ' && row.Type != 'JH_CZ'
          // ){
          //   return this.$message.error(`该笔订单开票规则为${row.TypeText}开票，不可开票`)
          // }
          this.selectRow = row
          this.invoiceRuleform.TradeID = row.TradeID
          this.invoiceRuleform.totalAmt = ''
          this.invoiceDialogVisible = true
          this.invoiceRuleform.time = this.$moment().format('YYYY-MM-DD HH:mm:ss')
          this.getInfoList()
        },
        sureInvoice() {
          this.$refs.invoiceRuleform.validate(async valid => {
            if(!valid) return
            try {
              this.disabledInvoice = true
              let form = {}
              form.TradeID = this.invoiceRuleform.TradeID
              form.Money = this.invoiceRuleform.amt
              form.bp_invoice_open_time = this.$moment(this.invoiceRuleform.time).unix()
              form.bp_invoice_summ = this.invoiceRuleform.remark
              form.is_bp_has_invoice = this.invoiceRuleform.sign
              form.codes = {}
              let TradeID = this.selectRow.TradeID
              form.codes[TradeID] = JSON.stringify({stid:this.selectRow.StationNO,pay_time:this.selectRow.TradeTime})
              const res = await this.$axios.post('/bp/repeatInvoices', form)
              if(res.data.status != 200) return this.$message.error(res.data.info)
              this.$message.success('开票标记成功')
              this.invoiceDialogVisible = false
              this.searchAction()
            }catch (e) {
              this.$message.error('网络错误')
            }finally {
              this.disabledInvoice = false
            }
          })
        },
        closeInvoice() {
          this.invoiceRuleform = {
            TradeID: '',
            totalAmt: '',
            amt: '',
            sign: '1',
            time: '',
            remark: ''
          }
          this.$refs.invoiceRuleform.resetFields()
        },
        infoRow(row) {
          console.log('查看',row)
          this.infoDialogVisible = true
          this.infoPage = 1
          this.infoSize = 10
          this.selectRow = row
          this.getInfoList()
        },
        async getInfoList() {
          try {
            this.infoLoading = true
            const res = await this.$axios.post('/bp/getInvoiceHistoryList', {
              TradeID: this.selectRow.TradeID,
              page: this.infoPage,
              pagesize: this.infoSize,
            })
            if(res.data.status != 200) return this.$message.error(res.data.info)
            this.invoiceRuleform.totalAmt = res.data.data.InvoiceMoney
            this.selectRow.UninvoicedMoney = res.data.data.UninvoicedMoney
            this.infoData = res.data.data.AccountInvoiceList
            this.infoData.forEach(v => {
              if(v.FPTime){
                v.FPTime = this.$moment(v.FPTime).format('YYYY-MM-DD HH:mm:ss')
              }
            })
            this.TotalQty = res.data.data.TotalQty
          }catch (e) {
            this.$message.error('网络错误')
          }finally {
            this.infoLoading = false
          }
        },
        editRow:function (item) {
            // if(
            //   item.RKPType == 2 ||
            //   (item.RKPType == 1 && item.Type != 'XF') ||
            //   item.RKPType == 0 && item.Type != 'CZ' && item.Type != 'JH_CZ'
            // ){
            //   return this.$message.error(`该笔订单开票规则为${item.TypeText}开票，不可开票`)
            // }

            if (item.Type == 'CZ') {
                this.invoiceRow(item)
                return
            }

            this.selectItem = item
            this.isMulti = false
            this.editDialogVisible = true

            if (item.is_bp_has_invoice) {
                this.dateDisable = false
                this.radio1 = 1
            }else {
                this.dateDisable = true
                this.radio1 = 0
            }
            if (item.bp_invoice_open_time) {
                this.edit_date = item.bp_invoice_open_time.substring(0, 10)
            }else{
                this.edit_date =  null
            }
            this.textarea = item.bp_invoice_summ
        },
        searchAction:function () {
            this.currentPage = 1;
            this.getInvoiceList({actionType:3})
        },
        // 修改确认
        editConfirm: function () {
            // 选择未开票
            if (this.radio1 == 0) {
                // 是已开票的
                if (this.isMulti) {
                    if (this.selectItems[0].is_bp_has_invoice) {
                        this.confirmDialogVisible = true
                    }else {
                        this.editConfirmAction()
                    }
                }else {
                    if (this.selectItem.is_bp_has_invoice) {
                        this.confirmDialogVisible = true
                    }else {
                        this.editConfirmAction()
                    }
                }
            // 选择已开票
            }else {
                this.editConfirmAction()
            }
        },
        editConfirmAction: function () {

            var that = this


            if (!this.edit_date && !this.dateDisable) {
                that.$message.error('请选择开票时间');
                return;
            }

            var codes = {}
            if (this.isMulti) {
                for (var i = 0; i < this.selectItems.length; i++ ) {
                    var item = this.selectItems[i]
                    let TradeID = item.TradeID
                    codes[TradeID] = JSON.stringify({stid:item.StationNO,pay_time:item.TradeTime})
                }
            }else {
                let TradeID = this.selectItem.TradeID
                codes[TradeID] = JSON.stringify({stid:this.selectItem.StationNO,pay_time:this.selectItem.TradeTime})
            }

            var openDate = that.edit_date ? that.$moment(that.edit_date).valueOf() / 1000 : ''

            if (this.radio1 == 0) {
                openDate = ""
            }

            var exts = {
                is_bp_has_invoice: this.radio1,
                bp_invoice_open_time: openDate,
                bp_invoice_summ: this.textarea
            }

            that.loading = true
            this.editDialogVisible = false
            that.$axios.post('/bp/oinvoicesAdd', {
                codes: codes,
                exts: exts
            })
            .then(function (res) {

                if(res.data.status == 200){
                    setTimeout(function (){
                        that.loading = false
                        that.getInvoiceList({actionType:4});
                    },3000);
                }else{
                    that.loading = false
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
                that.loading = false
                that.$message.error('修改出错');
            });
        },
        //获取发票数据列表
        getInvoiceList:function (arg={}) {
            var that = this

            var type = '';
            if (that.selectStatus == "充值") {
                type = "CZ"
            }else if (that.selectStatus == "消费") {
                type = "XF"
            }

            var start_time = that.selectDate ? that.$moment(that.selectDate[0]).format('YYYY-MM-DD HH:mm:ss') : ''
            var end_time = that.selectDate ? that.$moment(that.selectDate[1]).format('YYYY-MM-DD HH:mm:ss') : ''

            that.loading = true
            that.$axios.post('/bp/oinvoices', {
                fptype: 0,
                qtype: that.radio,
                cztype: type,
                query: that.inputValue,
                page: that.currentPage,
                start: start_time,
                end: end_time,
                pagesize: that.pageSize,
                fpstatus: that.typeRadio,
                fpstate: that.fpstate,
                CompanyIds: that.selectedCompanies, // 添加车队ID参数
                ...arg
            })
            .then(function (res) {
                that.loading = false;
                if(res.data.status == 200){
                    var list = res.data.data.dt

                    for (var i = 0; i < list.length; i++) {
                        var item = list[i]

                        if (item.bp_invoice_open_time) {
                            item.open_time = item.bp_invoice_open_time.substring(0, 10)
                        }else {
                            item.open_time = ''
                        }

                    }
                    that.totalCount = res.data.data.TotalQty;
                    that.currentGroupId = res.data.data.group_id;
                    that.tableData = list;
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
                that.loading = false;
                that.$message.error('获取列表出错');
            });
        },
        outputAction: function () {
            var that = this

            var type = '';
            if (that.selectStatus == "充值") {
                type = "CZ"
            }else if (that.selectStatus == "消费") {
                type = "XF"
            }

            var start_time = that.selectDate ? that.$moment(that.selectDate[0]).format('YYYY-MM-DD HH:mm:ss') : ''
            var end_time = that.selectDate ? that.$moment(that.selectDate[1]).format('YYYY-MM-DD HH:mm:ss') : ''

            that.loading = true;
            that.$axios.get('/bp/export', {
                params : {
                    fptype: 0,
                    qtype: that.radio,
                    cztype: type,
                    query: that.inputValue,
                    page: that.currentPage,
                    start: start_time,
                    end: end_time,
                    pagesize: that.pageSize,
                    name: "普通发票",
                    fpstatus: that.typeRadio,
                    fpstate: that.fpstate,
                    CompanyIds: that.selectedCompanies, // 添加车队ID参数
                }
            })
            .then(function (res) {
                that.loading = false;
                if(res.data.status == 200){
                    that.showDownloadTips = true

                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
                that.$message({
                    message: "导出出错",
                    type: 'error'
                });
                that.loading = false;
            });
        },

        handleCurrentChange:function (val) {
            this.currentPage = val
            this.getInvoiceList({actionType:5})
        },
        handleSizeChange:function (val) {
            this.pageSize = val
            this.getInvoiceList({actionType:6})
        },
        infoCurrentChange(val) {
          this.infoPage = val
          this.getInfoList()
        },
        infoSizeChange(val) {
          this.infoSize = val
          this.getInfoList()
        },
        multiEdit: function() {
            var that = this
            if (this.selectItems.length == 0) {
                that.$message.error('请至少选择一条记录');
                return
            }

            if (this.selectItems.length == 1) {
                this.editRow(this.selectItems[0])
                return
            }

            var openStatus = this.selectItems[0].is_bp_has_invoice;
            var notSame = false
            for (var i = 1; i < this.selectItems.length; i++ ) {
                var item = this.selectItems[i]

                if (item.is_bp_has_invoice != openStatus) {
                    notSame = true
                    break;
                }
            }

            if (notSame) {
                that.$message.error('选中订单开票状态不一致');
                return
            }

            this.isMulti = true
            this.selectItem = null
            this.editDialogVisible = true


            if (openStatus) {
                this.radio1 = 1
                this.dateDisable = false
            }else {
                this.radio1 = 0
                this.dateDisable = true
            }
        },
        handleSelectionChange: function (val) {
            this.selectItems = val
        },
        checkSelectSet(row, index) {
          if(this.selectStatus === '充值' && row.is_bp_has_invoice != 0){
            return false
          }
          // else if(
          //   row.RKPType == 2 ||
          //   (row.RKPType == 1 && row.Type != 'XF') ||
          //   row.RKPType == 0 && row.Type != 'CZ' && row.Type != 'JH_CZ'
          // ){
          //   return false
          // }
          else{
            return true
          }
        },

        // table style
        transactionCellstyle({row, column, rowIndex, columnIndex}){

            if (columnIndex != 11) {
                return "text-align:center";
            }

        },
        headerStyle({row, column, rowIndex, columnIndex}){
            if (columnIndex != 11) {
                return "text-align:center";
            }
        },
        //查询已开票已退款发票
        getDisabledInvoice(){
            if(this.fpstate == 1){
                this.fpstate = 0;
            }else{
                this.fpstate = 1;
            }
            this.typeRadio = 2;
            this.currentPage = 1;
            this.getInvoiceList({actionType:7});
        },
        //返回全部数据
        getAllData(){
            this.fpstate = 0;
            this.currentPage = 1;
            this.getInvoiceList({actionType:8});
        }
    },
    watch: {
        getCurrentStation(newValue,oldValue){
            if(newValue.merchant_type != 0 && newValue.value != oldValue.value){
                this.getInvoiceList({actionType:2});
            }
        },
    }
}
</script>

<style scoped>
.comany_label{
    margin:0 10px;
}

.regular {
    overflow: hidden;
    padding: 20px 0;
}

.header_datePick {
    margin-left: 30px;
}

.header_search {
    margin-top: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header_search_left {
    display: flex;
    align-items: center;
}

.search_input {
    margin-left: 20px;
    margin-right: 5px;
}

.search_title {
    margin-right: 15px;
}

/* 批量编辑 */
.edit_box {
    margin-top: 20px;
    display: flex;
    align-items: center;
}

.edit_des {
    margin-left: 20px;
    margin-right: 66px;
}

.count {
    color: #32af50;
}

.table_box {
    margin-top: 20px;
}

/* 页码 */
.page_content {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
}

/* 编辑 */
.edit_item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.edit_title {
    width: 80px;
    text-align: right;
    margin-right: 20px;
}

.textInput {
    width: 300px;
}

.edit_input {
    align-items: flex-start;
}

.edit_count {
    margin-bottom: 20px;
}

.select_count {
    color: #32af50;
}

.date_mark {
    background: red;
    display: inline-block;
    width: 10px;
    height: 10px;
    margin-right: 5px;
}

.confirmTips {
    text-align: center;
}

.mark_icon {
    display: inline-block;
    width: 8px;
    height: 8px;
}
.invoice-btn{
    display: inline-block;
    padding: 9.5px 20px;
    margin-right: 10px;
    font-size: 14px;
    border: 1px solid #DCDFE6;
    color: #606266;
    cursor: pointer;
    border-radius: 4px;
}
.invoice-btn.active,.invoice-btn:hover{
    color: #32AF50;
    border-color: #c2e7cb;
    background-color: #ebf7ee;
}
.infoOut{
  display: flex;
  margin-bottom: 20px;
}
.infoInner{
  width: 300px;
}

.header_company {
    margin-left: 30px;
    width: 250px;
}

.comany_select{
    width:300px;
}

.invoice_notice{
    font-size: 14px;
    color: #666;
    margin-bottom:0;
    margin-top:10px;
}
</style>

