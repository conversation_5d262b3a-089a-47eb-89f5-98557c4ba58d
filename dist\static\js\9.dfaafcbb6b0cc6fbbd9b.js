webpackJsonp([9],{"4jyv":function(e,t,a){"use strict";a.d(t,"a",function(){return n});var n={DATE:1,CLASS:4};t.b={name:"TableQueryCondition",data:function(){return{TYPE_VALUE:n,query:{stid:"",customer_type:"",invoice_type:"",type:1,company_id:[],dateValue:[]}}}}},EQFf:function(e,t,a){"use strict";var n=a("mvHQ"),i=a.n(n),r=a("Dd8w"),o=a.n(r),l=a("iJJg"),s=a("V2BW"),u=a("4jyv"),c=a("M4fF"),p=a("NYxO"),d=a("fTGR"),y=a("XAqB"),m={name:"InvoiceQuery",mixins:[l.c,u.b],components:{BanciDateTime:d.a,SelectPlus:s.a},computed:o()({},Object(p.d)(["stationListLoading","stationList","currentStation","companyListLoading"]),Object(p.c)(["companyArray"]),{EndTime:function(){return y.a}}),props:{dateType:{type:String,default:"datetimerange"},disabledDate:{type:Function,default:function(){return!1}},showInvoiceType:{type:Boolean,default:!1},company_id:{type:Array,default:function(){return[]}},invoice_type:{type:[String,Number],default:""},customer_type:{type:[String,Number],default:""},date_value:{type:Array,default:function(){return[]}}},data:function(){return{classDate:[],typeOptions:[{value:u.a.DATE,label:"按时间查询"},{value:u.a.CLASS,label:"按班结查询"}],invoiceValue:"",invoiceOptions:[{value:0,label:"充值开票"},{value:1,label:"消费开票"},{value:2,label:"不开票"}]}},created:function(){this.getCustomerType(),this.getSimpleCompanyList(),this.getStationList(),Object(c.isEmpty)(this.date_value)},watch:{date_value:{handler:function(e){Object(c.isEmpty)(e)||Object(c.isEqual)(e,this.query.dateValue)||(this.query.dateValue=Object(c.cloneDeep)(e))},immediate:!0,deep:!0},currentStation:{deep:!0,immediate:!0,handler:function(e,t){if(console.log("=>(InvoiceQuery.vue:116) val",JSON.parse(i()(e||{})),JSON.parse(i()(t||{}))),this.query.company_id=[],this.query.stid="",this.$emit("update:company_id",[]),this.$emit("update:stid",""),e&&"2"===String(e.merchant_type))return this.$store.dispatch("getStationList"),void this.$store.dispatch("getSimpleCompanyList");this.query.stid=e.merchant_id,this.$refs.banciRef&&this.query.stid&&this.$refs.banciRef.getBanci(e.merchant_id)}}},methods:o()({},Object(p.b)(["getStationList","getSimpleCompanyList"]),{updateCompanySelected:function(e){this.$emit("update:company_id",e);var t=this.companyArray.filter(function(t){return e.includes(t.ID)});console.log("=>(InvoiceQuery.vue:140) currentCompanies",t),this.$emit("update:currentCompanies",t)},changeType:function(){this.$emit("update:type",this.query.type),this.query.dateValue=[],this.$emit("update:date_value",[this.$moment().subtract(1,"days").format("YYYY-MM-DD 00:00:00"),this.$moment().format("YYYY-MM-DD 23:59:59")]),this.query.type!==u.a.DATE&&(this.$emit("update:date_value",[]),this.$refs.banciRef&&this.$refs.banciRef.getBanci(this.query.stid))}})},v={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-radio-group",{on:{input:e.changeType},model:{value:e.query.type,callback:function(t){e.$set(e.query,"type",t)},expression:"query.type"}},e._l(e.typeOptions,function(t){return a("el-radio-button",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label)+"\n    ")])}),1),e._v(" "),a("div",{staticClass:"mt-20px"},[a("el-form",{attrs:{inline:""}},[a("el-form-item",{attrs:{label:"车队名称/ID"}},[a("select-plus",{directives:[{name:"loading",rawName:"v-loading",value:e.companyListLoading,expression:"companyListLoading"}],key:"company_id",attrs:{placeholder:"请选择车队",list:e.companyArray,multiple:"",filterable:"",clearable:"","collapse-tags":"",attr:{label:"CompanyName",value:"ID",getLabel:function(e){return"ID "+e.ID+" "+e.CompanyName}}},on:{input:e.updateCompanySelected},model:{value:e.query.company_id,callback:function(t){e.$set(e.query,"company_id",t)},expression:"query.company_id"}})],1),e._v(" "),e.query.type===e.TYPE_VALUE.CLASS?[e.currentStation&&"2"===String(e.currentStation.merchant_type)?a("el-form-item",{attrs:{label:"油站名称"}},[a("select-plus",{directives:[{name:"loading",rawName:"v-loading",value:e.stationListLoading,expression:"stationListLoading"}],key:"stid",attrs:{"check-all":!1,placeholder:"请选择油站",list:e.stationList,filterable:"",clearable:"","collapse-tags":"",attr:{label:"stname",value:"stid"}},on:{input:function(t){e.$refs.banciRef.getBanci(e.query.stid),e.$emit("update:stid",t)}},model:{value:e.query.stid,callback:function(t){e.$set(e.query,"stid",t)},expression:"query.stid"}})],1):e._e(),e._v(" "),a("el-form-item",[a("banci-date-time",{ref:"banciRef",attrs:{"show-loading":!0,"date-value":e.classDate,stationValue:e.query.stid,"picker-options":e.EndTime},on:{classTimeChange:function(t){return e.$emit("update:date_value",t)},changeDate:function(t){e.query.dateValue=t}}})],1)]:e._e(),e._v(" "),e.showInvoiceType?a("el-form-item",{attrs:{label:"开票方式"}},[a("el-select",{staticClass:"w-250px mr-20px mb-5px",attrs:{clearable:"",placeholder:"请选择开票方式"},on:{change:function(t){return e.$emit("update:invoice_type",e.query.invoice_type)}},model:{value:e.query.invoice_type,callback:function(t){e.$set(e.query,"invoice_type",t)},expression:"query.invoice_type"}},e._l(e.invoiceOptions,function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})}),1)],1):e._e(),e._v(" "),a("el-form-item",{attrs:{label:"客户类型"}},[a("select-plus",{directives:[{name:"loading",rawName:"v-loading",value:e.customerTypeLoading,expression:"customerTypeLoading"}],attrs:{"check-all":!1,placeholder:"请选择客户类型",list:e.customerTypeOptions,clearable:"",attr:{label:"CompanyTypeName",value:"CompanyTypeID"}},on:{input:function(t){return e.$emit("update:customer_type",e.query.customer_type)}},model:{value:e.query.customer_type,callback:function(t){e.$set(e.query,"customer_type",t)},expression:"query.customer_type"}})],1),e._v(" "),e.query.type===e.TYPE_VALUE.DATE?a("el-form-item",{attrs:{label:"时间范围"}},[a("el-date-picker",{staticClass:"mb-5px",attrs:{"picker-options":{disabledDate:e.disabledDate},clearable:!1,"end-placeholder":"结束日期","range-separator":"至","start-placeholder":"开始日期",type:e.dateType,"value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"]},on:{change:function(t){e.$emit("clearData"),e.$emit("update:date_value",t)}},model:{value:e.query.dateValue,callback:function(t){e.$set(e.query,"dateValue",t)},expression:"query.dateValue"}})],1):e._e(),e._v(" "),a("el-form-item",[e._t("btns")],2)],2)],1)],1)},staticRenderFns:[]},_=a("VU/8")(m,v,!1,null,null,null);t.a=_.exports},"LNJ/":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a("Xxa5"),i=a.n(n),r=a("d7EF"),o=a.n(r),l=a("exGp"),s=a.n(l),u=a("Dd8w"),c=a.n(u),p=a("FZmr"),d=a("NYxO"),y=a("3pLw"),m=a("EQFf"),v=a("iJJg"),_=a("4jyv"),b={name:"fleetInvoiceReport",mixins:[v.c,_.b],components:{InvoiceQuery:m.a,DownloadTips:p.a},created:function(){this.query.dateValue=[this.$moment().subtract(1,"days").format("YYYY-MM-DD 00:00:00"),this.$moment().format("YYYY-MM-DD 23:59:59")]},data:function(){return{dateValue:[],stationId:[],stationName:"",tableData:[],tableColumns:[{label:"车队ID",prop:"company_id"},{label:"客户类型",prop:"customer_type"},{label:"车队名称",prop:"company_name"},{label:"开票方式",prop:"invoice_type"},{label:"油品名称",prop:"oil_name"},{label:"升数",prop:"oil_num"},{label:"应收金额",prop:"order_amount"},{label:"优惠总金额",prop:"discount_amount"},{label:"均价",prop:"oil_price"},{label:"实收金额",prop:"",children:[{label:"本金",prop:"bj_amount"},{label:"赠金",prop:"gift_amount"},{label:"合计",prop:"pay_amount"}]},{label:"开票金额",prop:"invoice_amount"}],loading:!1,start_time:"",end_time:"",orderMaker:"",orderMakingTime:"",isGroup:!0,showDownloadTips:!1}},computed:c()({},Object(d.c)({getCurrentStation:"getCurrentStation",customerTypeObject:"customerTypeObject"})),methods:{disabledDate:function(e){console.log("=>(fleetInvoiceReport.vue:164) date",e);var t=new Date;return t.setHours(23,59,59,999),e>t},changeDate:function(){var e=this;return s()(i.a.mark(function t(){var a,n,r,l,s,u,c,p,d,m,v,_;return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(e.query.dateValue&&0!==e.query.dateValue.length){t.next=2;break}return t.abrupt("return",e.$message.error("请选择查询时间"));case 2:return t.prev=2,e.loading=!0,a=e.query,n=a.customer_type,r=a.type,l=a.company_id,s=void 0===l?[]:l,u=a.dateValue,c=a.invoice_type,p=o()(u,2),d=p[0],m=p[1],v={start_time:e.$moment(d).unix(),end_time:e.$moment(m).unix(),company_id:s,customer_type:n,type:r,invoice_type:c},t.next=9,Object(y.c)("/CardReport/getCardCompanyReceiptReport",v);case 9:if(_=t.sent,console.log("=>(fleetInvoiceReport.vue:304) res",_),_.status===y.a.SUCCESS){t.next=15;break}return e.$message({type:"error",message:_.info}),console.log("=>(fleetInvoiceReport.vue:307) 获取数据异常",_),t.abrupt("return");case 15:_.data.card_invoicing&&_.data.card_invoicing.forEach(function(t){"0"!==String(t.customer_type)?t.customer_type=e.customerTypeObject[t.customer_type]||t.customer_type:t.customer_type="未分类"}),e.tableData=_.data.card_invoicing||[],t.next=22;break;case 19:t.prev=19,t.t0=t.catch(2),console.log("=>(fleetInvoiceReport.vue:307) 获取数据异常",t.t0);case 22:return t.prev=22,e.loading=!1,t.finish(22);case 25:case"end":return t.stop()}},t,e,[[2,19,22,25]])}))()},clearData:function(e){this.query.dateValue||(this.tableData=[],this.orderMakingTime="",this.orderMaker="")},printContent:function(){var e=document.querySelector("#myTable").innerHTML,t=document.body.innerHTML;document.body.innerHTML=e,document.getElementsByClassName("el-table__header")[0].style.width="100%",document.getElementsByClassName("el-table__header")[0].style["table-layout"]="auto",document.getElementsByClassName("el-table__body")[0].style.width="100%",document.getElementsByClassName("el-table__body")[0].style["table-layout"]="auto",window.print(),history.go(0),document.body.innerHTML=t},cardChargeDownload:function(){var e=this,t=this.query,a=t.customer_type,n=t.type,i=t.company_id,r=void 0===i?[]:i,l=t.dateValue,s=t.invoice_type,u=o()(l,2),c=u[0],p=u[1];10===c.length&&(c+=" 00:00:00"),10===p.length&&(p+=" 23:59:59");var d={start_time:this.$moment(c).unix(),end_time:this.$moment(p).unix(),company_id:r,invoice_type:s,type:n,customer_type:a};this.$axios.get("/CardReport/cardCompanyReceiptReportDownload",{params:d}).then(function(t){200==t.data.status?e.showDownloadTips=!0:e.$message({message:t.data.info,type:"error"})})}}},f={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"pt-20px"},[a("invoice-query",{attrs:{"disabled-date":e.disabledDate,"show-invoice-type":!0,type:e.query.type,company_id:e.query.company_id,invoice_type:e.query.invoice_type,customer_type:e.query.customer_type,date_value:e.query.dateValue,dateType:"daterange"},on:{"update:type":function(t){return e.$set(e.query,"type",t)},"update:company_id":function(t){return e.$set(e.query,"company_id",t)},"update:invoice_type":function(t){return e.$set(e.query,"invoice_type",t)},"update:customer_type":function(t){return e.$set(e.query,"customer_type",t)},"update:date_value":function(t){return e.$set(e.query,"dateValue",t)},clearData:e.clearData},scopedSlots:e._u([{key:"btns",fn:function(){return[a("el-button",{staticClass:"mb-5px ml-15px",attrs:{disabled:e.loading,type:"primary"},on:{click:function(t){return e.changeDate()}}},[e._v("生成")]),e._v(" "),a("el-button",{attrs:{disabled:e.loading||0===e.tableData.length,type:"primary"},on:{click:e.printContent}},[e._v("打印\n        ")]),e._v(" "),a("el-button",{attrs:{disabled:e.loading||0===e.tableData.length,type:"primary"},on:{click:e.cardChargeDownload}},[e._v("\n          下载数据\n        ")])]},proxy:!0}])}),e._v(" "),a("div",{attrs:{id:"myTable"}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"text-center"},[a("div",{staticClass:"text-24px font-bold mt-20px py-5px border border-solid border-#EBEEF5"},[e._v("车队开票销售汇总表")]),e._v(" "),a("div",{staticClass:"flex py-10px px-20px border-x-1px border-x-solid border-#EBEEF5"},[a("div",{staticClass:"mx-40px"},[e._v("开始时间："+e._s(e.query.dateValue.length?10==e.query.dateValue[0].length?e.query.dateValue[0]+" 00:00:00":e.query.dateValue[0]:"")+"\n          ")]),e._v(" "),a("div",[e._v("截止时间："+e._s(e.query.dateValue.length?10==e.query.dateValue[1].length?e.query.dateValue[1]+" 23:59:59":e.query.dateValue[1]:"")+"\n          ")])]),e._v(" "),a("el-table",{ref:"table",attrs:{data:e.tableData,border:"",size:"small"}},[e._l(e.tableColumns,function(t){return[a("el-table-column",{attrs:{align:"center",label:t.label,prop:t.prop}},[t.children?e._l(t.children,function(t){return a("el-table-column",e._b({key:t.prop},"el-table-column",t,!1))}):e._e()],2)]})],2),e._v(" "),a("div",{staticClass:"flex justify-between py-10px text-14px border border-solid border-#EBEEF5 border-t-0"},[a("div",{staticClass:"ml-20px"},[e._v("制表人："+e._s(e.orderMaker))]),e._v(" "),a("div",{staticClass:"mx-100px"},[e._v("制表时间："+e._s(e.orderMakingTime))]),e._v(" "),a("div",{staticClass:"mr-100px"},[e._v("签字：")])])],1)])],1),e._v(" "),a("download-tips",{attrs:{showDownloadTips:e.showDownloadTips},on:{"update:showDownloadTips":function(t){e.showDownloadTips=t},"update:show-download-tips":function(t){e.showDownloadTips=t}}})],1)},staticRenderFns:[]};var h=a("VU/8")(b,f,!1,function(e){a("cXCk")},"data-v-faec382e",null);t.default=h.exports},cXCk:function(e,t){}});