import {expect, test} from "@playwright/test";

const targetProject = 'T049--微信端（新）（ip/wx）'
// const targetProject = 'T034--车牌付项目vplate -wx（ip/vplate-wx）'
// const targetProject = 'T007--商户平台（ip/mp）'
// const targetProject = 'T101-前端-私域流量企微客户管理(dev/wecom-crm-front)'
// const targetProject = 'T063--异业平台管理系统（dev/yiye-admin-frontend）'
// const targetProject = 'T069--安全卫士_前端（security-guards/safe-frontend）'
// const targetProject = 'T093--威视特智慧油站_前端（bjcnpc/ioc_smart_screen_front）'
// const targetProject = 'T084--异业中心h5(dev/yiye_refuel_frontend)'
// const targetProject = 'T062--MO-运营平台(new85.243)（mo/operation）'
// const targetProject = 'T117--贝林商户平台（ip/beilin-mp）'
// const targetProject = 'T118--贝林喂通卡前端（ip/beilin-card-admin-frontend)'
// const targetProject = 'T119--星合喂通卡前端（ip/xinghe-card-admin-frontend)'
// const targetProject = 'T056--单点登录（ip/sso）'
// const targetProject = 'T040--微信活动分支（ip/wx-activity）'
// const targetProject = 'T112--零管仓库项目(dev/retail-management-system-frontend)'
// const targetProject = 'T054--储值卡管理系统（ip/card-admin-frontend）'
// const targetProject = 'T120--充电项目前端mc-web（chargeproject/mc-web)'
// const targetProject = 'P1--【202406】充电项目前端mc-web（chargeproject/mc-web) '
// const targetProject = 'T129--壳牌比亚迪充电项目-youdao-pc-omp前端(shellbydcharge/youdao-pc-omp)'
// const targetProject = 'T016--喂车车官网（pp/weicheche）'
// const targetProject = 'T097--服务商管理前端（dev/service-provider-admin-frontend）'
// const targetProject = 'T098--服务商管理平台前端（dev/service-provider-frontend）'
// const targetProject = 'T072--小程序接口（ip/groupbuyingoil）'
// const targetProject = 'T134--非油商城小程序【对标冠德】(dev/crmebv2.2)'
// const targetProject = 'T059--车队卡管理平台（新）（dev/fleet-card-frontend）'
// const targetProject = 'T101-前端-私域流量企微客户管理(dev/wecom-crm-front)'
const testBranch = '' || 'test'
test('测试Walle', async ({page}) => {
  await page.goto('http://test.walle.wcc.cn')
  await page.fill('#loginform-username', '<EMAIL>');
  await page.fill('#loginform-password', 'walle7436746');
  await page.click('[name="login-button"]');
  await page.click('text=提交上线单');
  await page.click(`text=${targetProject}`);
  // 请替换 targetProject 为你实际要点击的项目名称
  // 请求地址是get请求，需要通过正则表达式来匹配如果通过 **/x/x 来匹配则会匹配不到
  const getBranch = page.waitForResponse(/walle\/get-branch/);
  await page.click('.icon-refresh');
  const branchResult = await (await getBranch).json();
  await page.click('#select2-branch-container');
  await page.fill('[role="searchbox"]', testBranch)
  // 拦截分支的最新请求信息
  const getCommit = page.waitForResponse((ops) => {
    return ops.url().match(/walle\/get-commit-history\?projectId=\d+&branch=/) && ops.url().includes(`branch=${testBranch}`)
  })
  // 点击test分支
  await page.getByRole('option', {name: testBranch, exact: true}).click()
  // 填写测试分支的最新信息
  const res = await (await getCommit).json();
  const [firstCommit] = res.data;
  await page.fill('#task-title', firstCommit.message?.length > 99 ? firstCommit.message.slice(0,100):firstCommit.message)
  // 点击提交按钮
  await page.click('[type="submit"]')
  // 开启审核按钮后点击发布
  const commitCol = page.getByText(firstCommit.message.split(' -')[0]);
  // 同意审核请求
  const taskOperation = page.waitForResponse(/task\/task-operation/)
  // 判断项目是否直接可上线
  const publish = await page.locator('.table tr').filter({has: commitCol}).getByText('上线', {exact: true});
  if ((await publish.count()) === 0) {
    await page.locator('.table tr').filter({has: commitCol}).locator('label').first().click()
    await taskOperation;
  }
  // 点击上线，跳转页面
  await publish.first().click()
  const startDeploy = page.waitForRequest(/walle\/start-deploy/);
  await page.click('.btn-deploy')
  await startDeploy;
  while(true){
    const getProcess = page.waitForResponse(/walle\/get-process/);
    const processRes = await (await getProcess).json();
    console.log("=>(AutoPublish.spec.js:70) 发布进度",processRes?.data?.percent);
    if(String(processRes?.data?.percent) === '100'){
      return;
    }
  }
})


const uatProjectName = "297-预发布-延长壳牌--前端-私域流量企微客户管理(shell-china-motorist/shell-wecom-crm-front)"
// const uatProjectName = "312--前端-chatGPT项目(dev-chatgptfront)"
// const uatProjectName = "319-充电项目前端mc-web（chargeproject/mc-web)"
// const uatProjectName = "267--异业中心h5(dev/yiye_refuel_frontend)"
// const uatBranch = 'release-chenweixing-20240306.v1';
const uatBranch = 'test'||'uat';

test('预发布Walle', async ({page}) => {
  await page.goto('http://walle.wcc.cn')
  await page.fill('#loginform-username', '<EMAIL>');
  await page.fill('#loginform-password', 'walle7436746');
  await page.click('[name="login-button"]');
  await page.click('text=提交上线单');
  await page.click(`text=${uatProjectName}`);
  // 请替换 targetProject 为你实际要点击的项目名称
  // 请求地址是get请求，需要通过正则表达式来匹配如果通过 **/x/x 来匹配则会匹配不到
  const getBranch = page.waitForResponse(/walle\/get-branch/);
  await page.click('.icon-refresh');
  const branchResult = await (await getBranch).json();
  await page.click('#select2-branch-container');
  await page.fill('[role="searchbox"]', uatBranch)
  // 拦截分支的最新请求信息
  const getCommit = page.waitForResponse((ops) => {
    return ops.url().match(/walle\/get-commit-history\?projectId=\d+&branch=/) && ops.url().includes(`branch=${uatBranch}`)
  })
  // 点击test分支
  await page.getByRole('option', {name: uatBranch, exact: true}).click()
  // 填写测试分支的最新信息
  const res = await (await getCommit).json();
  const [firstCommit] = res.data;
  await page.fill('#task-title', firstCommit.message)
  // 点击提交按钮
  await page.click('[type="submit"]')
  // 开启审核按钮后点击发布
  const commitCol = page.getByText(firstCommit.message.split(' -')[0]);
  // 同意审核请求
  const taskOperation = page.waitForResponse(/task\/task-operation/)
  // 判断项目是否直接可上线
  const publish = await page.locator('.table tr').filter({has: commitCol}).getByText('上线', {exact: true});
  if ((await publish.count()) === 0) {
    await page.locator('.table tr').filter({has: commitCol}).locator('label').first().click()
    await taskOperation;
  }
  // 点击上线，跳转页面
  await publish.first().click()
  const startDeploy = page.waitForResponse(/walle\/start-deploy/);
  await page.click('.btn-deploy')
  await (await startDeploy).json();
})
