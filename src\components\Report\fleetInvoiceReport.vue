<template>
  <div>
    <div class="pt-20px">
      <invoice-query
        :disabled-date="disabledDate"
        :show-invoice-type="true"
        :type.sync="query.type"
        :company_id.sync="query.company_id"
        :invoice_type.sync="query.invoice_type"
        :customer_type.sync="query.customer_type"
        :date_value.sync="query.dateValue"
        @clearData="clearData"
        dateType="daterange"
      >
        <template #btns>
          <el-button :disabled="loading" type="primary" class="mb-5px ml-15px" @click="changeDate()">生成</el-button>
          <el-button :disabled="loading || tableData.length === 0" type="primary" @click="printContent">打印
          </el-button>
          <el-button :disabled="loading || tableData.length === 0" type="primary" @click="cardChargeDownload">
            下载数据
          </el-button>
        </template>
      </invoice-query>

      <div id="myTable">
        <div class="text-center" v-loading="loading">
          <!-- 班结日报 -->
          <div class="text-24px font-bold mt-20px py-5px border border-solid border-#EBEEF5">车队开票销售汇总表</div>
          <div class="flex py-10px px-20px border-x-1px  border-x-solid border-#EBEEF5">
            <div class="mx-40px">开始时间：{{
                query.dateValue.length ? query.dateValue[0].length == 10 ? query.dateValue[0] + " 00:00:00" : query.dateValue[0] : ""
              }}
            </div>
            <div>截止时间：{{
                query.dateValue.length ? query.dateValue[1].length == 10 ? query.dateValue[1] + " 23:59:59" : query.dateValue[1] : ""
              }}
            </div>
          </div>
          <el-table ref="table" :data="tableData" border size="small">
            <template v-for="v in tableColumns">
              <el-table-column align="center" :label="v.label" :prop="v.prop">
                <template v-if="v.children">
                  <el-table-column
                    v-for="col of v.children"
                    v-bind="col"
                    :key="col.prop">
                  </el-table-column>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <div class="flex justify-between py-10px text-14px border border-solid border-#EBEEF5 border-t-0">
            <div class="ml-20px">制表人：{{ orderMaker }}</div>
            <div class="mx-100px">制表时间：{{ orderMakingTime }}</div>
            <div class="mr-100px">签字：</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 下载中心提示 -->
    <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>
  </div>
</template>

<script>
import DownloadTips from '../DownloadTips.vue';
import {mapGetters} from 'vuex'
import {DataCode, post} from "../../utils/http";
import InvoiceQuery from "../InvoiceQuery/InvoiceQuery.vue";
import customerTypeMixins from "../../mixins/customerType";
import tableQueryCondition from "../../mixins/tableQueryCondition";

export default {
  name: 'fleetInvoiceReport',
  mixins: [customerTypeMixins, tableQueryCondition],
  components: {
    InvoiceQuery,
    DownloadTips
  },
  created(){
    this.query.dateValue = [this.$moment().subtract(1, 'days').format('YYYY-MM-DD 00:00:00'), this.$moment().format('YYYY-MM-DD 23:59:59')]
  },
  data() {
    return {
      dateValue: [],
      stationId: [],//选中油站
      stationName: "",//所选油站名称
      tableData: [],
      tableColumns: [
        {
          label: "车队ID",
          prop: "company_id"
        },
        {
          label: "客户类型",
          prop: "customer_type"
        },
        {
          label: "车队名称",
          prop: "company_name"
        },
        {
          label: "开票方式",
          prop: "invoice_type"
        },
        {
          label: "油品名称",
          prop: "oil_name"
        },
        {
          label: "升数",
          prop: "oil_num"
        },
        {
          label: "应收金额",
          prop: "order_amount"
        },
        {
          label: "优惠总金额",
          prop: "discount_amount"
        },
        {
          label: "均价",
          prop: "oil_price"
        },
        {
          label: "实收金额",
          prop: "",
          children: [
            {
              label: "本金",
              prop: "bj_amount"
            },
            {
              label: "赠金",
              prop: "gift_amount"
            },
            {
              label: "合计",
              prop: "pay_amount"
            },
          ]
        },
        {
          label: "开票金额",
          prop: "invoice_amount",
        },
      ],
      loading: false,
      start_time: "",
      end_time: "",
      orderMaker: "",
      orderMakingTime: "",
      isGroup: true,//是否是集团账号
      showDownloadTips: false,
    }
  },
  computed: {
    ...mapGetters({
      "getCurrentStation": "getCurrentStation",
      "customerTypeObject": "customerTypeObject"
    })
  },
  methods: {
    disabledDate(date) {
      console.log("=>(fleetInvoiceReport.vue:164) date", date);
      // 获取当前日期
      const today = new Date();
      // 设置时间为当前时间的 23:59:59 以包含当天
      today.setHours(23, 59, 59, 999);
      // 返回一个函数，如果选择的日期大于今天，则返回 true（即禁用）
      return date > today;
    },
    //选中时间生成报表
    async changeDate() {
      if (!this.query.dateValue || this.query.dateValue.length === 0) return this.$message.error('请选择查询时间')
      try {
        this.loading = true;
        const {customer_type, type, company_id = [], dateValue, invoice_type} = this.query;
        let [start_time, end_time] = dateValue
        let params = {
          start_time: this.$moment(start_time).unix(),
          end_time: this.$moment(end_time).unix(),
          company_id,
          customer_type,
          type,
          invoice_type
        };
        const res = await post('/CardReport/getCardCompanyReceiptReport', params)
        console.log("=>(fleetInvoiceReport.vue:304) res", res);
        if (res.status !== DataCode.SUCCESS) {
          this.$message({type: 'error', message: res.info})
          console.log("=>(fleetInvoiceReport.vue:307) 获取数据异常", res);
          return;
        }
        // 对数据类型进行转换
        if(res.data.card_invoicing){
          res.data.card_invoicing.forEach(item => {
            if(String(item.customer_type) === '0'){
              item.customer_type = '未分类';
              return;
            }
            item.customer_type = this.customerTypeObject[item.customer_type] || item.customer_type
          })
        }
        this.tableData = res.data.card_invoicing || [];
      } catch (e) {
        console.log("=>(fleetInvoiceReport.vue:307) 获取数据异常", e);
      } finally {
        this.loading = false;
      }
    },
    clearData(e) {
      if (!this.query.dateValue) {
        this.tableData = [];
        this.orderMakingTime = "";
        this.orderMaker = "";
      }
    },
    //打印
    printContent() {
      let wpt = document.querySelector('#myTable');
      let newContent = wpt.innerHTML;
      let oldContent = document.body.innerHTML;
      document.body.innerHTML = newContent;
      document.getElementsByClassName("el-table__header")[0].style.width = "100%";
      document.getElementsByClassName("el-table__header")[0].style["table-layout"] = "auto";
      document.getElementsByClassName("el-table__body")[0].style.width = "100%";
      document.getElementsByClassName("el-table__body")[0].style["table-layout"] = "auto";
      window.print(); //打印方法
      history.go(0);
      document.body.innerHTML = oldContent;
    },
    //导出
    cardChargeDownload() {
      let that = this;
      const {customer_type, type, company_id = [], dateValue, invoice_type} = this.query;
      let [start_time,end_time] = dateValue
      if (start_time.length === 10) start_time = start_time + ' 00:00:00'
      if (end_time.length === 10) end_time = end_time + ' 23:59:59'
      let params = {
        start_time: that.$moment(start_time).unix(),
        end_time: that.$moment(end_time).unix(),
        company_id,
        invoice_type,
        type,
        customer_type
      };

      this.$axios.get('/CardReport/cardCompanyReceiptReportDownload', {params: params}).then((res) => {
        if (res.data.status == 200) {
          this.showDownloadTips = true;
        } else {
          this.$message({
            message: res.data.info,
            type: 'error'
          });
        }
      })
    }
  },
}
</script>

<style scoped>
</style>
