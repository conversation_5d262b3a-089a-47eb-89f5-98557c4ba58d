webpackJsonp([21],{GRg3:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=a("Dd8w"),i=a.n(l),n=(a("mtWM"),a("FZmr")),o=a("fTGR"),s=a("NYxO"),r=a("XAqB"),d={name:"SpendDetailReport",components:{DownloadTips:n.a,BanciDateTime:o.a},data:function(){return{EndTime:r.a,isTotalReportForm:!0,typeOptions:[{value:1,label:"按自然日期"},{value:4,label:"按班结日期"}],typeValue:1,dateValue:[],dateBanciValue:"",stationId:[],stationName:"",stationOptions:[],companyOptions:[{ID:"0",CompanyName:"全部"}],companyValue:"0",goodsOptions:[{oil_name:"全部",oil_id:"0"}],goodsValue:"0",cardTypeOptions:[{value:"0",label:"全部"},{value:"1",label:"个人卡"},{value:"2",label:"车队卡"},{value:"3",label:"不记名卡"}],cardTypeValue:"0",cardOptions:[{name:"全部",ID:"0"}],cardValue:"0",searchTypeVlaue:"1",inputTxt:"",tableData:[],loading:!1,noMore:!0,page:1,pageSize:20,total:0,start_time:"",end_time:"",orderMaker:"",orderMakingTime:"",isGroup:!0,showDownloadTips:!1,checkList:[],classList:[],update:!0,nowEndTime:""}},mounted:function(){var t=localStorage.getItem("__userInfo__")||"";this.isTotalReportForm=426!=JSON.parse(t).group_id;var e=this.$moment(new Date).subtract(1,"days").format("YYYY-MM-DD HH:mm:ss"),a=this.$moment().format("YYYY-MM-DD HH:mm:ss");this.dateValue=[e,a],this.nowEndTime=a.slice(11);var l=this.$moment().subtract(1,"days").format("YYYY-MM-DD");if(this.dateBanciValue=l,void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.getCurrentStation&&2==this.getCurrentStation.merchant_type?this.isGroup=!0:this.isGroup=!1,this.getStationList(),this.getCompanyList(),this.getOilList(),this.getCardDatas(),this.changeDate()},computed:i()({},Object(s.c)({getCurrentStation:"getCurrentStation"})),methods:{changeBnaciDate:function(t){this.dateBanciValue=t},searchBanciDate:function(t){this.dateValue=t,this.changeDate()},searchBanci:function(t){if(t.length){var e,a;e=t.map(function(t){return new Date(t.stime).getTime()}),a=t.map(function(t){return new Date(t.etime).getTime()});var l=Math.max.apply(null,a),i=Math.min.apply(null,e);this.dateValue[0]=this.$moment(i).format("YYYY-MM-DD hh:mm:ss"),this.dateValue[1]=this.$moment(l).format("YYYY-MM-DD hh:mm:ss"),this.$forceUpdate()}else{this.dateValue=[];var n=this.$moment(new Date(this.dateBanciValue)),o=this.$moment(new Date(this.dateBanciValue));this.dateValue.push(this.$moment(n).format("YYYY-MM-DD")+" 00:00:00"),this.dateValue.push(this.$moment(o).format("YYYY-MM-DD")+" "+this.nowEndTime)}},scrollMore:function(){this.page+=1,this.changeDate()},getOilList:function(){var t=this;this.$axios.post("/CardReport/getOil",{stids:this.stationId}).then(function(e){200==e.data.status&&(t.goodsOptions=[{oil_id:"0",oil_name:"全部"}],t.goodsOptions=t.goodsOptions.concat(e.data.data))})},getCardDatas:function(){var t=this;this.$axios.post("/CardReport/getCardDatas",{state:0,station_id:this.stationId,page:1,page_size:100}).then(function(e){200==e.data.status&&(t.cardOptions=[{ID:"0",name:"全部"}],t.cardOptions=t.cardOptions.concat(e.data.data))})},changeStationValue:function(t){var e=this;this.stationName="";var a=this.stationId.length;1==this.typeValue?this.stationId.forEach(function(t,l){e.stationOptions.forEach(function(i){i.stid==t&&(e.stationName+=l==a-1?i.stname:i.stname+"，")})}):this.stationOptions.forEach(function(a){a.stid==t&&(e.stationName=a.stname)}),1==this.typeValue?(this.getOilList(),this.getCardDatas()):this.getOilList(),4==this.typeValue&&this.stationId&&this.$refs.banciRef.getBanci(t)},getStationList:function(){this.stationId=[];var t=this;this.$axios.post("/Stations/getStationList",{}).then(function(e){200==e.status&&(t.stationOptions=[],e.data.data.forEach(function(e){t.stationId.push(e.stid)}),t.stationOptions=e.data.data)})},getCompanyList:function(){var t=this;t.$axios.post("/CompanyCard/getSimpleCompanyList",{page:1,page_size:1250,input:""}).then(function(e){200==e.data.status?(t.companyOptions=[{ID:"0",CompanyName:"全部"}],t.companyOptions=t.companyOptions.concat(e.data.data.dt)):t.$message({message:e.data.info,type:"error"})}).catch(function(t){})},changeTypeValue:function(t){4==t?(this.stationId="",this.$refs.banciRef.clearDate()):this.stationId=[],1==this.getCurrentStation.merchant_type&&(this.stationId=this.getCurrentStation.merchant_id);var e=this.$moment(new Date).subtract(1,"days").format("YYYY-MM-DD HH:mm:ss"),a=this.$moment().format("YYYY-MM-DD HH:mm:ss");this.dateValue=[e,a];var l=this.$moment().subtract(1,"days").format("YYYY-MM-DD");this.dateBanciValue=l,this.companyValue="0",this.goodsValue="0",this.cardTypeValue="0",this.cardValue="0",this.orderMakingTime="",this.tableData=[],this.page=1},changeDate:function(){var t=this;t.loading=!0;var e={};10==t.dateValue[0].length&&(t.dateValue[0]=t.dateValue[0]+" 00:00:00"),10==t.dateValue[1].length&&(t.dateValue[1]==t.$moment(new Date).format("YYYY-MM-DD")?t.dateValue[1]=t.dateValue[1]+" "+this.$moment().format("HH:mm:ss"):t.dateValue[1]=t.dateValue[1]+" 23:59:59"),e=1==t.typeValue?{page_index:t.page,page_size:t.pageSize,start_time:t.dateValue?t.dateValue[0]:"",end_time:t.dateValue?t.dateValue[1]:"",company_id:t.companyValue,card_type:t.cardTypeValue,card_id:t.cardValue,oil_id:t.goodsValue,type:1,input_type:t.searchTypeVlaue,input:t.inputTxt,stid:t.stationId}:{page_index:t.page,page_size:t.pageSize,start_time:t.dateValue?t.dateValue[0]:"",end_time:t.dateValue?t.dateValue[1]:"",oil_id:t.goodsValue,type:1,stid:[t.stationId]},t.$axios.post("/CardReport/getOsCostDetails",e).then(function(e){if(t.loading=!1,t.tableData=[],200==e.data.status){t.orderMakingTime=t.$moment().format("YYYY-MM-DD"),t.noMore=e.data.data.pageInfo.pageIndex*e.data.data.pageInfo.pageSize>=e.data.data.pageInfo.totalCount,t.total=e.data.data.pageInfo.totalCount,t.pageSize=e.data.data.pageInfo.pageSize,t.tableData=t.tableData.concat(e.data.data.order_list);var a=localStorage.getItem("__userInfo__");!a||""===a&&"undefined"===a||(t.orderMaker=JSON.parse(a).name)}else t.$message({message:e.data.info,type:"error"})}).catch(function(t){})},clearData:function(t){this.dateValue||(this.tableData=[],this.orderMakingTime="",this.orderMaker=""),this.$moment(this.dateValue[1]).format("YYYY-MM-DD")===this.$moment().format("YYYY-MM-DD")&&this.$moment(this.dateValue[1]).unix()>this.$moment().unix()&&(this.dateValue[1]=this.$moment().format("YYYY-MM-DD HH:mm:ss"))},printContent:function(){var t=document.querySelector("#myTable").innerHTML,e=document.body.innerHTML;document.body.innerHTML=t,document.getElementsByClassName("el-table__header")[0].style.width="100%",document.getElementsByClassName("el-table__header")[0].style["table-layout"]="auto",document.getElementsByClassName("el-table__body")[0].style.width="100%",document.getElementsByClassName("el-table__body")[0].style["table-layout"]="auto",window.print(),history.go(0),document.body.innerHTML=e},handleCurrentChange:function(t){this.page=t,this.changeDate()},handleSizeChange:function(t){this.pageSize=t,this.changeDate()},cardChargeDownload:function(){var t=this,e={};e=1==this.typeValue?{page_index:this.page,page_size:this.pageSize,start_time:this.dateValue?this.dateValue[0]:"",end_time:this.dateValue?this.dateValue[1]:"",company_id:this.companyValue,card_type:this.cardTypeValue,card_id:this.cardValue,oil_id:this.goodsValue,type:1,input_type:this.searchTypeVlaue,input:this.inputTxt,stid:this.stationId}:{page_index:this.page,page_size:this.pageSize,start_time:this.dateValue?this.dateValue[0]:"",end_time:this.dateValue?this.dateValue[1]:"",oil_id:this.goodsValue,type:1,stid:[this.stationId]},this.$axios.get("/CardReport/osCostDetailsDownload",{params:e}).then(function(e){200==e.data.status?t.showDownloadTips=!0:t.$message({message:e.data.info,type:"error"})})}},watch:{getCurrentStation:function(t,e){0!=t.merchant_type&&t.value!=e.value&&(this.getCurrentStation&&2==this.getCurrentStation.merchant_type?this.isGroup=!0:this.isGroup=!1,this.getStationList(),this.getCompanyList(),this.getOilList(),this.getCardDatas(),this.changeDate())},typeValue:function(){var t=this;this.update=!1,setTimeout(function(){t.update=!0},0)}}},p={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"report"},[a("div",{staticClass:"report-content"},[a("el-radio-group",{on:{change:t.changeTypeValue},model:{value:t.typeValue,callback:function(e){t.typeValue=e},expression:"typeValue"}},t._l(t.typeOptions,function(e){return a("el-radio-button",{key:e.value,attrs:{label:e.value}},[t._v(t._s(e.label))])}),1),t._v(" "),a("div",{staticClass:"content-header"},[2==t.getCurrentStation.merchant_type?a("span",{staticClass:"txt"},[t._v("油站名称")]):t._e(),t._v(" "),2==t.getCurrentStation.merchant_type&&t.update?a("el-select",{staticStyle:{width:"250px","margin-right":"20px"},attrs:{multiple:1==t.typeValue,clearable:"","collapse-tags":"",placeholder:"请选择油站"},on:{change:t.changeStationValue},model:{value:t.stationId,callback:function(e){t.stationId=e},expression:"stationId"}},t._l(t.stationOptions,function(t,e){return a("el-option",{key:e,attrs:{label:t.stname,value:t.stid}})}),1):t._e(),t._v(" "),a("el-date-picker",{directives:[{name:"show",rawName:"v-show",value:1==t.typeValue,expression:"typeValue == 1"}],staticStyle:{"margin-right":"15px"},attrs:{"value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"],type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":t.EndTime},on:{change:t.clearData},model:{value:t.dateValue,callback:function(e){t.dateValue=e},expression:"dateValue"}}),t._v(" "),a("banci-date-time",{directives:[{name:"show",rawName:"v-show",value:4==t.typeValue,expression:"typeValue == 4"}],ref:"banciRef",attrs:{stationValue:t.stationId,dateValue:t.dateBanciValue,"picker-options":t.EndTime},on:{searchDate:t.searchBanciDate,changeDate:t.changeBnaciDate}}),t._v(" "),a("span",{staticClass:"txt"},[t._v("商品")]),t._v(" "),a("el-select",{staticStyle:{width:"100px","margin-right":"20px"},attrs:{placeholder:"请选择商品"},model:{value:t.goodsValue,callback:function(e){t.goodsValue=e},expression:"goodsValue"}},t._l(t.goodsOptions,function(t,e){return a("el-option",{key:e,attrs:{label:t.oil_name,value:t.oil_id}})}),1),t._v(" "),a("span",{directives:[{name:"show",rawName:"v-show",value:1==t.typeValue,expression:"typeValue == 1"}],staticClass:"txt"},[t._v("车队名称")]),t._v(" "),a("el-select",{directives:[{name:"show",rawName:"v-show",value:1==t.typeValue,expression:"typeValue == 1"}],staticStyle:{width:"130px","margin-right":"20px"},attrs:{filterable:"",placeholder:"请选择车队"},model:{value:t.companyValue,callback:function(e){t.companyValue=e},expression:"companyValue"}},t._l(t.companyOptions,function(t,e){return a("el-option",{key:e,attrs:{label:t.CompanyName,value:t.ID}})}),1),t._v(" "),a("span",{directives:[{name:"show",rawName:"v-show",value:1==t.typeValue,expression:"typeValue == 1"}],staticClass:"txt"},[t._v("卡类型")]),t._v(" "),a("el-select",{directives:[{name:"show",rawName:"v-show",value:1==t.typeValue,expression:"typeValue == 1"}],staticStyle:{width:"130px","margin-right":"20px"},attrs:{placeholder:"请选择卡类型"},model:{value:t.cardTypeValue,callback:function(e){t.cardTypeValue=e},expression:"cardTypeValue"}},t._l(t.cardTypeOptions,function(t,e){return a("el-option",{key:e,attrs:{label:t.label,value:t.value}})}),1),t._v(" "),a("span",{directives:[{name:"show",rawName:"v-show",value:1==t.typeValue,expression:"typeValue == 1"}],staticClass:"txt"},[t._v("卡名称")]),t._v(" "),a("el-select",{directives:[{name:"show",rawName:"v-show",value:1==t.typeValue,expression:"typeValue == 1"}],staticStyle:{width:"130px","margin-right":"20px"},attrs:{filterable:"",placeholder:"请选择卡名称"},model:{value:t.cardValue,callback:function(e){t.cardValue=e},expression:"cardValue"}},t._l(t.cardOptions,function(t,e){return a("el-option",{key:e,attrs:{label:t.name,value:t.ID}})}),1),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:1==t.typeValue,expression:"typeValue == 1"}],staticStyle:{"margin-top":"10px"}},[a("span",{staticClass:"txt"},[t._v("查询类型")]),t._v(" "),a("el-radio-group",{model:{value:t.searchTypeVlaue,callback:function(e){t.searchTypeVlaue=e},expression:"searchTypeVlaue"}},[a("el-radio",{attrs:{label:"1"}},[t._v("手机号")]),t._v(" "),a("el-radio",{attrs:{label:"0"}},[t._v("卡号")]),t._v(" "),a("el-radio",{attrs:{label:"2"}},[t._v("卡面卡号")]),t._v(" "),a("el-radio",{attrs:{label:"4"}},[t._v("持卡人")])],1),t._v(" "),a("el-input",{staticStyle:{width:"210px"},attrs:{placeholder:"1"==t.searchTypeVlaue?"请输入手机号":"0"==t.searchTypeVlaue?"请输入卡号":"2"==t.searchTypeVlaue?"请输入卡面卡号":"请输入持卡人名称",clearable:""},model:{value:t.inputTxt,callback:function(e){t.inputTxt=e},expression:"inputTxt"}}),t._v(" "),a("el-button",{attrs:{type:"primary",disabled:!t.dateValue},on:{click:function(e){t.page=1,t.tableData=[],t.noMore=!0,t.changeDate()}}},[t._v("生成")])],1),t._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:4==t.typeValue,expression:"typeValue == 4"}],attrs:{type:"primary",disabled:!t.dateBanciValue},on:{click:function(e){t.page=1,t.tableData=[],t.noMore=!0,t.changeDate()}}},[t._v("生成")])],1),t._v(" "),a("div",{staticClass:"search-box",attrs:{div:""}},[a("el-button",{attrs:{type:"primary",disabled:0==t.tableData.length},on:{click:t.printContent}},[t._v("打印")]),t._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:t.isTotalReportForm,expression:"isTotalReportForm"}],attrs:{type:"primary",disabled:0==t.tableData.length},on:{click:t.cardChargeDownload}},[t._v("下载数据")])],1),t._v(" "),a("div",{attrs:{id:"myTable"}},[a("div",{staticClass:"tableData reportData"},[a("div",{staticClass:"report_title"},[t._v("储值卡消费明细报表")]),t._v(" "),a("div",{staticClass:"report_header"},[t.isGroup?a("div",[t._v("集团名称："+t._s(t.getCurrentStation.label))]):a("div",[t._v("油站名称："+t._s(t.getCurrentStation.label))]),t._v(" "),a("div",[t._v("开始日期："+t._s(t.dateValue?t.dateValue[0]:""))]),t._v(" "),a("div",[t._v("结束日期："+t._s(t.dateValue?t.dateValue[1]:""))]),t._v(" "),a("div",[t._v("单位：元")])]),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"table",attrs:{data:t.tableData,border:"",size:"small",align:"center"}},[a("el-table-column",{attrs:{align:"center",prop:"activate_card_stid_name",label:"开卡油站",width:"160",formatter:t.formatterCellval}}),t._v(" "),2==t.getCurrentStation.merchant_type?a("el-table-column",{attrs:{align:"center",prop:"stid_name",label:"消费油站",width:"160",formatter:t.formatterCellval}}):t._e(),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"card_no",label:"卡号",width:"160",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"card_number",label:"卡面卡号",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"phone",label:"手机号",width:"100",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"cardholder_name",label:"持卡人",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"car_no",label:"车牌号",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"company_name",label:"车队名称",formatter:t.formatterCellval,width:"120"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"goods_name",label:"商品名称",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"gun_name",label:"油枪",formatter:t.formatterCellval,width:"60"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"han_price",label:"挂牌价",formatter:t.formatterCellval,width:"60"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"sl",label:"升量",formatter:t.formatterCellval,width:"70"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"origin_price",label:"订单原价",formatter:t.formatterCellval,width:"70"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"credit_price",label:"积分抵现",formatter:t.formatterCellval,width:"55"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"dis_money",label:"价格优惠",formatter:t.formatterCellval,width:"55"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"coupon_money",label:"券优惠",formatter:t.formatterCellval,width:"60"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"total_dis_money",label:"总优惠",formatter:t.formatterCellval,width:"60"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"dis_price",label:"实付单价",formatter:t.formatterCellval,width:"70"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"pay_price",label:"支付金额",formatter:t.formatterCellval,width:"70"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"pay_money",label:"支付本金",formatter:t.formatterCellval,width:"70"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"git_money",label:"支付赠金",formatter:t.formatterCellval,width:"70"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"credit_money",label:"信用额度支付",formatter:t.formatterCellval,width:"70"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡余额",formatter:t.formatterCellval,width:"70"},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.only_mz?a("span",[t._v("--")]):a("span",[t._v(t._s(e.row.card_balance))])]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡本金",formatter:t.formatterCellval,width:"70"},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.only_mz?a("span",[t._v("--")]):a("span",[t._v(t._s(e.row.card_money))])]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡赠金",formatter:t.formatterCellval,width:"70"},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.only_mz?a("span",[t._v("--")]):a("span",[t._v(t._s(e.row.git_balance))])]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"card_name",label:"卡名称",width:"160",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"card_type_name",label:"卡类型",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"order_no",label:"订单号",width:"160",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"pay_time",label:"交易时间",width:"140",formatter:t.formatterCellval}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"discount_mark",label:"价格优惠详情",width:"280",formatter:t.formatterCellval}})],1),t._v(" "),a("div",{staticClass:"page_content"},[a("el-pagination",{staticClass:"page_left",attrs:{"current-page":t.page,"page-size":t.pageSize,layout:"prev, pager, next",total:t.total},on:{"current-change":t.handleCurrentChange}}),t._v(" "),a("el-pagination",{staticClass:"page_right",attrs:{"page-sizes":[20,30,40,50],"page-size":t.pageSize,layout:"total, sizes",total:t.total},on:{"size-change":t.handleSizeChange}})],1)],1),t._v(" "),a("div",{staticClass:"table_des"},[a("div",{staticClass:"table_des_text"},[a("p",[t._v("注：")]),t._v(" "),a("div",[a("p",[t._v("1.车队子卡仅使用母账扣款的订单，卡余额显示为：-")]),t._v(" "),t.stationId.length!=t.stationOptions.length&&t.isGroup?a("p",{staticClass:"stations"},[t._v("2.取数油站："+t._s(t.stationName))]):t._e()])])]),t._v(" "),a("div",{staticClass:"des_bottom"},[a("div",[t._v("制表人："+t._s(t.orderMaker))]),t._v(" "),a("div",[t._v("制表时间："+t._s(t.orderMakingTime))]),t._v(" "),a("div",[t._v("签字：")])])])],1),t._v(" "),a("download-tips",{attrs:{showDownloadTips:t.showDownloadTips},on:{"update:showDownloadTips":function(e){t.showDownloadTips=e},"update:show-download-tips":function(e){t.showDownloadTips=e}}})],1)},staticRenderFns:[]};var c=a("VU/8")(d,p,!1,function(t){a("ajpV"),a("wW09")},"data-v-72e368c4",null);e.default=c.exports},ajpV:function(t,e){},wW09:function(t,e){}});