<template>
  <div id="capitalRecord" class="capitalRecord">
    <div class="header">
      <div class="group">
        <el-radio-group v-model="checkboxGroup" @change="changeData">
          <el-radio-button
            v-for="item in typeList"
            :key="item.index"
            :label="item.value"
            ><span @click="changeType(item.value)">{{
              item.label
            }}</span></el-radio-button
          >
        </el-radio-group>
      </div>
      <div class="classType">
        <el-radio-group v-model="typeClassValue" @change="changeClassTypeValue">
          <el-radio-button
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.value"
            >{{ item.label }}</el-radio-button
          >
        </el-radio-group>
        <el-select
          v-if="getCurrentStation.merchant_type == 2 && update"
          v-model="stationValue"
          :multiple="typeClassValue == 1"
          @change="changeBanciData"
          collapse-tags
          style="width:250px"
          placeholder="请选择油站"
        >
          <el-option
            v-for="item in stationOptions"
            :key="item.stid"
            :label="item.stname"
            :value="item.stid"
          >
          </el-option>
        </el-select>
        <el-date-picker
          v-show="typeClassValue == 1"
          class="date-picker"
          @change="changeData"
          v-model="dateValue"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          :picker-options="startTimePicker"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
        <banci-date-time
          ref="banciRef"
          :stationValue="stationValue"
          :dateValue="dateBanciValue"
          @searchDate="searchDate"
          :picker-options="startTimePicker"
          @changeDate="changeDate"
          v-show="typeClassValue == 4"
        >
        </banci-date-time>
      </div>
      <div class="select">
        <div style="display: flex;">
          <div class="right">
            <span class="txt">查询类型</span>
            <el-radio-group v-model="searchTypeVlaue">
              <el-radio label="1">手机号</el-radio>
              <el-radio label="0">卡号</el-radio>
              <el-radio label="2">卡面卡号</el-radio>
              <el-radio label="5">订单号</el-radio>
            </el-radio-group>
            <el-input
              v-model="inputTxt"
              style="width:210px"
              :placeholder="
                searchTypeVlaue == '0'
                  ? '请输入卡号'
                  : searchTypeVlaue == '1'
                  ? '请输入手机号'
                  : searchTypeVlaue == '2'
                  ? '请输入卡面卡号'
                  : '请输入订单号'
              "
              clearable
            ></el-input>
            <el-button type="primary" @click="changeData">查询</el-button>
          </div>
        </div>

        <el-button
          type="primary"
          :disabled="tableData.length == 0"
          v-if="userInfo.has_mch_id === 0 && showDown"
          @click="cardCapitalRecordDownload"
          >下载数据</el-button
        >
      </div>
    </div>
    <el-table
      v-loading="loading"
      class="capitalRecordData"
      :data="tableData"
      style="width: 100%; margin-bottom:20px"
    >
      <el-table-column
        align="left"
        label="交易油站"
        width="180"
        fixed
        prop="StationNO"
        :formatter="formatterCellval"
      >
        <template slot-scope="scope"
          ><span>{{ getStationName(scope.row.StationNO) }}</span></template
        >
      </el-table-column>
      <el-table-column
        align="center"
        label="卡号"
        width="190"
        prop="CardNO"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="卡面卡号"
        width="190"
        prop="CardNumber"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        width="140"
        label="手机号"
        prop="Phone"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="持卡人"
        min-width="120"
        prop="cardholder_name"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        min-width="140"
        label="车牌号"
        prop="CarNumber"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        v-if="checkboxGroup == 'QB' || checkboxGroup == 'QK'"
        align="center"
        prop="Type"
        label="变动类型"
        width="150"
      >
      </el-table-column>
      <el-table-column
        align="left"
        prop="Money"
        label="变动金额(元)"
        width="120"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="left"
        prop="ChangeFrontBalance"
        label="变动前金额(元)"
        :formatter="formatterCellval"
        width="140"
      >
      </el-table-column>
      <el-table-column
        align="left"
        prop="ChangeAfterBalance"
        label="变动后金额(元)"
        width="140"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="开户油站"
        width="140"
        prop="stNewName"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="TradeID"
        label="订单号"
        width="190"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column align="center" label="变动时间" width="200">
        <template slot-scope="scope"
          ><span>{{ getTime(scope.row.TradeTime) }}</span></template
        >
      </el-table-column>
      <el-table-column
        align="center"
        prop="AmountType"
        label="变动账户"
        width="120"
        :formatter="formatterCellval"
      >
      </el-table-column>
    </el-table>

    <!-- 页码 -->
    <div class="page_content">
      <el-pagination
        class="page_left"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-size="pageSize"
        layout="prev, pager, next"
        :total="total"
      >
      </el-pagination>
      <el-pagination
        class="page_right"
        @size-change="handleSizeChange"
        :page-sizes="[10, 15, 20, 30]"
        :page-size="pageSize"
        layout="total, sizes"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 下载中心提示 -->
    <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>
  </div>
</template>

<script>
const userInfo = JSON.parse(localStorage.getItem("__userInfo__"));
import DownloadTips from "./DownloadTips.vue";
import BanciDateTime from "./Banci/banciDateTime.vue";
import { mapGetters } from "vuex";
export default {
  name: "CapitalRecord",
  components: {
    DownloadTips,
    BanciDateTime
  },
  data() {
    return {
      isTotalReportForm: true,
      userInfo: userInfo,
      typeList: [
        {
          value: "QB",
          label: "全部"
        },
        {
          value: "CZ",
          label: "充值"
        },
        {
          value: "XF",
          label: "消费"
        },
        {
          value: "FP",
          label: "资金划拨"
        },
        {
          value: "TK",
          label: "充值退款"
        },
        {
          value: "XFCX",
          label: "消费退款"
        },
        {
          value: "QK",
          label: "余额清零"
        }
      ],
      checkboxGroup: "QB",
      dateValue: [], //选择日期
      stationOptions: [], //油站列表
      stationValue: [], //选中油站
      tableData: [], //表格数据
      loading: true,
      total: 0, //数据总条数
      pageSize: 10,
      currentPage: 1, //默认当前页码
      inputTxt: "", //搜索内容
      searchTypeVlaue: "1",
      showDownloadTips: false, //下載資金流水成功彈窗
      typeClassValue: 1,
      dateBanciValue: "",
      typeOptions: [
        {
          value: 1,
          label: "按自然日期"
        },
        {
          value: 4,
          label: "按班结日期"
        }
      ],
      checkList: [],
      classList: [], //班次列表
      nowEndTime: "",
      showDown: true,
      update: true
    };
  },
  mounted() {
    let userInfo = localStorage.getItem("__userInfo__") || "";
    this.isTotalReportForm = JSON.parse(userInfo).group_id != 426;
    this.showDown = JSON.parse(userInfo).group_id != 383;
    //获取前一个月的时间
    const startDate = this.$moment(new Date())
      .subtract(1, "months")
      .format("YYYY-MM-DD HH:mm:ss");
    const endDate = this.$moment(new Date());
    this.dateValue.push(
      this.$moment(startDate).format("YYYY-MM-DD") + " 00:00:00"
    );
    this.dateValue.push(
      this.$moment(endDate).format("YYYY-MM-DD HH:mm:ss")
    );
    this.nowEndTime = this.$moment(endDate).format("YYYY-MM-DD HH:mm:ss").slice(11)

    if (
      this.getCurrentStation.merchant_type == undefined ||
      this.getCurrentStation.merchant_type == 0
    ) {
      return false;
    }
    this.dateBanciValue = this.nowDate;

    this.getStations();
    this.stationValue = [];
    this.$nextTick(() => {
      if (this.getCurrentStation.merchant_type == 1) {
        this.stationValue.push(this.getCurrentStation.merchant_id);
      }
    });

    this.getCardCapitalRecordList();
  },
  computed: {
    ...mapGetters({
      getCurrentStation: "getCurrentStation"
    }),
    nowDate: function() {
      return this.$moment(new Date()).format("YYYY-MM-DD");
    },
    startTimePicker() {
      var _this = this;
      return {
        disabledDate(time) {
          if(!_this.isTotalReportForm){ // 武汉道达尔限制选 60 天
            let endDate = new Date();
            let today = _this.$moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
            endDate.setTime(_this.$moment(today).valueOf())
            const startDate = new Date();
            startDate.setTime(endDate.getTime() - 3600 * 1000 * 24 * 60);
            return (
              time.getTime() > endDate ||
              time.getTime() < startDate
            );
          }

          let endTime = new Date();
          const start = new Date();
          if (_this.userInfo.has_mch_id === 0) {
            return time.getTime() > new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1).getTime()
          }
          //最大值三个月范围限制
          start.setTime(endTime.getTime() - 3600 * 1000 * 24 * 90);
          return (
            time.getTime() > endTime ||
            time.getTime() < start ||
            time.getTime() > Date.now()
          );
        },
      };
    }
  },
  methods: {
    searchDate(value) {
      console.log("value", value);
      this.dateValue = value;
      this.getCardCapitalRecordList();
    },
    changeDate(value) {
      // console.log('value',value);
      this.dateBanciValue = value;
    },
    //切换班次类型
    changeClassTypeValue(e) {
      console.log("e", e);
      if (e == 4) {
        this.stationValue = "";
        this.$refs.banciRef.clearDate();
      } else {
        this.stationValue = [];
        this.dateValue = [];
        let startDate = this.$moment(new Date())
          .subtract(1, "months")
          .format("YYYY-MM-DD HH:mm:ss");
        let endDate = this.$moment(new Date());
        this.dateValue.push(
          this.$moment(startDate).format("YYYY-MM-DD") + " 00:00:00"
        );
        this.dateValue.push(
          this.$moment(endDate).format("YYYY-MM-DD") + ` ${this.nowEndTime}`
        );
        this.getCardCapitalRecordList();
      }
      //判断是否是单站，单站默认选中油站
      if (this.getCurrentStation.merchant_type == 1) {
        this.stationValue = this.getCurrentStation.merchant_id;
      }
    },
    //获取油站类别
    getStations() {
      this.$axios.post("/Stations/getStations", {}).then(res => {
        if (res.status == 200) {
          this.stationOptions = res.data.data.station_info;
        }
      });
    },
    //将油站id换算成油站名
    getStationName(id) {
      let stName = "";
      this.stationOptions.forEach(element => {
        if (element.stid == id) {
          stName = element.stname;
        }
      });
      return stName;
    },
    //获取账户类型
    getAccountType(type, cardNO) {
      let typeData = [];
      if (cardNO) {
        typeData = [
          { value: "BJ_MZ", label: "车队卡母账" },
          { value: "BJ_KZ", label: "子卡卡账" },
          { value: "XYED", label: "信用额度" },
          { value: "SKJ", label: "子卡赠金" }
        ];
      } else {
        typeData = [
          { value: "BJ_MZ", label: "车队卡母账" },
          { value: "BJ_KZ", label: "子卡卡账" },
          { value: "XYED", label: "信用额度" },
          { value: "SKJ", label: "母账赠金" }
        ];
      }
      let typeName = "";
      typeData.forEach(element => {
        if (element.value == type) {
          typeName = element.label;
        }
      });
      return typeName;
    },
    getTime(time) {
      return this.$moment(time).format("YYYY-MM-DD HH:mm:ss");
    },
    getCardCapitalRecordList() {
      this.loading = true;
      let data = {
        page: this.currentPage,
        page_size: this.pageSize,
        input_type: this.searchTypeVlaue,
        input: this.inputTxt,
        start_time: this.dateValue[0],
        end_time: this.dateValue[1],
        type: this.checkboxGroup,
        station_list: this.stationValue,
        mch_arr: this.userInfo.mch_arr
      };
      if (this.dateValue) {
        data.start_time = this.dateValue[0];
        data.end_time = this.dateValue[1];
      }
      if (this.dateValue[0].length == 10){
        this.dateValue[0] = this.dateValue[0] + " 00:00:00";
        data.start_time = this.dateValue[0]
      }

      if (this.dateValue[1].length == 10){
        if(this.dateValue[1] == this.$moment(new Date()).format('YYYY-MM-DD')){
          this.dateValue[1] = this.dateValue[1] + ` ${this.$moment().format('HH:mm:ss')}`
        }else{
          this.dateValue[1] = this.dateValue[1] + " 23:59:59";
        }
        data.end_time = this.dateValue[1]
      }

      if (this.typeClassValue == 4) {
        //单选得到的是字符串，接口需要数组
        console.log("判断", Object.prototype.toString.call(this.stationValue));
        if (
          Object.prototype.toString.call(this.stationValue) == "[object Number]"
        ) {
          data.station_list = [this.stationValue];
        }
      }
      this.$axios.post("/Card/getCardCapitalRecordList", data).then(res => {
        this.loading = false;
        this.tableData = []; //清空
        if (res.data.status == 200) {
          this.tableData = res.data.data.dt;
          this.total = res.data.data.TotalQty;
        } else {
          this.total = 0;
          this.$message.error(res.info);
        }
      });
    },
    changeBanciData(val) {
      if (this.typeClassValue == 4) {
        //根据开班日期
        this.$refs.banciRef.getBanci(val);
      } else {
        this.getCardCapitalRecordList();
      }
    },
    //改变类型
    changeData(val) {
      console.log("val", val);
      this.currentPage = 1; //默认第一页
      if(!this.dateValue){
        this.tableData = []
        return
      }
      this.getCardCapitalRecordList();
    },
    //changeType
    changeType(val) {
      this.currentPage = 1;
      this.checkboxGroup = val;
      this.getCardCapitalRecordList();
    },
    //切换页码
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getCardCapitalRecordList();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getCardCapitalRecordList();
    },
    //下载数据
    cardCapitalRecordDownload() {
      this.$axios
        .post("/Card/cardCapitalRecordDownload", {
          input_type: this.searchTypeVlaue,
          input: this.inputTxt,
          start_time: this.dateValue[0],
          end_time: this.dateValue[1],
          type: this.checkboxGroup,
          mch_arr: this.userInfo.mch_arr,
          station_list: Array.isArray(this.stationValue)
            ? this.stationValue
            : [this.stationValue]
        })
        .then(res => {
          if (res.data.status == 200) {
            this.showDownloadTips = true;
          } else {
            this.$message.error(res.data.info);
          }
        });
    }
  },
  watch: {
    getCurrentStation(newValue, oldValue) {
      if (newValue.merchant_type != 0 && newValue.value != oldValue.value) {
        this.getStations();
        this.stationValue = [];
        this.$nextTick(() => {
          if (this.getCurrentStation.merchant_type == 1) {
            this.stationValue.push(this.getCurrentStation.merchant_id);
          }
        });

        this.getCardCapitalRecordList();
      }
    },
    dateValue() {
      if(
        this.$moment(this.dateValue[1]).format('YYYY-MM-DD') === this.$moment().format('YYYY-MM-DD') &&
        this.$moment(this.dateValue[1]).unix() > this.$moment().unix()
      ){
        this.dateValue[1] = this.$moment().format('YYYY-MM-DD HH:mm:ss')
      }
    },
    //监听班次切换变化，让多选单选重新渲染，防止报错
    typeClassValue() {
      console.log("123");
      this.update = false;
      setTimeout(() => {
        this.update = true;
      }, 0);
    }
  }
};
</script>
<style scoped>
input[type="text"]:focus,
select:focus {
  outline: none;
}
.group {
  padding-bottom: 10px;
}
.select {
  display: flex;
  justify-content: space-between;
}

.classType {
  margin-bottom: 20px;
}
.lookClass,
.checkClass {
  display: inline-block;
}
.NoClass {
  font-size: 14px;
  color: #606266;
}
.capitalRecord .header {
  padding: 20px 0;
  display: block;
  border-bottom: 1px solid #ebeef5;
}
.capitalRecord .header .left {
  display: flex;
  align-items: center;
}
.capitalRecord .header .date-picker {
  margin-left: 32px;
}
.capitalRecord .header .right {
  margin-left: 32px;
}
.capitalRecord .header .right input {
  width: 220px;
  height: 36px;
  padding-left: 12px;
  line-height: 36px;
  border: 0;
  border-radius: 5px;
}
.capitalRecord .right .search-icon {
  padding: 8px 14px;
  font-size: 20px;
  color: #c4c4c4;
  cursor: pointer;
}
.capitalRecord .right .search-icon:hover {
  color: #32af50;
}

/* 页码 */
.page_content {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}
</style>
