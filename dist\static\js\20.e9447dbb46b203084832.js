webpackJsonp([20],{"47P+":function(t,e){},h1vq:function(t,e){},qiX8:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=a("Xxa5"),n=a.n(r),l=a("exGp"),o=a.n(l),i=a("Dd8w"),s=a.n(i),u=a("FZmr"),d=a("NYxO"),c={name:"Report",components:{DownloadTips:u.a},props:{report:""},data:function(){return{isTotalReportForm:!0,typeOptions:[{value:1,label:"按自然日期"},{value:4,label:"按开班日期"}],typeValue:1,dateValue:"",options:[],DailyTableData1:[],loading:!1,start_time:"",end_time:"",orderMaker:"",orderMakingTime:"",payWayList:[],arr:[],isGroup:!0,showDownloadTips:!1,isGroupSettle:Boolean}},mounted:function(){var t=localStorage.getItem("__userInfo__")||"";this.isTotalReportForm=426!=JSON.parse(t).group_id;var e=this.$moment().subtract(1,"days").format("YYYY-MM-DD");if(this.dateValue=e,void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.getCurrentStation&&2==this.getCurrentStation.merchant_type?this.isGroup=!0:this.isGroup=!1,this.changeDate()},computed:s()({},Object(d.c)({getCurrentStation:"getCurrentStation"})),methods:{changeTypeValue:function(){this.dateValue="",this.orderMakingTime="",this.DailyTableData1=[]},changeDate:function(){var t=this;t.dateValue?(t.loading=!0,t.$axios.post("/CardReport/getCardChargeReport",{type:t.typeValue,start_time:t.$moment(t.dateValue+" 00:00:00","YYYY-MM-DD HH:mm:ss").unix(),end_time:t.$moment(t.dateValue+" 23:59:59","YYYY-MM-DD HH:mm:ss").unix()}).then(function(e){t.DailyTableData1=[],t.loading=!1,200==e.data.status?t.$nextTick(function(){t.isGroupSettle=1==e.data.data.group_settle,t.payWayList=e.data.data.pay_way,t.DailyTableData1=e.data.data.charge,4==t.typeValue&&2==t.getCurrentStation.merchant_type?(t.setData1(t.DailyTableData1,"stname"),t.setData(t.DailyTableData1,"stname","banci"),t.setTable(t.DailyTableData1,"stname","banci")):4==t.typeValue&&(t.setData1(t.DailyTableData1,"banci"),t.setTable(t.DailyTableData1,"banci")),t.start_time=e.data.data.start_time,t.end_time=e.data.data.end_time,t.orderMakingTime=t.$moment().format("YYYY-MM-DD");var a=localStorage.getItem("__userInfo__");!a||""===a&&"undefined"===a||(t.orderMaker=JSON.parse(a).name),t.isGroupSettle&&2==t.getCurrentStation.merchant_type?(t.setData1(t.DailyTableData1,"organize"),t.setTable(t.DailyTableData1,"organize")):t.isGroupSettle&&3==t.getCurrentStation.merchant_type&&(t.setData1(t.DailyTableData1,"stname"),t.setTable(t.DailyTableData1,"stname"))}):t.$message({message:e.data.info,type:"error"})}).catch(function(t){})):(t.DailyTableData1=[],t.orderMakingTime="",t.orderMaker="")},clearData:function(){this.dateValue||(this.DailyTableData1=[],this.orderMakingTime="",this.orderMaker="")},printContent:function(){var t=document.querySelector("#myTable").innerHTML,e=document.body.innerHTML;document.body.innerHTML=t,document.getElementsByClassName("el-table__header")[0].style.width="100%",document.getElementsByClassName("el-table__header")[0].style["table-layout"]="auto",document.getElementsByClassName("el-table__body")[0].style.width="100%",document.getElementsByClassName("el-table__body")[0].style["table-layout"]="auto",window.print(),history.go(0),document.body.innerHTML=e},cardChargeDownload:function(){var t=this;this.$axios.get("/CardReport/cardChargeDownload",{params:{start_time:this.start_time,end_time:this.end_time,type:this.typeValue}}).then(function(e){200==e.data.status&&(t.showDownloadTips=!0)})},setTable:function(t,e,a){var r=[],n=0,l=[],o=0;t.forEach(function(i,s){0===s?(r.push(1),l.push(1)):String(i[e])&&String(i[e])==String(t[s-1][e])?(r[n]+=1,r.push(0),String(i[a])&&String(i[a])==String(t[s-1][a])?(l[o]+=1,l.push(0)):(l.push(1),o=s)):(r.push(1),n=s,l.push(1),o=s)});var i={};i[e]=r,i[a]=l,this.arr=[],this.arr.push(i)},setData:function(t,e,a){for(var r,n=t.length,l=0;l<n-1;l++)for(var o=l+1;o<n;o++)t[o][e]==t[l][e]&&t[o][a]==t[l][a]&&(r=t[l+1],t[l+1]=t[o],t[o]=r);return t},setData1:function(t,e){for(var a,r=t.length,n=0;n<r-1;n++)for(var l=n+1;l<r;l++)t[l][e]==t[n][e]&&(a=t[n+1],t[n+1]=t[l],t[l]=a);return t},objectSpanMethod:function(t){t.row,t.column;var e=t.rowIndex,a=t.columnIndex;if(this.isGroupSettle&&2==this.getCurrentStation.merchant_type&&0===a){var r=this.arr[0].organize[e];return{rowspan:r,colspan:r>0?1:0}}if(this.isGroupSettle&&3==this.getCurrentStation.merchant_type&&0===a){var n=this.arr[0].stname[e];return{rowspan:n,colspan:n>0?1:0}}if(4==this.typeValue&&2!=this.getCurrentStation.merchant_type&&0===a){var l=this.arr[0].banci[e];return{rowspan:l,colspan:l>0?1:0}}if(4==this.typeValue&&2==this.getCurrentStation.merchant_type){if(0===a){var o=this.arr[0].stname[e];return{rowspan:o,colspan:o>0?1:0}}if(1===a){var i=this.arr[0].banci[e];return{rowspan:i,colspan:i>0?1:0}}}}},watch:{getCurrentStation:function(t,e){var a=this;return o()(n.a.mark(function r(){var l;return n.a.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(0==t.merchant_type||t.value==e.value){r.next=9;break}return a.getCurrentStation&&2==a.getCurrentStation.merchant_type?a.isGroup=!0:a.isGroup=!1,r.next=4,a.$parent.getOldReport();case 4:if(l=window.sessionStorage.getItem("report"),console.log("report",l),0!=l){r.next=9;break}return r.next=9,a.changeDate();case 9:case"end":return r.stop()}},r,a)}))()}}},p={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading"}],staticClass:"report"},[a("div",{staticClass:"report-content"},[t.isGroupSettle?t._e():a("div",[a("el-radio-group",{on:{change:t.changeTypeValue},model:{value:t.typeValue,callback:function(e){t.typeValue=e},expression:"typeValue"}},t._l(t.typeOptions,function(e){return a("el-radio-button",{key:e.value,attrs:{label:e.value}},[t._v(t._s(e.label))])}),1)],1),t._v(" "),a("div",{staticClass:"content_header"},[a("div",{staticClass:"left"},[a("el-date-picker",{attrs:{type:"date",placeholder:"选择日期",format:"yyyy 年 MM 月 dd 日","value-format":"yyyy-MM-dd"},on:{change:t.clearData},model:{value:t.dateValue,callback:function(e){t.dateValue=e},expression:"dateValue"}}),t._v(" "),a("el-button",{attrs:{type:"primary",disabled:!t.dateValue},on:{click:t.changeDate}},[t._v("生成")])],1),t._v(" "),a("div",{staticClass:"right"},[a("el-button",{attrs:{type:"primary",disabled:0==t.DailyTableData1.length},on:{click:t.printContent}},[t._v("打印")]),t._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:t.isTotalReportForm,expression:"isTotalReportForm"}],attrs:{type:"primary",disabled:0==t.DailyTableData1.length},on:{click:t.cardChargeDownload}},[t._v("下载数据")])],1)]),t._v(" "),a("div",{attrs:{id:"myTable"}},[a("div",{staticClass:"tableData reportData"},[a("div",{staticClass:"report_title"},[t._v("储值卡充值资金日报表")]),t._v(" "),a("div",{staticClass:"report_header"},[t.isGroup?a("div",[t._v("集团名称："+t._s(t.getCurrentStation.label))]):a("div",[t._v("油站名称："+t._s(t.getCurrentStation.label))]),t._v(" "),a("div",[t._v("日期："+t._s(t.dateValue))]),t._v(" "),a("div",[t._v("单位：元")])]),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"table",attrs:{data:t.DailyTableData1,"span-method":t.objectSpanMethod,border:"",size:"small",align:"center"}},[2==t.getCurrentStation.merchant_type||3==t.getCurrentStation.merchant_type?a("el-table-column",{attrs:{align:"center",prop:"stname",label:"油站"}}):t._e(),t._v(" "),4==t.typeValue?a("el-table-column",{attrs:{align:"center",label:"班次",prop:"banci","min-width":"72px"}}):t._e(),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"source_name",label:"终端来源"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值"}},[a("el-table-column",{attrs:{align:"center",prop:"recharge_order_cnt",label:"充值笔数"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"pay_amt",label:"实付金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.pay_amt).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"discount_amt",label:"优惠金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.discount_amt).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"支付方式"}},[t.payWayList.length>0?t._l(t.payWayList,function(e){return a("el-table-column",{key:e.index,attrs:{align:"center",label:e.title,prop:e.prop},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v(t._s(Number(a.row[e.prop]).toFixed(2)))]}}],null,!0)})}):[t._v("\n                无\n              ")]],2),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"recharge_amt",label:"充值金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.recharge_amt).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"order_amt",label:"本金"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.order_amt).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"gift_amt",label:"赠金"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.gift_amt).toFixed(2)))]}}])})],1),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"退款"}},[a("el-table-column",{attrs:{align:"center",prop:"recharge_refund_order_cnt",label:"退款笔数"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"退款本金"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.recharge_refund_order_amt).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"退款赠金"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.recharge_refund_gift_amt).toFixed(2)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",prop:"recharge_refund_real_amt",label:"实退金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.recharge_refund_real_amt).toFixed(2)))]}}])})],1),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"到账金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(Number(e.row.received_amt).toFixed(2)))]}}])})],1)],1),t._v(" "),t._m(0),t._v(" "),a("div",{staticClass:"des_bottom"},[a("div",[t._v("制表人："+t._s(t.orderMaker))]),t._v(" "),a("div",[t._v("制表时间："+t._s(t.orderMakingTime))]),t._v(" "),a("div",[t._v("签字：")])])])]),t._v(" "),a("download-tips",{attrs:{showDownloadTips:t.showDownloadTips},on:{"update:showDownloadTips":function(e){t.showDownloadTips=e},"update:show-download-tips":function(e){t.showDownloadTips=e}}})],1)},staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"table_des"},[e("div",{staticClass:"table_des_text"},[e("p",[this._v("注:")]),this._v(" "),e("div",[e("p",[this._v("1.实付金额：车主充值实际支付的金额，实付金额+优惠金额=本金。")]),this._v(" "),e("p",[this._v("2.充值金额：实际计算到储值卡内的金额，本金+赠金=充值金额。")]),this._v(" "),e("p",[this._v("3.到账金额：油站实际到账的金额，充值实付金额-退款实退金额=到账金额。")])])])])}]};var _=a("VU/8")(c,p,!1,function(t){a("h1vq"),a("47P+")},"data-v-789ef093",null);e.default=_.exports}});