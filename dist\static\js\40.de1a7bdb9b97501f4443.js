webpackJsonp([40],{ai02:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=a("Dd8w"),i=a.n(s),n=a("NYxO"),o={name:"CustomerInvoices",data:function(){return{addDialogVisible:!1,deleteDialogVisible:!1,isAdd:!1,loading:!1,page:1,pageSize:10,totalCount:0,selectItem:{},company:"",taxNumber:"",taxAddress:"",taxPhone:"",bank:"",bankAccount:"",selectMotorcades:[],confirmDisabled:!0,motorcades:[],tableData:[]}},mounted:function(){if(void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.getInvoicesList(),this.getMotorcades()},computed:i()({},Object(n.c)({getCurrentStation:"getCurrentStation"})),methods:{getInvoicesList:function(){var t=this;t.loading=!0,t.$axios.get("/bp/invoices",{params:{page:t.page,pagesize:t.pageSize}}).then(function(e){t.loading=!1,200==e.data.status?(t.tableData=e.data.data.data,t.totalCount=parseInt(e.data.data.total)):t.$message({message:e.data.info,type:"error"})}).catch(function(e){t.loading=!1,t.$message.error("获取发票信息列表出错")})},getMotorcades:function(){var t=this;t.loading=!0,t.$axios.post("/oscard/getCompanies",{page:1,pagesize:999}).then(function(e){if(t.loading=!1,200==e.data.status){for(var a=[],s=0;s<e.data.data.length;s++){var i=e.data.data[s];i.id=i.ID,a.push(i)}t.motorcades=a}else t.$message({message:e.data.info,type:"error"})}).catch(function(e){t.loading=!1,t.$message.error("获取车队列表出错")})},addInvoicesAction:function(){for(var t=[],e=[],a=0;a<this.selectMotorcades.length;a++)for(var s=this.selectMotorcades[a],i=0;i<this.motorcades.length;i++){var n=this.motorcades[i];if(s==n.CompanyName){t.push(n.id),e.push(s);break}}t=t.join(","),e=e.join(",");var o=this;o.loading=!0,o.$axios.post("/bp/add",{title:o.company,snno:o.taxNumber,addr:o.taxAddress,phone:o.taxPhone,bank_name:o.bank,bank_no:o.bankAccount,company_ids:t,company_names:e}).then(function(t){o.loading=!1,200==t.data.status?o.getInvoicesList():o.$message({message:t.data.info,type:"error"})}).catch(function(t){o.loading=!1,o.$message.error("新增发票信息出错")})},editInvoicesAction:function(){for(var t=[],e=[],a=0;a<this.selectMotorcades.length;a++)for(var s=this.selectMotorcades[a],i=0;i<this.motorcades.length;i++){var n=this.motorcades[i];if(s==n.CompanyName){t.push(n.id),e.push(s);break}}t=t.join(","),e=e.join(",");var o=this;o.loading=!0,o.$axios.post("/bp/edit",{title:o.company,snno:o.taxNumber,addr:o.taxAddress,phone:o.taxPhone,bank_name:o.bank,bank_no:o.bankAccount,id:o.selectItem.id,company_ids:t,company_names:e}).then(function(t){o.loading=!1,200==t.data.status?o.getInvoicesList():o.$message({message:t.data.info,type:"error"})}).catch(function(t){o.loading=!1,o.$message.error("修改发票信息出错")})},deleInvoicesAction:function(){},handleCurrentChange:function(t){this.page=t,this.getInvoicesList()},handleSizeChange:function(t){this.pageSize=t,this.getInvoicesList()},addInvoices:function(){this.isAdd=!0,this.company="",this.taxNumber="",this.taxAddress="",this.taxPhone="",this.bank="",this.bankAccount="",this.selectMotorcades=[],this.addDialogVisible=!0},showEdit:function(t){this.isAdd=!1,this.selectItem=t,this.company=t.title,this.taxNumber=t.snno,this.taxAddress=t.addr,this.taxPhone=t.phone,this.bank=t.bank_name,this.bankAccount=t.bank_no,this.motorcade=[],this.selectMotorcades=t.company_names.length>0?t.company_names.split(","):[],this.addDialogVisible=!0},addConfirmAction:function(){0!=this.company.length&&0!=this.taxNumber.length&&0!=this.taxAddress.length&&0!=this.taxPhone.length&&0!=this.bank.length&&0!=this.bankAccount.length?(this.isAdd?this.addInvoicesAction():this.editInvoicesAction(),this.addDialogVisible=!1):this.$message.error("*号选项内容不能为空")},showDelete:function(t){this.deleteDialogVisible=!0,this.selectItem=t},deleteConfirmAction:function(){this.deleteDialogVisible=!1;var t=this;t.loading=!0,t.$axios.post("/bp/del",{id:t.selectItem.id,title:t.selectItem.title}).then(function(e){t.loading=!1,200==e.data.status?t.getInvoicesList():t.$message({message:e.data.info,type:"error"})}).catch(function(e){console.log(e),t.loading=!1,t.$message.error("删除出错")})},inputChange:function(){0==this.company.length||0==this.taxNumber.length||0==this.taxAddress.length||0==this.taxPhone.length||0==this.bank.length||this.bankAccount.length},transactionCellstyle:function(t){t.row,t.column,t.rowIndex,t.columnIndex;return"text-align:left"},headerStyle:function(t){t.row,t.column,t.rowIndex,t.columnIndex;return"text-align:left"}},watch:{getCurrentStation:function(t,e){0!=t.merchant_type&&t.value!=e.value&&(this.getInvoicesList(),this.getMotorcades())}}},l={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"customer"},[a("div",{staticClass:"header_add"},[a("el-button",{attrs:{type:"primary"},on:{click:t.addInvoices}},[t._v("添加发票信息")])],1),t._v(" "),a("div",{staticClass:"table_box"},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.tableData,"cell-style":t.transactionCellstyle,"header-cell-style":t.headerStyle}},[a("el-table-column",{attrs:{prop:"title",align:"left",label:"发票抬头"}}),t._v(" "),a("el-table-column",{attrs:{prop:"snno",label:"税号"}}),t._v(" "),a("el-table-column",{attrs:{prop:"addr",label:"税务登记地址"}}),t._v(" "),a("el-table-column",{attrs:{prop:"phone",label:"税务登记电话"}}),t._v(" "),a("el-table-column",{attrs:{prop:"bank_name",label:"开户银行"}}),t._v(" "),a("el-table-column",{attrs:{prop:"bank_no",label:"银行账号"}}),t._v(" "),a("el-table-column",{attrs:{prop:"company_names",label:"关联车队"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.showEdit(e.row)}}},[t._v("编辑")]),t._v(" "),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.showDelete(e.row)}}},[t._v("删除")])]}}])})],1)],1),t._v(" "),a("div",{staticClass:"page_content"},[a("el-pagination",{staticClass:"page_left",attrs:{"current-page":t.page,"page-size":t.pageSize,layout:"prev, pager, next",total:t.totalCount},on:{"current-change":t.handleCurrentChange}}),t._v(" "),a("el-pagination",{staticClass:"page_right",attrs:{"page-sizes":[10,15,20,30],"page-size":t.pageSize,layout:"total, sizes",total:t.totalCount},on:{"size-change":t.handleSizeChange}})],1),t._v(" "),a("el-dialog",{attrs:{visible:t.addDialogVisible,"close-on-click-modal":!1,width:"660px"},on:{"update:visible":function(e){t.addDialogVisible=e}}},[a("span",{staticClass:"dialog-footer",attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.isAdd?"添加发票信息":"编辑"))]),t._v(" "),a("div",[a("div",{staticClass:"add_item"},[a("div",{staticClass:"add_title"},[a("span",{staticClass:"add_mark"},[t._v("*")]),t._v("  发票抬头")]),t._v(" "),a("el-input",{staticClass:"add_input",attrs:{placeholder:"请输入发票抬头"},on:{input:t.inputChange},model:{value:t.company,callback:function(e){t.company=e},expression:"company"}})],1),t._v(" "),a("div",{staticClass:"add_item"},[a("div",{staticClass:"add_title"},[a("span",{staticClass:"add_mark"},[t._v("*")]),t._v("  纳税人识别号")]),t._v(" "),a("el-input",{staticClass:"add_input",attrs:{placeholder:"请输入纳税人识别号"},on:{input:t.inputChange},model:{value:t.taxNumber,callback:function(e){t.taxNumber=e},expression:"taxNumber"}})],1),t._v(" "),a("div",{staticClass:"add_item"},[a("div",{staticClass:"add_title"},[a("span",{staticClass:"add_mark"},[t._v("*")]),t._v("  税务登记地址")]),t._v(" "),a("el-input",{staticClass:"add_input",attrs:{placeholder:"请输入税务登记地址"},on:{input:t.inputChange},model:{value:t.taxAddress,callback:function(e){t.taxAddress=e},expression:"taxAddress"}})],1),t._v(" "),a("div",{staticClass:"add_item"},[a("div",{staticClass:"add_title"},[a("span",{staticClass:"add_mark"},[t._v("*")]),t._v("  税务登记电话")]),t._v(" "),a("el-input",{staticClass:"add_input",attrs:{placeholder:"请输入税务登记电话"},on:{input:t.inputChange},model:{value:t.taxPhone,callback:function(e){t.taxPhone=e},expression:"taxPhone"}})],1),t._v(" "),a("div",{staticClass:"add_item"},[a("div",{staticClass:"add_title"},[a("span",{staticClass:"add_mark"},[t._v("*")]),t._v("  开户银行")]),t._v(" "),a("el-input",{staticClass:"add_input",attrs:{placeholder:"请输入开户银行"},on:{input:t.inputChange},model:{value:t.bank,callback:function(e){t.bank=e},expression:"bank"}})],1),t._v(" "),a("div",{staticClass:"add_item"},[a("div",{staticClass:"add_title"},[a("span",{staticClass:"add_mark"},[t._v("*")]),t._v("  银行账号")]),t._v(" "),a("el-input",{staticClass:"add_input",attrs:{placeholder:"请输入银行账号"},on:{input:t.inputChange},model:{value:t.bankAccount,callback:function(e){t.bankAccount=e},expression:"bankAccount"}})],1),t._v(" "),a("div",{staticClass:"add_item"},[a("div",{staticClass:"add_title"},[t._v("  关联车队")]),t._v(" "),a("el-select",{attrs:{multiple:"",placeholder:"请选择车队（可多选）"},model:{value:t.selectMotorcades,callback:function(e){t.selectMotorcades=e},expression:"selectMotorcades"}},t._l(t.motorcades,function(t){return a("el-option",{key:t.id,attrs:{label:t.CompanyName,value:t.CompanyName}})}),1)],1)]),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:t.addConfirmAction}},[t._v("确 定")]),t._v(" "),a("el-button",{attrs:{size:"mini"},on:{click:function(e){t.addDialogVisible=!1}}},[t._v("取 消")])],1)]),t._v(" "),a("el-dialog",{attrs:{title:"提示","close-on-click-modal":!1,visible:t.deleteDialogVisible,width:"350px",center:""},on:{"update:visible":function(e){t.deleteDialogVisible=e}}},[a("div",{staticClass:"confirmTips"},[t._v("请确认是否删除发票信息")]),t._v(" "),a("div",{staticClass:"confirmTips"},[t._v(t._s(t.selectItem.title))]),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:t.deleteConfirmAction}},[t._v("确 定")]),t._v(" "),a("el-button",{attrs:{size:"mini"},on:{click:function(e){t.deleteDialogVisible=!1}}},[t._v("取 消")])],1)])],1)},staticRenderFns:[]};var c=a("VU/8")(o,l,!1,function(t){a("tJqb")},"data-v-32f60ec0",null);e.default=c.exports},tJqb:function(t,e){}});