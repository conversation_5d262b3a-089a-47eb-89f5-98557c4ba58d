<!-- 查询班次封装的组件 -->
<template>
  <div id="banciDateTime" v-loading="showLoading && loading">
    <el-date-picker
      v-model="value"
      value-format="yyyy-MM-dd"
      type="daterange"
      :clearable="false"
      range-separator="至"
      start-placeholder="开始日期"
      @change="changeDate"
      end-placeholder="结束日期"
      :picker-options="pickerOptions"
    >
    </el-date-picker>
    <el-select
      style="width:220px"
      v-model="banciStartValue"
      @change="changeStart"
      placeholder="请选择开班时间"
    >
      <el-option
        v-for="(item, index) in startOptions"
        :key="index"
        :label="item.label"
        :value="item.label"
        :disabled="item.disabled"
      >
        <span style="float: left">{{ item.value }}</span>
        <span
          v-if="item.label"
          style="float: right; color: #8492a6; font-size: 13px"
          >{{ item.label }}</span
        >
      </el-option>
    </el-select>
    <el-select
      style="width:200px"
      v-model="banciEndValue"
      @change="changeEnd"
      placeholder="请选择交班时间"
    >
      <el-option
        v-for="(item, index) in endOptions"
        :key="index"
        :label="item.label"
        :value="item.label"
        :disabled="item.disabled"
      >
        <span style="float: left">{{ item.value }}</span>
        <span style="float: right; color: #8492a6; font-size: 13px">{{
          item.label
        }}</span>
      </el-option>
    </el-select>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
export default {
  name: "banciDateTime",
  //传日期和油站id
  props: ["dateValue", "stationValue", "pickerOptions","showLoading"],
  data() {
    let that = this;
    return {
      loading:false,
      value: this.dateValue,
      stid: this.stationValue,
      banciStartValue: "",
      banciEndValue: "",
      startOptions: [
        {
          value: "请选择开班日期",
          label: "",
          disabled: true
        }
      ],
      endOptions: [
        {
          value: "请选择交班日期",
          label: "",
          disabled: true
        }
      ]
    };
  },
  watch: {
    ...mapGetters({
      getCurrentStation: "getCurrentStation"
    }),
    stationValue(newValue, oldValue) {
      if (Object.prototype.toString.call(newValue) == "[object Array]") {
        this.stid = newValue[0];
      } else {
        this.stid = newValue;
      }
    },
    dateValue(newValue) {
      this.value = newValue;
      if (this.value.length == 0) {
        this.banciStartValue = "";
        this.banciEndValue = "";
      }
    }
  },
  methods: {
    changeDate() {
      this.$emit("changeDate", this.value);
      if (this.stationValue) {
        this.getSettlementList();
      } else {
        this.$message.error("请选择油站");
      }
    },
    getBanci(val) {
      //父组件选择了油站后调用的方法
      if (this.value.length) {
        this.getSettlementList(val);
      }
    },
    //班次接口
    getSettlementList(stid) {
      if (stid) {
        this.stid = stid;
      }
      this.banciStartValue = "";
      this.banciEndValue = "";
      this.startOptions = [
        {
          value: "请选择开班时间",
          label: "",
          disabled: true
        }
      ];
      this.endOptions = [
        {
          value: "请选择交班时间",
          label: "",
          disabled: true
        }
      ];
      let data = {
        stid: this.stid,
        sdate: this.value[0],
        edate: this.value[1]
      };
      this.loading = true;
      //获取班次
      this.$axios.post("/CardCharge/getSettlementList", data).then(res => {
        if (res.data.status == 200) {
          //开班日期
          if (res.data.data.start) {
            res.data.data.start.forEach(item => {
              if (!item.bcmc.includes("班")) {
                item.bcmc += "班";
              }
              this.startOptions.push({
                label: item.stime,
                value: item.bcmc
              });
            });
            //班次默认选择第一个班
            this.banciStartValue = this.startOptions[1].label;
          } else {
            this.startOptions = [
              {
                value: "当天暂无班结记录",
                label: "",
                disabled: true
              }
            ];
          }
          //交班日期
          if (res.data.data.end) {
            res.data.data.end.forEach(item => {
              if (!item.bcmc.includes("班")) {
                item.bcmc += "班";
              }
              this.endOptions.push({
                label: item.etime,
                value: item.bcmc
              });
            });
            this.banciEndValue = this.endOptions[1].label;
          } else {
            this.endOptions = [
              {
                value: "当天暂无班结记录",
                label: "",
                disabled: true
              }
            ];
          }

          //选完班结日期默认调用父组件的获取列表信息
          let banciValue = [this.banciStartValue, this.banciEndValue];
          if (this.banciStartValue || this.banciEndValue) {
            if (!this.banciStartValue) banciValue[0] = this.value[0];
            if (!this.banciEndValue) banciValue[1] = this.value[1];
            this.$emit("searchDate", banciValue);
            this.$emit("classTimeChange", banciValue);
          } else {
            this.$emit("searchDate", this.value);
            this.$emit("classTimeChange", this.value);
          }
        } else {
          this.$message.error(res.data.info);
        }
      }).finally(()=>{
        this.loading = false;
      });
    },
    //修改开班时间
    changeStart(e) {
      let banciValue = [];
      if (this.banciEndValue) {
        banciValue = [e, this.banciEndValue];
      } else {
        banciValue = [e, this.value[1]];
      }
      this.$emit("searchDate", banciValue);
      this.$emit("classTimeChange", banciValue);
    },
    //修改交班时间
    changeEnd(e) {
      let banciValue = [];
      if (this.banciStartValue) {
        banciValue = [this.banciStartValue, e];
      } else {
        banciValue = [this.value[0], e];
      }
      this.$emit("searchDate", banciValue);
      this.$emit("classTimeChange", banciValue);
    },
    clearDate() {
      this.startOptions = [
        {
          value: "请选择开班时间",
          label: "",
          disabled: true
        }
      ];
      this.endOptions = [
        {
          value: "请选择交班时间",
          label: "",
          disabled: true
        }
      ];
      this.banciStartValue = "";
      this.banciEndValue = "";
      this.value = [];
    }
  }
};
</script>
<style>
#banciDateTime {
  display: inline-block;
}
#banciDateTime .el-scrollbar {
  width: 220px;
}
#banciDateTime .el-date-editor {
  width: 230px;
}
li.el-select-dropdown__item.is-disabled {
  color: #333 !important;
}
/* #banciDateTime .el-range-editor input{
        display: none;
    }
    #banciDateTime .el-range-editor span{
        width: 120px;
    }
    #banciDateTime .el-date-editor--daterange.el-input__inner{
        width: 150px;
    } */
</style>
