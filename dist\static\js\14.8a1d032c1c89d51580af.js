webpackJsonp([14],{DH9m:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=i("Xxa5"),o=i.n(a),s=i("mvHQ"),n=i.n(s),l=i("exGp"),r=i.n(l),c=i("Dd8w"),d=i.n(c),p=i("FZmr"),u=i("V2BW"),m=i("NYxO"),v=i("M4fF"),f={name:"RegularInvoice",components:{DownloadTips:p.a,SelectPlus:u.a},data:function(){return{selectRow:{},invoiceText:"开票",invoiceRuleform:{TradeID:"",totalAmt:"",amt:"",sign:"1",time:"",remark:""},rules:{amt:[{required:!0,validator:function(e,t,i){/^\d+(\.\d{1,2})?$/.test(t)&&0!=t?i():i(new Error("请输入正确的开票金额，最多保留两位小数"))},trigger:"blur"}],time:[{required:!0,message:"请选择标记时间",trigger:["blur","change"]}]},disabledInvoice:!1,infoData:[],infoLoading:!1,infoPage:1,infoSize:10,TotalQty:0,isTotalReportForm:!0,loading:!1,selectStatus:"全部",selectDate:[],radio:1,pageSize:10,currentPage:1,totalCount:0,inputValue:"",tableData:[],radio1:0,edit_date:null,textarea:"",dateDisable:!0,isMulti:!1,selectItem:null,selectItems:[],confirmDialogVisible:!1,showDownloadTips:!1,editDialogVisible:!1,invoiceDialogVisible:!1,infoDialogVisible:!1,pickerOptions:{disabledDate:function(e){return e.getTime()>Date.now()}},typeRadio:2,fpstate:0,bpGroupId:1,currentGroupId:0,selectedCompanies:[]}},computed:d()({},Object(m.d)(["companyListLoading"]),Object(m.c)({getCurrentStation:"getCurrentStation",companyArray:"companyArray"})),mounted:function(){var e=localStorage.getItem("__userInfo__")||"";this.isTotalReportForm=426!=JSON.parse(e).group_id;var t=this.$moment(new Date).subtract(1,"months").format("YYYY-MM-DD HH:mm:ss"),i=this.$moment(new Date);if(this.selectDate.push(this.$moment(t).format("YYYY-MM-DD")+" 00:00:00"),this.selectDate.push(this.$moment(i).format("YYYY-MM-DD")+" 23:59:59"),void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;-1!=window.location.href.indexOf("card-admin.zhihuiyouzhan.com")?this.bpGroupId=1374:this.bpGroupId=1,this.getInvoiceList({actionType:1}),this.getSimpleCompanyList(),this.debouncedSearchAction=Object(v.debounce)(this.searchAction,300)},methods:d()({},Object(m.b)(["getSimpleCompanyList"]),{modifyConfirm:function(e){1==e&&this.editConfirmAction(),this.confirmDialogVisible=!1,this.editDialogVisible=!1},invoiceStatusChange:function(e){this.dateDisable=0==e},invoiceRow:function(e){console.log("开票",e),this.selectRow=e,this.invoiceRuleform.TradeID=e.TradeID,this.invoiceRuleform.totalAmt="",this.invoiceDialogVisible=!0,this.invoiceRuleform.time=this.$moment().format("YYYY-MM-DD HH:mm:ss"),this.getInfoList()},sureInvoice:function(){var e,t=this;this.$refs.invoiceRuleform.validate((e=r()(o.a.mark(function e(i){var a,s,l;return o.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(i){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,t.disabledInvoice=!0,(a={}).TradeID=t.invoiceRuleform.TradeID,a.Money=t.invoiceRuleform.amt,a.bp_invoice_open_time=t.$moment(t.invoiceRuleform.time).unix(),a.bp_invoice_summ=t.invoiceRuleform.remark,a.is_bp_has_invoice=t.invoiceRuleform.sign,a.codes={},s=t.selectRow.TradeID,a.codes[s]=n()({stid:t.selectRow.StationNO,pay_time:t.selectRow.TradeTime}),e.next=15,t.$axios.post("/bp/repeatInvoices",a);case 15:if(200==(l=e.sent).data.status){e.next=18;break}return e.abrupt("return",t.$message.error(l.data.info));case 18:t.$message.success("开票标记成功"),t.invoiceDialogVisible=!1,t.searchAction(),e.next=26;break;case 23:e.prev=23,e.t0=e.catch(2),t.$message.error("网络错误");case 26:return e.prev=26,t.disabledInvoice=!1,e.finish(26);case 29:case"end":return e.stop()}},e,t,[[2,23,26,29]])})),function(t){return e.apply(this,arguments)}))},closeInvoice:function(){this.invoiceRuleform={TradeID:"",totalAmt:"",amt:"",sign:"1",time:"",remark:""},this.$refs.invoiceRuleform.resetFields()},infoRow:function(e){console.log("查看",e),this.infoDialogVisible=!0,this.infoPage=1,this.infoSize=10,this.selectRow=e,this.getInfoList()},getInfoList:function(){var e=this;return r()(o.a.mark(function t(){var i;return o.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,e.infoLoading=!0,t.next=4,e.$axios.post("/bp/getInvoiceHistoryList",{TradeID:e.selectRow.TradeID,page:e.infoPage,pagesize:e.infoSize});case 4:if(200==(i=t.sent).data.status){t.next=7;break}return t.abrupt("return",e.$message.error(i.data.info));case 7:e.invoiceRuleform.totalAmt=i.data.data.InvoiceMoney,e.selectRow.UninvoicedMoney=i.data.data.UninvoicedMoney,e.infoData=i.data.data.AccountInvoiceList,e.infoData.forEach(function(t){t.FPTime&&(t.FPTime=e.$moment(t.FPTime).format("YYYY-MM-DD HH:mm:ss"))}),e.TotalQty=i.data.data.TotalQty,t.next=17;break;case 14:t.prev=14,t.t0=t.catch(0),e.$message.error("网络错误");case 17:return t.prev=17,e.infoLoading=!1,t.finish(17);case 20:case"end":return t.stop()}},t,e,[[0,14,17,20]])}))()},editRow:function(e){"CZ"!=e.Type?(this.selectItem=e,this.isMulti=!1,this.editDialogVisible=!0,e.is_bp_has_invoice?(this.dateDisable=!1,this.radio1=1):(this.dateDisable=!0,this.radio1=0),e.bp_invoice_open_time?this.edit_date=e.bp_invoice_open_time.substring(0,10):this.edit_date=null,this.textarea=e.bp_invoice_summ):this.invoiceRow(e)},searchAction:function(){this.currentPage=1,this.getInvoiceList({actionType:3})},editConfirm:function(){0==this.radio1?this.isMulti?this.selectItems[0].is_bp_has_invoice?this.confirmDialogVisible=!0:this.editConfirmAction():this.selectItem.is_bp_has_invoice?this.confirmDialogVisible=!0:this.editConfirmAction():this.editConfirmAction()},editConfirmAction:function(){var e=this;if(this.edit_date||this.dateDisable){var t={};if(this.isMulti)for(var i=0;i<this.selectItems.length;i++){var a=this.selectItems[i];t[a.TradeID]=n()({stid:a.StationNO,pay_time:a.TradeTime})}else t[this.selectItem.TradeID]=n()({stid:this.selectItem.StationNO,pay_time:this.selectItem.TradeTime});var o=e.edit_date?e.$moment(e.edit_date).valueOf()/1e3:"";0==this.radio1&&(o="");var s={is_bp_has_invoice:this.radio1,bp_invoice_open_time:o,bp_invoice_summ:this.textarea};e.loading=!0,this.editDialogVisible=!1,e.$axios.post("/bp/oinvoicesAdd",{codes:t,exts:s}).then(function(t){200==t.data.status?setTimeout(function(){e.loading=!1,e.getInvoiceList({actionType:4})},3e3):(e.loading=!1,e.$message({message:t.data.info,type:"error"}))}).catch(function(t){e.loading=!1,e.$message.error("修改出错")})}else e.$message.error("请选择开票时间")},getInvoiceList:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=this,i="";"充值"==t.selectStatus?i="CZ":"消费"==t.selectStatus&&(i="XF");var a=t.selectDate?t.$moment(t.selectDate[0]).format("YYYY-MM-DD HH:mm:ss"):"",o=t.selectDate?t.$moment(t.selectDate[1]).format("YYYY-MM-DD HH:mm:ss"):"";t.loading=!0,t.$axios.post("/bp/oinvoices",d()({fptype:0,qtype:t.radio,cztype:i,query:t.inputValue,page:t.currentPage,start:a,end:o,pagesize:t.pageSize,fpstatus:t.typeRadio,fpstate:t.fpstate,CompanyIds:t.selectedCompanies},e)).then(function(e){if(t.loading=!1,200==e.data.status){for(var i=e.data.data.dt,a=0;a<i.length;a++){var o=i[a];o.bp_invoice_open_time?o.open_time=o.bp_invoice_open_time.substring(0,10):o.open_time=""}t.totalCount=e.data.data.TotalQty,t.currentGroupId=e.data.data.group_id,t.tableData=i}else t.$message({message:e.data.info,type:"error"})}).catch(function(e){t.loading=!1,t.$message.error("获取列表出错")})},outputAction:function(){var e=this,t="";"充值"==e.selectStatus?t="CZ":"消费"==e.selectStatus&&(t="XF");var i=e.selectDate?e.$moment(e.selectDate[0]).format("YYYY-MM-DD HH:mm:ss"):"",a=e.selectDate?e.$moment(e.selectDate[1]).format("YYYY-MM-DD HH:mm:ss"):"";e.loading=!0,e.$axios.get("/bp/export",{params:{fptype:0,qtype:e.radio,cztype:t,query:e.inputValue,page:e.currentPage,start:i,end:a,pagesize:e.pageSize,name:"普通发票",fpstatus:e.typeRadio,fpstate:e.fpstate,CompanyIds:e.selectedCompanies}}).then(function(t){e.loading=!1,200==t.data.status?e.showDownloadTips=!0:e.$message({message:t.data.info,type:"error"})}).catch(function(t){e.$message({message:"导出出错",type:"error"}),e.loading=!1})},handleCurrentChange:function(e){this.currentPage=e,this.getInvoiceList({actionType:5})},handleSizeChange:function(e){this.pageSize=e,this.getInvoiceList({actionType:6})},infoCurrentChange:function(e){this.infoPage=e,this.getInfoList()},infoSizeChange:function(e){this.infoSize=e,this.getInfoList()},multiEdit:function(){if(0!=this.selectItems.length)if(1!=this.selectItems.length){for(var e=this.selectItems[0].is_bp_has_invoice,t=!1,i=1;i<this.selectItems.length;i++){if(this.selectItems[i].is_bp_has_invoice!=e){t=!0;break}}t?this.$message.error("选中订单开票状态不一致"):(this.isMulti=!0,this.selectItem=null,this.editDialogVisible=!0,e?(this.radio1=1,this.dateDisable=!1):(this.radio1=0,this.dateDisable=!0))}else this.editRow(this.selectItems[0]);else this.$message.error("请至少选择一条记录")},handleSelectionChange:function(e){this.selectItems=e},checkSelectSet:function(e,t){return"充值"!==this.selectStatus||0==e.is_bp_has_invoice},transactionCellstyle:function(e){e.row,e.column,e.rowIndex;if(11!=e.columnIndex)return"text-align:center"},headerStyle:function(e){e.row,e.column,e.rowIndex;if(11!=e.columnIndex)return"text-align:center"},getDisabledInvoice:function(){1==this.fpstate?this.fpstate=0:this.fpstate=1,this.typeRadio=2,this.currentPage=1,this.getInvoiceList({actionType:7})},getAllData:function(){this.fpstate=0,this.currentPage=1,this.getInvoiceList({actionType:8})}}),watch:{getCurrentStation:function(e,t){0!=e.merchant_type&&e.value!=t.value&&this.getInvoiceList({actionType:2})}}},h={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"regular"},[a("div",{staticClass:"header_kind"},[a("el-radio-group",{on:{change:e.searchAction},model:{value:e.selectStatus,callback:function(t){e.selectStatus=t},expression:"selectStatus"}},[a("el-radio-button",{attrs:{label:"全部"}}),e._v(" "),a("el-radio-button",{attrs:{label:"充值"}}),e._v(" "),a("el-radio-button",{attrs:{label:"消费"}})],1),e._v(" "),a("el-date-picker",{staticClass:"header_datePick",attrs:{type:"datetimerange","default-time":["00:00:00","23:59:59"],"range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:e.searchAction},model:{value:e.selectDate,callback:function(t){e.selectDate=t},expression:"selectDate"}}),e._v(" "),a("span",{staticClass:"comany_label"},[e._v("车队名称")]),e._v(" "),a("select-plus",{directives:[{name:"loading",rawName:"v-loading",value:e.companyListLoading,expression:"companyListLoading"}],staticClass:"comany_select",attrs:{checkAll:!1,placeholder:"请选择车队",list:e.companyArray,multiple:"",filterable:"",clearable:"","collapse-tags":"",attr:{label:"CompanyName",value:"ID",getLabel:function(e){return"ID "+e.ID+" "+e.CompanyName}}},on:{input:e.debouncedSearchAction},model:{value:e.selectedCompanies,callback:function(t){e.selectedCompanies=t},expression:"selectedCompanies"}})],1),e._v(" "),a("p",{staticClass:"invoice_notice"},[e._v("显示个人卡充值订单、消费订单和车队充值订单。")]),e._v(" "),a("div",{staticClass:"header_search"},[a("div",{staticClass:"header_search_left"},[a("div",{staticClass:"search_title"},[e._v("查询客户")]),e._v(" "),a("div",[a("el-radio-group",{model:{value:e.radio,callback:function(t){e.radio=t},expression:"radio"}},[a("el-radio",{attrs:{label:1}},[e._v("手机号")]),e._v(" "),a("el-radio",{attrs:{label:2}},[e._v("卡号")]),e._v(" "),a("el-radio",{attrs:{label:3}},[e._v("卡面卡号")])],1)],1),e._v(" "),a("el-input",{staticClass:"search_input",staticStyle:{width:"210px"},attrs:{placeholder:"2"==e.radio?"请输入卡号":"1"==e.radio?"请输入手机号":"请输入卡面卡号",clearable:""},model:{value:e.inputValue,callback:function(t){e.inputValue=t},expression:"inputValue"}}),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.searchAction}},[e._v("查询")])],1),e._v(" "),a("div",[e.currentGroupId==e.bpGroupId?a("span",{staticClass:"invoice-btn",class:{active:1==e.fpstate},on:{click:e.getDisabledInvoice}},[e._v("已开票退款订单")]):e._e(),e._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:e.isTotalReportForm,expression:"isTotalReportForm"}],staticClass:"output_btn",attrs:{type:"primary"},on:{click:e.outputAction}},[e._v("下载数据")])],1)]),e._v(" "),0===e.fpstate?a("div",{staticClass:"edit_box"},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){e.invoiceText="批量开票",e.multiEdit()}}},[e._v("批量开票")]),e._v(" "),a("div",{staticClass:"edit_des"},[e._v("选中"),a("span",{staticClass:"count"},[e._v(e._s(e.selectItems.length))]),e._v("条数据")]),e._v(" "),a("el-radio-group",{on:{change:e.searchAction},model:{value:e.typeRadio,callback:function(t){e.typeRadio=t},expression:"typeRadio"}},[a("el-radio",{attrs:{label:2}},[e._v("全部")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("仅看未开票")]),e._v(" "),a("el-radio",{attrs:{label:1}},[e._v("仅看已开票")]),e._v(" "),a("el-radio",{attrs:{label:3}},[e._v("仅看部分开票")])],1)],1):a("div",{staticClass:"edit_box"},[a("el-button",{attrs:{size:"mini"},on:{click:e.getAllData}},[e._v("返回")])],1),e._v(" "),a("div",{staticClass:"table_box"},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData,"cell-style":e.transactionCellstyle,"header-cell-style":e.headerStyle},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{fixed:"left",type:"selection",width:"55"}}),e._v(" "),a("el-table-column",{attrs:{prop:"StationName",label:"所属油站",width:"160"}}),e._v(" "),a("el-table-column",{attrs:{prop:"TradeID",label:"订单号",width:"200"}}),e._v(" "),a("el-table-column",{attrs:{prop:"CardNO",label:"卡号",width:"200"}}),e._v(" "),a("el-table-column",{attrs:{prop:"Phone",label:"手机号",width:"120",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{attrs:{prop:"CompanyName",label:"车队名称",width:"160"}}),e._v(" "),a("el-table-column",{attrs:{prop:"CardNumber",label:"卡面卡号",width:"100",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{attrs:{prop:"address",label:"操作类型"},scopedSlots:e._u([{key:"default",fn:function(t){return a("div",{},[a("span",[e._v(e._s("CZ"==t.row.Type?"充值":"消费"))])])}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"bp_has_invoice_text",label:"是否开票"}}),e._v(" "),a("el-table-column",{attrs:{prop:"open_time",label:"开票时间",width:"110",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{attrs:{prop:"pay_money",width:"120",label:"实付金额（元）"}}),e._v(" "),a("el-table-column",{attrs:{prop:"SFBJ",width:"120",label:"实付本金（元）"}}),e._v(" "),a("el-table-column",{attrs:{prop:"SKJ",width:"120",label:"实付赠金（元）"}}),e._v(" "),a("el-table-column",{attrs:{prop:"TradeTime",label:"交易时间",width:"160"}}),e._v(" "),a("el-table-column",{attrs:{prop:"bp_invoice_summ",label:"备注",width:"350",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{attrs:{fixed:"right",width:"充值"==e.selectStatus?120:80,label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return["充值"!=e.selectStatus?a("el-button",{attrs:{type:"text",size:"small"},nativeOn:{click:function(i){i.preventDefault(),e.invoiceText="开票",e.editRow(t.row)}}},[e._v("开票\n                    ")]):[a("el-button",{attrs:{type:"text",size:"small"},nativeOn:{click:function(i){return i.preventDefault(),e.invoiceRow(t.row)}}},[e._v("开票\n                      ")]),e._v(" "),a("el-button",{attrs:{type:"text",size:"small"},nativeOn:{click:function(i){return i.preventDefault(),e.infoRow(t.row)}}},[e._v("查看")])]]}}])})],1)],1),e._v(" "),a("div",{staticClass:"page_content"},[a("el-pagination",{staticClass:"page_left",attrs:{"current-page":e.currentPage,"page-size":e.pageSize,layout:"prev, pager, next",total:e.totalCount},on:{"current-change":e.handleCurrentChange}}),e._v(" "),a("el-pagination",{staticClass:"page_right",attrs:{"page-sizes":[10,15,20,30],"page-size":e.pageSize,layout:"total, sizes",total:e.totalCount},on:{"size-change":e.handleSizeChange}})],1),e._v(" "),a("el-dialog",{attrs:{title:e.invoiceText,visible:e.editDialogVisible,width:"500px","close-on-click-modal":!1},on:{"update:visible":function(t){e.editDialogVisible=t}}},[a("div",[e.isMulti?a("div",{staticClass:"edit_count"},[e._v("已选中"),a("span",{staticClass:"select_count"},[e._v(e._s(e.selectItems.length))]),e._v("笔订单")]):e._e(),e._v(" "),a("div",{staticClass:"edit_item"},[a("div",{staticClass:"edit_title"},[e._v("是否开票")]),e._v(" "),a("div",[a("el-radio-group",{attrs:{disabled:1==e.fpstate},on:{change:e.invoiceStatusChange},model:{value:e.radio1,callback:function(t){e.radio1=t},expression:"radio1"}},[a("el-radio",{attrs:{label:0}},[e._v("未开票")]),e._v(" "),a("el-radio",{attrs:{label:1}},[e._v("已开票")])],1)],1)]),e._v(" "),a("div",{staticClass:"edit_item"},[a("div",{staticClass:"edit_title"},[1==e.radio1?a("img",{staticClass:"mark_icon",attrs:{src:i("ooDP")}}):e._e(),e._v("\n                    开票时间\n                ")]),e._v(" "),a("div",[a("el-date-picker",{attrs:{disabled:1==e.fpstate||e.dateDisable,"picker-options":e.pickerOptions,type:"date",placeholder:"请选择开票时间"},model:{value:e.edit_date,callback:function(t){e.edit_date=t},expression:"edit_date"}})],1)]),e._v(" "),a("div",{staticClass:"edit_item edit_input"},[a("div",{staticClass:"edit_title"},[e._v("备注")]),e._v(" "),a("div",[a("el-input",{staticClass:"textInput",attrs:{disabled:1==e.fpstate,type:"textarea",rows:4,maxlength:"180","show-word-limit":"",placeholder:"请输入内容"},model:{value:e.textarea,callback:function(t){e.textarea=t},expression:"textarea"}})],1)])]),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:e.editConfirm}},[e._v("确 定")]),e._v(" "),a("el-button",{attrs:{size:"mini"},on:{click:function(t){e.editDialogVisible=!1}}},[e._v("取 消")])],1)]),e._v(" "),a("el-dialog",{attrs:{title:"提示",visible:e.confirmDialogVisible,width:"350px","close-on-click-modal":!1,center:""},on:{"update:visible":function(t){e.confirmDialogVisible=t}}},[a("div",{staticClass:"confirmTips"},[e._v('请确认是否将"已开票"修改为"未开票？"')]),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.modifyConfirm(1)}}},[e._v("确 定")]),e._v(" "),a("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.modifyConfirm(2)}}},[e._v("取 消")])],1)]),e._v(" "),a("download-tips",{attrs:{showDownloadTips:e.showDownloadTips},on:{"update:showDownloadTips":function(t){e.showDownloadTips=t},"update:show-download-tips":function(t){e.showDownloadTips=t}}}),e._v(" "),a("el-dialog",{attrs:{title:"开票",visible:e.invoiceDialogVisible,width:"500px","modal-append-to-body":"","close-on-click-modal":!1},on:{"update:visible":function(t){e.invoiceDialogVisible=t},close:e.closeInvoice}},[a("el-form",{ref:"invoiceRuleform",attrs:{model:e.invoiceRuleform,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"订单号:",prop:"TradeID"}},[a("span",[e._v(e._s(e.invoiceRuleform.TradeID))])]),e._v(" "),a("el-form-item",{attrs:{label:"当前可开票金额:",prop:"totalAmt"}},[a("span",[e._v(e._s(e.invoiceRuleform.totalAmt))])]),e._v(" "),a("el-form-item",{attrs:{label:"已开票金额:"}},[a("span",[e._v(e._s(e.selectRow.UninvoicedMoney))])]),e._v(" "),a("el-form-item",{attrs:{label:"开票金额",prop:"amt"}},[a("el-input",{staticStyle:{width:"220px"},attrs:{placeholder:"请输入开票金额"},model:{value:e.invoiceRuleform.amt,callback:function(t){e.$set(e.invoiceRuleform,"amt",t)},expression:"invoiceRuleform.amt"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"开票标记",prop:"sign",required:""}},[a("el-radio",{attrs:{label:"1"},model:{value:e.invoiceRuleform.sign,callback:function(t){e.$set(e.invoiceRuleform,"sign",t)},expression:"invoiceRuleform.sign"}},[e._v("已开票")]),e._v(" "),a("el-radio",{attrs:{label:"0",disabled:0==e.selectRow.UninvoicedMoney},model:{value:e.invoiceRuleform.sign,callback:function(t){e.$set(e.invoiceRuleform,"sign",t)},expression:"invoiceRuleform.sign"}},[e._v("未开票")])],1),e._v(" "),a("el-form-item",{attrs:{label:"开票时间",prop:"time"}},[a("el-date-picker",{attrs:{"picker-options":e.pickerOptions,"default-time":"23:59:59",type:"datetime",placeholder:"请选择标记时间"},model:{value:e.invoiceRuleform.time,callback:function(t){e.$set(e.invoiceRuleform,"time",t)},expression:"invoiceRuleform.time"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{staticStyle:{width:"220px"},attrs:{type:"textarea",placeholder:"请输入内容","show-word-limit":"",maxlength:"110",rows:2},model:{value:e.invoiceRuleform.remark,callback:function(t){e.$set(e.invoiceRuleform,"remark",t)},expression:"invoiceRuleform.remark"}})],1)],1),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini",type:"primary",disabled:e.disabledInvoice},on:{click:e.sureInvoice}},[e._v("确 定")]),e._v(" "),a("el-button",{attrs:{size:"mini"},on:{click:function(t){e.invoiceDialogVisible=!1}}},[e._v("取 消")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"历史记录查看",visible:e.infoDialogVisible,width:"1000px","modal-append-to-body":"","close-on-click-modal":!1},on:{"update:visible":function(t){e.infoDialogVisible=t}}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.infoLoading,expression:"infoLoading"}]},[a("div",{staticClass:"infoOut"},[a("div",{staticClass:"infoInner"},[e._v("实付金额：￥"+e._s(e.selectRow.pay_money))]),e._v(" "),a("div",{staticClass:"infoInner"},[e._v("卡号："+e._s(e.selectRow.CardNO))])]),e._v(" "),a("div",{staticClass:"infoOut"},[a("div",{staticClass:"infoInner"},[e._v("已开票金额：￥"+e._s(e.selectRow.UninvoicedMoney))]),e._v(" "),a("div",{staticClass:"infoInner"},[e._v("剩余可开票金额：￥"+e._s(e.invoiceRuleform.totalAmt))])]),e._v(" "),a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.infoData,border:"","cell-style":{textAlign:"center"},"header-cell-style":{textAlign:"center"}}},[a("el-table-column",{attrs:{type:"index",label:"序号",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s((e.infoPage-1)*e.infoSize+t.$index+1))])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"FPTime",label:"开票时间"}}),e._v(" "),a("el-table-column",{attrs:{prop:"FPTag",label:"开票标记"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(1==t.row.FPTag?"已开票":"未开票"))])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"FPMoney",label:"开票金额"}}),e._v(" "),a("el-table-column",{attrs:{prop:"FPRemainderMoney",label:"剩余开票金额"}}),e._v(" "),a("el-table-column",{attrs:{prop:"Remark",label:"备注",formatter:e.formatterCellval}})],1)],1),e._v(" "),a("div",{staticStyle:{display:"flex","justify-content":"space-between","margin-top":"20px"}},[a("el-pagination",{attrs:{"current-page":e.infoPage,"page-sizes":[10,20,30,50],"page-size":e.infoSize,layout:"prev, pager, next",total:e.TotalQty},on:{"size-change":e.infoSizeChange,"current-change":e.infoCurrentChange}}),e._v(" "),a("el-pagination",{attrs:{"current-page":e.infoPage,"page-sizes":[10,20,30,50],"page-size":e.infoSize,layout:"total, sizes",total:e.TotalQty},on:{"size-change":e.infoSizeChange,"current-change":e.infoCurrentChange}})],1)])],1)},staticRenderFns:[]};var _=i("VU/8")(f,h,!1,function(e){i("F4J2")},"data-v-a53a57c0",null);t.default=_.exports},F4J2:function(e,t){},ooDP:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAACJklEQVQ4T5WRy08TYRTFz5kZraQpHSKuOg0xbrpBURqJj2A7EJ9x69oQg8aVsnBjTEiMCzd2pwEluvYPwEfSVoxoRKuCG1yhMF2YQJjSEGnofNfMtJoi1IS7u/fc+8v5vkP8p35E0sd8uaOcn2y2xmZCMZrqB/Xnvi6iTlmlfHar3aaAhWh6UNO0Ef9IKRmMl3IPtwdotc9oOscDgCdn4yu5Z9sCFM3eg8COj7Wj9WTMff15S8D3qH3IIO8C4sDzRq3yxDt/0WlJWdylzwZ/sOYlrF+vnGAeOXEEunEZQIxYv8EFs29MIwYCukhViJeA3K9g5U0I5ld/XIHbGULrcYBXKTgJ0qjtqzEW2+zzAjwgGNtgUaQg4F5/RsgcyO5GXSBFQF0JUnAih3d7CCcMQ+sSkSSBTpAJAOH60SpEZgWYIVmoVtUXHauzVnlqacsYp7E/3G62j4Psrb1MJpbcxXMHMLP670duAjwF9KOm/YjkRQAv/HsAp0XkyVs3d+kC4DVCNgEc086QvCaCCqB6hJpQMEUiJCIZy80NNQPQMe2bJG8HtoFRaznrxwWnrW+EwGD9ObcsN3en7gx/HcxHU0ld0z/U41xcY6V73/LkvN8XW3rjCBmfQLb7fVWpZEcpX6glVK+5SCqx09DfA2gVUUOWm880WnXM9HVSuydAiVXVEyvnv20ABFbNVJcn+p7HpWx2GFCNgGFAG4ja/Rrlp+Xmp/9ovwETrNLINPJrWQAAAABJRU5ErkJggg=="}});