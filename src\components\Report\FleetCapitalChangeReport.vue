<template>
  <div v-loading="bigLoading">
    <!--  新页面  -->
    <div class="report" v-if="new_customer_report">
      <div class="report-content">
        <div style="margin-bottom: 20px">
          <el-radio-group v-model="typeValue" @change="changeTypeValue">
            <el-radio-button v-for="item in typeOptions" :key="item.value" :label="item.value">{{item.label}}</el-radio-button>
          </el-radio-group>
        </div>
        <div style="display: flex;justify-content: space-between;align-items:end;flex-wrap: wrap">
          <div style="display: flex;flex-wrap: wrap">
<!--            <el-select v-model="stationValue" multiple collapse-tags style="width:250px;margin-bottom: 5px"
                       placeholder="请选择油站" @change="getCheckedStation">
              <el-option
                v-for="(item,index) in stationOptions"
                :key="index"
                :label="item.stname"
                :value="item.stid">
              </el-option>
            </el-select>-->
            <el-date-picker
              style="margin: 0 10px;"
              v-model="dateValue"
              :clearable="false"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              :default-time="['00:00:00', '23:59:59']"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @change="changeDateValue">
            </el-date-picker>
            <el-select v-if="typeValue==1 && dataShow" v-model="companyValue"
                       filterable
                       remote
                       :remote-method="remoteSearchLegalEntity"
                       :loading="legalEntityLoading"
                       style="width:220px;margin-right: 10px"
                       placeholder="请选择法体"
                       clearable
                       @change="changeCompanyValue">
              <el-option
                v-for="(item,index) in companyOptions"
                :key="index"
                :label="item.legal_entity_name"
                :value="item.id">
              </el-option>
            </el-select>
            <div style="margin-bottom: 5px">
              <el-button type="primary" :disabled="!showGet || showTitles" @click="createReport">生成</el-button>
            </div>
          </div>

          <div class="content_header">
            <el-button class="my_btn" :disabled="showTitles" @click="showTitle">自定义显示字段</el-button>
            <el-button type="primary" :disabled="!showPrint" @click="printContent">打印</el-button>
            <el-button type="primary" :disabled="!showDownload" @click="cardChargeDownload" v-show="isTotalReportForm">下载数据</el-button>
          </div>
        </div>

        <div id="myTable">
          <div class="tableData reportData" v-loading="loading">
            <div class="text-2xl font-bold mx-auto py-2.5 !border-none">法体资金对账报表</div>
            <div class="report_header">
              <div v-if="isGroup">集团名称：{{getCurrentStation.label}}</div>
              <div v-else>油站名称：{{getCurrentStation.label}}</div>
              <div v-if="companyValue && typeValue==1">法体名称：{{companyName}}</div>
              <div>开始时间：{{start_time}}</div>
              <div>结束时间：{{end_time}}</div>
              <div>单位：元</div>
            </div>
            <!-- 车队消费汇总表 -->
            <div class="text-xl font-bold mx-auto py-2.5 b-0 border-l border-t border-r border-solid border-gray-200">车队消费汇总表</div>
            <el-table :data="fleetSummaryData" border size="small" align="center" :header-cell-style="{background: '#F5F7FA'}">
              <el-table-column align="center" label="车队" prop="fleetName"></el-table-column>
              <el-table-column align="center" label="数量(升)" prop="quantity"></el-table-column>
              <el-table-column align="center" label="交易笔数" prop="transactionCount"></el-table-column>
              <el-table-column align="center" label="应付金额" prop="payableAmount"></el-table-column>
              <el-table-column align="center" label="优惠金额" prop="discountAmount"></el-table-column>
              <el-table-column align="center" label="实付金额" prop="actualAmount"></el-table-column>
            </el-table>

<div class="mt-4 !text-left">
              <el-form :inline="true">
                <el-form-item label="车队名称">
                  <el-select :disabled="!companyValue" v-model="searchForm.fleetName" placeholder="请选择车队" clearable @change="onFleetNameChange">
                    <el-option
                      v-for="item in fleetOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-form>
            </div>

            <!-- 车队资金变动明细表 -->
            <div class="text-xl font-bold mx-auto py-2.5 b-0 border-l border-t border-r border-solid border-gray-200">车队资金变动明细表</div>
            <!--        母账        -->
            <div v-if="typeValue==1 && showT3 == 1">
              <el-table :data="tableData3" border size="small" :header-cell-style="{background: '#F5F7FA'}">
                <template v-for="(v,i) in copyMom">
                  <el-table-column v-if="v.is_checked" align="center" :prop="v.field" :label="v.name"
                                   :width="v.field == 'card_no' ? '180px' : (v.field == 'order_no' ? '180px' : (v.field == 'pay_time' ? '150px' : '')) ">
                    <template slot-scope="scope">
                      <span v-if="v.field == 'card_no' || v.field == 'card_number'">{{ scope.row[v.field] }}</span>
                      <template v-else-if="v.field == 'type'">
                        <span v-if="!copyMom[0].is_checked && scope.$index==tableData3.length-1">合计</span>
                        <span v-else>{{ scope.row[v.field] }}</span>
                      </template>
                      <span v-else-if="Math.abs(Number(scope.row[v.field])) > -1">{{ Number(scope.row[v.field]).toFixed(2) }}</span>
                      <span v-else>{{ scope.row[v.field] }}</span>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <!--<el-table :data="tableData3" border size="small" :header-cell-style="{background: '#F5F7FA'}">
                <el-table-column align="center" label="油站名称">
                  <div slot-scope="scope">
                    <template  v-if="scope.row.stid_name.length == 0"></template>
                    <template v-else-if="scope.row.stid_name == '-' ">-</template>
                    <template v-else>{{scope.row.stid_name}}</template>
                  </div>
                </el-table-column>
                <el-table-column align="center" prop="type" label="变动类型"></el-table-column>
                <el-table-column align="center" prop="card_no" label="卡号" width="180"></el-table-column>
                <el-table-column align="center" prop="card_number" label="卡面卡号"></el-table-column>
                <el-table-column align="center" prop="car_no" label="车牌号"></el-table-column>
                <el-table-column align="center" prop="goods_name" label="油品名称"></el-table-column>
                <el-table-column align="center" label="挂牌单价">
                  <div slot-scope="scope">
                    <template  v-if="scope.row.han_price == '-' ">{{scope.row.han_price}}</template>
                    <template v-else>{{Number(scope.row.han_price).toFixed(2)}}</template>
                  </div>
                </el-table-column>
                <el-table-column align="center" label="实付单价">
                  <div slot-scope="scope">
                    <template v-if="scope.row.zh_dj == '-' ">{{scope.row.zh_dj}}</template>
                    <template v-else>{{Number(scope.row.zh_dj).toFixed(2)}}</template>
                  </div>
                </el-table-column>
                <el-table-column align="center" label="数量(升)">
                  <div slot-scope="scope">
                    <template v-if="scope.row.sl == '-' ">{{scope.row.sl}}</template>
                    <template v-else>{{Number(scope.row.sl).toFixed(2)}}</template>
                  </div>
                </el-table-column>
                <el-table-column align="center" label="油品应付">
                  <div slot-scope="scope">
                    <template v-if="scope.row.origin_money == '-' ">{{scope.row.origin_money}}</template>
                    <template v-else>{{Number(scope.row.origin_money).toFixed(2)}}</template>
                  </div>
                </el-table-column>
                <el-table-column align="center" label="非油应付">
                  <div slot-scope="scope">
                    <template v-if="scope.row.goods_money == '-' ">{{scope.row.goods_money}}</template>
                    <template v-else>{{Number(scope.row.goods_money).toFixed(2)}}</template>
                  </div>
                </el-table-column>
                <el-table-column align="center" label="优惠金额">
                  <div slot-scope="scope">
                    <template v-if="scope.row.dis_money == '-' ">{{scope.row.dis_money}}</template>
                    <template v-else>{{Number(scope.row.dis_money).toFixed(2)}}</template>
                  </div>
                </el-table-column>
                <el-table-column align="center" label="实付金额">
                  <div slot-scope="scope">
                    <template v-if="scope.row.pay_money == '-' ">{{scope.row.pay_money}}</template>
                    <template v-else>{{Number(scope.row.pay_money).toFixed(2)}}</template>
                  </div>
                </el-table-column>
                <el-table-column align="center" label="充值金额">
                  <div slot-scope="scope">
                    <template v-if="scope.row.recharge_money == '-' ">{{scope.row.recharge_money}}</template>
                    <template v-else>{{Number(scope.row.recharge_money).toFixed(2)}}</template>
                  </div>
                </el-table-column>
                <el-table-column align="center" label="余额">
                  <div slot-scope="scope">
                    <template v-if="scope.row.after_money=='-' ">-</template>
                    <template v-else>{{Number(scope.row.after_money).toFixed(2)}}</template>
                  </div>
                </el-table-column>
                <el-table-column align="center" label="交易时间" prop="pay_time" width="150"></el-table-column>
              </el-table>-->
            </div>
            <!--        卡列表        -->
            <div v-if="tableList.length > 0">
              <div v-if="tableList.length==0">
                <div class="table3_head">
                  <span>卡号：- &nbsp; 卡面卡号：- &nbsp; 车牌：- &nbsp;</span>
                  <span>卡名称：- </span>
                </div>
                <el-table :data="tableList" border size="small" align="center" :header-cell-style="{background: '#F5F7FA'}">
                  <template v-for="(v,i) in copyMom">
                    <el-table-column v-if="v.is_checked" align="center" :prop="v.field" :label="v.name"
                                     :width="v.field == 'pay_time' ? '150px' : '' ">
                      <template slot-scope="scope">
                        <span v-if="Math.abs(Number(scope.row[v.field])) > -1">{{ Number(scope.row[v.field]).toFixed(2) }}</span>
                        <template v-else-if="v.field == 'type'">
                          <span v-if="!copyMom[0].is_checked && scope.$index==tableList.length-1">合计</span>
                          <span v-else>{{ scope.row[v.field] }}</span>
                        </template>
                        <span v-else>{{ scope.row[v.field] }}</span>
                      </template>
                    </el-table-column>
                  </template>
                </el-table>
              </div>
              <div v-else>
                <div v-for="item in tableList">
                  <div class="table3_head" v-if="item.card_info">
                    <span v-if="!item.card_info.CardNo">卡号：-</span>
                    <span v-else>卡号：{{item.card_info.CardNo}}</span> &nbsp;
                    <span v-if="!item.card_info.CardNumber">卡面卡号：- </span>
                    <span v-else>卡面卡号：{{item.card_info.CardNumber}}</span> &nbsp;
                    <span v-if="!item.card_info.CarNumber"> 车牌：- </span>
                    <span v-else>车牌：{{item.card_info.CarNumber}}</span> &nbsp;
                    <span v-if="!item.card_info.CardName">卡名称：- </span>
                    <span v-else>卡名称：{{item.card_info.CardName}}</span>
                  </div>
                  <el-table :data="item.card_list" border size="small" align="center" :header-cell-style="{background: '#F5F7FA'}">
                    <template v-for="(v,i) in copyMom">
                      <el-table-column v-if="v.is_checked" align="center" :prop="v.field" :label="v.name"
                                       :width="v.field == 'card_no' ? '180px' : (v.field == 'order_no' ? '180px' : (v.field == 'pay_time' ? '150px' : '')) ">
                        <template slot-scope="scope">
                          <span v-if="Math.abs(Number(scope.row[v.field])) > -1">{{ Number(scope.row[v.field]).toFixed(2) }}</span>
                          <template v-else-if="v.field == 'type'">
                            <span v-if="!copyMom[0].is_checked && scope.$index==item.card_list.length-1">合计</span>
                            <span v-else>{{ scope.row[v.field] }}</span>
                          </template>
                          <span v-else>{{ scope.row[v.field] }}</span>
                        </template>
                      </el-table-column>
                    </template>
                  </el-table>
                </div>
              </div>
            </div>

            <div class="des_bottom">
              <div>制表人：{{orderMaker}}</div>
              <div>制表时间：{{orderMakingTime}}</div>
              <div>签字：</div>
            </div>
          </div>
        </div>
      </div>

      <!--自定义表格表头显示字段-->
      <el-dialog title="自定义显示字段" :visible.sync="titleVisible" width="650px" append-to-body :close-on-click-modal="false">
        <div class="titleBody">
          <div class="titleLeft">
            <div class="titleTop">可选字段</div>
            <div class="leftBody">
              <div class="left-body-title">车队资金变动明细表</div>
              <div class="left-body-inner">
                <div>
                  <el-checkbox-group v-model="checkedMom" @change="changeMom">
                    <el-checkbox v-for="v in momList" :label="v.field" :disabled="v.disabled" :key="v.id">{{v.name}}</el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
            </div>
          </div>
          <div class="titleRight">
            <div class="titleTop">已选字段</div>
            <div class="rightBody">
              <div class="right-item">
                <div class="right-inner" v-for="v in momList">
                  <span v-show="v.is_checked" :class="[v.disabled?'greyColor':'']">{{v.name}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!--底部按钮-->
        <div class="titleBottom">
          <div>
            <el-button @click="resetTitle">重 置</el-button>
            <el-checkbox v-model="is_select" style="margin-left: 15px">记住选择</el-checkbox>
          </div>
          <div>
            <el-button @click="titleVisible = false">取 消</el-button>
            <el-button type="primary" :disabled="title_submit" @click="sureTitle">确 定</el-button>
          </div>
        </div>
      </el-dialog>

    <!--   明细表超出条数提示   -->
    <el-dialog
      title="提示"
      center
      :visible.sync="showThirdDownLoad"
      :close-on-click-modal="false"
      @close="showThirdDownLoad = false"
      width="400px">
      <div class="thirdText">
        <div>客户资金变动明细超过{{new_customer_report_download_count}}条，</div>
        <div>如需查看，请手动下载明细数据</div>
      </div>
      <span slot="footer" class="dialog-footer">
          <el-button @click="showThirdDownLoad = false">取 消</el-button>
          <el-button type="primary" @click="showThirdDownLoad=false;cardChargeDownload()" v-show="isTotalReportForm">下载数据</el-button>
      </span>
    </el-dialog>

      <!-- 下载中心提示 -->
      <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>
    </div>
    <!--  旧页面  -->
    <div v-else>
      <old-card-customer-report></old-card-customer-report>
    </div>
  </div>
</template>
<!--
  TODO 提测前检查
    - 检验接口参数是否正确
    - 车队名称没有对接接口数据
    - 车队名称对应的ID没有传入接口

-->
<script>
import DownloadTips from '../DownloadTips.vue';
import CardCustomerTable from './CardCustomerTable'
import oldCardCustomerReport from "./oldCardCustomerReport";
import {mapGetters} from 'vuex'
import axiosReport from 'axios'
import printJS from 'print-js'
export default {
  name: 'FleetCapitalChangeDetailReport',
  components:{
    DownloadTips,
    CardCustomerTable,
    oldCardCustomerReport
  },
  data () {
    return {
      // 自定义表格显示字段 start
      titleVisible: false, // 自定义表头弹窗
      showTitles: true, // 自定义按钮禁用
      is_select: true, // 记住选择
      momList: [
        {id:1,name:'油站名称',field:'stid_name',is_checked:true,disabled:true},
        {id:2,name:'法体编号',field:'legal_entity_code',is_checked:true,disabled:false},
        {id:3,name:'法体名称',field:'legal_entity_name',is_checked:true,disabled:false},
        {id:4,name:'所属车队',field:'belong_team',is_checked:true,disabled:false},
        {id:5,name:'所属公司',field:'belong_company',is_checked:true,disabled:false},
        {id:6,name:'车辆ID',field:'car_id',is_checked:true,disabled:false},
        {id:7,name:'车牌号',field:'car_no',is_checked:true,disabled:false},
        {id:8,name:'车牌颜色',field:'car_color',is_checked:true,disabled:false},
        {id:9,name:'变动类型',field:'type',is_checked:true,disabled:true},
        {id:10,name:'卡号',field:'card_no',is_checked:true,disabled:false},
        {id:11,name:'卡面卡号',field:'card_number',is_checked:true,disabled:false},
        {id:12,name:'油品名称',field:'goods_name',is_checked:true,disabled:false},
        {id:13,name:'挂牌单价',field:'han_price',is_checked:true,disabled:false},
        {id:14,name:'实付单价',field:'zh_dj',is_checked:true,disabled:false},
        {id:15,name:'数量(升)',field:'sl',is_checked:true,disabled:false},
        {id:16,name:'油品应付',field:'origin_money',is_checked:true,disabled:false},
        {id:17,name:'优惠金额',field:'dis_money',is_checked:true,disabled:false},
        {id:18,name:'实付金额',field:'pay_money',is_checked:true,disabled:false},
        {id:19,name:'充值金额',field:'recharge_money',is_checked:true,disabled:false},
        {id:20,name:'余额',field:'after_money',is_checked:true,disabled:false},
        {id:21,name:'订单号',field:'order_no',is_checked:true,disabled:false},
        {id:22,name:'交易时间',field:'pay_time',is_checked:true,disabled:true},
      ], // 母账弹窗字段，参与表单互动和最终传参
      copyMom: [], // 母账表格字段，不参与表单互动，用作表格表头显示
      checkedMom: [], // 选中的母账
      title_submit: false, // 能否提交
      // 自定义表格显示字段 end
      isTotalReportForm: true,
      new_customer_report: 1, // 灰度页面，1新页面；0 旧页面
      bigLoading: false, // 整个页面的loading
      fleetSummaryData: [
  {
    "fleetName": "一队",
    "quantity": 150.50,
    "transactionCount": 3,
    "payableAmount": 1150.75,
    "discountAmount": 35.25,
    "actualAmount": 1115.50
  },
  {
    "fleetName": "二队",
    "quantity": 210.00,
    "transactionCount": 5,
    "payableAmount": 1780.00,
    "discountAmount": 52.00,
    "actualAmount": 1728.00
  },
  {
    "fleetName": "三队",
    "quantity": 95.20,
    "transactionCount": 2,
    "payableAmount": 680.40,
    "discountAmount": 18.90,
    "actualAmount": 661.50
  },
  {
    "fleetName": "四队",
    "quantity": 180.80,
    "transactionCount": 4,
    "payableAmount": 1450.20,
    "discountAmount": 41.70,
    "actualAmount": 1408.50
  },
  {
    "fleetName": "五队",
    "quantity": 125.00,
    "transactionCount": 3,
    "payableAmount": 980.50,
    "discountAmount": 27.50,
    "actualAmount": 953.00
  },
  {
    "fleetName": "六队",
    "quantity": 250.30,
    "transactionCount": 6,
    "payableAmount": 2050.90,
    "discountAmount": 60.40,
    "actualAmount": 1990.50
  },
  {
    "fleetName": "七队",
    "quantity": 80.75,
    "transactionCount": 2,
    "payableAmount": 590.15,
    "discountAmount": 15.65,
    "actualAmount": 574.50
  },
  {
    "fleetName": "八队",
    "quantity": 165.90,
    "transactionCount": 4,
    "payableAmount": 1320.60,
    "discountAmount": 38.10,
    "actualAmount": 1282.50
  },
  {
    "fleetName": "九队",
    "quantity": 110.40,
    "transactionCount": 3,
    "payableAmount": 870.30,
    "discountAmount": 23.80,
    "actualAmount": 846.50
  },
  {
    "fleetName": "九队",
    "quantity": 110.40,
    "transactionCount": 3,
    "payableAmount": 870.30,
    "discountAmount": 23.80,
    "actualAmount": 846.50
  },
  {
    "fleetName": "九队",
    "quantity": 110.40,
    "transactionCount": 3,
    "payableAmount": 870.30,
    "discountAmount": 23.80,
    "actualAmount": 846.50
  },
  {
    "fleetName": "九队",
    "quantity": 110.40,
    "transactionCount": 3,
    "payableAmount": 870.30,
    "discountAmount": 23.80,
    "actualAmount": 846.50
  },
  {
    "fleetName": "九队",
    "quantity": 110.40,
    "transactionCount": 3,
    "payableAmount": 870.30,
    "discountAmount": 23.80,
    "actualAmount": 846.50
  },
  {
    "fleetName": "九队",
    "quantity": 110.40,
    "transactionCount": 3,
    "payableAmount": 870.30,
    "discountAmount": 23.80,
    "actualAmount": 846.50
  },
  {
    "fleetName": "九队",
    "quantity": 110.40,
    "transactionCount": 3,
    "payableAmount": 870.30,
    "discountAmount": 23.80,
    "actualAmount": 846.50
  },
  {
    "fleetName": "九队",
    "quantity": 110.40,
    "transactionCount": 3,
    "payableAmount": 870.30,
    "discountAmount": 23.80,
    "actualAmount": 846.50
  },
  {
    "fleetName": "九队",
    "quantity": 110.40,
    "transactionCount": 3,
    "payableAmount": 870.30,
    "discountAmount": 23.80,
    "actualAmount": 846.50
  },
  {
    "fleetName": "九队",
    "quantity": 110.40,
    "transactionCount": 3,
    "payableAmount": 870.30,
    "discountAmount": 23.80,
    "actualAmount": 846.50
  },
  {
    "fleetName": "十队",
    "quantity": 195.60,
    "transactionCount": 5,
    "payableAmount": 1590.80,
    "discountAmount": 47.30,
    "actualAmount": 1543.50
  }
], // 车队消费汇总表数据
      fleetOptions: [], // 车队列表
      fleetValue: '', // 选中的车队
      searchForm: {
        fleetName: ''
      },
      typeOptions:[{
        value:1,
        label:"按法体查询",
      }],
      typeValue:1,
      checkAll: false,
      checkedReport: [],//勾选选项
      isIndeterminate: false,

      stationOptions:[],//油站列表
      stationValue:[],//选中油站

      dateValue:[],//选中时间

      companyOptions:[],//法体列表
      companyValue:"",//选中法体
      legalEntityLoading: false, // 法体搜索加载状态


      companyName:"",//法体名称
      customerName:"",//客户名称
      start_time:"",//开始时间
      end_time:"",//结束时间
      stationName:"",//所选油站名称

      tableData: [],//资金对账数据
      tableData3: [],//母账数据
      tableList: [], // 卡列表数据
      showTable: false, // 卡查询表格
      showGet: true, // 生成按钮
      showPrint: false, // 打印
      showDownload: false, // 下载
      spanList: [], // 表格合并
      spanAllList: [], // 表格合并
      isGroup:true,//是否是集团账号
      loading: false,//loading

      orderMaker:"",//制表人
      orderMakingTime:"",//制表时间

      params:{}, //表1
      params3:{},//表3
      showDownloadTips:false,
      data1: [],// 以下用来保存上次生成的数据
      data2: [],
      data3: [],
      data4: [],
      is_stid: 0, // 0 集团；1 单站
      single_stid: 0, // 0 集团；xxx 单站
      showT3: 1, // 0母账隐藏 1 显示
      dataShow: 1, // 1 法体下拉列表显示； 2 隐藏
      new_customer_report_download: 0, // 是否灰度下载。0 不灰度 ；1 灰度
      new_customer_report_download_count: 0, // 灰度下载的条数
      showThirdDownLoad: false, // 超出条数下载提示弹窗
    }
  },
  created() {
    this.getOldReport()
  },
  mounted(){
    let userInfo = localStorage.getItem("__userInfo__") || "";
    this.isTotalReportForm = JSON.parse(userInfo).group_id != 426;
    if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
      return false;
    }
    if(this.getCurrentStation && this.getCurrentStation.merchant_type == 2){
      this.isGroup = true;
    }else{
      this.isGroup = false;
    }
    this.getStations();
    this.getCompanyList();
    this.getCardReportColumn() // 获取自定义表头字段
    this.getFleetList(); // 新增：获取车队列表
  },
  computed:{
    ...mapGetters({
      "getCurrentStation":"getCurrentStation"
    })
  },
  methods: {
    // 获取自定义表头数据
    async getCardReportColumn(){
      try{
        let res = await this.$axios.post('/CardReportColumn/getCardReportColumn',{
          type: [10]
        })
        if(res.data.status != 200) return this.$message.error(res.data.info)
        this.momList = res.data.data[10]
        this.copyMom = JSON.parse(JSON.stringify(res.data.data[10]))
        this.showTitles = false
      }catch (e) {
        this.$message.error('网络错误')
      }
    },
    // 显示自定义表格字段
    showTitle(){
      // 打开时从表头获取当前展示的字段
      this.momList = JSON.parse(JSON.stringify(this.copyMom))
      this.setTitle()
      this.titleVisible = true
    },
    // 选中可选字段
    setTitle(){
      this.checkedMom = []
      this.momList.forEach(v => {
        if(v.is_checked){
          this.checkedMom.push(v.field)
        }
      })
    },
    // 选择母账
    changeMom(){
      this.momList.forEach(v => {
        if(this.checkedMom.includes(v.field)){
          this.$set(v,'is_checked',true)
        }else{
          this.$set(v,'is_checked',false)
        }
      })
    },
    // 重置自定义字段
    resetTitle(){
      this.checkedMom = []
      this.momList.forEach(v => {
        this.checkedMom.push(v.field)
        this.$set(v,'is_checked',true)
      })
    },
    sureTitle(){
      // 未选择记住选择，不调用接口 ，把表单字段赋值给表格显示
      if(!this.is_select){
        this.copyMom = JSON.parse(JSON.stringify(this.momList))
        this.titleVisible = false
      }else {
        this.submitTitle() // 提交数据
      }
    },
    // 确定提交自定义字段
    async submitTitle(){
      try{
        this.title_submit = true
        let changeType = 1 // 1-新增；2-更新；3-删除
        let userReportColumn = [
          {
            type: 10,
            cid_list: [],
            is_keep_tiered: true
          }
        ]
        this.momList.forEach(v => {
          if(v.is_checked){
            userReportColumn[0].cid_list.push(v.cid)
          }
          if(v.crid == 0){ // 新增
            changeType = 1
          }else{ // 更新
            changeType = 2
            userReportColumn[0].crid = v.crid
          }
        })
        let res = await this.$axios.post('/CardReportColumn/changeUserReportColumn',{
          changeType: changeType,
          userReportColumn: userReportColumn,
        })
        if(res.data.status != 200) return this.$message.error(res.data.info)
        this.$message.success('自定义显示字段成功')
        this.getCardReportColumn()
        this.titleVisible = false
      }catch (e) {
        this.$message.error('网络错误')
      }finally {
        this.title_submit = false
      }
    },
    // 灰度新旧报表
    async getOldReport() {
      try {
        this.bigLoading = true
        const res = await this.$axios.post('/Ostn/getGroupBaseInfo')
        console.log('灰度res',res.data.data)
        if(res.data.status != 200) return this.$message.error(this.data.info)
        this.new_customer_report = res.data.data.new_customer_report
        this.stationValue = res.data.data.group_stids
        this.new_customer_report_download = res.data.data.new_customer_report_download
        this.new_customer_report_download_count = res.data.data.new_customer_report_download_count
        this.changeTypeValue()
        this.bigLoading = false
        // this.setTime()
        console.log(this.dateValue)
      } catch (e) {
        this.bigLoading = false
      }
    },
    // 默认为前一月的数据
    setTime() {
      this.dateValue = []
      let today = this.$moment().format('YYYY-MM-DD');
      let yesterday = this.$moment().subtract(1, 'months').format('YYYY-MM-DD');
      this.dateValue.push(yesterday + ' 00:00:00');
      this.dateValue.push(today + ' 23:59:59');
    },
    // 切换法体，卡查询数据置空
    changeTypeValue(){
      console.log(this.typeValue)
      this.setTime()
      this.start_time = this.dateValue[0]
      this.end_time = this.dateValue[1]
      if(this.typeValue != this.flag) {
        this.tableData = []
        this.tableData3 = []
        this.tableList = []
      }
      this.showPrint = false
      this.showDownload = false
      if(this.typeValue==1) {
        this.companyValue = ''
      }
    },
    //获取油站列表
    getStations(){
      let that = this;
      that.$axios.post('/Stations/getStations',{}).then((res)=>{
        if(res.data.status == 200){
          if(res.data.data.is_group == 1) {
            that.is_stid = 0
            that.single_stid = 0
          } else {
            that.is_stid = 1
            that.single_stid = res.data.data.station_info[0].stid
          }
          console.log('is_stid',that.is_stid,that.single_stid)
        }else{
          that.$message({
            message: res.data.info,
            type: 'error'
          });
        }
      })
    },

    //获取法体名称
    getCompanyName(val){
      this.companyOptions.forEach(item=>{
        if(val == item.id){
          this.companyName = item.legal_entity_name;
        }
      })

    },

    // 远程搜索法体
    async remoteSearchLegalEntity(query) {
      if (!query) {
        // 如果没有搜索关键字，加载默认数据
        await this.getCompanyList();
        return;
      }

      try {
        this.legalEntityLoading = true;
        const res = await axiosReport.create().post('/mock/JinjiangCompanyConfig/getLegalEntityList', {
          page: 1,
          page_size: 1250,
          input: query,
        }, {
          timeout: 30000,
        });

        if (res.data.status === 200) {
          this.companyOptions = res.data.data.list || [];
        } else {
          this.$message.error(res.data.info);
        }
      } catch (e) {
        this.$message.error('搜索法体失败');
      } finally {
        this.legalEntityLoading = false;
      }
    },

    //获取法体信息列表
    async getCompanyList(){
      let that = this;
      await axiosReport.create().post('/mock/JinjiangCompanyConfig/getLegalEntityList', {
        page: 1,
        page_size: 100,
        input: "",
      },{
        timeout: 30000,
      })
        .then(function (res) {
          if(res.data.status == 200){
            that.companyOptions = res.data.data.list || [];
            //默认选择数组第一个
            // if(that.companyOptions.length>0){
            //     that.companyValue = that.companyOptions[0].id;
            // }
          }else{
            that.$message({
              message: res.data.info,
              type: 'error'
            });
          }
        })
    },

    //获取选择的油站名称
    getCheckedStation(){
      this.stationName = "";
      let len = this.stationValue.length;
      this.stationValue.forEach((item,index)=>{
        this.stationOptions.forEach((subitem)=>{
          if(subitem.stid == item){
            if(index == len-1){
              this.stationName += subitem.stname;

            }else{
              this.stationName += subitem.stname + "，";
            }
          }
        })
      })
    },

    //控制时间控件只能选择366天以内的日期
    changeDateValue(val){
      if(this.dateValue){
        let startTime = this.$moment(this.dateValue[0], 'YYYY-MM-DD HH:mm:ss').unix();
        let endTime = this.$moment(this.dateValue[1], 'YYYY-MM-DD HH:mm:ss').unix();
        if((endTime-startTime)/(60*60*24) > 366){
          this.$message({
            message: "只能获取一年(366天)的数据",
            type: 'warning'
          });
          this.dateValue[1] = this.$moment((startTime + (60*60*24)*366)*1000).format('YYYY-MM-DD HH:mm:ss');
        }
      }
    },

    //改变法体时，把表格法体名称设置为空
    changeCompanyValue(){
      this.companyName = "";
    },

    //生成报表
    async createReport(){
      // if(this.stationValue.length == 0){
      //   this.$message({
      //     message: "请选择油站",
      //     type: 'error'
      //   });
      //   return;
      // }
      if(!this.dateValue || this.dateValue.length == 0){
        this.$message({
          message: "请选择时间",
          type: 'error'
        });
        return;
      }
      if(!this.companyValue && this.typeValue==1){
        this.$message({
          message: "请选择法体",
          type: 'error'
        });
        return;
      }

      this.loading = true;
      this.showGet = false
      this.showPrint = false
      this.showDownload = false

      this.params.type = 6;
      this.params.start_time = this.dateValue[0];
      this.params3.start_time = this.dateValue[0];
      this.params.end_time = this.dateValue[1];
      this.params3.end_time = this.dateValue[1];
      this.params.station_ids = this.stationValue
      this.params3.stids = this.stationValue
      this.params.is_stid = this.is_stid
      this.params3.is_stid = this.is_stid
      this.params.single_stid = this.single_stid
      this.params3.single_stid = this.single_stid
      if(this.typeValue==1) { // 按法体
        delete this.params.query_type
        delete this.params.content
        this.params.company_id = this.companyValue ? this.companyValue : 0
        this.params3.company_id = this.companyValue ? this.companyValue : 0
        this.params.fleet_name = this.searchForm.fleetName; // 添加车队名称筛选
        this.params3.fleet_name = this.searchForm.fleetName; // 添加车队名称筛选
        delete this.params3.phone
        delete this.params3.card_no
        delete this.params3.card_number
        delete this.params3.car_no
      }
      this.getCompanyName(this.companyValue);
      this.start_time = this.dateValue[0];
      this.end_time = this.dateValue[1];
      let userInfo = localStorage.getItem('__userInfo__');
      if(userInfo && (userInfo !== "" || userInfo !== "undefined")){
        this.orderMaker = JSON.parse(userInfo).name;
      }
      this.orderMakingTime = this.$moment().format("YYYY-MM-DD");

      // 调用接口获取车队资金变动明细表数据
      try {
        const res = await this.getCustomerOrderLists();
        if (res.data.status === 200) {
          // 处理返回的数据
          this.tableData3 = res.data.data.result || [];
          this.tableList = res.data.data.card_result || [];

          // 设置表格显示状态
          this.showT3 = 1;
          if (this.tableData3.length === 0 && this.tableList.length > 0) {
            this.showT3 = 0;
          }

          // 启用按钮
          this.showPrint = true;
          this.showDownload = true;
        } else {
          this.$message.error(res.data.info);
        }
      } catch (e) {
        this.$message.error('获取车队资金变动明细表数据失败');
        console.error(e);
      } finally {
        this.loading = false;
        this.showGet = true;
      }
    },

    // 表格合并
    setTable() {
      this.spanList = []
      this.tableList.forEach((v,i) => {
        this.spanList[i] = 0
        if(!v.type) {
          this.spanList[i] = 1
        }
      })
      console.log('spanList',this.spanList)
    },

    // 表格拆分
    setList() {
      console.log('tableList2222',this.tableList)
      let list = [0]
      this.tableList.forEach((v,i) => {
        if(!v.type) {
          console.log('i',i)
          list.push(i)
        }
      })
      list.push((this.tableList.length))
      console.log('list',list)
      let newTable = []
      for (let i=0; i<list.length-1; i++) {
        console.log(list[i],list[i+1])
        newTable.push(this.tableList.slice(list[i],list[i+1]))
        list[i+1] = list[i+1] + 1
      }
      console.log('newtable',newTable)
      this.tableList = newTable
    },

    // 表格合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // console.log(columnIndex)
      if (this.spanList[rowIndex] > 0) {
        if (columnIndex === 0) {
          return [1, 15];
        } else {
          return [0, 0];
        }
      }
    },

    //客户资金对账汇总表
    getCapitalDetails(fundSummaryAxios){
      return new Promise((resolve, reject)=>{
        fundSummaryAxios.post('/CardReportForm/getNewCapitalDetails', this.params).then(function (res) {
          resolve(res);
        }).catch((err)=>{
          reject(err);
        })
      })
    },
    // 客户资金变动明细表
    getCustomerOrderLists() {
      return new Promise((resolve, reject)=>{
        // 构建新接口需要的参数
        const params = {
          company_id: this.companyValue,
          company_type: 1, // 车队类型，固定为1
          start_time: this.dateValue[0],
          end_time: this.dateValue[1],
          stids: this.stationValue,
          is_stid: this.is_stid,
          single_stid: this.single_stid,
          fleet_name: this.searchForm.fleetName // 添加车队名称参数
        };

        axiosReport.create().post('/mock/CardReport/getCustomerOrderListsJinjiang', params, {
          timeout: 30000,
        }).then(function (res) {
          resolve(res);
        }).catch((err)=>{
          reject(err);
        })
      })
    },

    //打印
    printContent(){
      // 检查是否有数据可以打印
      if (this.tableData3.length === 0 && this.tableList.length === 0) {
        this.$message.error("当前无数据，无需打印");
        return;
      }

      // 构建打印HTML内容
      let html = this.buildPrintHTML();

      // 使用print-js进行打印
      printJS({
        printable: html,
        type: 'raw-html',
        style: this.getPrintStyles()
      });
    },

    // 构建打印HTML内容
    buildPrintHTML() {
      let html = `
        <div class="print-container">
          <h1>法体资金对账报表</h1>
          <div class="report-header">
            <div>${this.isGroup ? '集团名称' : '油站名称'}：${this.getCurrentStation.label}</div>
            ${this.companyValue && this.typeValue==1 ? `<div>法体名称：${this.companyName}</div>` : ''}
            <div>开始时间：${this.start_time}</div>
            <div>结束时间：${this.end_time}</div>
            <div>单位：元</div>
          </div>
      `;

      // 添加车队消费汇总表
      if (this.fleetSummaryData && this.fleetSummaryData.length > 0) {
        html += `
          <div class="section-title">车队消费汇总表</div>
          <table class="print-table">
            <thead>
              <tr>
                <th>车队</th>
                <th>数量(升)</th>
                <th>交易笔数</th>
                <th>应付金额</th>
                <th>优惠金额</th>
                <th>实付金额</th>
              </tr>
            </thead>
            <tbody>
              ${this.fleetSummaryData.map(item => `
                <tr>
                  <td>${item.fleetName || '-'}</td>
                  <td>${item.quantity || '-'}</td>
                  <td>${item.transactionCount || '-'}</td>
                  <td>${item.payableAmount || '-'}</td>
                  <td>${item.discountAmount || '-'}</td>
                  <td>${item.actualAmount || '-'}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        `;
      }

      // 添加车队资金变动明细表
      html += `<div class="section-title">车队资金变动明细表</div>`;

      // 母账数据
      if (this.typeValue == 1 && this.showT3 == 1 && this.tableData3.length > 0) {
        html += this.buildTableHTML(this.tableData3);
      }

      // 卡列表数据
      if (this.tableList.length > 0) {
        this.tableList.forEach(item => {
          if (item.card_info) {
            html += `
              <div class="card-info">
                <span>卡号：${item.card_info.CardNo || '-'}</span>
                <span>卡面卡号：${item.card_info.CardNumber || '-'}</span>
                <span>车牌：${item.card_info.CarNumber || '-'}</span>
                <span>卡名称：${item.card_info.CardName || '-'}</span>
              </div>
            `;
            html += this.buildTableHTML(item.card_list);
          }
        });
      }

      // 添加底部信息
      html += `
          <div class="report-footer">
            <div>制表人：${this.orderMaker}</div>
            <div>制表时间：${this.orderMakingTime}</div>
            <div>签字：</div>
          </div>
        </div>
      `;

      return html;
    },

    // 构建表格HTML
    buildTableHTML(data) {
      if (!data || data.length === 0) return '';

      // 获取显示的列
      const visibleColumns = this.copyMom.filter(col => col.is_checked);

      let tableHTML = `
        <table class="print-table">
          <thead>
            <tr>
              ${visibleColumns.map(col => `<th>${col.name}</th>`).join('')}
            </tr>
          </thead>
          <tbody>
            ${data.map(row => `
              <tr>
                ${visibleColumns.map(col => {
                  let value = row[col.field];
                  // 格式化数值
                  if (col.field !== 'card_no' && col.field !== 'card_number' && col.field !== 'type' &&
                      col.field !== 'pay_time' && col.field !== 'order_no' &&
                      Math.abs(Number(value)) > -1 && value !== '-') {
                    value = Number(value).toFixed(2);
                  }
                  return `<td>${value || '-'}</td>`;
                }).join('')}
              </tr>
            `).join('')}
          </tbody>
        </table>
      `;

      return tableHTML;
    },

    // 获取打印样式
    getPrintStyles() {
      return `
        @media print {
          @page {
            size: A4 landscape;
            margin: 15mm;
          }

          body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            font-size: 10pt;
            line-height: 1.2;
          }

          .print-container {
            width: 100%;
            max-width: none;
          }

          h1 {
            text-align: center;
            font-size: 16pt;
            font-weight: bold;
            margin: 0 0 10pt 0;
            padding: 0;
          }

          .report-header {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            margin-bottom: 15pt;
            font-size: 9pt;
          }

          .report-header div {
            margin-right: 20pt;
            margin-bottom: 5pt;
          }

          .section-title {
            font-size: 12pt;
            font-weight: bold;
            text-align: center;
            margin: 15pt 0 8pt 0;
            padding: 5pt;
            border: 1px solid #000;
            border-bottom: 0;
          }

          .print-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15pt;
            font-size: 8pt;
          }

          .print-table th,
          .print-table td {
            border: 1px solid #000;
            padding: 3pt;
            text-align: center;
            vertical-align: middle;
          }

          .print-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            font-size: 8pt;
          }

          .card-info {
            background-color: #f9f9f9;
            border: 1px solid #000;
            border-bottom: 0;
            padding: 5pt 10pt;
            font-size: 8pt;
            display: flex;
            justify-content: space-between;
            margin-top: 10pt;
          }

          .card-info span {
            margin-right: 15pt;
          }

          .report-footer {
            display: flex;
            justify-content: flex-end;
            margin-top: 20pt;
            font-size: 9pt;
          }

          .report-footer div {
            margin-left: 50pt;
          }

          /* 分页控制 */
          .print-table {
            page-break-inside: avoid;
          }

          .card-info + .print-table {
            page-break-inside: avoid;
          }
        }
      `;
    },

    //下载数据
    async cardChargeDownload(){
      try {
        await this.exportCapitalDetails();
        this.showDownloadTips = true;
      }catch (e) {
      }
    },

    exportCapitalDetails(){
      let params = {}
      params.new_capital_detail = this.params
      params.customer_order_lists = JSON.parse(JSON.stringify(this.params3))
      // 增加自定义表格字段参数
      let mz_report_column = []
      this.copyMom.forEach(v => {
        if(v.is_checked){
          mz_report_column.push({
            cid : v.cid,
            name : v.name,
            pid : v.pid,
            field : v.field,
          })
        }
      })
      params.customer_order_lists.mz_report_column = mz_report_column
      params.customer_order_lists.sub_report_column = []
      console.log('下载params',params)
      let that = this;
      return new Promise((resolve, reject)=>{
        this.$axios.post('/CardReport/downLoadNewCustomerOrder', params).then(function (res) {
          if(res.data.status == 200){
            resolve(res);
          }else{
            that.$message({
              message: res.data.info,
              type: 'error'
            });
            reject(res);
            return;
          }
        }).catch((err)=>{
          resolve(err);
        })
      })
    },

    // 获取车队列表
    async getFleetList() {
      // 模拟数据，实际应从接口获取
      this.fleetOptions = [
        { value: 'fleet1', label: '车队一' },
        { value: 'fleet2', label: '车队二' },
        { value: 'fleet3', label: '车队三' },
      ];
      // 实际接口调用示例
      // try {
      //   let res = await this.$axios.post('/api/getFleetList'); // 假设有获取车队列表的接口
      //   if (res.data.status === 200) {
      //     this.fleetOptions = res.data.data.map(item => ({
      //       value: item.id,
      //       label: item.name
      //     }));
      //   } else {
      //     this.$message.error(res.data.info);
      //   }
      // } catch (e) {
      //   this.$message.error('获取车队列表失败');
      // }
    },
    // 搜索车队报表
    searchFleetReport() {
      // 根据选中的车队名称重新生成报表
      this.createReport();
    },

    // 处理车队名称变化
    async onFleetNameChange(fleetName) {
      if (!fleetName) {
        // 如果清空了车队选择，清空表格数据
        this.tableData3 = [];
        this.tableList = [];
        this.showPrint = false;
        this.showDownload = false;
        return;
      }

      // 检查是否已经选择了必要的条件
      if (!this.companyValue) {
        this.$message.warning('请先选择法体');
        return;
      }

      if (!this.dateValue || this.dateValue.length !== 2) {
        this.$message.warning('请先选择时间范围');
        return;
      }

      // 自动重新加载车队资金变动明细表数据
      try {
        this.loading = true;
        this.showGet = false;

        const res = await this.getCustomerOrderLists();
        if (res.data.status === 200) {
          // 处理返回的数据
          this.tableData3 = res.data.data.result || [];
          this.tableList = res.data.data.card_result || [];

          // 设置表格显示状态
          this.showT3 = 1;
          if (this.tableData3.length === 0 && this.tableList.length > 0) {
            this.showT3 = 0;
          }

          // 启用按钮
          this.showPrint = true;
          this.showDownload = true;
        } else {
          this.$message.error(res.data.info);
        }
      } catch (e) {
        this.$message.error('获取车队资金变动明细表数据失败');
        console.error(e);
      } finally {
        this.loading = false;
        this.showGet = true;
      }
    },

  },
  watch: {
    async getCurrentStation(newValue,oldValue){
      if(newValue.merchant_type != 0 && newValue.value != oldValue.value){
        if(this.getCurrentStation && this.getCurrentStation.merchant_type == 2){
          this.isGroup = true;
        }else{
          this.isGroup = false;
        }
        this.tableData = []
        this.tableData3 = []
        this.tableList = []
        this.data1 = []
        this.data3 = []
        this.data4 = []
        this.bigLoading = true
        this.getStations();
        this.dataShow = 0
        this.getCardReportColumn()
        await this.getCompanyList();
        this.dataShow = 1
        this.getOldReport()
      }
    },
  },
}
</script>

<style scoped>
.el-checkbox{
  margin-bottom: 8px;
}
.titleBody {
  width: 100%;
  height: 420px;
  display: flex;
  justify-content: space-between;
}
.titleTop {
  padding: 10px 0;
  margin-top: -30px;
  font-weight: bold;
}
.titleLeft{
  width: 420px;
}
.titleRight{
  width: 180px
}
.leftBody{
  border: 1px solid #E7E7E7;
  height: 360px;
  box-sizing: border-box;
  padding: 15px;
  overflow-y: auto;
}
.left-body-title {
  font-size: 15px;
  font-weight: bolder;
}
.left-body-inner {
  margin-top: 15px;
  width: 100%;
}
.left-inner-title {
  font-weight: bold;
  padding-bottom: 10px;
}
.rightBody{
  border: 1px solid #E7E7E7;
  height: 360px;
  overflow-y: auto;
}
.right-item {
  margin: 10px 0 0 10px;
  padding-bottom: 5px;
}
.right-inner {
  margin-left: 20px;
  line-height: 23px;
}
.greyColor {
  color: #999;
}
.titleBottom {
  position: absolute;
  left: 0;
  bottom: 15px;
  width: 100%;
  box-sizing: border-box;
  margin-top: 10px;
  padding: 10px 20px 0;
  border-top: 1px solid #E7E7E7;
  display: flex;
  justify-content: space-between;
}
.my_btn{
  color: #32AF50;
  border: 1px solid #32AF50;
}
.report {
  position: relative;
  height: 100%;
  background:rgba(245,245,245,1);
  margin: 0px auto;
  font-size: 14px;
}
.report-content{
  background: #fff;
  padding:20px 0;

}
.report .checkbox-box{
  display: flex;
  align-content: center;
  margin-bottom: 20px;
}
.report .checkbox-box .checkAll{
  margin-right: 30px;
  margin-left: 30px;
}
.search-box{
  margin-right: 10px;
}
.report .content_header {
  margin-bottom: 5px;
  display: flex;
}
.tableData{
  text-align: center;
}
.tableData .report_header {
  display: flex;
  margin:10px 10px;
  font-size: 14px;
}
.tableData .report_header div{
  min-width: 100px;
  text-align: left;
  margin-right: 20px;
}
.tableData .header_table {
  width: 100%;
  border-right:1px solid #EBEEF5;
  border-bottom:1px solid #EBEEF5;
  margin-top: 20px;
}
.tableData .header_table td {
  border-left:1px solid #EBEEF5;
  border-top:1px solid #EBEEF5;
}
.tableData .header_table_row {
  height: 40px;
}
.tableData .table_des {
  margin-top: 40px;
  margin-bottom: 20px;
}
.tableData .table_des_text {
  text-align: left;
}
.stations{
  text-align: left;
}
.stations span{
  color: #32AF50;
  margin-right: 10px;
}
.des_bottom {
  display: flex;
  justify-content: flex-end;
  font-size: 14px;
  font-weight: 500;
  margin-top: 10px;
}
.des_bottom div{
  padding-right: 100px;
}
.table3_head {
  height: 40px;
  line-height: 40px;
  text-align: left;
  padding-left: 20px;
  border-left: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
  border-top: 1px solid #EBEEF5;
}
/*.table3_head:first-child {*/
/*  border-top: 1px solid #EBEEF5;*/
/*}*/
.el-table thead.is-group th {
  background: #F5F7FA;
}
.thirdText {
  text-align: center;
  font-size: 16px;
  color: #333;
  line-height: 1.5;
}

::v-deep .el-loading-spinner{
  top: 20%;
}
</style>
<style>
.reportData td:first-child, .reportData th:first-child{
  padding-left: 0 !important;
}
</style>
