export default function setColumnWidth(tableData,num) {
    let arr = []
    tableData.forEach(item=>{
      // console.log('item',item.Remark);
      arr.push(item.Remark)
    })
    // console.log('arr',arr);
    //将文字排序,选出最多字符的文字
    let str = arr.sort((a,b)=>b.length-a.length)[0]
    // console.log('str',str);
    if(str){
      let columnWidth = 0;
      for (let char of str) {
        if ((char >= 'A' && char <= 'Z') || (char >= 'a' && char <= 'z')) {
          // 如果是英文字符，为字符分配10个单位宽度，单位宽度可根据字体大小调整
          columnWidth += 10
        } else if (char >= '\u4e00' && char <= '\u9fa5') {
          // 如果是中文字符，为字符分配14个单位宽度，单位宽度可根据字体大小调整 3行展示除3
          columnWidth += 15/num
        } else {
          // 其他种类字符，为字符分配10个单位宽度，单位宽度可根据字体大小调整
          columnWidth += 10
        }
      }
      if (columnWidth < 120) {
        // 设置最小宽度
        columnWidth = 120
      }
      return columnWidth + 'px'
    }

  }