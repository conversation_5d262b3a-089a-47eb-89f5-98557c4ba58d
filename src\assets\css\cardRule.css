input[type=text]:focus, select:focus{
	outline:none;
}

.cardRule{
    padding-bottom: 20px;
}
.cardRuleListTable{
    width: 100%;
    border-top: 1px solid #EBEEF5;
    margin: 20px 0 20px 0;
}
.given-form .card-radio-group {
    display: -webkit-box;
    align-items: center;
    flex-direction: inherit;
}
.given-form .card-radio-group .item{
    color: #606266;
    display: flex;
    align-items: center;
}
.given-form .card-radio-group .el-radio{
    margin-right: 30px;
}
.given-form .item03{
    margin-bottom: 10px !important;
    margin-left: 100px;
}
.given-form .item04{
    width: 600px;
    margin-left: 211px;
}
.given-form .item04 .el-input-number{
    width: 100px;
}
.given-form .item04 .el-input-number .el-input__inner{
    width: 80px;
    padding: 0;
}
.given-form .item04 .el-input-number__decrease, .given-form .item04 .el-input-number__increase{
    width: 20px;
}
.rule-form{
    margin: 30px 0 0 30px;
}

/* 使用条件 */
.customer-type{
    margin-bottom: 6px;
}
.customer-type .el-radio-button__orig-radio:checked+.el-radio-button__inner{
    background: #fff;
    color: #32AF50;
}
.customer-select{
    margin-left: 73px;
    width: 250px;
}

/* 时间规则 */
.time-form .el-form-item:last-child{
    margin: 30px 0 0 30px;
}
.time-btn-box{
    margin: 110px;
}

/*充值赠送*/
.given-form .last-form-item{
    margin-left: 100px;
    margin-top: 40px;
}
.given-form .item03 .el-form-item__error{
    margin-left: 114px;
}

.price-form .el-form-item{
    margin-left: 14px;
    margin-right: 14px;
}
.price-form .last-form-item{
    margin-left: 84px;
}
.price-form .el-form-item__content{
    display: flex;
}

/* 页码 */
.page_content {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
}
