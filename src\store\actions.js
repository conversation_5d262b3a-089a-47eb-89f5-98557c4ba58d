import * as types from './mutations-types'

const actions = {
    changeCurrentStation({commit},n){//简写
        commit('CHANGECURRENTSTATION',n);
    },
    changeEditableTabsValue({commit},n){
        commit('CHANGEDITABLETABSVALUE',n);
    },
    changeEditableTabs(context,n){//接收一个与store实例具有相同方法的context对象
        context.commit('CHANGEDITABLETABS',n);
    },
    changeCacheArray({commit},n){
        commit('CHANGEDCACHEARRAY',n);
    },
}

export default actions