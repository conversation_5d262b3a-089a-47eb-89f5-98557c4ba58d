import Vue from 'vue'
import axios from "axios"
import Router from 'vue-router'
const CardTheme = () => import("@/components/CardTheme")
const CardRule = () => import("@/components/CardRule")
const CardManager = () => import("@/components/CardManager")
const CustomerGroupManagement = () => import("@/components/CustomerGroupManagement")
const EnterpriseInformationManagement = () => import("@/components/EnterpriseInformationManagement")
const Refund = () => import("@/components/Refund")
const Report = () => import("@/components/Report/Report")
const oldReport = () => import("@/components/Report/oldReport")
const SpendDetailReport = () => import("@/components/Report/SpendDetailReport")
const BetweenStationsReport = () => import("@/components/Report/BetweenStationsReport")
const CapitalRecord = () => import("@/components/CapitalRecord")
const CardTransfer = () => import("@/components/CardTransfer")
const CapitalJXC = () => import("@/components/Report/CapitalJXC")
const fleetJXC = () => import("@/components/Report/fleetJXC")
const fleetInvoiceReport = () => import("@/components/Report/fleetInvoiceReport")
const GroupReport = () => import("@/components/Report/GroupReport")
const GroupCapitalRecord = () => import("@/components/GroupCapitalRecord")
const Record = () => import("@/components/Record")
const Setting = () => import("@/components/Setting")
const RegularInvoice = () => import("@/components/RegularInvoice")
const TaxInvoice = () => import("@/components/TaxInvoice")
const CustomerInvoices = () => import("@/components/CustomerInvoices")
const CardCapitalDayReport = () => import("@/components/Report/CardCapitalDayReport")
const CardStationReport = () => import("@/components/Report/CardStationReport")
const CardCustomerReport = () => import("@/components/Report/CardCustomerReport")
const FleetCapitalChangeReport = () => import("@/components/Report/FleetCapitalChangeReport")
const CustomerSpendReport = () => import("@/components/Report/CustomerSpendReport")
const oldCardCustomerReport = () => import("@/components/Report/oldCardCustomerReport")
const RechargeRecodeList = () => import("@/components/Report/RechargeRecodeList")
const FleetTopUpInvoiceReport = () => import("@/components/Report/FleetTopUpInvoiceReport")
const InventorySummary = () => import("@/views/InventorySummary/InventorySummary")
const DownloadList = () => import("@/components/DownloadList")
const CardLog = () => import("@/components/Log/CardLog")
const HandMadeLog = () => import("@/components/Log/HandMadeLog")
const NotFount = () => import("@/components/NotFount")
const Illegal = () => import("@/components/Illegal")
const newUserReport = () => import("@/components/Report/newUserReport")
const customerOilReport = () => import("@/components/Report/customerOilReport")
const MarketingRules = () => import('@/components/MarketingRules')
const unitPriceLock = () => import('@/components/unitPriceLock')
const RechargeApproval = () => import('@/views/RechargeApproval/RechargeApproval')

//首页
const Navigation = () => import("@/components/Navigation");
const index = () => import("@/components/index");
import eventBus from "@/components/eventBus.js";

import { Loading } from "new-wcc-ui";
import ssoUrlObj from "../assets/js/url.js";

const lh = window.location.href;
var myURL = "";
if (lh.indexOf("localhost:8081") != -1) {
  axios.defaults.baseURL = "/apis/cardApi";
  Vue.prototype.baseURL = "/apis/cardApi";
  myURL = ssoUrlObj.ssoTest;
} else if (lh.indexOf("test-card-admin.wcc.cn") != -1) {
  axios.defaults.baseURL = "/api/cardApi";
  Vue.prototype.baseURL = "/api/cardApi";
  myURL = ssoUrlObj.ssoTest;
} else if (lh.indexOf("preview-card-admin.weicheche.cn") != -1) {
  axios.defaults.baseURL = "/api/cardApi";
  Vue.prototype.baseURL = "/api/cardApi";
  myURL = ssoUrlObj.ssoPreview;
} else if (lh.indexOf("card-admin.zhihuiyouzhan.com") != -1) {
  axios.defaults.baseURL = "/api/cardApi";
  Vue.prototype.baseURL = "/api/cardApi";
  myURL = ssoUrlObj.ssoProduct;
} else {
  axios.defaults.baseURL = "/apis/cardApi";
  Vue.prototype.baseURL = "/apis/cardApi";
  myURL = ssoUrlObj.ssoTest;
}

Vue.use(Router);

let pending = []; //声明一个数组用于存储每个ajax请求的取消函数和ajax标识
let cancelToken = axios.CancelToken;
// let removePending = (config) => {
//   for(let p in pending){
//     if(pending[p].u === config.url + '&' + config.method) { //当前请求在数组中存在时执行函数体
//       pending[p].f(); //执行取消操作
//       pending.splice(p, 1); //把这条记录从数组中移除
//     }
//   }
// }

// ajax请求统一增加请求头
axios.interceptors.request.use(
  config => {
    config.headers.common = {
      "Content-Type": "application/x-www-form-urlencoded",
      "X-Requested-With": "XMLHttpRequest",
      "Access-Control-Allow-Headers": "X-Requested-With,Content-Type",
      "Access-Control-Allow-Methods": "PUT,POST,GET,DELETE,OPTIONS"
    };

    config.timeout = 10000;
    let str_data = JSON.stringify(config.data || "{}");
    // 参数中携带cancelHttp，不防止多次请求
    if (str_data.indexOf("cancelHttp") > -1) {
      //   httpFlag = false
    } else {
      //   removePending(config); //在一个ajax发送前执行一下取消操作
      config.cancelToken = new cancelToken(c => {
        // 这里的ajax标识我是用请求地址&请求方式拼接的字符串，当然你可以选择其他的一些方式
        pending.push({ u: config.url + "&" + config.method, f: c });
      });
    }
    return config;
  },
  err => {
    return Promise.resolve(err.response);
  }
);
//对响应数据进行拦截
axios.interceptors.response.use(
  res => {
    if (res.data.status == "302") {
      localStorage.removeItem("__userInfo__");
      sessionStorage.removeItem("__userInfo__");
      localStorage.removeItem("currentStation");
      localStorage.removeItem("options");
      window.location.href =
        myURL +
        "/v1/login?redirect=" +
        encodeURIComponent(window.location.href.split("?")[0]);
    }

    return res;
  },
  err => {
    // 对响应错误做些什么
    return Promise.resolve(err.response); // 可在组件内获取到服务器返回信息
  }
);

const myrouter = new Router({
  mode: "history",
  routes: [
    {
      path: "/",
      name: "Navigation",
      redirect: "index",
      component: Navigation,
      children: [
        {
          path: "/index",
          name: "index",
          component: index
        },
        {
          path: "/CardTheme",
          name: "CardTheme",
          component: CardTheme
        },
        {
          path: "/CustomerGroupManagement",
          name: "CustomerGroupManagement",
          component: CustomerGroupManagement
        },
        {
          path: "/CardRule",
          name: "CardRule",
          component: CardRule
        },
        {
          path: "/CardManager",
          name: "CardManager",
          component: CardManager
        },
        {
          path: "/EnterpriseInformationManagement",
          name: "EnterpriseInformationManagement",
          component: EnterpriseInformationManagement
        },
        {
          path: "/CustomerType",
          name: "CustomerType",
          component: ()=>import('@/views/CustomerType/CustomerType')
        },
        {
          path: "/Refund",
          name: "Refund",
          component: Refund
        },
        {
          path: "/oldReport",
          name: "oldReport",
          component: oldReport
        },
        {
          path: "/Report",
          name: "Report",
          component: Report
        },
        {
          path: "/SpendDetailReport",
          name: "SpendDetailReport",
          component: SpendDetailReport
        },
        {
          path: "/BetweenStationsReport",
          name: "BetweenStationsReport",
          component: BetweenStationsReport
        },
        {
          path: "/CapitalRecord",
          name: "CapitalRecord",
          component: CapitalRecord
        },
        {
          path: "/CardTransfer",
          name: "CardTransfer",
          component: CardTransfer
        },
        {
          path: "/GroupCapitalRecord",
          name: "GroupCapitalRecord",
          component: GroupCapitalRecord
        },
        {
          path: "/CapitalJXC",
          name: "CapitalJXC",
          component: CapitalJXC
        },
        {
          path: "/fleetJXC",
          name: "fleetJXC",
          component: fleetJXC
        },
        {
          path: "/fleetInvoiceReport",
          name: "fleetInvoiceReport",
          component: fleetInvoiceReport
        },
        {
          path: "/GroupReport",
          name: "GroupReport",
          component: GroupReport
        },
        {
          path: "/Record",
          name: "Record",
          component: Record
        },
        {
          path: "/Setting",
          name: "Setting",
          component: Setting
        },
        {
          path: "/RegularInvoice",
          name: "RegularInvoice",
          component: RegularInvoice
        },
        {
          path: "/TaxInvoice",
          name: "TaxInvoice",
          component: TaxInvoice
        },
        {
          path: "/CustomerInvoices",
          name: "CustomerInvoices",
          component: CustomerInvoices
        },
        {
          path: "/CardCapitalDayReport",
          name: "CardCapitalDayReport",
          component: CardCapitalDayReport
        },
        {
          path: "/CardStationReport",
          name: "CardStationReport",
          component: CardStationReport
        },
        {
          path: "/CardCustomerReport",
          name: "CardCustomerReport",
          component: CardCustomerReport
        },
        {
          path: "/CustomerSpendReport",
          name: "CustomerSpendReport",
          component: CustomerSpendReport
        },
        {
          path: "/RechargeRecodeList",
          name: "RechargeRecodeList",
          component: RechargeRecodeList
        },
        {
          path: "/FleetTopUpInvoiceReport",
          name: "FleetTopUpInvoiceReport",
          component: FleetTopUpInvoiceReport
        },
        {
          path: "/InventorySummary",
          name: "InventorySummary",
          component: InventorySummary
        },
        {
          path: "/DownloadList",
          name: "DownloadList",
          component: DownloadList
        },
        {
          path: "/CardLog",
          name: "CardLog",
          meta: { requiresAuth: true },
          component: CardLog
        },
        {
          path: "/HandMadeLog",
          name: "HandMadeLog",
          meta: { requiresAuth: true },
          component: HandMadeLog
        },
        {
          path: "/newUserReport",
          name: "newUserReport",
          meta: { requiresAuth: true },
          component: newUserReport
        },
        {
          path: "/customerOilReport",
          name: "customerOilReport",
          meta: { requiresAuth: true },
          component: customerOilReport
        },
        {
          path: "/MarketingRules",
          name: "MarketingRules",
          meta: { requiresAuth: true },
          component: MarketingRules
        },
        {
          path: "/unitPriceLock",
          name: "unitPriceLock",
          meta: { requiresAuth: true },
          component: unitPriceLock
        },
        {
          path: "/RechargeApproval",
          name: "RechargeApproval",
          component: RechargeApproval
        },
        {
          path: "/FleetCapitalChangeDetailReport",
          name: "FleetCapitalChangeDetailReport",
          component: FleetCapitalChangeReport
        }
      ]
    },
    {
      path: "/Illegal",
      name: "Illegal",
      component: Illegal
    },
    {
      path: "*",
      name: "NotFount",
      component: NotFount
    }
  ]
});

myrouter.beforeEach((to, from, next) => {

  let userInfo = sessionStorage.getItem("__userInfo__");
  eventBus.$emit("__userInfo__", userInfo);

  //路由权限拦截
  // let canIReadLog = JSON.parse(userInfo).logconf;
  // if(to.meta.requiresAuth && !canIReadLog){
  //     next("/Illegal");
  //     return;
  // }

  let _ticket = getQueryVariable("_code");
  //1、判断userInfo有没有  有的话 就要判断时间  时间过期了就要调后端接口   没有过期就不用处理
  //2、userInfo没有的话  判断有没有_ticket   如果没有_ticket   就去登陆页面
  //3、如果有_ticket  调后端接口  获取用户信息  返回成功之后  缓存userInfo  没有的话  跳登陆页面

  if (userInfo && userInfo != "" && userInfo != "undefined") {
    //判断时间是否过期
    next();
    return;
  }
  if (_ticket) {
    localStorage.removeItem("__userInfo__");
    sessionStorage.removeItem("__userInfo__");
    //调用获取用户信息接口
    loginIn(_ticket, next);
  } else {
    //调用登陆
    window.location.href =
      myURL +
      "/v1/login?redirect=" +
      encodeURIComponent(window.location.href.split("?")[0]);
  }

  function getQueryVariable(variable) {
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
      var pair = vars[i].split("=");
      if (pair[0] == variable) {
        return pair[1];
      }
    }
    return false;
  }

  //登录
  function loginIn(ticket, Fn) {
    let loading = Loading.service({ fullscreen: true, text: "登录中..." });
    axios
      .post("/ssoLogin/login", {
        code: ticket
      })
      .then(function(res) {
        loading.close();
        if (res.data.status == 200) {
          localStorage.setItem("__userInfo__", JSON.stringify(res.data.data));
          sessionStorage.setItem("__userInfo__", JSON.stringify(res.data.data));
          eventBus.$emit("__userInfo__", JSON.stringify(res.data.data));


          let userInfo = localStorage.getItem("__userInfo__");
          if (userInfo && (userInfo !== "" || userInfo !== "undefined")) {
            localStorage.setItem(
              "__isGroupSettle__",
              JSON.parse(userInfo).is_group_settle
            );
            eventBus.$emit(
              "__isGroupSettle__",
              JSON.parse(userInfo).is_group_settle
            );
          }

          if(Vue.prototype.getQueryVariable("label")){
              switchOstn();
              return;
          }
          Fn && Fn();
        } else {
          Vue.prototype.$message({
            message: res.data.info,
            type: "error"
          });
        }
      })
      .catch(function(error) {
        loading.close();
      });
  }
  function switchOstn (){
    let params = {
        label: decodeURI(Vue.prototype.getQueryVariable("label")),
        merchant_id: Vue.prototype.getQueryVariable("merchant_id"),
        merchant_type: Vue.prototype.getQueryVariable("merchant_type"),
        pid: Vue.prototype.getQueryVariable("pid"),
        value: Vue.prototype.getQueryVariable("value"),
    }
    Vue.prototype.$axios.post("/Ostn/switchOstn", params)
    .then((res) => {
        if(res.status == 200){
            let url = window.location.href;
            let urlArr = url.split("?");
            let jumpUrl = urlArr[0];
            if(urlArr[1]){
              //把code拼接回去，去掉跳转过来的参数
              jumpUrl = jumpUrl + "?" + urlArr[1].split("&")[0];
            }
            window.location.href = jumpUrl;
        }
    }).catch((error) => {
        console.log(error);
    })
  }
});

// 如下四个处理多次点击同一个菜单报错
// 获取原型对象push函数
const originalPush = Router.prototype.push

// 获取原型对象replace函数
const originalReplace = Router.prototype.replace

// 修改原型对象中的push函数
Router.prototype.push = function push(location){
  return originalPush.call(this , location).catch(err=>err)
}

// 修改原型对象中的replace函数
Router.prototype.replace = function replace(location){
  return originalReplace.call(this , location).catch(err=>err)
}

export default myrouter;
