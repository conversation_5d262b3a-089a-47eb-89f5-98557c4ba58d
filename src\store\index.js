import Vue from 'vue'//引入vue
import Vuex from 'vuex'//引入vuex
import actions from './actions'
import * as getters from './getters'
import state from './state'
import mutations from './mutations'
import { Store } from 'vuex';
import {isEmpty} from "lodash";
import {DataCode, post} from "../utils/http";
import {Message} from "new-wcc-ui";
//使用vuex
Vue.use(Vuex);

let currentStation = JSON.parse(localStorage.getItem("currentStation"));
let userInfo = localStorage.getItem('__userInfo__');

const store = new Vuex.Store({
    state:{
        userInfo:userInfo?userInfo:{},
        currentStation:currentStation ? currentStation : {},//当前油站
        editableTabsValue:"1",//当前tab
        editableTabs: [],//tab列表
        tabIndex:"1",
        cacheArray:[],
      groupBaseInfo:{},
      customerType:[],
      stationList:[],
      stationListLoading:false,
      companyList:[],
      companyListLoading:false,
      isGroup:'0',
      roleJurisdiction: null
    },
    getters:{//类似vue的computed
      companyArray(state){
        return isEmpty(state.companyList.dt)?[]:state.companyList.dt
      },
      customerTypeObject(state){
        const res = {};
        state.customerType.forEach(item => {
          res[item.CompanyTypeID] = item.CompanyTypeName;
        })
        return res;
      },
        getUserInfo(state){
            return state.userInfo
        },
        getCurrentStation(state){
            return state.currentStation
        },
        getEditableTabsValue(state){
            return state.editableTabsValue
        },
        getEditableTabs(state){
            return state.editableTabs
        },
        getTabIndex(state){
            return state.tabIndex
        },
        getCacheArray(state){
            return state.cacheArray
        },
      showLockPrice(state){
        return state.groupBaseInfo && (String(state.groupBaseInfo.lock_price_show) === '1')
      },
      showRechargeApproval(state){
        return state.groupBaseInfo && (String(state.groupBaseInfo.is_recharge_approval) === '1')
      },
      showPayNameConfig(state){
        return state.groupBaseInfo && (String(state.groupBaseInfo.is_pay_name_config) === '1')
      },
      showBatchDownload(state){
        return state.groupBaseInfo && (String(state.groupBaseInfo.is_batch_download) === '1')
      },
      rechargeApprovalPrivilege(state) {
        const roles = state.roleJurisdiction;
        if (!roles || !Array.isArray(roles)) return {};
        for (const role of roles) {
          if (Array.isArray(role.children)) {
            const foundChild = role.children.find(child => child.privilege_value2 === 'CompanyCard/auditApplyRecharge');
            if (foundChild) {
              return foundChild;
            }
          }
        }
        return {};
      },
      showFleetCapitalChangeDetailReport(state) {
        return state.groupBaseInfo && (String(state.groupBaseInfo.show_fleet_capital_change_detail_report) === '1')
      }
    },
    mutations:{
      SET_IS_GROUP(state,p){
        state.isGroup = String(p) === '1';
      },
      SET_GROUP_BASE_INFO(state,data){
        if(isEmpty(data)){
          return;
        }
        state.groupBaseInfo = data;
      },
      SET_STATION_LIST(state,data){
        if(isEmpty(data)){
          return;
        }
        state.stationList = data;
      },
      SET_COMPANY_LIST(state,data){
        if(isEmpty(data)){
          return;
        }
        state.companyList = data;
      },
      SET_CUSTOMER_TYPE(state,data){
        if(isEmpty(data)){
          return;
        }
        state.customerType = data;
      },
        //Vue 建议我们mutation 类型用大写常量表示
        CHANGEUSERINFO(state,n){
            state.userInfo = n;
        },
        CHANGECURRENTSTATION(state,n){
            state.currentStation = n;
        },
        CHANGEDITABLETABSVALUE(state,n){
            state.editableTabsValue = n;
        },
        CHANGEDITABLETABS(state,n){
            state.editableTabs.push(n);
        },
        SETEDITABLETABS(state,n){
            state.editableTabs = n;
        },
        DELETEEDITABLETABS(state,n){    //删除某个tab栏
            state.editableTabs.splice(state.editableTabs.indexOf(n),1)
        },
        SETTABINDEX(state,n){
            state.tabIndex = String(n);
        },
        CHANGEDCACHEARRAY(state,n){
            state.cacheArray.push(n);
        },
        SETCACHEARRAY(state,n){
            state.cacheArray = n;
        },
        SET_ROLE_JURISDICTION(state, payload) {
          state.roleJurisdiction = payload;
        }
    },
    actions:{//类似vue的methods
      async getStationList({state,commit}){
        try {
          state.stationListLoading = true;
          const res = await post('/Stations/getStationList')
          if(res.status !== DataCode.SUCCESS){
            Message({type:'error',message:res.info})
            return
          }
          commit('SET_STATION_LIST',res.data)
        } catch (e) {
        } finally {
          state.stationListLoading = false;
        }
      },
      async getSimpleCompanyList({state,commit}){
        try {
          state.companyListLoading = true;
          const res = await post('/CompanyCard/getSimpleCompanyList',{
            page: 1,
            page_size: 1250,
            input: "",
          })
          if(res.status !== DataCode.SUCCESS){
            Message({type:'error',message:res.info})
            return
          }
          commit('SET_COMPANY_LIST',res.data)
        } catch (e) {
        } finally {
          state.companyListLoading = false;
        }
      },
        changeUserInfo({commit},n){
            commit('CHANGEUSERINFO',n);
        },
        changeCurrentStation({commit,dispatch},n){//简写
            commit('CHANGECURRENTSTATION',n);
          dispatch('getSimpleCompanyList');
        },
        changeEditableTabsValue({commit},n){
            commit('CHANGEDITABLETABSVALUE',n);
        },
        changeEditableTabs(context,n){//接收一个与store实例具有相同方法的context对象
            context.commit('CHANGEDITABLETABS',n);
        },
        changeCacheArray({commit},n){
            commit('CHANGEDCACHEARRAY',n);
        },
    }
});
// const store = new Vuex.Store({
//     state,
//     getters,
//     mutations,
//     actions,
// });

export default store //导出store

