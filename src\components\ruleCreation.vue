<template>
    <div id="ruleCreation" v-cloak>
        <!-- 步骤条 -->
        <el-steps :active="defaultActive" finish-status="success" simple style="margin-bottom: 20px">
            <el-step title="基本信息" ></el-step>
            <el-step title="优惠明细" ></el-step>
            <el-step title="优惠限制" ></el-step>
        </el-steps>
        <!-- 基本信息 -->
        <div v-show="showBaseInfo" class="baseInfo">
            <el-form ref="ruleForm" :model="form" label-width="80px" :rules="formRule">
                <el-form-item label="规则名称" prop="ruleName">
                    <el-input v-model="form.ruleName" placeholder="请输入名称"  maxlength="30" show-word-limit  style="width: 360px;"></el-input>
                </el-form-item>
                <el-form-item label="活动时间">
                    <el-date-picker
                        v-model="form.time"
                        :default-time="['00:00:00', '23:59:59']"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"    align="right">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="优惠油站" prop="selectionStids">
                    <el-checkbox :indeterminate="isIndeterminateOfStids" v-model="checkAllOfStids" @change="CheckAllChangeOfStids">全选</el-checkbox>
                    <div style="margin: 0;"></div>
                    <el-checkbox-group @change="getStationChannel" v-model="form.selectionStids">
                        <el-checkbox :label="station.stid" :key="'stname'+index"  v-for="(station,index) in station_list">{{station.stname}}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="使用类型">
                    <el-radio-group v-model="form.useType" @change="changeUseType">
                      <el-radio :label="1">按制卡规则</el-radio>
                      <el-radio :label="0">按车队客户</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="卡名称" v-if="form.useType == 1" required>
                    <el-checkbox :indeterminate="isIndeterminateOfCard" v-model="checkAllOfCard" @change="CheckAllChangeOfCard">全选</el-checkbox>
                    <div style="margin: 0;"></div>
                    <el-checkbox-group v-model="form.selectionCards">
                        <el-checkbox :label="card.ID" :key="'stname'+index"  v-for="(card,index) in cards_list">{{card.Name}}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="车队名称" v-if="form.useType == 0">
                    <el-checkbox :indeterminate="isIndeterminateOfCar" v-model="checkAllOfCar" @change="CheckAllChangeOfCar">全选</el-checkbox>
                    <div style="margin: 0;"></div>
                    <el-checkbox-group v-model="form.company_id">
                        <el-checkbox :label="card.ID" :key="'company_id'+index"  v-for="(card,index) in companyList">{{card.CompanyName}}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="卡类型" v-if="form.useType == 1">
                    <el-checkbox :indeterminate="isIndeterminateOfCardType" v-model="checkAllOfCardType" @change="CheckAllChangeOfCardType">全选</el-checkbox>
                    <div style="margin: 0;"></div>
                    <el-checkbox-group v-model="form.customeTrType">
                        <el-checkbox label="1" >个人卡</el-checkbox>
                        <el-checkbox label="2" >车队子卡</el-checkbox>
                        <el-checkbox label="3" >不记名卡</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="用户限制" required v-if="form.useType == 1">
                    <el-radio-group v-model="form.account_type" @change="changeLimit">
                        <el-radio label="4">不限</el-radio>
                        <el-radio label="3">会员等级</el-radio>
                        <el-radio label="2" v-show="form.showCardTheme">卡组</el-radio>
                        <!-- <el-radio label="0" v-show="false">车队账户</el-radio> -->
                    </el-radio-group>
                    <div class="cardbox" v-show="form.account_type != 4">
                        <!-- 会员等级 -->
                        <el-radio-group v-if="form.account_type==3" v-model="form.selectLevelsType">
                            <el-radio label="0">不限</el-radio>
                            <!-- <el-radio label="1"> 非会员</el-radio> -->
                            <el-radio label="2">
                                <el-select v-model="form.GradeStr"  @change="form.selectLevelsType='2'" multiple placeholder="请选择">
                                    <el-option
                                        v-for="(item,index) in levels"
                                        :key="'GradeStr'+index"
                                        :label="item.level_name"
                                        :value="item.id">
                                    </el-option>
                                </el-select>
                            </el-radio>
                        </el-radio-group>
                        <!-- 卡组 -->
                        <el-radio-group v-else-if="form.account_type==2" v-model="form.selectCustomerGroupType">
                            <el-radio label="0">不限</el-radio>
                            <el-radio label="2">
                                <el-select v-model="form.customer_group_id" filterable @change="form.selectCustomerGroupType='2'" collapse-tags  multiple placeholder="请选择">
                                    <el-option
                                        v-for="(item,index) in customerGroupList"
                                        :key="'customer_group_id'+index"
                                        :label="item.CustomerGroupName"
                                        :value="item.ID">
                                    </el-option>
                                </el-select>
                            </el-radio>
                        </el-radio-group>
                        <!-- 车队客户 -->
                        <!-- <el-radio-group v-else-if="form.account_type==0" v-model="form.selectCompanyListType">
                            <el-radio label="0">不限</el-radio>
                            <el-radio label="2">
                                <el-select v-model="form.company_id" filterable @change="form.selectCompanyListType='2'" collapse-tags  multiple placeholder="请选择">
                                    <el-option
                                        v-for="(item,index) in companyList"
                                        :key="'company_id'+index"
                                        :label="item.CompanyName"
                                        :value="item.ID">
                                    </el-option>
                                </el-select>
                            </el-radio>
                        </el-radio-group>     -->
                    </div>
                </el-form-item>
                <el-form-item label="优惠油品" prop="selectOilsList">
                    <el-checkbox :indeterminate="isIndeterminateOfOils" v-model="checkAllOfOils" @change="CheckAllChangeOfOils">全选</el-checkbox>
                    <div style="margin: 0;"></div>
                    <el-checkbox-group v-model="form.selectOilsList">
                        <el-checkbox :label="oil.oil_id" :key="'oilsList'+index"  v-for="(oil,index) in oils">{{oil.oil_name}}</el-checkbox>
                        <!-- <el-checkbox :checked="check(oil.oil_id)" :label="{oil_id:oil.oil_id,oil_number:oil.oil_number}" :key="'oilsList'+index"  v-for="(oil,index) in oils">{{oil.oil_name}}</el-checkbox> -->
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="付款方式" prop="selectPayTypeList">
                    <el-checkbox :indeterminate="isIndeterminateOfPayType" v-model="checkAllOfPayType" @change="CheckAllChangeOfPayType">全选</el-checkbox>
                    <div style="margin: 0;"></div>
                    <el-checkbox-group v-model="form.selectPayTypeList">
                        <el-checkbox :label="item.BH" :key="'item'+index"  v-for="(item,index) in payType">{{item.MC}}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="重复类型" required v-if="false">
                    <el-select v-model="form.repeatTime" placeholder="请选择">
                        <el-option v-for="(item,index) in repeatList" :key="index"  :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-if="false">
                    <el-repeat-time-picker
                        @change = "checkTime"
                        xstyle="width: 380px;"
                        style="margin-bottom:25px"
                        value-format="HH:mm:ss"
                        :max-length="5"
                        v-model="form.selectedTime"
                        :type="form.repeatTime">
                    </el-repeat-time-picker>
                </el-form-item>
                <el-form-item>
                    <el-button @click="cancleBtn">{{isCreate ? '取消' : '取消修改' }}</el-button>
                    <el-button :disabled="isDisabled" type="primary" @click="baseNext('ruleForm')">下一步</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- 优惠明细 -->
        <div v-show="showOfferDetails">
            <el-form v-for="(item,index) in detailList" :model="item" class="detailBox" ref="detailForm" :rules="detailRule" :key="index">
                <el-form-item prop="oil">
                    <span>优惠油品</span>
                    <el-select v-model="item.oil" multiple clearable collapse-tags placeholder="请选择" style="width:180px;margin-left:36px" value-key="oil_id">
                        <el-option
                         v-for="oilItem in oilOption"
                         :key="oilItem.oil_id"
                         :label="oilItem.oil_name"
                         :value="oilItem">
                        </el-option>
                 </el-select>
                  <span>已选优惠油品：</span> {{showOil(item.oil)}}
                </el-form-item>
                <el-form-item class="calculation" v-for="(cal,index2) in item.calculationList" prop="calculationList" :key="index2">
                    <span>充值金额大于等于</span>
                    <el-input-number :controls="false" v-model="cal.LowerLimit" style="margin:0 10px" :min="0"  :precision="2" :step="1" controls-position="right"></el-input-number>
                    <span>小于</span>
                    <el-input-number :controls="false" v-model="cal.UpperLimit" style="margin:0 10px" :min="0"  :precision="2" :step="1" controls-position="right"></el-input-number>
                    <span>元, 每升直降</span>
                    <el-input-number v-model="cal.Discounts" style="margin:0 10px" :min="0"  :precision="2" :step="0.1" controls-position="right"></el-input-number>
                    <span>元</span>
                    <el-button type="text" @click="deleteCal(index,index2)"><i class="el-icon-delete" v-if="index2 != 0"></i></el-button>
                    <el-button type="text" @click="addCal(index)" v-if="index2 == item.calculationList.length - 1">添加</el-button>
                </el-form-item>
                <!-- <el-form-item class="detailBoxBtn">
                    <el-button type="text" @click="deleteDetails(index)"><i class="el-icon-delete" v-if="index != 0"></i></el-button>
                    <el-button type="text" @click="addDetails(index)" >添加</el-button>
                </el-form-item> -->
            </el-form>
                <div class="footer">
                    <el-button @click="basePre">上一步</el-button>
                    <el-button type="primary" @click="detailNext">下一步</el-button>
                </div>
        </div>
        <!-- 优惠限制 -->
        <div v-show="showControl">
            <el-form style="margin-left:20px" label-width="200px">
                <el-form-item label="是否与积分抵现共享">
                    <el-radio v-model="IFBonus" label="1">不限制</el-radio>
                    <el-radio v-model="IFBonus" label="0">不可共享</el-radio>
                </el-form-item>
                <el-form-item label="是否与抵扣券共享">
                    <el-radio v-model="IFVoucher" label="1">不限制</el-radio>
                    <el-radio v-model="IFVoucher" label="0">不可共享</el-radio>
                </el-form-item>
                <el-form-item label="是否与其他优惠共享">
                    <el-radio v-model="share" label="1">可共享</el-radio>
                    <el-radio v-model="share" label="0">不可共享</el-radio>
                </el-form-item>
                <el-form-item label="优先级">
                    <el-select v-model="priority" placeholder="请选择" style="width:150px">
                        <el-option v-for="item in 10" :key="item.index" :label="item" :value="item"></el-option>
                    </el-select>
                    <el-popover
                       placement="right-start"
                       width="420"
                       trigger="hover">
                        <p class="tipck">1.优先级定为1，2，3...10，数字越高优先级越低</p>
                        <p class="tipck">2.优先级的判断原则： </p>
                        <p class="tipck">1）判断是否不同类型优惠规则如折扣、直降、返赠、返送等；</p>
                        <p class="tipck">2）优先级数字越低则优先级越高，即优先使用该规则；</p>
                        <p class="tipck">3）若优先级一样，则根据优惠规则的创建或修改时间来判断，创建时间越早则优先级越高</p>
                        <i class="el-icon-warning" slot="reference"></i>
                     </el-popover>
                </el-form-item>
                <el-form-item label="状态">
                    <el-radio v-model="state" :label="100">启用</el-radio>
                    <el-radio v-model="state" :label="101">禁用</el-radio>
                </el-form-item>
            </el-form>
            <div class="footer">
                <el-button @click="detailPre">上一步</el-button>
                <el-button :disabled="isCreateDisabled" type="primary" @click="submit">{{isCreate ? '创建' : '修改'}}</el-button>
            </div>
        </div>
    </div>
</template>
<script>
import {mapGetters} from 'vuex'
export default {
    name:'ruleCreation',
    data() {
        //验证优惠油品是否有重复的选项
        var validateOil = (rule, value, callback) => {
            console.log('选中的油品',value);
            let flag = false
            let checkArr = []
            let oilId = []
            //遍历已选中的油品id
            this.detailList.forEach(item=>{
                item.oil.forEach(e=>{
                    console.log('e',e);
                    checkArr.push(e.oil_id)
                })
            })
            //遍历选中的油品id
            value.forEach(val=>{
                oilId.push(val.oil_id)
            })
            //遍历已选中的油品id去除选中的值后，再判断是否有相同值
            oilId.forEach(item=>{
                if(checkArr.indexOf(item) !== -1){
                    checkArr.splice(checkArr.indexOf(item),1)
                }
            })
            oilId.forEach(item=>{
                if(checkArr.indexOf(item) !== -1){
                    flag = true
                }
            })
            console.log('flag',flag);
            if(!value.length){
                callback(new Error('请选择优惠油品'))
            }else if (flag) {
              callback(new Error('请勿选择重复的油品'));
            } else {
              callback();
            }
        };
        var validateCalculation = (rule, value, callback) =>{
            console.log('value',value);
            let flag = false
            let Lower = false
            let Upper = false
            let Discount = false
            value.forEach(item=>{
                if(item.LowerLimit >= item.UpperLimit){
                    flag = true
                }
                if(item.LowerLimit < 0){
                    Lower = true
                }
                if(item.UpperLimit > 1000000){
                    Upper = true
                }
                if(item.Discounts < 0 || item.Discounts > 20){
                    Discount = true
                }
            })
             if (flag) {
              callback(new Error('充值金额最小值必须小于最大值'));
            } else if(Lower){
                callback(new Error('充值金额最小值不能小于0'));
            } else if(Upper){
                callback(new Error('充值金额最大值不能大于1000000'));
            } else if(Discount){
                callback(new Error('每升直降限制0-20元'));
            } else {
              callback();
            }
        }
        return {
            // id:'',
            defaultActive:0,  //步骤条激活位置
            showBaseInfo:true,  //展示基本信息
            showOfferDetails:false, //展示优惠明细
            showControl:false,  //展示优惠限制
            form:{
                ruleName:'',    //规则名称
                time:[],    //活动时间
                useType:1,  //使用类型
                selectionStids:[],  //选择的油站
                selectionCards:[],  //选择的卡名称
                customeTrType:[],   //客户类型
                account_type:"4",      //用户限制
                showCardTheme:true, //是否显示主题卡

                selectLevelsType:'0', //选择的用户等级 0无限制 1非会员 2会员
                GradeStr:[],    //选择的会员等级

                selectCustomerGroupType:'0', //
                customer_group_id:[],     //选择的卡组

                selectCompanyListType:'0',
                company_id:[],      //车队id

                selectOilsList:[],    //选择的优惠油品
                selectPayTypeList:[],
                repeatTime:1,       //重复类型
                selectedTime:[],        //选择的时间
            },
            isIndeterminateOfStids:true,    //油站全选样式控制
            checkAllOfStids:false,          //油站是否全选
            isIndeterminateOfCard:true,     //卡名称全选样式控制
            checkAllOfCard:false,           //卡名称是否全选
            isIndeterminateOfCar:true,  //车队卡
            checkAllOfCar:false,
            isIndeterminateOfCardType:true, //卡类型全选样式控制
            checkAllOfCardType:false,       //卡类型是否全选
            isIndeterminateOfOils:true,     //优惠油站全选样式控制
            checkAllOfOils:false,           //优惠油站是否全选
            isIndeterminateOfPayType:true,  //支付方式
            checkAllOfPayType:false,

            formRule:{
                ruleName:[
                    {required: true, message : '请输入规则名称', trigger:'blur'}
                ],
                selectionStids:[
                    {required: true, message : '请选择至少一个油站', trigger:'change'}
                ],
                selectionCards:[
                    {required: true, message : '请选择至少一张卡', trigger:'change'},
                    // {trigger:'change',
                    //     validator:(rule, value, callback)=>{
                    //         if(this.form.useType == 0 && !value.length){
                    //             callback(new Error('请选择至少一张卡'))
                    //         }else{
                    //             callback()
                    //         }
                    //     }
                    // }
                ],
                // selectionCars:[
                //     {required: true, message : '请选择至少一张卡', trigger:'change'},
                // ],
                selectPayTypeList:[
                    {required: true, message : '请选择至少一种支付方式', trigger:'change'}
                ],
                selectOilsList:[
                    {required: true, message : '请选择至少一种优惠油品', trigger:'change'}
                ]
            },
            detailRule:{
                oil:[
                    { validator: validateOil, trigger:'change'}
                ],
                calculationList:[
                    {validator: validateCalculation, trigger:'change'},
                    {validator: validateCalculation, trigger:'blur'}
                ]
            },


            station_list:[],    //油站列表
            cards_list:[],      //卡主题列表
            customerGroupList:[],     //卡组
            companyList:[],             //车队卡列表
            levels:[],           //等级
            oils:[],            //优惠油品
            payType:[],         //支付方式

            repeatList:[
                {label:'每日重复',value:1},
                {label:'每周重复',value:2},
                {label:'每月重复',value:3},
            ],

            detailList:[ //优惠详情
                {
                    oil:[],
                    calculationList:[
                        {
                            LowerLimit:0, //充值起始
                            UpperLimit:0, //充值最大
                            Discounts:0, //降价多少
                        }
                    ]
                }
            ],
            oilOption:[],
            IFBonus:'0',    //是否与积分抵现共享
            IFVoucher:'0',
            share:'0',
            priority:10,    //优先级
            state:100,      //启用100 禁用101
            isCreate:true,

            isDisabled:false,   //是否禁用按钮

            isCreateDisabled:false, //创建按钮禁止
        }
    },
    created() {
        this.createDate()
    },
    mounted() {
        if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
            return false;
        }
        this.getRuleOil()
        this.getCardThemeRuleList()
        this.getChargeRuleTem()
        this.getStationList()
        this.getCompanyList()
        this.getCustomerGroupList()
    },
    computed:{
        ...mapGetters({
            "getCurrentStation":"getCurrentStation"
        }),
        // cacheArray:{
        //     get(){
        //         return this.$store.getters.getCacheArray
        //     },
        //     set(newValue){
        //         this.$store.commit("SETCACHEARRAY",newValue)
        //     }
        // },
    },
    methods: {
        //切换类型清除校验
        changeUseType(){
            if(this.form.useType != 1){
                console.log('123');
                this.$nextTick(()=>{
                    this.$refs['ruleForm'].clearValidate('selectionCards')

                })
            }else{
                //切换默认回到不限制
                this.form.account_type = '4'
            }
        },
        //展示选中油品的数据
        showOil(oil){
            // console.log('oil123',oil);
            let OilId = oil.map(item=>item.oil_id)
            let selectOilName = []
            this.oils.forEach(item=>{
                if(OilId.includes(item.oil_id)){
                    selectOilName.push(item.oil_name)
                }
            })
            return selectOilName.join(" , ")
        },
        //编辑的初始化数据
        createDate(id){
            console.log('id', id);
            if(id){
                this.isDisabled = true
                this.id = id
                this.isCreate = false
                this.$axios.post('/CardRule/getCardChargeRuleInfo',{ id:this.id })
                .then((res)=>{
                   if(res.data.status == 200){
                        this.isDisabled = false
                    console.log('编辑的规则',res.data.data);
                    let params = res.data.data
                    // public_config
                    this.form.ruleName = params.public_config.rule_name //规则名
                    this.form.time = [params.public_config.start_time, params.public_config.end_time]
                    this.form.selectionStids = params.public_config.use_station_list   //油站
                    this.state = params.public_config.state     //状态100启用 101禁用
                    this.priority = params.public_config.priority       //优先级

                    //extend_rule
                    this.form.selectionCards = params.extend_rule.card_theme     //卡名称
                    this.form.GradeStr = params.extend_rule.GradeStr      //会员等级id
                    this.form.selectPayTypeList = params.extend_rule.pay_way     //付款方式
                    this.form.company_id = params.extend_rule.company_id     //车队ID
                    this.form.customer_group_id = params.extend_rule.customer_group_id   //卡组id
                    this.form.customeTrType = params.extend_rule.card_type    //卡类型,1个人卡，2车队卡，3不记名卡

                    //discount_config
                    this.IFBonus = params.extend_rule.discount_config.IFBonus    //是否与积分抵现共享1：不限制，0：不可共享
                    this.IFVoucher = params.extend_rule.discount_config.IFVoucher      //是否与抵扣券共享1：不限制，0：不可共享
                    this.share  = params.extend_rule.discount_config.share    //是否与其他优惠共享1：不限制，0：不可共享

                    // params.extend_rule.account_type == '' ? this.form.account_type = '4' : ''
                    //用户限制
                    if(params.extend_rule.account_type === ''){
                        this.form.account_type = '4'
                    }else{
                        this.form.account_type = params.extend_rule.account_type + ''
                    }
                    //会员
                    if(params.extend_rule.gradeStr.length){
                        this.form.GradeStr = params.extend_rule.gradeStr.map(item=> Number(item))
                        this.form.selectLevelsType = '2'
                    }else{
                        this.form.selectLevelsType = '0'
                    }
                    //卡组
                    if(params.extend_rule.customer_group_id.length){
                        this.form.customer_group_id = params.extend_rule.customer_group_id.map(item=> Number(item))
                        this.form.selectCustomerGroupType = '2'
                    }else{
                        this.form.selectCustomerGroupType = '0'
                    }
                    //车队
                    // if(params.extend_rule.company_id.length){
                    //     this.form.company_id = params.extend_rule.company_id.map(item=> Number(item))
                    //     this.form.selectCompanyListType = '2'
                    // }else{
                    //     this.form.selectCompanyListType = '0'
                    // }
                    if(params.extend_rule.account_type === '0'){
                        this.form.company_id = params.extend_rule.company_id.map(item=> Number(item))
                        this.form.useType = 0
                    }else{
                        this.form.useType = 1
                    }

                    //oils_list
                    params.extend_rule.oils_list.forEach(item=>{
                        this.form.selectOilsList.push(item.OilsID)
                    })
                    let olis_list = params.extend_rule.oils_list
                    let myArr = []
                    this.detailList = []
                    //将后端返回的油品信息整合成前端需要的数据结构
                    olis_list.forEach((item,index)=>{
                        console.log('this.detailList',this.detailList);
                        if(myArr.indexOf(item.Oil_Group) === -1){
                            //后端返回的是string，循环处理将字符串转换成number
                            item.Oilsmxes.forEach(item=>{
                                for (const key in item) {
                                    item[key] = Number(item[key])
                                }
                            })
                            // console.log('item.Oilsmxes',item.Oilsmxes);
                            this.detailList.push({
                                oil:[{
                                    oil_id: item.OilsID,
                                    oil_number: item.OilsBH,
                                }],
                                calculationList:item.Oilsmxes
                            })
                            myArr.push(item.Oil_Group)
                        }else{
                            let idx = myArr.indexOf(item.Oil_Group)
                            this.detailList[idx].oil.push({
                                    oil_id: item.OilsID,
                                    oil_number: item.OilsBH,
                            })
                        }
                    })
                   }else{
                       this.$message.error(res.data.info)
                   }
                })
            }else{
                this.isDisabled = false
                this.isCreate = true
            }
        },
        //退出时清空选中数据，为了减少请求其他进行缓存
        clearData(){
            this.showBaseInfo = true  //展示基本信息
            this.showOfferDetails = false //展示优惠明细
            this.showControl = false  //展示优惠限制
            //基本信息
            this.form.ruleName = ''
            this.form.selectedTime = []
            this.form.selectionStids = []
            this.form.selectionCards = []
            this.form.customeTrType = []
            this.form.useType = 1
            this.form.account_type = '4'
            this.form.showCardTheme = true
            this.form.selectOilsList = []
            this.form.selectPayTypeList = []
            this.form.time = []

            //优惠油品
            this.detailList = [
                {
                    oil:[],
                    calculationList:[
                        {
                            LowerLimit:0, //充值起始
                            UpperLimit:0, //充值最大
                            Discounts:0, //降价多少
                        }
                    ]
                }
            ]

            //优惠限制
            this.IFBonus = '0'
            this.IFVoucher = '0'
            this.share = '0'
            this.priority = 10
            this.state = 100
            this.isCreate = true

            //全选控制器
            this.isIndeterminateOfStids = true    //油站全选样式控制
            this.checkAllOfStids = false          //油站是否全选
            this.isIndeterminateOfCard = true     //卡名称全选样式控制
            this.checkAllOfCard = false           //卡名称是否全选
            this.isIndeterminateOfCar = true
            this.checkAllOfCar = false
            this.isIndeterminateOfCardType = true //卡类型全选样式控制
            this.checkAllOfCardType = false       //卡类型是否全选
            this.isIndeterminateOfOils = true     //优惠油站全选样式控制
            this.checkAllOfOils = false           //优惠油站是否全选
            this.isIndeterminateOfPayType = true  //支付方式
            this.checkAllOfPayType = false

            setTimeout(() => {
                console.log('清空');
                this.$refs['ruleForm'].clearValidate()
                this.$refs['detailForm'].forEach(item=>{
                    item.clearValidate()
                 })
            }, 100);
        },
        //el-checked数组对象无法回显，判断是否包含，强制回显
        check(oil_id){
            // console.log('oil_id',oil_id);
            let oil_ids = this.form.selectOilsList.map(item=>item.oil_id)
            // console.log('oil_ids',oil_ids);
            // console.log('选中',oil_ids.includes(oil_id));
            return oil_ids.includes(oil_id)
        },
        //获取卡列表,根据选择的可用油站获取相应的列表
        getCardThemeRuleList(){
            this.$axios.post('/CardRule/getCardThemeRuleList',{
                state:100,
                station_id:0,
                page:1,
                page_size:500,
            }).then((res)=>{
                console.log('卡类型',res.data.data);
                if(res.data.status == 200){
                    this.cards_list = res.data.data.dt.filter(item => item.Type == 1);
                    // that.allCardThemeRuleList = res.data.data.dt;
                }else{
                    this.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
        },
        //获取付款方式列表
        getChargeRuleTem(){
            this.$axios.post('/CardRule/getChargeRuleTem',{}).then((res)=>{
                console.log('付款方式',res.data.data);
                if(res.data.status == 200){
                    this.payType = res.data.data.pay_way;
                    this.levels = res.data.data.level
                }else{
                    this.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
        },
        //优惠油站全选
        CheckAllChangeOfStids(val){
            console.log('油站全选',val);
            let stid = []
            this.station_list.forEach(item=>{
                stid.push(item.stid)
            })
            this.form.selectionStids = val ? stid : []
            this.isIndeterminateOfStids = false
        },
        //获取
        getStationChannel(val){
            console.log('val',val);
        },
        //卡名称全选
        CheckAllChangeOfCard(val){
            console.log('卡名称全选',val);
            let id = []
            this.cards_list.forEach(item=>{
                id.push(item.ID)
            })
            this.form.selectionCards = val ? id : []
            this.isIndeterminateOfCard = false
        },
        CheckAllChangeOfCar(val){
            let id = []
            this.companyList.forEach(item=>{
                id.push(item.ID)
            })
            this.form.company_id = val ? id : []
            this.isIndeterminateOfCar = false
        },
        //卡类型全选
        CheckAllChangeOfCardType(val){
            console.log('卡类型全选',val);
            this.form.customeTrType = val ? ['1','2','3'] : []
            this.isIndeterminateOfCardType = false

        },
        //优惠油品全选
        CheckAllChangeOfOils(val){
            console.log('优惠油品全选',val);
            let oils_ids = []
            this.oils.forEach(item=>{
                oils_ids.push(item.oil_id)
            })
            this.form.selectOilsList = val ? oils_ids : []
            this.isIndeterminateOfOils = false
        },
        //支付方式全选
        CheckAllChangeOfPayType(val){
            console.log('卡名称全选',val);
            let payList = []
            this.payType.forEach(item=>{
                payList.push(item.BH)
            })
            this.form.selectPayTypeList = val ? payList : []
            this.isIndeterminateOfPayType = false
        },
        //检查时间合法性
        checkTime(val){

            this.isCheckTime = val;
        },
        //获取可用油站
        getStationList(){
            this.$axios.post('/Stations/getStationList',{}).then((res)=>{
                if(res.status == 200){
                    console.log('油站',res.data.data);
                    this.station_list = res.data.data;
                }
            })
        },
        //退出按钮
        cancleBtn(){
            this.$confirm('确定要退出规则吗？','提示',{
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(()=>{
                // this.$store.commit('DELETEEDITABLETABS','ruleCreation')
                // this.cacheArray.remove('ruleCreation')
                // this.$forceUpdate()
                this.clearData()
                // this.$router.push('/MarketingRules')
                this.$emit('exit')
            }).catch(()=>{
                this.$message({
                    type: 'info',
                    message: '已取消退出'
                });
            })
        },
        //基本详情下一步
        baseNext(formName){
            // this.$refs[formName].fields.map(item=>{
            //     console.log('item',item.prop);
            //     if(item.prop == 'selectionCards'){
            //         item.clearValidate()
            //     }
            // })
            // if(this.form.useType != 1){
            //     console.log('888');
            //     this.$refs[formName].clearValidate(['selectionCards'])
            // }
            if(this.form.useType == 1 && !this.form.selectionCards.length){
                this.$message.error('请选择卡名称')
                return false
            }
            this.$refs[formName].validate((valid,obj) => {
                if (valid) {
                    this.showBaseInfo = false
                    this.showOfferDetails = true
                    this.defaultActive = 1
                } else {
                    this.$message.error('请完成基本信息填写')
                    return false;
                }
            });
            //选中的优惠油品
            this.oilOption = []
             this.oils.forEach(item=>{
                this.form.selectOilsList.forEach(value=>{
                    if(item.oil_id == value){
                        this.oilOption.push(item)
                    }
                })
            })

            let myOilOption = this.oilOption.map(item=>item.oil_id)
            console.log('oilOption', myOilOption);
            this.detailList[0].oil = []
            this.oilOption.forEach(item=>{
                this.detailList[0].oil.push({
                    oil_id:item.oil_id,
                    oil_number:item.oil_number
                })
            })

            //讲选中的优惠油品中没有的剔除
            this.detailList.forEach(item=>{
                for(let i = 0; i < item.oil.length ;i++){
                    console.log('oil', item.oil[i].oil_id);
                    console.log('oil_id', myOilOption.includes(item.oil[i].oil_id));
                    if(!myOilOption.includes(item.oil[i].oil_id)){
                        item.oil.splice(i,1)
                        i--
                    }
                }

            })
        },
        //上一步到基本详情
        basePre(){
            this.showBaseInfo = true
            this.showOfferDetails = false
            this.defaultActive = 0
        },
        //下一步到优惠限制
        detailNext(){
            console.log(this.$refs.detailForm);
            let flag = true
             this.$refs.detailForm.forEach(item=>{
                 item.validate((valid)=>{
                     if(!valid){
                        flag = false
                     }
                 })
             })
                if (flag) {
                    this.showOfferDetails = false
                    this.showControl = true
                    this.defaultActive = 2
                } else {
                    // this.$message.error('请完成基本信息填写')
                    return false;
                }
        },
        //上一步到优惠限制
        detailPre(){
            this.showOfferDetails = true
            this.showControl = false
            this.defaultActive = 1
        },
        submit(){
            this.setCardChargeRuleBatch()
        },
        //添加优惠
        addCal(index){
            this.detailList[index].calculationList.push({
                    startNum:0, //充值起始
                    endNum:0,   //充值最大
                    calculationNum:0,   //降价多少
            })
        },
        //删除优惠
        deleteCal(index,index2){
            this.detailList[index].calculationList.splice(index2,1)
        },
        //添加详情
        addDetails(){
            this.detailList.push({
                    oil:'',
                    calculationList:[
                        {
                            LowerLimit:0, //充值起始
                            UpperLimit:0, //充值最大
                            Discounts:0, //降价多少
                        }
                    ]
                })
        },
        deleteDetails(index){
            this.detailList.splice(index,1)
        },
        //创建规则
        setCardChargeRuleBatch(){
            let oils_list = []
            this.detailList.forEach((item,index)=>{
                let Oilsmxes = JSON.parse(JSON.stringify(item.calculationList))
                //将前端数据number转成string输出给后端
                Oilsmxes.forEach(obj=>{
                    for (const key in obj) {
                        obj[key] = String(obj[key])
                    }
                })
                item.oil.forEach(value=>{
                    // console.log('calculationList',Oilsmxes);
                    oils_list.push({
                        Oil_Group:index,
                        OilsBH: value.oil_number,    //油品编号
                        OilsID: value.oil_id,        //油品ID
                        Oilsmxes: Oilsmxes  //油品规则
                    })
                })
            })
            // console.log('oils_list',oils_list);

            let params = {
                public_config:{
                    id:this.id,
                    rule_name:this.form.ruleName,   //规则名
                    start_time:this.form.time[0],   //时间
                    end_time:this.form.time[1],
                    state:this.state,  //状态100启用 101禁用
                    use_station_list:this.form.selectionStids,  //油站
                    priority:this.priority, //优先级
                },
                extend_rule:{
                    card_theme:this.form.selectionCards,    //卡名称
                    GradeStr:this.form.GradeStr,    //会员等级id
                    pay_way:this.form.selectPayTypeList,    //付款方式
                    account_type:this.form.account_type,
                    company_id:this.form.company_id,    //车队ID
                    customer_group_id:this.form.customer_group_id,  //卡组id
                    card_type:this.form.customeTrType,   //卡类型,1个人卡，2车队卡，3不记名卡
                },
                oils_list:oils_list,    //油品集合
                discount_config:{
                    IFBonus:this.IFBonus, //是否与积分抵现共享1：不限制，0：不可共享
                    IFVoucher:this.IFVoucher,   //是否与抵扣券共享1：不限制，0：不可共享
                    share:this.share, //是否与其他优惠共享1：不限制，0：不可共享
                },
                time_rule:{
                    rule:[
                        {
                            start_time:this.form.time[0],
                            end_time:this.form.time[1]
                        }
                    ],
                    type:1
                }
            }

            //制卡规则
            if(this.form.useType == 1){
                if(this.form.account_type == 4){    //不限制
                    params.extend_rule.account_type = ''
                    params.extend_rule.customer_group_id = []
                    params.extend_rule.company_id = []
                    params.extend_rule.GradeStr = []
                }else if(this.form.account_type == 3){  //会员等级
                    params.extend_rule.customer_group_id = []
                    params.extend_rule.company_id = []
                    if(this.form.selectLevelsType == '0'){  //会员等级不限制
                        params.extend_rule.GradeStr = ''
                    }
                    // else if(this.form.selectLevelsType == '1'){
                    //     params.extend_rule.GradeStr = 'nonmember'
                    // }
                }else if(this.form.account_type == 2){  //卡组
                    params.extend_rule.company_id = []
                    params.extend_rule.GradeStr = []
                    if(this.form.selectCustomerGroupType == '0'){   //卡组不限制
                        params.extend_rule.customer_group_id = ''
                    }
                }
                // else if(this.form.account_type == 0){  //车队
                //     params.extend_rule.customer_group_id = []
                //     params.extend_rule.GradeStr = []
                //     if(this.form.selectCompanyListType == '0'){   //车队不限制
                //         params.extend_rule.company_id = ''
                //     }
                // }
            }else if(this.form.useType == 0){  //车队客户
                params.extend_rule.account_type = '0'
                params.extend_rule.card_theme = []
                params.extend_rule.customer_group_id = []
                params.extend_rule.GradeStr = []
                if(!this.form.company_id.length){   //车队不限制
                    params.extend_rule.company_id = ''
                }
            }
            //选择个人卡 不记名卡
            // if((this.form.customeTrType.includes('1')||this.form.customeTrType.includes('3')) && !this.form.customeTrType.includes('2')){
            //     params.extend_rule.account_type = 1
            // }
            // console.log('account_type',this.form.account_type);
            this.isCreateDisabled = true
            this.$axios.post('CardRule/setCardChargeRuleBatch',params)
                .then(res=>{
                    console.log('res',res);
                    if(res.data.status == 200){
                        this.$message.success(this.isCreate ? "创建成功" : '修改成功');
                        // this.cacheArray.remove('ruleCreation')
                        // this.$router.push('/MarketingRules')
                        this.clearData()
                        this.$emit('exit')
                    }else{
                        this.$message.error(res.data.info);
                    }
                }).catch(err=>{
                    console.log('err',err);
                }).finally(()=>{
                    this.isCreateDisabled = false
                })
        },
        getRuleOil(){
            this.$axios.post('CardRule/getRuleOil').then(res=>{
                console.log('油品',res.data.data);
                if(res.data.status == 200){
                    this.oils = res.data.data
                }
            })
        },
        //获取卡列表,根据选择的可用油站获取相应的列表
        getCustomerGroupList(){
            /*接口做了分页，暂时定义了500条数据 */
            this.$axios.post('/CustomerGroup/getCustomerGroupList',{
                page:1,
                page_size:500,
            }).then((res)=>{
                console.log('卡组',res.data.data);
                if(res.data.status == 200){
                    this.customerGroupList = res.data.data.dt;
                }else{
                    this.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
        },
        //获取车队信息列表
        getCompanyList(){
            this.$axios.post('/CompanyCard/getCompanyList', {
                page: 1,
                page_size: 1000,
                input: "",
                state:100
            })
            .then((res)=> {
                console.log('车队信息',res.data.data);
                if(res.data.status == 200){
                    this.companyList = res.data.data.dt;
                }else{
                    this.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
            });
        },
        //改变用户限制
        changeLimit(value){
            console.log('value',value);
        }
    },
    watch:{
        // $route(newValue){
        //     // console.log('newValue',newValue);
        //     if(newValue.query.id != ''){
        //         this.createDate()
        //     }
        // },
        // id(newValue){
        //     if(newValue){
        //         this.createDate()
        //     }
        // },
         getCurrentStation(newValue,oldValue){
            if(newValue.merchant_type != 0 && newValue.value != oldValue.value){
                this.getRuleOil()
                this.getCardThemeRuleList()
                this.getChargeRuleTem()
                this.getStationList()
                this.getCompanyList()
                this.getCustomerGroupList()
            }
        }
    }
}
</script>
<style>
    [v-cloak]{
        display: none;
    }
    #ruleCreation{
        margin-top: 20px;
    }
    .el-step__head.is-process{
        border-color: #32AF50;
    }
    .el-step__title.is-process{
        color: #32AF50;
    }
    .baseInfo{
        margin-left: 30px;
    }
    .cardbox{
        padding: 20px;
        border: 1px solid #DADADA;
        border-radius: 4px;
        width:500px
    }
    .detailBox{
        font-size: 14px;
        color: #606266;
        width: 960px;
        /* border: 1px solid #DADADA; */
        /* border-radius: 4px; */
        padding: 30px;
        margin:20px 0px;
        position: relative;
    }
    .detailBoxBtn{
        position: absolute;
        right: -6%;
        top: 50%;
        transform: translateY(-50%);
    }
    .calculation{
        margin-top: 8px;
    }
    .footer{
        margin-left: 100px;
    }
</style>
