import {test,expect} from '@playwright/test'
import exp from "constants";

const baseURL = 'http://mc-web.wcc.cn/#'

let operatorList = [];

test.beforeEach('登录',async ({page})=>{
  await page.goto(`${baseURL}/login`)
  await page.locator('#loginname').fill('admin')


  const loginUrl = '**/auth/login';
  const loginRequest = page.waitForRequest(loginUrl)
  const loginResponse = page.waitForResponse(loginUrl)
  const operatorResponse = page.waitForResponse('**/operator/getNameList')
  await page.locator('#password').press('Enter')

  const [loginRes,loginResult] = await Promise.all([loginRequest,loginResponse]);
  // 判断当前当前登录账号密码
  await expect(loginRes.postData()).toEqual('loginname=admin');
  // 判断登录结果
  await expect(loginResult.ok()).toBeTruthy();
  const loginBody = await loginResult.json();
  await expect(loginBody).toHaveProperty('record.token')
  await expect(loginBody.record.token).toBeTruthy()

  // 获取运营商列表
  const operatorRes = await operatorResponse
  const operatorBody = await operatorRes.json();
  await expect(operatorRes.ok()).toBeTruthy();
  await expect(operatorBody).toHaveProperty('records');
  await expect(operatorBody.records.length).toBeGreaterThan(0);
  operatorList = operatorBody.records;
})
test('分账列表查询',async ({page})=>{
  const stationUrl ='**/admin/station/getNameList';
  let stationRequest = page.waitForRequest(stationUrl)
  let stationResponse = page.waitForResponse(stationUrl)
  await page.goto(`${baseURL}/system/settlement/settleRecords`)
  // 进入页面获取油站列表
  const [stationResult,stationRes] = await Promise.all([stationResponse,stationRequest])
  if(operatorList.length > 1){
    await expect(stationRes.postData()).toEqual('isPrivate=0')
    await expect(stationResult.ok()).toBeTruthy();
    const body = await stationResult.json();
    // 油站大于0
    await expect(body.records.length).toBeGreaterThan(0);
  }
  await page.locator('[data-cy="operator"]').click();
  // 断言运营商渲染
  const operatorOptions = page.locator('[data-cy="operatorOption"]');
  await expect(await (operatorOptions)).toHaveCount(operatorList.length)
  await expect(operatorOptions).toHaveText([...operatorList.map(item=>item.shortName)])

  // 选中某一个运营商
  const operatorIndex = operatorList.findIndex(item => item.shortName === 'SC充电实测')

  const stationBoUrl = '**/admin/station/getBoNameList'
  const stationRequest2 = page.waitForRequest(stationBoUrl)
  const stationResponse2 = page.waitForResponse(stationBoUrl)
  await operatorOptions.nth(operatorIndex).click()
  const [stationResult2,stationRes2] = await Promise.all([stationResponse2,stationRequest2])
  // 判断请求参数
  await expect(stationRes2.postData()).toEqual(`operatorIds=${operatorList[operatorIndex].id}`)
  await expect(stationResult2.ok()).toBeTruthy();
  const body2 = await stationResult2.json();
  // 油站大于0
  await expect(body2.records.length).toBeGreaterThan(0);

  // 点击所属电站
  page.locator('[data-cy="station"]').click();
  const stationOptions = page.locator('[data-cy="stationOption"]');
  await expect(await (stationOptions)).toHaveCount(body2.records.length)
  await expect(stationOptions).toHaveText([...body2.records.map(item=>item.name)])
})
