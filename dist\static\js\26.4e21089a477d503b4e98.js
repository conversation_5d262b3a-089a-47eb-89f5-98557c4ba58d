webpackJsonp([26],{cbWt:function(t,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var s=e("Xxa5"),n=e.n(s),l=e("exGp"),r=e.n(l),i=e("Dd8w"),c=e.n(i),d=e("Keoy"),u=e("NYxO"),o={name:"Setting",data:function(){return{cardInstallVar:[{id:"",type:"",install_value:"",extend:""},{id:"",type:"",install_value:"",extend:""}]}},mounted:function(){if(void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.getCardConfig()},computed:c()({},Object(u.c)({getCurrentStation:"getCurrentStation"})),methods:{getCardConfig:function(){var t=this;return r()(n.a.mark(function a(){var e;return n.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,Object(d.d)();case 2:(e=a.sent).data&&200==e.data.status?e.data.data.forEach(function(a){"ZHFP"==a.Type&&(t.cardInstallVar[0].id=a.ID,t.cardInstallVar[0].type=a.Type,t.cardInstallVar[0].install_value=String(a.InstallValue),t.cardInstallVar[0].extend=a.Extend),"CardGroup"==a.Type&&(t.cardInstallVar[1].id=a.ID,t.cardInstallVar[1].type=a.Type,t.cardInstallVar[1].install_value=String(a.InstallValue),t.cardInstallVar[1].extend=a.Extend)}):t.$message({message:e.data.info,type:"error"});case 4:case"end":return a.stop()}},a,t)}))()},setting:function(){var t=this;Object(d.i)({cardInstallVar:this.cardInstallVar}).then(function(a){200==a.data.status?t.$message({message:"设置成功",type:"success"}):t.$message({message:a.data.info,type:"error"})})}},watch:{getCurrentStation:function(t,a){0!=t.merchant_type&&t.value!=a.value&&this.getCardConfig()}}},v={render:function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"setting"},[e("div",{staticClass:"item"},[e("p",{staticClass:"title"},[t._v("扣款方式")]),t._v(" "),e("div",{staticClass:"content"},[e("span",{staticClass:"tab"},[t._v("扣款方式：")]),t._v(" "),e("el-radio-group",{model:{value:t.cardInstallVar[0].install_value,callback:function(a){t.$set(t.cardInstallVar[0],"install_value",a)},expression:"cardInstallVar[0].install_value"}},[e("el-radio",{attrs:{label:"1"}},[t._v("按比例扣款"),e("span",{staticClass:"tips",class:{active:1==t.cardInstallVar[0].install_value}},[t._v("赠金 ÷ (本金 + 赠金)")])]),t._v(" "),e("el-radio",{attrs:{label:"0"}},[t._v("本金优先")])],1)],1)]),t._v(" "),e("div",{staticClass:"item"},[e("p",{staticClass:"title"},[t._v("卡组管理")]),t._v(" "),e("div",{staticClass:"content"},[e("span",{staticClass:"tab"},[t._v("加入卡组：")]),t._v(" "),e("el-radio-group",{model:{value:t.cardInstallVar[1].install_value,callback:function(a){t.$set(t.cardInstallVar[1],"install_value",a)},expression:"cardInstallVar[1].install_value"}},[e("el-radio",{attrs:{label:"0"}},[t._v("单卡加入多个卡组")]),t._v(" "),e("el-radio",{attrs:{label:"1"}},[t._v("单卡加入单个卡组")])],1)],1)]),t._v(" "),e("div",{staticClass:"item"},[e("div",{staticClass:"content"},[e("el-button",{attrs:{type:"primary"},on:{click:t.setting}},[t._v("确认")])],1)])])},staticRenderFns:[]};var p=e("VU/8")(o,v,!1,function(t){e("g8zp")},"data-v-e8f2ae04",null);a.default=p.exports},g8zp:function(t,a){}});