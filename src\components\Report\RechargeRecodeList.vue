<template>
    <div class="left_select">
        <div class="report-content">
            <div>
                <el-radio-group v-model="typeValue" @change="changeTypeValue">
                    <el-radio-button v-for="item in typeOptions" :key="item.value" :label="item.value">{{item.label}}</el-radio-button>
                </el-radio-group>
                <span class="txt" v-if="getCurrentStation.merchant_type == 2">油站名称</span>
                <el-select v-model="stationId" :multiple="typeValue == 1" clearable collapse-tags style="width:250px;margin-right:20px;" placeholder="请选择油站" v-if="getCurrentStation.merchant_type == 2 && update" @change="changeStationValue">
                    <el-option
                    v-for="(item,index) in stationOptions"
                    :key="index"
                    :label="item.stname"
                    :value="item.stid">
                    </el-option>
                </el-select>
                <el-date-picker
                        v-show="typeValue == 1"
                        v-model="dateValue"
                        type="datetimerange"
                        :default-time="['00:00:00', '23:59:59']"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :picker-options="EndTime"
                        @change="clearData">
                    </el-date-picker>
                    <banci-date-time
                        ref="banciRef"
                        :stationValue="stationId"
                        :dateValue="dateBanciValue"
                        @searchDate="searchBanciDate"
                        @changeDate="changeBnaciDate"
                        :picker-options="EndTime"
                        v-show="typeValue == 4">
                    </banci-date-time>
                    <!-- <el-date-picker
                        v-show="typeValue == 4"
                        style="margin-right:15px"
                        v-model="dateBanciValue"
                        type="date"
                        placeholder="选择日期"
                        format="yyyy 年 MM 月 dd 日"
                        value-format="yyyy-MM-dd"
                        @change="clearData">
                    </el-date-picker>
                    <span class="txt" v-show="typeValue == 4">查看班次</span>
                <el-checkbox-group v-model="checkList" v-show="typeValue == 4" @change="searchBanci" class="banci">
                    <el-checkbox v-for="item in classList" :key="item.index" :label="item">{{item.bcmc}}</el-checkbox>
                </el-checkbox-group>
                <span v-show="typeValue == 4 && !classList.length " class="banci">暂无班次</span> -->
            </div>
            <div class="content_header">
                <div class="left">
                    <span class="txt">查询类型</span>
                    <el-radio-group v-model="searchTypeValue" @change="changeValue">
                        <el-radio label="1">手机号</el-radio>
                        <el-radio label="2">订单号</el-radio>
                        <el-radio label="3">卡号</el-radio>
                    </el-radio-group>
                    <el-input v-if="searchTypeValue==1" v-model="phone" style="width:210px" placeholder="请输入手机号" clearable></el-input>
                    <el-input v-if="searchTypeValue==2"  v-model="order_code" style="width:210px" placeholder="请输入订单号" clearable></el-input>
                    <el-input v-if="searchTypeValue==3"  v-model="vipcard_no" style="width:210px" placeholder="请输入卡号" clearable></el-input>
                    <el-button type="primary" @click="page=1;tableData=[];noMore=true;changeDate()" :disabled="!btn_disabled">生成</el-button>

                </div>
                <div class="right">
                    <el-button type="primary" :disabled="!dateValue" @click="printContent">打印</el-button>
                    <el-button type="primary" :disabled="!dateValue" @click="cardChargeDownload" v-show="isTotalReportForm">下载数据</el-button>
                </div>
            </div>

            <!-- 充值收付款流水表 -->
            <div id="myTable" ref="print">
                <div class="report" style="text-align: center;">
                <div class="report_title">充值收付款流水表</div>
                <div class="tips">
                    <div v-if="isGroup">集团名称：{{getCurrentStation.label}}</div>
                    <div v-else>油站名称：{{getCurrentStation.label}}</div>
                    <div>开始日期：{{dateValue?dateValue[0]:""}}</div>
                    <div>结束日期：{{dateValue?dateValue[1]:""}}</div>
                    <div>单位：元</div>
                </div>

                <el-table :data="tableData" v-loading="loading" size="small" align="center">
                    <el-table-column align="center" prop="pay_datetime" label="充值时间" min-width="140px"></el-table-column>
                    <el-table-column align="center" prop="order_code" label="订单号" min-width="180px"></el-table-column>
                    <el-table-column align="center" prop="card_no" label="储值卡号" min-width="180px" :formatter="formatterCellval"></el-table-column>
                    <el-table-column align="center" prop="cardholder_name" label="客户名称" min-width="72px" :formatter="formatterCellval"></el-table-column>
                    <el-table-column align="center" prop="phone" label="客户手机号码" min-width="95px" :formatter="formatterCellval"></el-table-column>
                    <el-table-column align="center" label="本金账户充值金额" min-width="120px">
                        <template slot-scope="scope">{{Number(scope.row.orig_amount).toFixed(2)}}</template>
                    </el-table-column>
                    <el-table-column align="center" label="赠金账户充值金额" min-width="120px">
                        <template slot-scope="scope">{{Number(scope.row.gift_amount).toFixed(2)}}</template>
                        、</el-table-column>
                    <el-table-column align="center" label="充值金额合计" min-width="95px">
                        <template slot-scope="scope">{{Number(scope.row.charge_money).toFixed(2)}}</template>
                    </el-table-column>
                    <el-table-column align="center" prop="refund_orig_amount" label="本金账户退款金额" min-width="120px" :formatter="formatterCellval">
                        <template slot-scope="scope">{{Number(scope.row.refund_orig_amount).toFixed(2)}}</template>
                    </el-table-column>
                    <el-table-column align="center" prop="refund_gift_amount" label="赠金账户退款金额" min-width="120px" :formatter="formatterCellval">
                        <template slot-scope="scope">{{Number(scope.row.refund_gift_amount).toFixed(2)}}</template>
                    </el-table-column>
                    <el-table-column align="center" prop="refund_charge_money" label="退款金额合计" min-width="95px" :formatter="formatterCellval">
                        <template slot-scope="scope">{{Number(scope.row.refund_charge_money).toFixed(2)}}</template>
                    </el-table-column>
                    <el-table-column align="center" label="净收款金额" min-width="85px">
                        <template slot-scope="scope">{{Number(scope.row.clear_charge_money).toFixed(2)}}</template>
                    </el-table-column>
                    <el-table-column align="center" prop="pay_way" label="充值方式" min-width="72px" :formatter="formatterCellval"></el-table-column>
                </el-table>
                <p v-if="!noMore" style="cursor: pointer; color:#32AF50" @click="scrollMore">点击加载更多</p>
                <div class="table_des">
                    <div class="table_des_text">
                        <p>注：</p>
                        <div>
                            <p>1. 充值金额合计 = 本金账户充值金额 + 赠金账户充值金额。</p>
                            <p>2. 退款金额合计 = 本金账户退款金额 + 赠金账户退款金额。</p>
                            <p>3. 净收款金额 = 当笔本金账户充值金额 - 当笔本金账户退款金额。</p>
                        </div>
                    </div>
                </div>
                <div class="des_bottom">
                    <div>制表人：{{orderMaker}}</div>
                    <div>制表时间：{{orderMakingTime}}</div>
                    <div>签字：</div>
                </div>
                </div>
            </div>
        </div>

        <!-- 下载中心提示 -->
        <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>
    </div>
</template>

<script>
import DownloadTips from '../DownloadTips.vue';
import BanciDateTime from '../Banci/banciDateTime.vue'
import {mapGetters} from 'vuex'
import {EndTime} from "./endTime";
export default {
    name: 'RechargeRecodeList',
    components:{
        DownloadTips,
        BanciDateTime
    },
    data() {
        return {
          EndTime:EndTime,
          isTotalReportForm: true,
            typeOptions:[{
                value:1,
                label:"按自然日期",
            },{
                value:4,
                label:"按班结日期",
            }],
            typeValue:1,
            dateBanciValue:'',
            checkList:[],   //选择班次
            classList:[],   //班次列表
            stationId:[],
            stationOptions: [],//油站列表
            dateValue: [],
            tableData: [],
            loading:false,
            btn_disabled: false,
            orderMaker:"",
            orderMakingTime:"",
            oilName:"",
            arr:[],
            isGroup:true,//是否是集团账号
            showDownloadTips:false,
            searchTypeValue:"1",
            phone:"",
            vipcard_no:"",
            order_code:"",
            page:1,
            page_size:20,
            noMore:true,
            update:true,
            nowEndTime:'',
        }
    },
    mounted(){
      let userInfo = localStorage.getItem("__userInfo__") || "";
      this.isTotalReportForm = JSON.parse(userInfo).group_id != 426;
        let today = this.$moment().format('YYYY-MM-DD HH:mm:ss');
        //默认为前一天的数据
        let yesterday = this.$moment().subtract(1, 'months').format('YYYY-MM-DD');
        this.dateValue.push(yesterday + ' 00:00:00');
        this.dateValue.push(today);
        this.nowEndTime = today.slice(11)

        if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
            return false;
        }
        this.changeDate();
        if(this.getCurrentStation && this.getCurrentStation.merchant_type == 2){
            this.isGroup = true;
        }else{
            this.isGroup = false;
        }
        this.getStationList();
    },
    computed:{
        ...mapGetters({
            "getCurrentStation":"getCurrentStation"
        })
    },
    methods:{
        changeBnaciDate(value){
            console.log('value',value);
            this.dateBanciValue = value
        },
        //查询班次
        searchBanciDate(value){
            console.log('dataValue',value);
            this.dateValue = value
            this.tableData=[]
            this.changeDate()
        },
        //改变油站，相对于商品列表和卡列表都得变化
        changeStationValue(e){
            console.log('e',e);
            //获取班次
            if(this.typeValue == 4 && e){
                this.$refs.banciRef.getBanci(e)
            }
        },
        //获取可用油站
        async getStationList(){
            this.stationId = [];
            let that = this;
            await this.$axios.post('/Stations/getStationList',{}).then((res)=>{
                if(res.data.status == 200){
                    that.stationOptions = [];
                    res.data.data.forEach((item)=>{
                        that.stationId.push(item.stid);
                    })
                    that.stationOptions = res.data.data;
                }
            })
          console.log('id',that.stationId)
        },
        searchBanci(e){
            console.log('班次',e);
            if(e.length){
                let startTime = []
                let endTime = []
                //获取时间戳存入数组中
                startTime = e.map(item=> new Date(item.stime).getTime())
                endTime = e.map(item=> new Date(item.etime).getTime())
                console.log('startTime',startTime)
                console.log('endTime',endTime)
                //判断大小，选出最小值为开始时间 最大值为结束时间
                let searchEndTime = Math.max.apply(null, endTime)
                let searchStartTime = Math.min.apply(null, startTime)

                this.dateValue[0] = this.$moment(searchStartTime).format('YYYY-MM-DD hh:mm:ss')
                this.dateValue[1] = this.$moment(searchEndTime).format('YYYY-MM-DD hh:mm:ss')
                console.log('dateValue',this.dateValue);
                this.$forceUpdate()
            }else{
                this.dateValue = [];
                let startDate = this.$moment(new Date(this.dateBanciValue))
                let endDate = this.$moment(new Date(this.dateBanciValue));
                this.dateValue.push(this.$moment(startDate).format('YYYY-MM-DD')+ ' 00:00:00');
                this.dateValue.push(this.$moment(endDate).format('YYYY-MM-DD')+ ` ${this.nowEndTime}`);
            }
        },
        //加载更多
        scrollMore() {
            this.page += 1;
            this.changeDate();
        },
        //changeTypeValue
        changeTypeValue(e){
            if(e == 4){
                this.stationId = ''
                this.$refs.banciRef.clearDate()
            }else{
                this.stationId = []
            }
            //自然日默认为前一天的数据
            // let currentDateStart = this.$moment(new Date()).subtract(1,'days').format('YYYY-MM-DD HH:mm:ss');
            // let currentDateEnd = this.$moment().format('YYYY-MM-DD HH:mm:ss');
            // this.dateValue = [currentDateStart,currentDateEnd];
            //班结默认为前一天的数据
            let _today = this.$moment();
            let yesterday = _today.subtract(1, 'days').format('YYYY-MM-DD');

            this.dateValue = "";
            this.orderMakingTime = "";
            this.tableData = [];
            this.dateBanciValue = ''
            if(this.getCurrentStation.merchant_type == 1){
                this.stationId = this.getCurrentStation.merchant_id
            }
        },
        changeValue(){
            if(this.searchTypeValue == 1){
                this.vipcard_no = "";
                this.order_code = "";
            }else if(this.searchTypeValue == 2){
                this.phone = "";
                this.vipcard_no = "";
            }else{
                this.phone = "";
                this.order_code = "";
            }
        },
        //获取表格数据
        async changeDate(){
            let that = this;
            if(that.dateValue){
              console.log('时间提交111',that.dateValue)
              if(that.dateValue[0].length==10) that.dateValue[0]=that.dateValue[0]+' 00:00:00'
              if(that.dateValue[1].length==10) {
                if(that.dateValue[1] == that.$moment(new Date()).format('YYYY-MM-DD')){
                  that.dateValue[1] = that.dateValue[1]+` ${this.$moment().format('HH:mm:ss')}`;
                }else{
                  that.dateValue[1] = that.dateValue[1]+' 23:59:59'
                }
              }
                that.loading = true;
                that.btn_disabled = false;
              console.log('时间提交222',that.dateValue)
                await that.$axios.post('/CardCharge/getTotalCompanyChargeRunfundOrder', {
                    phone: that.phone,
                    order_code: that.order_code,
                    vipcard_no: that.vipcard_no,
                    start_time: that.$moment(that.dateValue[0]).unix(),
                    end_time: that.$moment(that.dateValue[1]).unix(),
                    page: that.page,
                    page_size: that.page_size,
                  station_id: that.stationId
                })
                .then(function (res) {
                    that.loading = false;
                    that.btn_disabled = true;
                    if(res.data.status == 200){
                        that.tableData = that.tableData.concat(res.data.data.order_list);
                        that.noMore = res.data.data.total - res.data.data.offset > that.page_size ? false : true ;

                        that.orderMakingTime = that.$moment().format("YYYY-MM-DD");
                        let userInfo = localStorage.getItem('__userInfo__');
                        if(userInfo && (userInfo !== "" || userInfo !== "undefined")){
                            that.orderMaker = JSON.parse(userInfo).name;
                        }
                        let currentStation = JSON.parse(localStorage.getItem("currentStation"));
                        that.oilName = currentStation.label;
                    }else{
                        that.tableData = [];
                        that.$message({
                            message: res.data.info,
                            type: 'error'
                        });
                    }
                })
                .catch(function (error) {
                    that.loading = false;
                });
            }else{
                that.tableData = [];
                that.orderMakingTime = "";
                that.orderMaker = "";
                that.oilName = "";
            }

        },
        clearData(){
            if(!this.dateValue){
                this.tableData = [];
                this.orderMakingTime = "";
                this.orderMaker = "";
                this.oilName = "";
            }
            if(this.typeValue == 4){
                this.dateValue = [];
                let startDate = this.$moment(new Date(this.dateBanciValue))
                let endDate = this.$moment(new Date(this.dateBanciValue));
                this.dateValue.push(this.$moment(startDate).format('YYYY-MM-DD')+ ' 00:00:00');
                this.dateValue.push(this.$moment(endDate).format('YYYY-MM-DD')+ ` ${this.nowEndTime}`);
            }
            if(
              this.$moment(this.dateValue[1]).format('YYYY-MM-DD') === this.$moment().format('YYYY-MM-DD') &&
              this.$moment(this.dateValue[1]).unix() > this.$moment().unix()
            ){
              this.dateValue[1] = this.$moment().format('YYYY-MM-DD HH:mm:ss')
            }
        },
        //打印
        printContent(){
            let wpt = document.querySelector('#myTable');
            let newContent = wpt.innerHTML;
            let oldContent = document.body.innerHTML;

            document.body.innerHTML = newContent;
            document.getElementsByClassName("el-table__header")[0].style.width = "100%"
            document.getElementsByClassName("el-table__header")[0].style["table-layout"] = "auto"
            document.getElementsByClassName("el-table__body")[0].style.width = "100%"
            document.getElementsByClassName("el-table__body")[0].style["table-layout"] = "auto"
            window.print(); //打印方法
            history.go(0)
            document.body.innerHTML = oldContent;
        },
        //下载数据
        cardChargeDownload(){
            let that = this;
            this.$axios.post('/CardCharge/exportTotalCompanyChargeOrders',{
                phone: that.phone,
                order_code: that.order_code,
                vipcard_no: that.vipcard_no,
                start_time:that.$moment(that.dateValue[0]).unix(),
                end_time:that.$moment(that.dateValue[1]).unix(),
                station_id: that.stationId
            }).then((res)=>{
                if(res.data.status == 200){
                    this.showDownloadTips = true;
                }else{
                    this.$message.error(res.data.info);
                }
            })
        },
    },
    watch:{
        async getCurrentStation(newValue,oldValue){
            if(newValue.merchant_type != 0 && newValue.value != oldValue.value){
                await this.getStationList()
                this.page = 1
                this.tableData = []
                await this.changeDate();
                if(this.getCurrentStation && this.getCurrentStation.merchant_type == 2){
                    this.isGroup = true;
                }else{
                    this.isGroup = false;
                }
            }
        },
        //监听班次切换变化，让多选单选重新渲染，防止报错
        'typeValue'(){
            this.update = false
            setTimeout(() => {
                this.update = true
            }, 0);
        },
        // 监听是否有日期，让生成按钮是否点击
      dateValue() {
          this.dateValue?(this.btn_disabled=true):(this.btn_disabled=false)
        }
    }
}
</script>

<style scoped>
    .left_select{
        position: relative;
        height: 100%;
        background:rgba(245,245,245,1);
        margin: 0px auto;
    }
    .report-content{
        background: #fff;
        padding:20px 0;

    }
    .report-content .content_header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 20px 0;
    }
    .report .report_title {
        font-size: 24px;
        font-weight: bolder;
    }
    .report .header_table {
        width: 100%;
        border-right:1px solid #EBEEF5;
        border-bottom:1px solid #EBEEF5;
        margin-top: 20px;
    }
    .report .header_table td {
        border-left:1px solid #EBEEF5;
        border-top:1px solid #EBEEF5;
    }
    .report .header_table_row {
        height: 40px;
    }
    .report .table_des {
        margin: 20px 0;
    }
    .report .table_des_text {
        font-size: 14px;
        display: flex;
        text-align: left;
    }
    .report .table_des_text p{
        margin: 0
    }
    .des_bottom {
        display: flex;
        justify-content: flex-end;
        font-size: 14px;
        font-weight: 500;
        margin-top: 10px;
    }
    .des_bottom div{
        padding-right: 100px;
    }
    .tips{
        display: flex;
        align-items: center;
        margin: 20px 0;
    }
    .tips div{
        min-width: 100px;
        text-align: left;
        margin-right: 40px;
    }
    .banci{
        display: inline-block;
        font-size: 14px;
        color: #606266;
    }
</style>
