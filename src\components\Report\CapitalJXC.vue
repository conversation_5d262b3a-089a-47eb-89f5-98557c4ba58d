<template>
  <div class="report">
    <div class="left_select">
      <div class="group_select">
        <el-radio-group v-model="groupValue" @change="changeGroupValue">
          <el-radio-button v-if="showGroupValue" label="2">集团查看</el-radio-button>
          <el-radio-button label="1">单站查看</el-radio-button>
        </el-radio-group>
      </div>
      <div v-if="!isGroupSettle">
        <el-radio-group v-model="typeValue" @change="changeTypeValue">
          <el-radio-button label="1">按自然日期</el-radio-button>
          <el-radio-button label="4" v-if="groupValue == 1">按开班日期</el-radio-button>
        </el-radio-group>
      </div>
      <div class="content_header">
        <div class="left">
          <el-date-picker
            v-model="dateValue"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="clearData"
          >
          </el-date-picker>
          <el-button type="primary" @click="changeDate" :disabled="!dateValue"
            >生成</el-button
          >
        </div>
        <div class="right">
          <el-button
            type="primary"
            :disabled="DailyTableData.length == 0"
            @click="printContent"
            >打印</el-button
          >
          <el-button
            type="primary"
            :disabled="DailyTableData.length == 0"
            @click="cardChargeDownload"
            v-show="isTotalReportForm"
            >下载数据</el-button
          >
        </div>
      </div>

      <div id="myTable">
        <div class="tableData reportData">
          <div class="report_title">储值卡资金进销存报表</div>
          <div class="report_header">
            <div v-if="isGroup">集团名称：{{ getCurrentStation.label }}</div>
            <div v-else>油站名称：{{ getCurrentStation.label }}</div>
            <div>开始日期：{{ dateValue ? dateValue[0] : "" }}</div>
            <div>结束日期：{{ dateValue ? dateValue[1] : "" }}</div>
            <div>单位：元</div>
          </div>
          <!-- <el-table :show-summary="getCurrentStation.merchant_type == 2" :summary-method="getSummaries" :data="DailyTableData" :span-method="objectSpanMethod" border v-loading="loading" size="small" align="center" fit> -->
          <el-table
            :data="DailyTableData"
            :span-method="objectSpanMethod"
            border
            v-loading="loading"
            size="small"
            align="center"
            fit
          >
            <el-table-column
              align="center"
              prop="date"
              :label="typeValue == 1 ? '日期' : '开班日期'"
              width="90"
            ></el-table-column>
            <el-table-column
              v-if="getCurrentStation.merchant_type == 2 && groupValue != 2"
              align="center"
              prop="stname"
              label="油站"
              width="150"
            >
            </el-table-column>
            <el-table-column
              align="center"
              prop="banci"
              label="班次"
              v-if="typeValue == 4"
              width="150"
            >
            </el-table-column>
            <el-table-column align="center" label="期初余额">
              <el-table-column
                align="center"
                label="期初总余额"
                width="100"
                prop="qichu_amt"
              >
                <template slot-scope="scope">{{
                  scope.row.qimo_amt === ""
                    ? "--"
                    : Number(scope.row.qichu_amt).toFixed(2)
                }}</template>
              </el-table-column>
              <el-table-column
                align="center"
                label="期初本金"
                width="100"
                prop="qichu_bj_amt"
              >
                <template slot-scope="scope">{{
                  scope.row.qimo_amt === ""
                    ? "--"
                    : Number(scope.row.qichu_bj_amt).toFixed(2)
                }}</template>
              </el-table-column>
              <el-table-column
                align="center"
                label="期初赠金"
                prop="qichu_skj_amt"
              >
                <template slot-scope="scope">{{
                  scope.row.qimo_amt === ""
                    ? "--"
                    : Number(scope.row.qichu_skj_amt).toFixed(2)
                }}</template>
              </el-table-column>
            </el-table-column>
            <el-table-column align="center" label="期间充值">
              <el-table-column
                align="center"
                label="充值金额"
                width="100"
                prop="recharge_amt"
              >
                <template slot-scope="scope">{{
                  Number(scope.row.recharge_amt).toFixed(2)
                }}</template>
              </el-table-column>
              <el-table-column
                align="center"
                label="充值本金"
                width="100"
                prop="recharge_bj_amt"
              >
                <template slot-scope="scope">{{
                  Number(scope.row.recharge_bj_amt).toFixed(2)
                }}</template>
              </el-table-column>
              <el-table-column
                align="center"
                label="充值赠金"
                prop="recharge_skj_amt"
              >
                <template slot-scope="scope">{{
                  Number(scope.row.recharge_skj_amt).toFixed(2)
                }}</template>
              </el-table-column>
            </el-table-column>
            <el-table-column align="center" label="期间消费">
              <el-table-column
                align="center"
                label="消费金额"
                width="100"
                prop="consume_amt"
              >
                <template slot-scope="scope">{{
                  Number(scope.row.consume_amt).toFixed(2)
                }}</template>
              </el-table-column>
              <el-table-column
                align="center"
                label="消费本金"
                width="100"
                prop="consume_bj_amt"
              >
                <template slot-scope="scope">{{
                  Number(scope.row.consume_bj_amt).toFixed(2)
                }}</template>
              </el-table-column>
              <el-table-column
                align="center"
                label="消费赠金"
                prop="consume_skj_amt"
              >
                <template slot-scope="scope">{{
                  Number(scope.row.consume_skj_amt).toFixed(2)
                }}</template>
              </el-table-column>
            </el-table-column>
            <el-table-column align="center" label="期间退款">
              <el-table-column align="center" label="充值退款">
                <el-table-column
                  align="center"
                  label="充值退款金额"
                  width="100"
                  prop="recharge_refund_amt"
                >
                  <template slot-scope="scope">{{
                    Number(scope.row.recharge_refund_amt).toFixed(2)
                  }}</template>
                </el-table-column>
                <el-table-column
                  align="center"
                  label="充值退款本金"
                  width="100"
                  prop="recharge_refund_bj_amt"
                >
                  <template slot-scope="scope">{{
                    Number(scope.row.recharge_refund_bj_amt).toFixed(2)
                  }}</template>
                </el-table-column>
                <el-table-column
                  align="center"
                  label="充值退款赠金"
                  width="100"
                  prop="recharge_refund_skj_amt"
                >
                  <template slot-scope="scope">{{
                    Number(scope.row.recharge_refund_skj_amt).toFixed(2)
                  }}</template>
                </el-table-column>
              </el-table-column>
              <el-table-column align="center" label="消费退款">
                <el-table-column
                  align="center"
                  label="消费退款金额"
                  width="100"
                  prop="consume_refund_amt"
                >
                  <template slot-scope="scope">{{
                    Number(scope.row.consume_refund_amt).toFixed(2)
                  }}</template>
                </el-table-column>
                <el-table-column
                  align="center"
                  label="消费退款本金"
                  width="100"
                  prop="consume_refund_bj_amt"
                >
                  <template slot-scope="scope">{{
                    Number(scope.row.consume_refund_bj_amt).toFixed(2)
                  }}</template>
                </el-table-column>
                <el-table-column
                  align="center"
                  label="消费退款赠金"
                  width="100"
                  prop="consume_refund_skj_amt"
                >
                  <template slot-scope="scope">{{
                    Number(scope.row.consume_refund_skj_amt).toFixed(2)
                  }}</template>
                </el-table-column>
              </el-table-column>
            </el-table-column>
            <el-table-column align="center" label="余额清零">
              <el-table-column
                align="center"
                label="清零金额"
                width="100"
                prop="clear_amt"
              >
                <template slot-scope="scope">{{
                  Number(scope.row.clear_amt).toFixed(2)
                }}</template>
              </el-table-column>
              <el-table-column
                align="center"
                label="清零本金"
                width="100"
                prop="clear_bj_amt"
              >
                <template slot-scope="scope">{{
                  Number(scope.row.clear_bj_amt).toFixed(2)
                }}</template>
              </el-table-column>
              <el-table-column
                align="center"
                label="清零赠金"
                width="100"
                prop="clear_skj_amt"
              >
                <template slot-scope="scope">{{
                  Number(scope.row.clear_skj_amt).toFixed(2)
                }}</template>
              </el-table-column>
            </el-table-column>
            <el-table-column align="center" label="余额冲正" width="100" prop="balance_correction" v-if="showAdjust==1">
              <template slot-scope="scope">
                <template v-if="scope.row.balance_correction">{{Number(scope.row.balance_correction).toFixed(2)}}</template>
                <template v-else>--</template>
              </template>
            </el-table-column>
            <el-table-column align="center" label="期末余额">
              <el-table-column
                align="center"
                label="期末总余额"
                width="100"
                prop="qimo_amt"
              >
                <template slot-scope="scope">{{
                  scope.row.qimo_amt === ""
                    ? "--"
                    : Number(scope.row.qimo_amt).toFixed(2)
                }}</template>
              </el-table-column>
              <el-table-column
                align="center"
                label="期末本金"
                width="100"
                prop="qimo_bj_amt"
              >
                <template slot-scope="scope">{{
                  scope.row.qimo_amt === ""
                    ? "--"
                    : Number(scope.row.qimo_bj_amt).toFixed(2)
                }}</template>
              </el-table-column>
              <el-table-column
                align="center"
                label="期末赠金"
                width="100"
                prop="qimo_skj_amt"
              >
                <template slot-scope="scope">{{
                  scope.row.qimo_amt === ""
                    ? "--"
                    : Number(scope.row.qimo_skj_amt).toFixed(2)
                }}</template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </div>
        <div class="table_des">
          <div class="table_des_text">
            <p>注：</p>
            <div v-if="!isGroupSettle">
              <p>
                1.仅统计油站自有储值卡会员数据，他站会员至本站消费、充值数据不统计。
              </p>
              <p>
                2.期末总余额=期初总余额+（充值金额-充值退款金额）-（消费金额-消费退款金额）。
              </p>
            </div>
            <div v-else>
              <p>
                1.期末总余额=期初总余额+（充值金额-充值退款金额）-（消费金额-消费退款金额）。
              </p>
            </div>
          </div>
        </div>
        <div class="des_bottom">
          <div>制表人：{{ orderMaker }}</div>
          <div>制表时间：{{ orderMakingTime }}</div>
          <div>签字：</div>
        </div>
      </div>
    </div>

    <!-- 下载中心提示 -->
    <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>
  </div>
</template>

<script>
import DownloadTips from "../DownloadTips.vue";
import { mapGetters } from "vuex";
export default {
  name: "CapitalJXC",
  components: {
    DownloadTips,
  },
  data() {
    return {
      isTotalReportForm: true,
      groupValue: 2, // 2集团查看，1单站查看
      showGroupValue: true, // 集团和单站判断，true是集团
      typeValue: 1,
      dateValue: [],
      BCoptions1: [],
      BCoptions2: [],
      BCValue1: "",
      BCValue2: "",
      DailyTableData: [],
      loading: false,
      start_time: "",
      end_time: "",
      orderMaker: "",
      orderMakingTime: "",
      tableStartTime: "",
      tableEndTime: "",
      oilName: "",
      arr: [],
      isGroup: true, //是否是集团账号
      showDownloadTips: false,
      isGroupSettle: Boolean,
      showAdjust: 1, //余额冲正 1显示，0不显示
    };
  },
  mounted() {
    let userInfo = localStorage.getItem("__userInfo__") || "";
    this.isTotalReportForm = JSON.parse(userInfo).group_id != 426;
    let _today = this.$moment();
    //默认为前一天的数据
    let yesterday = _today.subtract(1, "days").format("YYYY-MM-DD");
    this.dateValue.push(yesterday);
    this.dateValue.push(yesterday);

    if (
      this.getCurrentStation.merchant_type == undefined ||
      this.getCurrentStation.merchant_type == 0
    ) {
      return false;
    }
    if (this.getCurrentStation && this.getCurrentStation.merchant_type == 2) {
      this.isGroup = true;
      this.showGroupValue = true
      this.groupValue = 2
    } else {
      this.isGroup = false;
      this.showGroupValue = false
      this.groupValue = 1
    }
    this.changeDate();
  },
  computed: {
    ...mapGetters({
      getCurrentStation: "getCurrentStation",
    }),
  },
  methods: {
    //改变类型触发
    changeTypeValue() {
      this.dateValue = "";
      this.DailyTableData = [];
      this.orderMakingTime = "";
    },
    changeGroupValue(){
      if(this.groupValue == 2){
        this.typeValue = 1
      }
      this.DailyTableData = [];
    },
    //选中时间生成报表
    changeDate() {
      let that = this;
      let start_time = 0;
      let end_time = 0;
      if (that.dateValue) {
        start_time = that
          .$moment(that.dateValue[0] + " 00:00:00", "YYYY-MM-DD HH:mm:ss")
          .unix();
        end_time = that
          .$moment(that.dateValue[1] + " 23:59:59", "YYYY-MM-DD HH:mm:ss")
          .unix();
        that.getCardInvoicingReport(start_time, end_time);
      } else {
        that.DailyTableData = [];
        that.orderMakingTime = "";
        that.orderMaker = "";
        that.oilName = "";
      }
    },
    //生成报表
    getCardInvoicingReport(start_time, end_time) {
      //仅设置了集团清结算报表可以查看进销存
      let isGroupSettle = localStorage.getItem("__isGroupSettle__");
      if (isGroupSettle == 1 && this.getCurrentStation.merchant_type != 2) {
        this.DailyTableData = [];
        this.orderMakingTime = "";
        this.orderMaker = "";
        this.oilName = "";
        return;
      }
      let that = this;
      that.loading = true;
      let type = that.typeValue
      if(that.groupValue == 2){
        type = 2
      }
      that.$axios
        .post("/CardReport/getCardInvoicingReport", {
          type: type,
          start_time: start_time,
          end_time: end_time,
        })
        .then(function (res) {
          that.DailyTableData = [];
          that.loading = false;
          if (res.data.status == 200) {
            that.DailyTableData = res.data.data.card_invoicing;
            // 余额冲正
            that.$nextTick(()=>{
              that.showAdjust = res.data.data.balance_correction
            })
            var total = res.data.data.total;
            //期初期末不展示汇总
            if (total && Object.keys(total).length != 0) {
              total.date = "汇总";
              total.stname = "--";
              total.qichu_amt = "--";
              total.qichu_bj_amt = "--";
              total.qichu_skj_amt = "--";
              total.qimo_amt = "";
              total.qimo_bj_amt = "";
              total.qimo_skj_amt = "";
              total.start_time = "";
              total.end_time = "";
              total.banci = "--";
              that.DailyTableData.push(total);
            }
            if (that.typeValue == 4) {
              that.setData(that.DailyTableData, "date", "stid");
              that.setTable(that.DailyTableData, "date", "stid");
            }
            that.start_time = res.data.data.start_time;
            that.end_time = res.data.data.end_time;
            console.log(res.data.data.group_settle);
            that.isGroupSettle = res.data.data.group_settle == 1 ? true : false;
            that.tableStartTime = that
              .$moment(Number(that.start_time + "000"))
              .format("YYYY-MM-DD");
            that.tableEndTime = that
              .$moment(Number(that.end_time + "000"))
              .format("YYYY-MM-DD");
            that.orderMakingTime = that.$moment().format("YYYY-MM-DD");
            let userInfo = localStorage.getItem("__userInfo__");
            if (userInfo && (userInfo !== "" || userInfo !== "undefined")) {
              that.orderMaker = JSON.parse(userInfo).name;
            }
            let getCurrentStation = JSON.parse(
              localStorage.getItem("getCurrentStation")
            );
            that.oilName = getCurrentStation.label;

          } else {
            that.$message({
              message: res.data.info,
              type: "error",
            });
          }
        })
        .catch(function (error) {});
    },
    clearData() {
      if (!this.dateValue) {
        this.DailyTableData = [];
        this.orderMakingTime = "";
        this.orderMaker = "";
        this.oilName = "";
      }
    },
    //打印
    printContent() {
      let wpt = document.querySelector("#myTable");
      let newContent = wpt.innerHTML;
      let oldContent = document.body.innerHTML;
      document.body.innerHTML = newContent;
      document.getElementsByClassName("el-table__header")[0].style.width =
        "100%";
      document.getElementsByClassName("el-table__header")[0].style[
        "table-layout"
      ] = "auto";
      document.getElementsByClassName("el-table__body")[0].style.width = "100%";
      document.getElementsByClassName("el-table__body")[0].style[
        "table-layout"
      ] = "auto";
      window.print(); //打印方法
      history.go(0);
      document.body.innerHTML = oldContent;
      this.$print(this.$refs.print);
    },
    //下载数据
    cardChargeDownload() {
      let type = this.typeValue
      if(this.groupValue == 2){
        type = 2
      }
      this.$axios
        .get("/CardReport/cardInvoicingReportDownload", {
          params: {
            start_time: this.start_time,
            end_time: this.end_time,
            type: type,
            balance_correction: this.showAdjust,
          },
        })
        .then((res) => {
          if (res.data.status == 200) {
            this.showDownloadTips = true;
          } else {
            this.$message.error(res.data.info);
          }
        });
    },

    setTable(data, key, key01) {
      let spanOneArr = [];
      let concatOne = 0;
      let subArr = [];
      let subConcatOne = 0;
      data.forEach((item, index) => {
        if (index === 0) {
          spanOneArr.push(1);
          subArr.push(1);
        } else {
          if (
            String(item[key]) &&
            String(item[key]) == String(data[index - 1][key])
          ) {
            //当前项和前一项比较
            spanOneArr[concatOne] += 1; //相同值第一个出现的位置，统计需要合并多少行
            spanOneArr.push(0); //新增一个被合并行
            if (
              String(item[key01]) &&
              String(item[key01]) == String(data[index - 1][key01])
            ) {
              //当前项和前一项比较
              subArr[subConcatOne] += 1; //相同值第一个出现的位置，统计需要合并多少行
              subArr.push(0); //新增一个被合并行
            } else {
              subArr.push(1);
              subConcatOne = index;
            }
          } else {
            spanOneArr.push(1); //否则不合并
            concatOne = index; //指向位移
            subArr.push(1);
            subConcatOne = index;
          }
        }
      });
      var obj = {};
      obj[key] = spanOneArr;
      obj[key01] = subArr;
      this.arr = [];
      this.arr.push(obj);
    },
    //整理数据
    setData(arr, key1, key2) {
      var len = arr.length;
      var temp;
      for (var i = 0; i < len - 1; i++) {
        for (var j = i + 1; j < len; j++) {
          if (arr[j][key1] == arr[i][key1] && arr[j][key2] == arr[i][key2]) {
            temp = arr[i + 1];
            arr[i + 1] = arr[j];
            arr[j] = temp;
          }
        }
      }
      for (var i = 0; i < len - 1; i++) {
        for (var j = 0; j < len - 1 - i; j++) {
          if (
            arr[j + 1][key1] == arr[j][key1] &&
            arr[j + 1][key2] == arr[j][key2] &&
            arr[j]["start_time"] < arr[j + 1]["start_time"]
          ) {
            // 相邻元素两两对比
            let temp1 = arr[j + 1]; // 元素交换
            arr[j + 1] = arr[j];
            arr[j] = temp1;
          }
        }
      }
      return arr;
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      //只有类型是班结才进行合并
      if (this.arr[0] && this.typeValue == 4) {
        if (columnIndex === 0) {
          const _row = this.arr[0].date[rowIndex]; //因为rowIndex出现会从1到结尾
          const _col = _row > 0 ? 1 : 0;
          return {
            rowspan: _row,
            colspan: _col,
          };
        }
        if (columnIndex === 1 && this.getCurrentStation.merchant_type == 2) {
          const stname_row = this.arr[0].stid[rowIndex]; //因为rowIndex出现会从1到结尾
          const stname_col = stname_row > 0 ? 1 : 0;
          return {
            rowspan: stname_row,
            colspan: stname_col,
          };
        }
      }
    },
    //汇总
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "汇总";
          return;
        } else if (
          index == 1 ||
          index == 2 ||
          index == 3 ||
          index == 4 ||
          index == 22 ||
          index == 21 ||
          index == 20
        ) {
          //期初余额，期末余额不计算
          sums[index] = "--";
          return;
        }
        const values = data.map((item) => item[column.property]);
        if (!values.every((value) => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] = sums[index].toFixed(2);
        } else {
          sums[index] = "--";
        }
      });
      return sums;
    },
  },
  watch: {
    getCurrentStation(newValue, oldValue) {
      if (newValue.merchant_type != 0 && newValue.value != oldValue.value) {
        if (
          this.getCurrentStation &&
          this.getCurrentStation.merchant_type == 2
        ) {
          this.isGroup = true;
          this.showGroupValue = true
          this.groupValue = 2
          this.typeValue = 1
        } else {
          this.isGroup = false;
          this.showGroupValue = false
          this.groupValue = 1
        }
        this.changeDate();
      }
    }
  },
};
</script>

<style scoped>
.left_select {
  position: relative;
  height: 100%;
  margin: 0px auto;
  padding: 20px 0;
}
.group_select {
  margin-bottom: 20px;
}
.report .segmentation_view {
  background-color: #e4e4e4;
  position: fixed;
  left: 27%;
  top: 62px;
  width: 10px;
  height: 100%;
}
.report .content_header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20px 0;
}
.report .content_header1 {
  display: flex;
  margin-top: 10px;
  margin-left: 10px;
}
.report .content_header2 {
  display: flex;
  margin-left: 10px;
  margin-top: 10px;
}
.tableData {
  text-align: center;
}
.tableData .report_title {
  font-size: 24px;
  font-weight: bolder;
  margin-top: 20px;
}
.tableData .report_header {
  display: flex;
  margin: 10px 20px;
}
.tableData .report_header div {
  min-width: 100px;
  text-align: left;
  margin-right: 40px;
}
.tableData .header_table {
  width: 100%;
  border-right: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
  margin-top: 20px;
}
.tableData .header_table td {
  border-left: 1px solid #ebeef5;
  border-top: 1px solid #ebeef5;
}
.tableData .header_table_row {
  height: 40px;
}
.report .table_des {
  margin: 20px 0;
}
.report .table_des_text {
  font-size: 14px;
  display: flex;
  text-align: left;
}
.report .table_des_text p {
  margin: 0;
}
.des_bottom {
  display: flex;
  justify-content: flex-end;
  font-size: 14px;
  font-weight: 500;
  margin-top: 10px;
}
.des_bottom div {
  padding-right: 100px;
}
</style>
<style>
.reportData td:first-child,
.reportData th:first-child {
  padding-left: 0 !important;
}
</style>
