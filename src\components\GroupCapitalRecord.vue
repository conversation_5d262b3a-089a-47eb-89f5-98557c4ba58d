<template>
  <div id="GroupCapitalRecord" class="capitalRecord">
    <div class="top-box" v-if="userInfo.has_mch_id === 0">
      <el-select
        filterable
        v-model="companyValue"
        @change="changeData"
        collapse-tags
        style="width:240px"
        placeholder="请选择车队"
      >
        <el-option
          v-for="item in companyOptions"
          :key="item.ID"
          :label="item.CompanyName"
          :value="item.ID"
        >
        </el-option>
      </el-select>

      <div class="data-box" v-show="hide_time_type == 0">
        <div class="item">
          <p class="title">消费金额(元)</p>
          <p class="number hasdata">
            {{
              companyData.ConsumptionAmount == "--"
                ? companyData.ConsumptionAmount
                : Math.abs(companyData.ConsumptionAmount).toFixed(2)
            }}
          </p>
          <span class="line"></span>
          <p class="tleft">
            母账：本金<span class="hasdata">{{
              companyData.ConsumptionMZBJ == "--"
                ? companyData.ConsumptionMZBJ
                : Math.abs(companyData.ConsumptionMZBJ).toFixed(2)
            }}</span
            >元 赠金<span class="hasdata">{{
              companyData.ConsumptionMZSKJ == "--"
                ? companyData.ConsumptionMZSKJ
                : Math.abs(companyData.ConsumptionMZSKJ).toFixed(2)
            }}</span
            >元
          </p>
          <p  class="tleft">
            子账：本金<span class="hasdata">{{
              companyData.ConsumptionZZBJ == "--"
                ? companyData.ConsumptionZZBJ
                : Math.abs(companyData.ConsumptionZZBJ).toFixed(2)
            }}</span
            >元 赠金<span class="hasdata">{{
              companyData.ConsumptionZZSKJ == "--"
                ? companyData.ConsumptionZZSKJ
                : Math.abs(companyData.ConsumptionZZSKJ).toFixed(2)
            }}</span
            >元
          </p>
          <p class="tleft">
            信用额度：<span class="hasdata">{{companyData.ConsumptionMZXYED ? Math.abs(companyData.ConsumptionMZXYED) : 0  }}</span>元
          </p>
        </div>
        <div class="box-line"></div>
        <div class="item">
          <p class="title">充值金额(元)</p>
          <p class="number hasdata">{{ companyData.RechargeAmount }}</p>
          <span class="line"></span>
          <p class="tleft">
            母账：本金<span class="hasdata">{{ companyData.RechargeMZBJ }}</span
            >元 赠金<span class="hasdata">{{ companyData.RechargeMZSKJ }}</span
            >元
          </p>
          <p class="tleft">
            子账：本金<span class="hasdata">{{ companyData.RechargeZZBJ }}</span
            >元 赠金<span class="hasdata">{{ companyData.RechargeZZSKJ }}</span
            >元
          </p>
          <p class="tleft">
            信用额度：<span class="hasdata">{{companyData.RechargeMZXYED ? Math.abs(companyData.RechargeMZXYED) : 0  }}</span>元
          </p>
        </div>
        <div class="box-line"></div>
        <div class="item">
          <p class="title">当前余额(元)</p>
          <p class="number hasdata">{{ companyData.CurrentBalance }}</p>
          <span class="line"></span>
          <p class="tleft">
            母账：本金<span class="hasdata">{{ companyData.BalanceMZBJ }}</span
            >元 赠金<span class="hasdata">{{ companyData.BalanceMZSKJ }}</span
            >元
          </p>
          <p class="tleft">
            子账：本金<span class="hasdata">{{ companyData.BalanceZZBJ }}</span
            >元 赠金<span class="hasdata">{{ companyData.BalanceZZSKJ }}</span
            >元
          </p>
          <p class="tleft">
            已用信用额度：<span class="hasdata">{{companyData.Balance_UseXYED ? Number(companyData.Balance_UseXYED).toFixed(2) : 0  }}</span>元
          </p>
        </div>
        <div
          class="box-line"
          v-if="
            (companyData.CreditTotalAmount != 0 ||
              companyData.MarginMoney != 0) &&
              companyValue != 0
          "
        ></div>
        <div
          class="item"
          v-if="companyData.CreditTotalAmount != 0 && companyValue != 0"
        >
          <p class="title">信用额度(元)</p>
          <p class="number hasdata">{{ companyData.CreditBalance }}</p>
          <span class="line"></span>
          <p>
            初始额度：<span class="hasdata">{{
              companyData.CreditTotalAmount
            }}</span
            >元
          </p>
        </div>
        <div
          class="item"
          v-if="companyData.MarginMoney != 0 && companyValue != 0"
        >
          <p class="title">保证金(元)</p>
          <p class="number hasdata">
            {{ Math.abs(companyData.MarginMoney).toFixed(2) }}
          </p>
          <span class="line"></span>
        </div>
      </div>
    </div>

    <div class="header">
      <div class="group">
        <div class="classType">
          <el-radio-group
            v-model="typeClassValue"
            @change="changeClassTypeValue"
          >
            <el-radio-button
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.value"
              >{{ item.label }}</el-radio-button
            >
          </el-radio-group>
          <el-select
            v-model="stationValue"
            :multiple="typeClassValue == 1"
            clearable
            collapse-tags
            style="width:220px;margin-right:20px;"
            placeholder="请选择油站"
            v-if="
              getCurrentStation.merchant_type == 2 &&
                update &&
                typeClassValue == 4
            "
            @change="changeStationValue"
          >
            <el-option
              v-for="item in stationOptions"
              :key="item.stid"
              :label="item.stname"
              :value="item.stid"
            >
            </el-option>
          </el-select>
          <el-date-picker
            v-show="typeClassValue == 1"
            :clearable="false"
            class="date-picker"
            @change="changeData"
            v-model="dateValue"
            :picker-options="startTimePicker"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
          <banci-date-time
            ref="banciRef"
            :stationValue="stationValue"
            :dateValue="dateBanciValue"
            :picker-options="startTimePicker"
            @searchDate="searchBanciDate"
            @changeDate="changeDate"
            v-show="typeClassValue == 4"
          >
          </banci-date-time>
        </div>
        <el-radio-group v-model="checkboxGroup" @change="changeData">
          <el-radio-button
            v-for="item in typeList"
            :key="item.index"
            :label="item.value"
            ><span @click="changeType(item.value)">{{
              item.label
            }}</span></el-radio-button
          >
        </el-radio-group>
      </div>
      <div class="select">
        <div class="right">
          <span class="txt">查询类型</span>
          <el-radio-group v-model="searchTypeVlaue">
            <el-radio label="1">手机号</el-radio>
            <el-radio label="0">卡号</el-radio>
            <el-radio label="2">卡面卡号</el-radio>
            <el-radio label="5">订单号</el-radio>
          </el-radio-group>
          <el-input
            v-model="inputTxt"
            style="width:210px"
            :placeholder="
              searchTypeVlaue == '0'
                ? '请输入卡号'
                : searchTypeVlaue == '1'
                ? '请输入手机号'
                : searchTypeVlaue == '2'
                ? '请输入卡面卡号'
                : '请输入订单号'
            "
            clearable
          ></el-input>
          <el-button type="primary" @click="changeData">查询</el-button>
          <div
            style="display:inline-block;margin-left: 10px;"
            v-if="checkList.length"
          >
            <span>时间范围:</span>
            <span class="Noclass">{{ dateValue ? dateValue[0] : "-" }}</span> 至
            <span class="Noclass">{{ dateValue ? dateValue[1] : "-" }}</span>
          </div>
        </div>
        <div v-show="userInfo.group_id != 426">
          <el-button
            v-if="userInfo.has_mch_id === 0"
            type="primary"
            :disabled="tableData.length == 0"
            @click="exportCompanyCapitalRecordList">下载数据</el-button>
        </div>
      </div>
    </div>
    <el-table
      v-loading="loading"
      class="capitalRecordData"
      :data="tableData"
      style="width: 100%; margin-bottom:20px"
    >
      <el-table-column
        fixed
        align="center"
        min-width="140"
        prop="StationName"
        label="操作油站"
      >
      </el-table-column>
      <el-table-column
        align="left"
        label="卡号"
        min-width="200"
        prop="CardNO"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        min-width="180"
        label="卡面卡号"
        prop="CardFaceNumber"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        min-width="140"
        label="手机号"
        prop="Phone"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="持卡人"
        min-width="120"
        prop="cardholder_name"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        min-width="140"
        label="车牌号"
        prop="CarNumber"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        min-width="140"
        label="车队名称"
        prop="CompanyName"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        v-if="checkboxGroup == '' || checkboxGroup == 'QK'"
        align="center"
        min-width="150"
        prop="Type"
        label="变动类型"
      >
      </el-table-column>
      <el-table-column
        align="left"
        prop="Money"
        min-width="120"
        label="变动金额(元)"
      >
      </el-table-column>
      <el-table-column
        align="left"
        min-width="120"
        prop="ChangeFrontBalance"
        label="变动前金额(元)"
      >
      </el-table-column>
      <el-table-column
        align="left"
        min-width="120"
        prop="ChangeAfterBalance"
        label="变动后金额(元)"
      >
      </el-table-column>
      <el-table-column
        align="center"
        min-width="120"
        prop="AmountType"
        label="变动账户"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="TradeID"
        min-width="180"
        label="订单号"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="TradeTime"
        min-width="180"
        label="变动时间"
      >
        <template slot-scope="scope">
          <span>{{
            $moment(scope.row.TradeTime).format("YYYY-MM-DD HH:mm:ss")
          }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 页码 -->
    <div class="page_content">
      <el-pagination
        class="page_left"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-size="pageSize"
        layout="prev, pager, next"
        :total="total"
      >
      </el-pagination>
      <el-pagination
        class="page_right"
        @size-change="handleSizeChange"
        :page-sizes="[10, 15, 20, 30]"
        :page-size="pageSize"
        layout="total, sizes"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 下载中心提示 -->
    <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>
  </div>
</template>

<script>
const userInfo = JSON.parse(localStorage.getItem("__userInfo__"));

import DownloadTips from "./DownloadTips.vue";
import BanciDateTime from "./Banci/banciDateTime.vue";
import { mapGetters } from "vuex";
export default {
  name: "GroupCapitalRecord",
  components: {
    DownloadTips,
    BanciDateTime
  },
  data() {
    return {
      userInfo: userInfo,
      typeList: [
        {
          value: "",
          label: "全部"
        },
        {
          value: "CZ",
          label: "充值"
        },
        {
          value: "XF",
          label: "消费"
        },
        {
          value: "FP",
          label: "资金划拨"
        },
        {
          value: "FK",
          label: "资金返款"
        },
        {
          value: "TK",
          label: "充值退款"
        },
        {
          value: "XFCX",
          label: "消费退款"
        },
        {
          value: "QK",
          label: "余额清零"
        }
      ],
      checkboxGroup: "",
      dateValue: [], //选择日期
      companyOptions: [
        {
          CompanyName: "全部车队",
          ID: 0
        }
      ], //车队列表
      companyValue: 0, //选中车队
      tableData: [], //表格数据
      loading: true,
      total: 0, //数据总条数
      pageSize: 10,
      currentPage: 1, //默认当前页码
      inputTxt: "", //搜索内容
      searchTypeVlaue: "1",
      stationOptions: [],
      stationValue: [],
      companyData: {
        CurrentBalance: "--",
        ConsumptionAmount: "--",
        RechargeAmount: "--",
        BalanceMZBJ: "--",
        BalanceMZSKJ: "--",
        BalanceZZBJ: "--",
        BalanceZZSKJ: "--",
        ConsumptionMZBJ: "--",
        ConsumptionZZBJ: "--",
        ConsumptionMZSKJ: "--",
        ConsumptionZZSKJ: "--",
        RechargeMZBJ: "--",
        RechargeZZBJ: "--",
        RechargeMZSKJ: "--",
        RechargeZZSKJ: "--",
        CreditTotalAmount: 0,
        MarginMoney: 0
      },
      showDownloadTips: false,

      typeClassValue: 1,
      dateBanciValue: "",
      typeOptions: [
        {
          value: 1,
          label: "按自然日期"
        },
        {
          value: 4,
          label: "按班结日期"
        }
      ],
      checkList: [],
      classList: [], //班次列表
      update: true,
      hide_time_type: 1, // 0不隐藏，1和2隐藏金额
    };
  },
  mounted() {
    //获取当前时间
    const startDate = this.$moment(new Date())
      .subtract(1, "months")
      .format("YYYY-MM-DD HH:mm:ss");
    const endDate = this.$moment(new Date());
    this.dateValue.push(
      this.$moment(startDate).format("YYYY-MM-DD") + " 00:00:00"
    );
    this.dateValue.push(
      this.$moment(endDate).format("YYYY-MM-DD") + " 23:59:59"
    );

    if (
      this.getCurrentStation.merchant_type == undefined ||
      this.getCurrentStation.merchant_type == 0
    ) {
      return false;
    }

    this.dateBanciValue = this.nowDate;

    this.getCompanyList();
    this.getStations();
    this.getCompanyCapitalRecordList();
    this.getGroupBaseInfo()
  },
  computed: {
    ...mapGetters({
      getCurrentStation: "getCurrentStation"
    }),
    nowDate: function() {
      return this.$moment(new Date()).format("YYYY-MM-DD");
    },
    startTimePicker() {
      var _this = this;
      return {
        disabledDate(time) {
          if (_this.userInfo.has_mch_id === 0) {
            return false;
          }

          let endTime = new Date();
          const start = new Date();
          //最大值三个月范围限制
          start.setTime(endTime.getTime() - 3600 * 1000 * 24 * 90);
          return (
            time.getTime() > endTime ||
            time.getTime() < start ||
            time.getTime() > Date.now()
          );
        }
      };
    }
  },
  methods: {
    async getGroupBaseInfo() {
      try {
        const res = await this.$axios.post('/Ostn/getGroupBaseInfo')
        if(res.data.status != 200) return this.$message.error(res.data.info)
        this.hide_time_type = res.data.data.hide_time_type || 0
      } catch (e) {
        this.$message.error('网络错误！')
      }
    },
    changeDate(value) {
      // console.log('value',value);
      this.dateBanciValue = value;
    },
    //查询班次
    searchBanciDate(value) {
      this.dateValue = value;
      this.getCompanyCapitalRecordList();
    },
    //切换油站
    changeStationValue(val) {
      console.log("val", val);
      if (this.typeClassValue == 4 && val) {
        this.$refs.banciRef.getBanci(val);
      }
    },
    //切换班次类型
    changeClassTypeValue(e) {
      console.log("e", e);
      if (e == 4) {
        this.stationValue = "";
        this.$refs.banciRef.clearDate();
      } else {
        // this.stationValue = []
        this.dateValue = [];
        let startDate = this.$moment(new Date())
          .subtract(1, "months")
          .format("YYYY-MM-DD HH:mm:ss");
        let endDate = this.$moment(new Date());
        this.dateValue.push(
          this.$moment(startDate).format("YYYY-MM-DD") + " 00:00:00"
        );
        this.dateValue.push(
          this.$moment(endDate).format("YYYY-MM-DD") + " 23:59:59"
        );
        this.getCompanyCapitalRecordList();
      }
      //判断是否是单站，单站默认选中油站
      if (this.getCurrentStation.merchant_type == 1) {
        this.stationValue = this.getCurrentStation.merchant_id;
      }
    },
    //获取车队列表
    getCompanyList() {
      this.$axios
        .post("/CompanyCard/getSimpleCompanyList", {
          page: 1,
          page_size: 1250,
          input: "",
          state: 0
        })
        .then(res => {
          if (res.data.status == 200) {
            this.companyOptions = [
              {
                CompanyName: "全部车队",
                ID: 0
              }
            ];
            this.companyOptions = this.companyOptions.concat(res.data.data.dt);
          }else{
            this.$message.error(res.data.info)
          }
        });
    },
    //获取资金流水列表数据
    getCompanyCapitalRecordList() {
      let that = this;
      that.loading = true;
      that.companyData.CreditTotalAmount = 0;
      that.companyData.MarginMoney = 0;
      if (that.dateValue[0].length == 10)
        that.dateValue[0] = that.dateValue[0] + " 00:00:00";
      if (that.dateValue[1].length == 10)
        that.dateValue[1] = that.dateValue[1] + " 23:59:59";
      that.$axios
        .post("/CompanyCard/getCompanyCapitalRecordList", {
          Page: that.currentPage,
          PageSize: that.pageSize,
          QueryType: that.searchTypeVlaue,
          RequestStr: that.inputTxt,
          QueryStartTime: that.dateValue ? that.dateValue[0] : "",
          QueryEndTime: that.dateValue ? that.dateValue[1] : "",
          TradeType: that.checkboxGroup,
          CompanyID: that.companyValue,
          mch_arr: that.userInfo.mch_arr
        })
        .then(res => {
          that.loading = false;
          that.tableData = []; //清空
          if (res.data.status == 200) {
            that.tableData = res.data.data.list.dt;
            if (res.data.data.count) {
              that.companyData = res.data.data.count;
            } else {
              that.companyData = {
                CurrentBalance: "--",
                ConsumptionAmount: "--",
                RechargeAmount: "--",
                BalanceMZBJ: "--",
                BalanceMZSKJ: "--",
                BalanceZZBJ: "--",
                BalanceZZSKJ: "--",
                ConsumptionMZBJ: "--",
                ConsumptionZZBJ: "--",
                ConsumptionMZSKJ: "--",
                ConsumptionZZSKJ: "--",
                RechargeMZBJ: "--",
                RechargeZZBJ: "--",
                RechargeMZSKJ: "--",
                RechargeZZSKJ: "--",
                CreditTotalAmount: 0,
                MarginMoney: 0
              };
            }
            that.total = res.data.data.list.TotalQty;
          } else {
            that.total = 0;
            that.$message.error(res.data.info);
          }
        });
    },
    //改变类型
    changeData() {
      this.currentPage = 1; //默认第一页
      this.getCompanyCapitalRecordList();
    },
    //changeType
    changeType(val) {
      this.currentPage = 1;
      this.checkboxGroup = val;
      this.getCompanyCapitalRecordList();
    },
    //切换页码
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getCompanyCapitalRecordList();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getCompanyCapitalRecordList();
    },

    //获取油站类别
    getStations() {
      this.$axios.post("/Stations/getStations", {}).then(res => {
        if (res.status == 200) {
          this.stationOptions = res.data.data.station_info;
        }
      });
    },

    //下载数据
    exportCompanyCapitalRecordList() {
      this.$axios
        .post("/CompanyCard/exportCompanyCapitalRecordList", {
          Page: this.currentPage,
          PageSize: this.pageSize,
          QueryType: this.searchTypeVlaue,
          RequestStr: this.inputTxt,
          QueryStartTime: this.dateValue ? this.dateValue[0] : "",
          QueryEndTime: this.dateValue ? this.dateValue[1] : "",
          TradeType: this.checkboxGroup,
          CompanyID: this.companyValue,
          mch_arr: this.userInfo.mch_arr
        })
        .then(res => {
          if (res.data.status == 200) {
            this.showDownloadTips = true;
          } else {
            this.$message.error(res.data.info);
          }
        });
    }
  },
  watch: {
    getCurrentStation(newValue, oldValue) {
      if (newValue.merchant_type != 0 && newValue.value != oldValue.value) {
        this.getCompanyList();
        this.getStations();
        this.getCompanyCapitalRecordList();
      }
    },
    //监听班次切换变化，让多选单选重新渲染，防止报错
    typeClassValue() {
      this.update = false;
      setTimeout(() => {
        this.update = true;
      }, 0);
    }
  }
};
</script>
<style scoped>
input[type="text"]:focus,
select:focus {
  outline: none;
}
.group {
  padding-bottom: 10px;
}
.tleft{
  text-align: left;
}
.select {
  display: flex;
  justify-content: space-between;
}
.capitalRecord .header {
  padding: 20px 0;
  display: block;
  border-bottom: 1px solid #ebeef5;
}
.capitalRecord .header .date-picker {
  margin-left: 32px;
}
.capitalRecord .header .right input {
  width: 220px;
  height: 36px;
  padding-left: 12px;
  line-height: 36px;
  border: 0;
  border-radius: 5px;
}
.capitalRecord .right .search-icon {
  padding: 8px 14px;
  font-size: 20px;
  color: #c4c4c4;
  cursor: pointer;
}
.capitalRecord .right .search-icon:hover {
  color: #32af50;
}
.top-box {
  padding: 20px 0;
  font-size: 14px;
  border-bottom: 1px solid #ebeef5;
}
.top-box .data-box {
  text-align: center;
  display: flex;
  justify-content: inherit;
}
.top-box .data-box .item p {
  padding: 0 22px;
  margin: 10px 0;
  font-size: 13px;
}
.top-box .data-box .item .title {
  font-size: 16px;
}
.top-box .data-box .item .number {
  font-size: 24px;
  margin-bottom: 0;
}
.top-box .data-box .hasdata {
  color: #32af50;
  font-weight: 700;
}
.data-box .line {
  display: inline-block;
  height: 1px;
  width: 100%;
  background: #d8d8d8;
}
.top-box .box-line {
  height: 88px;
  width: 1px;
  background: #d8d8d8;
  margin: auto 20px;
}

/* 页码 */
.page_content {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}

.classType {
  margin-bottom: 20px;
}
.lookClass,
.checkClass {
  display: inline-block;
}
.Noclass {
  font-size: 14px;
  color: #606266;
}
</style>
