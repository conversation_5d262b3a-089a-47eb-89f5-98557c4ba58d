﻿.cardManagement .header{
    font-size: 14px;
    padding: 20px 0 ;
}
.cardManagement .header .txt{
    margin-right: 5px;
    margin-bottom: 10px;
    word-break: keep-all;
}
.cardManagement .header .select-box{
    margin-right: 20px;
    margin-bottom: 10px;
}
.cardManagement .header .search{
    display: inline-block;
    margin-left:20px;
}
.cardManagement .header .search input{
    width: 217px;
    padding-left: 13px;
    border: 0;
    height: 36px;
    border-radius: 5px;
    border: 1px solid #EAEAEA;

}
.cardManagement .header .search .search-icon{
    padding:8px 14px;
    font-size:20px;
    color:#C4C4C4;
    cursor: pointer;
}
.cardManagement .header .search .search-icon:hover{
    color:#32AF50;
}

.cardManagement .editAll{
    padding: 0 0 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.cardManagement .table-data{
    margin-bottom: 25px;
    border-top: 1px solid #EAEAEA;
}
.cardManagement .table-data .state{
    color: #777;
}
.cardManagement .table-data .state.active{
    color: #006BC4;
}

.dialog .main{
    padding: 0 10px;
}
.dialog .main .tips{
    margin-bottom: 35px;
}
.dialog .main .tips span{
    color: #32AF50;
}
.dialog .main .item{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}
.dialog .main .item.first-item{
    background: #F5F5F5;
    height: 48px;
    line-height: 48px;
    padding: 0 25px;
}
.dialog .main .item:last-child{
    margin-bottom: 0;
}
.dialog .main .item .tab{
    display: inline-block;
    width: 70px;
    text-align: right;
    margin-right: 16px;
}

/* 查看详情 */
.dialog .check-box{
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}
.dialog .check-box .left{
    width: 35%;
}
.dialog .check-box .right{
    width: 50%;
}
.dialog .check-box p{
    text-align: left;
    margin-bottom: 20px;
    color: #333;
    display: flex;
}
.dialog .check-box .right p {
    text-align: left;
    display: flex;
    align-items: center;
}
.dialog .check-box .cartxt .el-button--text{
    padding: 0;
}
.dialog .txt{
    color: #777;
    word-break: keep-all;
}
.cardRuleDialog .check-box{
    margin-top: 0;
}
.cardRuleDialog .check-box .left{
    width: 50%;
    margin-right: 30px;
}

/* 页码 */
.page_content {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
}