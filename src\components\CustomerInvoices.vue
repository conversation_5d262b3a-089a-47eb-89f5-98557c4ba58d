<template>
    <div class="customer" v-loading="loading">
        <div class="header_add">
            <el-button type="primary" @click="addInvoices">添加发票信息</el-button>
        </div>
        <!-- 表格 -->
        <div class="table_box">
            <el-table
                :data="tableData"
                :cell-style="transactionCellstyle"
                :header-cell-style="headerStyle"
                style="width: 100%">
                <el-table-column
                    prop="title"
                    align="left"
                    label="发票抬头">
                </el-table-column>
                <el-table-column
                    prop="snno"
                    label="税号">
                </el-table-column>
                <el-table-column
                    prop="addr"
                    label="税务登记地址">
                </el-table-column>
                <el-table-column
                    prop="phone"
                    label="税务登记电话">
                </el-table-column>
                <el-table-column
                    prop="bank_name"
                    label="开户银行">
                </el-table-column>
                <el-table-column
                    prop="bank_no"
                    label="银行账号">
                </el-table-column>
                <el-table-column
                    prop="company_names"
                    label="关联车队">
                </el-table-column>
                <el-table-column
                    label="操作">
                    <template slot-scope="scope">
                        <el-button @click="showEdit(scope.row)" type="text" size="small">编辑</el-button>
                        <el-button @click="showDelete(scope.row)" type="text" size="small">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 页码 -->
        <div class="page_content">
            <el-pagination class="page_left"
                @current-change="handleCurrentChange"
                :current-page="page"
                :page-size="pageSize"
                layout="prev, pager, next"
                :total="totalCount">
            </el-pagination>
            <el-pagination class="page_right"
                @size-change="handleSizeChange"
                :page-sizes="[10, 15, 20, 30]"
                :page-size="pageSize"
                layout="total, sizes"
                :total="totalCount">
            </el-pagination>
        </div>
        <!-- 添加 -->
        <el-dialog
            :visible.sync="addDialogVisible"
            :close-on-click-modal = false
            width="660px">
            <span slot="title" class="dialog-footer">{{isAdd ? "添加发票信息" : "编辑"}}</span>
            <div>
                <div class="add_item">
                    <div class="add_title"><span class="add_mark">*</span>  发票抬头</div>
                    <el-input class="add_input" v-model="company" placeholder="请输入发票抬头" @input="inputChange"></el-input>
                </div>
                <div class="add_item">
                    <div class="add_title"><span class="add_mark">*</span>  纳税人识别号</div>
                    <el-input class="add_input" v-model="taxNumber" placeholder="请输入纳税人识别号" @input="inputChange"></el-input>
                </div>
                <div class="add_item">
                    <div class="add_title"><span class="add_mark">*</span>  税务登记地址</div>
                    <el-input class="add_input" v-model="taxAddress" placeholder="请输入税务登记地址" @input="inputChange"></el-input>
                </div>
                <div class="add_item">
                    <div class="add_title"><span class="add_mark">*</span>  税务登记电话</div>
                    <el-input class="add_input" v-model="taxPhone" placeholder="请输入税务登记电话" @input="inputChange"></el-input>
                </div>
                <div class="add_item">
                    <div class="add_title"><span class="add_mark">*</span>  开户银行</div>
                    <el-input class="add_input" v-model="bank" placeholder="请输入开户银行" @input="inputChange"></el-input>
                </div>
                <div class="add_item">
                    <div class="add_title"><span class="add_mark">*</span>  银行账号</div>
                    <el-input class="add_input" v-model="bankAccount" placeholder="请输入银行账号" @input="inputChange"></el-input>
                </div>
                <div class="add_item">
                    <div class="add_title">  关联车队</div>
                      <el-select v-model="selectMotorcades" multiple placeholder="请选择车队（可多选）">
                        <el-option
                        v-for="item in motorcades"
                        :key="item.id"
                        :label="item.CompanyName"
                        :value="item.CompanyName">
                        </el-option>
                    </el-select>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" type="primary" @click="addConfirmAction">确 定</el-button>
                <el-button size="mini" @click="addDialogVisible = false">取 消</el-button>
            </span>
        </el-dialog>
        <!-- 删除确认 -->
        <el-dialog
            title="提示"
            :close-on-click-modal = false
            :visible.sync="deleteDialogVisible"
            width="350px"
            center>
            <div class="confirmTips">请确认是否删除发票信息</div>
            <div class="confirmTips">{{selectItem.title}}</div>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" type="primary" @click="deleteConfirmAction">确 定</el-button>
                <el-button size="mini" @click="deleteDialogVisible = false">取 消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import {mapGetters} from 'vuex'
export default {
    name:"CustomerInvoices",
    data() {
        return {
            addDialogVisible: false,
            deleteDialogVisible: false,
            isAdd: false,
            loading: false,

            page: 1,
            pageSize: 10,
            totalCount: 0,

            selectItem: {},

            company: '',
            taxNumber: '',
            taxAddress: '',
            taxPhone: '',
            bank: '',
            bankAccount: '',
            selectMotorcades: [],

            confirmDisabled: true,

            motorcades: [], // 车队

            tableData:[]
        }
    },
    mounted() {
        if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
            return false;
        }
        this.getInvoicesList()
        this.getMotorcades()
    },
    computed:{
        ...mapGetters({
            "getCurrentStation":"getCurrentStation"
        })
    },
    methods : {
        // 获取列表
        getInvoicesList:function () {
            var that = this;
            that.loading = true
            that.$axios.get('/bp/invoices', {
                params: {
                    page: that.page,
                    pagesize: that.pageSize
                }
            })
            .then(function (res) {
                that.loading = false
                if(res.data.status == 200){
                    
                    that.tableData = res.data.data.data
                    that.totalCount = parseInt(res.data.data.total) 
                }else{
            
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
                that.loading = false
                that.$message.error('获取发票信息列表出错');
            });
        },
        // 车队列表
        getMotorcades:function () {
            var that = this;
            that.loading = true
            that.$axios.post('/oscard/getCompanies', {
                page: 1,
                pagesize: 999
            })
            .then(function (res) {
                that.loading = false
                if(res.data.status == 200){
                    var companys = []
           
                    for (var i = 0; i < res.data.data.length; i++) {
                        var item = res.data.data[i];
                        item.id = item.ID;
                        companys.push(item)
                    }
                    that.motorcades = companys
                }else{
            
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
                that.loading = false
                that.$message.error('获取车队列表出错');
            });
        },
        // 添加发票
        addInvoicesAction: function () {

            var company_ids = []
            var company_names = []

            for (var i = 0; i < this.selectMotorcades.length; i++) {
                var cadeName = this.selectMotorcades[i]

                for (var j = 0; j < this.motorcades.length; j++) {
                    var cade = this.motorcades[j]
                    if (cadeName == cade.CompanyName) {
                        company_ids.push(cade.id)
                        company_names.push(cadeName)
                        break;
                    }
                }
            }

            company_ids = company_ids.join(',')
            company_names = company_names.join(',')
            
            var that = this
            that.loading = true
            that.$axios.post('/bp/add', {
                title: that.company,
                snno: that.taxNumber,
                addr: that.taxAddress,
                phone: that.taxPhone,
                bank_name: that.bank,
                bank_no: that.bankAccount,
                company_ids: company_ids,
                company_names: company_names
            })
            .then(function (res) {
                that.loading = false
                if(res.data.status == 200){
                    that.getInvoicesList()
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
                that.loading = false
                that.$message.error('新增发票信息出错');
            });

        },
        // 修改发票
        editInvoicesAction: function () {

            var company_ids = []
            var company_names = []

            for (var i = 0; i < this.selectMotorcades.length; i++) {
                var cadeName = this.selectMotorcades[i]

                for (var j = 0; j < this.motorcades.length; j++) {
                    var cade = this.motorcades[j]
                    if (cadeName == cade.CompanyName) {
                        company_ids.push(cade.id)
                        company_names.push(cadeName)
                        break;
                    }
                }
            }

            company_ids = company_ids.join(',')
            company_names = company_names.join(',')

            var that = this
            that.loading = true
            that.$axios.post('/bp/edit', {
                title: that.company,
                snno: that.taxNumber,
                addr: that.taxAddress,
                phone: that.taxPhone,
                bank_name: that.bank,
                bank_no: that.bankAccount,
                id: that.selectItem.id,
                company_ids: company_ids,
                company_names: company_names
            })
            .then(function (res) {
                that.loading = false
                if(res.data.status == 200){
                    that.getInvoicesList()
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
                that.loading = false
                that.$message.error('修改发票信息出错');
            });

        },
        // 删除发票
        deleInvoicesAction: function () {

        },
        handleCurrentChange: function (val) {
            this.page = val
            this.getInvoicesList()
        },
        handleSizeChange: function (val) {
            this.pageSize = val
            this.getInvoicesList()
        },
        // 添加
        addInvoices:function () {

            this.isAdd = true
    
            this.company = '',
            this.taxNumber = '',
            this.taxAddress = '',
            this.taxPhone = '',
            this.bank = '',
            this.bankAccount = '',
            this.selectMotorcades = []

            this.addDialogVisible = true
        },
        // 编辑
        showEdit: function (item) {
            this.isAdd = false
            this.selectItem = item

            this.company = item.title,
            this.taxNumber = item.snno,
            this.taxAddress = item.addr,
            this.taxPhone = item.phone,
            this.bank = item.bank_name,
            this.bankAccount = item.bank_no,
            this.motorcade = []
            this.selectMotorcades = item.company_names.length > 0 ? item.company_names.split(",") : []

            this.addDialogVisible = true
        },
        addConfirmAction: function () {

            var that = this
            if (that.company.length == 0 ||
                that.taxNumber.length == 0 ||
                that.taxAddress.length == 0 ||
                that.taxPhone.length == 0 ||
                that.bank.length == 0 ||
                that.bankAccount.length == 0
            ) {
                that.$message.error('*号选项内容不能为空');
                return
            }
            if (this.isAdd) {
                this.addInvoicesAction()
            }else{
                this.editInvoicesAction()
            }
            this.addDialogVisible = false
        },
        showDelete: function (item) {
            this.deleteDialogVisible = true
            this.selectItem = item
        },
        deleteConfirmAction: function () {
            this.deleteDialogVisible = false
            var that = this;
            that.loading = true
            that.$axios.post('/bp/del', {
                id: that.selectItem.id,
                title: that.selectItem.title
            })
            .then(function (res) {
                that.loading = false
                if(res.data.status == 200){
                    that.getInvoicesList()
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
                console.log(error);
                that.loading = false
                that.$message.error('删除出错');
            });
        },
        inputChange: function () {
            var that = this;
            if (that.company.length == 0 ||
                that.taxNumber.length == 0 ||
                that.taxAddress.length == 0 ||
                that.taxPhone.length == 0 ||
                that.bank.length == 0 ||
                that.bankAccount.length == 0
            ) {

            }else {

            }
        },
         // table style
        transactionCellstyle({row, column, rowIndex, columnIndex}){
            return "text-align:left";
        },
        headerStyle({row, column, rowIndex, columnIndex}){
            return "text-align:left";
        }

    },
    watch: {
        getCurrentStation(newValue,oldValue){
            if(newValue.merchant_type != 0 && newValue.value != oldValue.value){
                this.getInvoicesList()
                this.getMotorcades()
            }
        },
    },
}
</script>

<style scoped>

.customer {
    overflow: hidden;
    padding: 20px 0;
}

/* 表格 */
.table_box {
    margin-top: 20px;
}

/* 页码 */
.page_content {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
}

/* 添加弹窗 */
.add_item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.add_input {
    width: 360px;
}

.add_title {
    width: 130px;
    text-align: right;
    margin-right: 30px;
}

/* 删除弹窗 */
.confirmTips {
    text-align: center;
}

.add_mark {
    color: red;
}

</style>