import {test} from "@playwright/test";

// const project = 'ip/wx'; // 微信端
// const project = 'dev/yiye_refuel_frontend'; // 异业中心--H5项目
const project = 'ip/card-admin-frontend'; // 喂通卡储值卡管理系统前端项目
// const project = 'dev/fleet-card-frontend'; // 车队卡管理平台
// const project = 'shell-china-motorist/shell-wecom-crm-front'; // 延长壳牌私域
// const project = 'mo/operation'; // 运营平台
// const project = 'ip/mp'; // 商户平台
// const project = 'dev/yiye_refuel_miniapp'; // 视频号小程序
// const project = 'dev/wecom-crm-front'; // 私域流量前端项目

const branchName = 'feature-shenbo-customer_detail_optimization';
// const branchName = 'release-shenbo-remove_recharge_modal';

test('create branch', async ({page}) => {
  // 登录
  const url = `https://gitlab4.weicheche.cn`;
  await page.goto(url)
  await page.locator('#user_login').fill('<EMAIL>')
  await page.locator('#user_password').fill('gitlab7436746')
  await page.locator('input[type="submit"]').click()

  // 进入指定项目
  await page.goto(`${url}/${project}/branches/new`)
  await page.fill('#branch_name', branchName);
  await page.getByText('Create branch').click()
})
