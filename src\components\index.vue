<template>
  <div id="index" class="index" v-loading="fullscreenLoading">
    <div class="content">
      <div class="dataBox" v-if="userInfo.has_mch_id === 0">
        <ul class="dataList" v-show="showMoney">
          <li v-for="(item, index) in moneyList" :key="index">
            <span class="data-name">{{ item.name }}</span>
            <span class="data-money">{{ item.num }}</span>
          </li>
        </ul>
      </div>

      <div style="position:relative" v-show="showYZ">
        <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
          <el-tab-pane label="今日" name="today"></el-tab-pane>
          <el-tab-pane label="本月" name="currentMonth" v-if="!(hide_time_type == 1)"></el-tab-pane>
          <el-tab-pane label="上月" name="prevMonth" v-if="hide_time_type == 0 || hide_time_type ==3"></el-tab-pane>
        </el-tabs>
        <el-date-picker
          v-show="hide_time_type == 0"
          class="indextime"
          @change="changeTimeDate"
          v-model="selectDateInput"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </div>

      <div class="tab" v-show="showYZ">
        <ul class="tabList">
          <li
            :class="tabIndex == index ? 'on' : ''"
            v-for="(item, index) in tabList"
            :key="index"
            @click="getData(index)"
          >
            <span class="select-name">{{ item.name }}</span>
            <span class="select-money">{{ item.num }}</span>
          </li>
        </ul>
      </div>

      <div id="myChart" class="chart" v-show="showYZ"></div>
      <template v-if="userInfo.has_mch_id === 0 && showYZ && showFJ">
        <p class="quick-way">快捷入口</p>
        <ul class="menu-list">
          <li
            @click="toMenu(item)"
            v-for="(item, index) in menuList"
            :key="index"
          >
            {{ item.name }}
          </li>
        </ul>
      </template>
    </div>
  </div>
</template>

<script>
const userInfo = JSON.parse(localStorage.getItem("__userInfo__"));

import { mapGetters, mapActions } from "vuex";
export default {
  name: "index",
  data() {
    return {
      hide_time_type: 1, //0不隐藏，1隐藏本月/上月/时间选择，2隐藏上月/时间选择
      userInfo: userInfo,
      activeName: "today",
      selectDateInput: "",
      tabIndex: "0",
      menuList: [
        {
          name: "卡管理",
          url: "CardManager",
          path: "/CardManager",
          prev: []
        },
        {
          name: "充值管理",
          url: "Refund",
          path: "/Refund",
          prev: ["订单管理"]
        },
        {
          name: "充值日报表",
          url: "Report",
          path: "/Report",
          prev: ["报表管理"]
        },
        {
          name: "站间清结算报表",
          url: "BetweenStationsReport",
          path: "/BetweenStationsReport",
          prev: ["报表管理"]
        }
      ],
      tabList: [
        {
          name: "领卡会员",
          num: "-"
        },
        {
          name: "充值金额",
          num: "-"
        },
        {
          name: "消费金额",
          num: "-"
        }
      ],
      moneyList: [
        {
          name: "总余额（元）",
          num: "-"
        },
        {
          name: "本金（元）",
          num: "-"
        },
        {
          name: "赠金（元）",
          num: "-"
        }
      ],

      startDate: "2020-02-19",
      endDate: "2020-03-21",
      dateTpye: 1,
      fullscreenLoading: true,
      showMoney: true, //三个金额
      showYZ: true, //除了3个金额都隐藏
      showFJ: true,
      echartsDomInit: {}
    };
  },
  mounted() {
    let myInfo = localStorage.getItem("__userInfo__") || "";
    if(JSON.parse(myInfo).group_id == 1545){ //中国国际能源金泰山加油站
      this.tabList.splice(1,2)
      // this.showMoney = false
    }if(JSON.parse(myInfo).group_id == 1321){ //中由石化亚珠站集团
      this.showYZ = false
    }
    if(JSON.parse(myInfo).group_id == 383){ // 福建汇鸿石化
      this.showFJ = false
    }
    this.echartsDomInit = this.$echarts.init(
      document.getElementById("myChart")
    );
    this.getGroupBaseInfo()
    if (
      this.getCurrentStation.merchant_type == undefined ||
      this.getCurrentStation.merchant_type == 0
    ) {
      return false;
    }
    this.handleClick({ name: this.activeName });
    this.getBalanceSummary();
  },
  computed: {
    editableTabsValue: {
      get() {
        return this.$store.getters.getEditableTabsValue;
      },
      set(newValue) {}
    },
    editableTabs: {
      get() {
        return this.$store.getters.getEditableTabs;
      },
      set(newValue) {
        this.$store.commit("SETEDITABLETABS", newValue);
      }
    },
    tabsIndex: {
      get() {
        return this.$store.getters.getTabIndex;
      },
      set(newValue) {
        this.$store.commit("SETTABINDEX", newValue);
      }
    },
    cacheArray: {
      get() {
        return this.$store.getters.getCacheArray;
      },
      set(newValue) {
        this.$store.commit("SETCACHEARRAY", newValue);
      }
    },
    ...mapGetters({
      getCurrentStation: "getCurrentStation"
    })
  },
  methods: {
    ...mapActions({
      changeEditableTabsValue: "changeEditableTabsValue",
      changeEditableTabs: "changeEditableTabs",
      changeCacheArray: "changeCacheArray"
    }),
    async getGroupBaseInfo() {
      console.log('获取info')
      try {
        const res = await this.$axios.post('/Ostn/getGroupBaseInfo')
        if(res.data.status != 200) return this.$message.error(res.data.info)
        this.hide_time_type = res.data.data.hide_time_type || 0
      } catch (e) {
        this.$message.error('网络错误！')
      }
    },
    toMenu(val) {
      this.$emit("currentNav", {
        current: val.name,
        prev: val.prev
      });

      this.$router.push({
        name: val.url
      });

      //判断缓存数组是否存在组件
      let name = val.path.substr(1);
      let currentName = this.cacheArray.find(element => element == name);
      if (!currentName) {
        this.changeCacheArray(name);
      }

      let newTabName = ++this.tabsIndex + "";
      //判断点击的菜单是否存在editableTabs中
      let currentTab = this.editableTabs.find(
        element => element.title == val.name
      );
      if (currentTab) {
        this.changeEditableTabsValue(currentTab.name);
        return;
      } else {
        this.changeEditableTabs({
          title: val.name,
          name: newTabName,
          router: val.path,
          current: val.name,
          prev: val.prev
        });
        this.changeEditableTabsValue(newTabName);
      }
    },
    handleClick(tab, event) {
      this.selectDateInput = "";
      if (tab.name == "today") {
        this.startDate = this.$moment().format("YYYY-MM-DD");
        this.endDate = this.$moment()
          .subtract(-1, "days")
          .format("YYYY-MM-DD");
        this.dateTpye = 0;
        this.getData(this.tabIndex);
      } else if (tab.name == "currentMonth") {
        this.startDate = this.$moment()
          .startOf("month")
          .format("YYYY-MM-DD");
        this.endDate = this.$moment()
          .endOf("month")
          .subtract(-1, "days")
          .format("YYYY-MM-DD");
        this.dateTpye = 1;
        this.getData(this.tabIndex);
      } else if (tab.name == "prevMonth") {
        this.startDate = this.$moment()
          .month(this.$moment().month() - 1)
          .startOf("month")
          .format("YYYY-MM-DD");
        this.endDate = this.$moment()
          .month(this.$moment().month() - 1)
          .endOf("month")
          .subtract(-1, "days")
          .format("YYYY-MM-DD");
        this.dateTpye = 1;
        this.getData(this.tabIndex);
      } else if (tab.name == "selectDate") {
      }
    },

    changeDate() {
      this.startDate = this.$moment(this.selectDateInput[0]).format(
        "YYYY-MM-DD"
      );
      this.endDate = this.$moment(this.selectDateInput[1])
        .subtract(-1, "days")
        .format("YYYY-MM-DD");

      if (
        this.selectDateInput[0].toString() == this.selectDateInput[1].toString()
      ) {
        this.dateTpye = 0;
      } else {
        this.dateTpye = 1;
      }

      this.getData(this.tabIndex);
    },

    getData(index) {
      this.tabIndex = index;

      this.getCollarCardSumNum(this.startDate, this.endDate);
      this.getCollarCardLinear(this.startDate, this.endDate);
      this.getVipCardRchg(this.startDate, this.endDate);
    },

    drawLine(list) {
      // 基于准备好的dom，初始化echarts实例
      if (this.dateTpye == 0) {
        var xList = [
          "00",
          "01",
          "02",
          "03",
          "04",
          "05",
          "06",
          "07",
          "08",
          "09",
          "10",
          "11",
          "12",
          "13",
          "14",
          "15",
          "16",
          "17",
          "18",
          "19",
          "20",
          "21",
          "22",
          "23"
        ];
        var yList = [
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0
        ];
        list.forEach(element => {
          yList[Number(element.CollarTime)] = element.CollarNum;
        });
      } else {
        var xList = [];
        var yList = [];

        for (var element in list) {
          var e = list[element];
          xList.push(element);
          yList.push(e.CollarNum);
          name = "领卡会员";
        }
      }

      // 绘制图表
      this.$nextTick(() => {
        this.echartsDomInit.setOption({
          title: { text: "" },
          tooltip: {},
          xAxis: {
            type: "category",
            data: xList,
            name: "日期"
          },
          yAxis: {
            type: "value",
            name: "领卡数"
          },
          series: [
            {
              name: "",
              type: "line",
              data: yList,
              smooth: true
            }
          ]
        });
      });
    },

    drawLine2(list, type) {
      // 基于准备好的dom，初始化echarts实例
      let name = "";
      if (this.dateTpye == 0) {
        var xList = [
          "0",
          "1",
          "2",
          "3",
          "4",
          "5",
          "6",
          "7",
          "8",
          "9",
          "10",
          "11",
          "12",
          "13",
          "14",
          "15",
          "16",
          "17",
          "18",
          "19",
          "20",
          "21",
          "22",
          "23"
        ];
        var yList = [
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0
        ];
        list.forEach(element => {
          if (type == 2) {
            yList[Number(element.hour)] = element.consume_num;
            name = "消费金额";
          } else if (type == 1) {
            yList[Number(element.hour)] = element.deposit_sum;
            name = "储值金额";
          }
        });
      } else {
        var xList = [];
        var yList = [];

        for (var element in list) {
          var e = list[element];
          xList.push(e.batch_date);
          if (type == 2) {
            yList.push(e.consume_num);
            name = "消费金额";
          } else if (type == 1) {
            yList.push(e.deposit_sum);
            name = "储值金额";
          }
        }
      }

      // 绘制图表
      this.$nextTick(() => {
        this.$nextTick(() => {
          this.echartsDomInit.setOption({
            title: { text: "" },
            tooltip: {},
            xAxis: {
              type: "category",
              data: xList,
              name: "日期"
            },
            yAxis: {
              type: "value",
              name: name
            },
            series: [
              {
                name: "",
                type: "line",
                data: yList,
                smooth: true
              }
            ]
          });
        });
      });
    },

    //查询领卡数
    getCollarCardSumNum(startDate, endDate) {
      let that = this;
      that.$axios
        .post("/CardHome/getCollarCardSumNum", {
          start_time:
            that.$moment(startDate, "YYYY-MM-DD HH:mm:ss").valueOf() / 1000,
          end_time:
            that.$moment(endDate, "YYYY-MM-DD HH:mm:ss").valueOf() / 1000,
          mch_arr: this.userInfo.mch_arr
        })
        .then(function(res) {
          if (res.data.status == 200) {
            that.tabList[0].num = res.data.data;
          } else {
            that.$message({
              message: res.data.info,
              type: "error"
            });
          }
        })
        .catch(function(error) {});
    },

    //查询领卡数
    getCollarCardLinear(startDate, endDate) {
      let that = this;
      that.$axios
        .post("/CardHome/getCollarCardLinear", {
          start_time:
            that.$moment(startDate, "YYYY-MM-DD HH:mm:ss").valueOf() / 1000,
          end_time:
            that.$moment(endDate, "YYYY-MM-DD HH:mm:ss").valueOf() / 1000,
          query_type: that.dateTpye,
          mch_arr: this.userInfo.mch_arr
        })
        .then(function(res) {
          if (res.data.status == 200) {
            if (that.tabIndex == 0) {
              that.drawLine(res.data.data);
            }
          } else {
            that.$message({
              message: res.data.info,
              type: "error"
            });
          }
        })
        .catch(function(error) {});
    },

    //查询当前总余额
    getBalanceSummary() {
      let that = this;
      that.fullscreenLoading = true;
      that.$axios
        .post("/CardHome/getBalanceSummary", {
          mch_arr: this.userInfo.mch_arr
        })
        .then(function(res) {
          that.fullscreenLoading = false;

          if (res.data.status == 200) {
            let data = JSON.parse(res.data.data);
            let moneyList = [];
            for (let i = 0; i < data.length; i++) {
              if (data[i].AmountType == "SKJ") {
                moneyList[2] = data[i].Balance;
              } else if (data[i].AmountType == "BJ_KZ") {
                moneyList[1] = data[i].Balance;
              } else if (data[i].AmountType == "BJ_MZ") {
                moneyList[0] = data[i].Balance;
              }
            }
            if (moneyList[0]) {
              that.moneyList[0].num = (
                moneyList[0] +
                moneyList[1] +
                moneyList[2]
              ).toFixed(2);
              that.moneyList[1].num = (moneyList[0] + moneyList[1]).toFixed(2);
            } else {
              that.moneyList[0].num = (moneyList[1] + moneyList[2]).toFixed(2);
              that.moneyList[1].num = moneyList[1].toFixed(2);
            }
            that.moneyList[2].num = moneyList[2].toFixed(2);
            //this
          } else {
            that.$message({
              message: res.data.info,
              type: "error"
            });
          }
        })
        .catch(function(error) {});
    },

    //查询当前总余额
    getVipCardRchg(startDate, endDate) {
      let that = this;
      that.$axios
        .post("/CardHome/getVipStatistics", {
          type: that.dateTpye,
          start_date:
            that.$moment(startDate, "YYYY-MM-DD HH:mm:ss").valueOf() / 1000,
          end_date:
            that.$moment(endDate, "YYYY-MM-DD HH:mm:ss").valueOf() / 1000
        })
        .then(function(res) {
          if (res.data.status == 200) {
            that.tabList[2].num = res.data.data.total_consume_num;
            that.tabList[1].num = res.data.data.total_deposit_sum;
            if (that.tabIndex == 1) {
              that.drawLine2(res.data.data.list, that.tabIndex);
            } else if (that.tabIndex == 2) {
              that.drawLine2(res.data.data.list, that.tabIndex);
            }
          } else {
            that.$message({
              message: res.data.info,
              type: "error"
            });
          }
        })
        .catch(function(error) {});
    },
    changeTimeDate() {
      this.activeName = "";
      this.changeDate();
    }
  },
  watch: {
    getCurrentStation(newValue, oldValue) {
      if (newValue.merchant_type != 0 && newValue.value != oldValue.value) {
        this.handleClick({ name: this.activeName });
        this.getBalanceSummary();
      }
    }
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->

<style scoped>
.index {
  padding: 0px;
}

.content {
  padding: 20px;
  background: #fff;
}

.select-name {
  display: block;
  text-align: center;
  font-size: 16px;
  color: #4c4c4c;
  line-height: 20px;
  font-weight: bolder;
}
.select-money {
  display: block;
  text-align: center;
  font-size: 24px;
  color: #fa5a00;
  font-weight: 600;
  line-height: 28px;
}
.chart {
  width: 100%;
  height: 400px;
  margin-top: 10px;
}

.tab {
  padding-top: 5px;
}

.dataBox {
  padding: 0px 10px 20px;
  margin: 0;
}
.dataList {
  padding: 0;
  margin: 0;
}
.dataBox li {
  box-sizing: content-box;
  display: inline-block;
  width: 240px;
  padding-left: 30px;
  margin: 0;
  border-right: 1px solid #d8d8d8;
}
.dataBox li:last-child {
  border-right: none;
}
.dataBox li:first-child {
  padding-left: 0px;
}

.data-name {
  display: block;
  font-size: 16px;
  color: #333;
  line-height: 20px;
}
.data-money {
  display: block;
  font-size: 24px;
  color: #32af50;
  font-weight: 600;
  line-height: 32px;
}
.tabList {
  padding: 0;
  margin: 0;
  border-left: 1px solid rgba(231, 231, 231, 1);
}
.tabList li {
  box-sizing: content-box;
  display: inline-block;
  width: 33%;
  padding: 10px 0px;
  background: rgba(245, 245, 245, 1);
  text-align: center;
  margin: 0;
  border-top: 1px solid rgba(231, 231, 231, 1);
  border-right: 1px solid rgba(231, 231, 231, 1);
  border-bottom: 1px solid rgba(231, 231, 231, 1);
}
.tabList li.on {
  background: #fff;
  border-bottom: none;
}

.menu-list {
  margin: 0 20px;
  padding: 0px;
  display: flex;
  align-items: center;
}
.menu-list li {
  height: 64px;
  line-height: 64px;
  width: 500px;
  margin: 20px 29px 0px 0px;
  background: #fff;
  text-align: center;
  color: #32af50;
  font-size: 18px;
  list-style: none;
  display: inline-block;
  flex-direction: column;
  cursor: pointer;
  border: 1px solid #32af50;
}
.quick-way {
  width: 64px;
  text-align: left;
  margin: 0 0 0 20px;
}

.uni-list::before {
  background-color: none;
}
.indextime {
  position: absolute;
  left: 230px;
  top: 0;
  border-bottom: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
</style>
