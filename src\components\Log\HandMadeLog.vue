<template>
    <div class="log">
        <div class="header">
            <el-select
                filterable
                v-model="memberValue"
                collapse-tags
                @change="handleChangeData"
                style="width:180px;"
                placeholder="请选择人员">
                <el-option
                    v-for="(item,index) in memberOptions"
                    :key="index"
                    :label="item.name"
                    :value="item.adid">
                </el-option>
            </el-select>
            <el-select
                filterable
                v-model="companyValue"
                collapse-tags
                @change="handleChangeData"
                style="width:240px"
                placeholder="请选择车队">
                <el-option
                    v-for="(item,index) in companyOptions"
                    :key="index"
                    :label="item.CompanyName"
                    :value="item.ID">
                </el-option>
            </el-select>
            <el-date-picker
                style="width:360px"
                v-model="dateValue"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time='["00:00:00","23:59:59"]'
                value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
            <el-button type="primary" @click="handleChangeData">查询</el-button>
        </div>
        <div class="log-data" v-loading="loading">
            <div v-if="listData.length == 0" class="item no-data">
                暂无数据
            </div>
            <div class="item" v-for="item in listData" :key="item.log_id">
                <p class="txt">{{item.oper_name}} {{item.log_message}} </p>
                <p class="time">{{getTime(item.oper_date)}}</p>
            </div>
        </div>

        <!-- 页码 -->
        <div class="page_content">
            <el-pagination class="page_left"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-size="pageSize"
                layout="prev, pager, next"
                :total="total">
            </el-pagination>
            <el-pagination class="page_right"
                @size-change="handleSizeChange"
                :page-sizes="[10, 15, 20, 30]"
                :page-size="pageSize"
                layout="total, sizes"
                :total="total">
            </el-pagination>
        </div>
    </div>
</template>

<script>
import {mapGetters} from 'vuex'
export default {
    name: 'HandMadeLog',
    data () {
        return {
            memberOptions: [{
                name:"全部人员",
                adid:0,
            }],//人员列表
            memberValue: 0,//选中人员
            companyOptions: [{
                CompanyName:"全部车队",
                ID:0,
            }],//车队列表
            companyValue: 0,//选中车队
            dateValue:[],
            listData:[],
            loading:false,
            pageSize:10,
            currentPage:1,
            total:0,
        }
    },
    mounted(){
        //默认为前一月的数据
        const startDate = this.$moment(new Date()).subtract(1,'months').format('YYYY-MM-DD HH:mm:ss');
        const endDate = this.$moment(new Date());
        this.dateValue.push(this.$moment(startDate).format('YYYY-MM-DD')+ ' 00:00:00');
        this.dateValue.push(this.$moment(endDate).format('YYYY-MM-DD')+ ' 23:59:59');

        if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
            return false;
        }
        this.getMemberList();
        this.getCompanyList();
        this.getBalanceOperationList();
    },
    computed:{
        ...mapGetters({
            "getCurrentStation":"getCurrentStation"
        })
    },
    methods:{
        //改变数据
        handleChangeData(){
            this.currentPage = 1;
            this.getBalanceOperationList();
        },
        //切换页码
        handleCurrentChange(val) {
            this.currentPage = val;
            this.getBalanceOperationList();
        },
        //切换条数
        handleSizeChange(val) {
            this.pageSize = val;
            this.getBalanceOperationList();
        },
        //获取人员列表
        getMemberList(){
            this.$axios.post('/admins/getAdmins',{
                page: 1,
                pageSize: '100'
            }).then((res)=>{
                if(res.data.status == 200){
                    this.memberOptions = [{
                        name:"全部人员",
                        adid:0,
                    }];
                    this.memberOptions = this.memberOptions.concat(res.data.data);
                }else{
                    this.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
        },
        //获取车队列表
        getCompanyList(){
            this.$axios.post('/CompanyCard/getSimpleCompanyList',{
                page: 1,
                page_size: '1250',
                input: "",
                state:0,
            }).then((res)=>{
                if(res.data.status == 200){
                    this.companyOptions = [{
                        CompanyName:"全部车队",
                        ID:0,
                    }];
                    this.companyOptions = this.companyOptions.concat(res.data.data.dt);
                }else{
                    this.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
        },
        //获取列表信息
        getBalanceOperationList(){
            let that = this;
            that.loading = true;
            that.listData = [];
            if(that.dateValue){
                that.$axios.post('/card/getBalanceOperationList', {
                    startDate:that.dateValue[0],
                    endDate:that.dateValue[1],
                    page:that.currentPage,
                    pagesize:that.pageSize,
                    is_fund:1,
                    company_id:that.companyValue,
                    oper_user:that.memberValue
                })
                .then(function (res) {
                    if(res.data.status == 200){
                        that.loading = false;
                        that.listData = res.data.data.dt;
                        that.total = res.data.data.total;
                    }else{
                        that.$message({
                            message: res.data.info,
                            type: 'error'
                        });
                    }
                })
                .catch(function (error) {
                });
            }
        },
        //获取时间
        getTime(val){
            let time = "";
            var today = this.$moment().startOf('day');
            var yesterday = this.$moment().subtract(1, 'days').startOf('day');

            if (this.$moment(val*1000).isSame(today, 'd')){
                time = this.$moment(val*1000).format(' HH:mm:ss')
            }else if (this.$moment(val*1000).isSame(yesterday, 'd')){
                time = '昨天' + this.$moment(val*1000).format(' HH:mm:ss')
            }else{
                time = this.$moment(val*1000).format('YYYY-MM-DD HH:mm:ss')
            }
            return time;
        }
    },
    watch:{
        getCurrentStation(newValue,oldValue){
            if(newValue.merchant_type != 0 && newValue.value != oldValue.value){
                this.getMemberList();
                this.getCompanyList();
                this.getBalanceOperationList();
            }
        }
    }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.log .header{
    padding: 13px 0;
    border-bottom: 1px solid #EAEAEA;
}
.log-data .item{
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #EAEAEA;
}
.log-data .item .txt{
    width: 85%;
}
.log-data .item .time{
    color: #777;
}
.log .no-data{
    justify-content: center;
    padding: 20px 0;
}
    /* 页码 */
.page_content {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
}
</style>
