<script>
import customerTypeMixins from "../../mixins/customerType";
import SelectPlus from "../SelectPlus/SelectPlus.vue";
import tableQueryCondition, {TYPE_VALUE} from "../../mixins/tableQueryCondition";
import {cloneDeep, isEmpty, isEqual} from "lodash";
import {mapActions, mapGetters, mapState} from "vuex";
import BanciDateTime from "../Banci/banciDateTime.vue";
import {EndTime} from "../Report/endTime";


export default {
  name: "InvoiceQuery",
  mixins: [customerTypeMixins, tableQueryCondition],
  components: {
    BanciDateTime,
    SelectPlus,
  },
  computed: {
    ...mapState(['stationListLoading', 'stationList','currentStation','companyListLoading']),
    ...mapGetters(['companyArray']),
    EndTime() {
      return EndTime
    }
  },
  props: {
    dateType:{
      type:String,
      default:'datetimerange'
    },
    disabledDate:{
      type:Function,
      default:(()=> false)
    },
    showInvoiceType: {
      type: Boolean,
      default: false,
    },
    company_id: {
      type: Array,
      default: () => ([])
    },
    invoice_type: {
      type: [String,Number],
      default: ''
    },
    customer_type: {
      type: [String,Number],
      default: ''
    },
    date_value: {
      type: Array,
      default: () => ([])
    },
  },
  data() {
    return {
      classDate:[],
      typeOptions: [
        {
          value: TYPE_VALUE.DATE,
          label: "按时间查询",
        },
        {
          value: TYPE_VALUE.CLASS,
          label: "按班结查询",
        },
      ],
      invoiceValue: '',
      invoiceOptions: [
        {
          value: 0,
          label: "充值开票",
        },
        {
          value: 1,
          label: "消费开票",
        },
        {
          value: 2,
          label: "不开票",
        }
      ],
    }
  },
  created() {
    this.getCustomerType()
    this.getSimpleCompanyList()
    this.getStationList();

    if (!isEmpty(this.date_value)) {
      return;
    }

  },
  watch: {
    date_value: {
      handler(val) {
        if (isEmpty(val) || isEqual(val, this.query.dateValue)) {
          return;
        }
        this.query.dateValue = cloneDeep(val)
      },
      immediate: true,
      deep: true
    },
    currentStation:{
      deep:true,
      immediate:true,
      handler(val,old){
        console.log("=>(InvoiceQuery.vue:116) val", JSON.parse(JSON.stringify(val||{})),JSON.parse(JSON.stringify(old||{})));
        this.query.company_id = [];
        this.query.stid = '';
        this.$emit('update:company_id',[]);
        this.$emit('update:stid','');
        if(val &&String(val.merchant_type) === '2'){
          this.$store.dispatch('getStationList')
          this.$store.dispatch('getSimpleCompanyList')
          return;
        }
        this.query.stid = val.merchant_id;
        this.$refs.banciRef && this.query.stid && this.$refs.banciRef.getBanci(val.merchant_id)
      }
    }
  },
  methods: {
    ...mapActions(['getStationList','getSimpleCompanyList']),
    updateCompanySelected($event){
      this.$emit('update:company_id',$event);
      const currentCompanies = this.companyArray.filter(item => $event.includes(item.ID))
      console.log("=>(InvoiceQuery.vue:140) currentCompanies", currentCompanies);
      this.$emit('update:currentCompanies',currentCompanies);
    },
    changeType() {
      this.$emit('update:type',this.query.type)
      this.query.dateValue = [];
      this.$emit('update:date_value',[this.$moment().subtract(1,'days').format('YYYY-MM-DD 00:00:00'),this.$moment().format('YYYY-MM-DD 23:59:59')])
      if(this.query.type === TYPE_VALUE.DATE){
        return;
      }
      this.$emit('update:date_value',[])
      this.$refs.banciRef && this.$refs.banciRef.getBanci(this.query.stid)
    }
  }
}
</script>

<template>
  <div>
    <el-radio-group v-model="query.type" @input="changeType">
      <el-radio-button v-for="item in typeOptions" :key="item.value" :label="item.value">{{ item.label }}
      </el-radio-button>
    </el-radio-group>
    <div class="mt-20px">
      <el-form inline>
        <el-form-item label="车队名称/ID">
          <select-plus key="company_id" placeholder="请选择车队" v-model="query.company_id"
                       v-loading="companyListLoading"
                       @input="updateCompanySelected" :list="companyArray" multiple
                       filterable clearable collapse-tags
                       :attr="{label:'CompanyName',value:'ID',getLabel:item => `ID ${item.ID} ${item.CompanyName}`}"/>
        </el-form-item>
        <template v-if="query.type === TYPE_VALUE.CLASS">
          <el-form-item label="油站名称" v-if="currentStation && (String(currentStation.merchant_type) === '2')">
            <select-plus key="stid" :check-all="false" v-loading="stationListLoading" placeholder="请选择油站"
                         v-model="query.stid"
                         @input="$refs.banciRef.getBanci(query.stid),$emit('update:stid',$event)" :list="stationList"
                         filterable clearable collapse-tags
                         :attr="{label:'stname',value:'stid'}"/>
          </el-form-item>
          <el-form-item>
            <banci-date-time
              @classTimeChange="$emit('update:date_value',$event)"
              :show-loading="true"
              :date-value="classDate"
              ref="banciRef"
              @changeDate="query.dateValue = $event"
              :stationValue="query.stid"
              :picker-options="EndTime">
            </banci-date-time>
          </el-form-item>
        </template>
        <el-form-item label="开票方式" v-if="showInvoiceType">
          <el-select @change="$emit('update:invoice_type',query.invoice_type)" v-model="query.invoice_type"
                     class="w-250px mr-20px mb-5px"
                     clearable
                     placeholder="请选择开票方式">
            <el-option
              v-for="(item,index) in invoiceOptions"
              :key="index"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="客户类型">
          <select-plus @input="$emit('update:customer_type',query.customer_type)" :check-all="false"
                       v-loading="customerTypeLoading" placeholder="请选择客户类型"
                       v-model="query.customer_type" :list="customerTypeOptions"
                       clearable :attr="{label:'CompanyTypeName',value:'CompanyTypeID'}"/>
        </el-form-item>
        <el-form-item label="时间范围" v-if="query.type === TYPE_VALUE.DATE">
          <el-date-picker
            :picker-options="{disabledDate}"
            :clearable="false"
            v-model="query.dateValue"
            end-placeholder="结束日期"
            range-separator="至"
            start-placeholder="开始日期"
            class="mb-5px"
            :type="dateType"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            @change="$emit('clearData'),$emit('update:date_value',$event)">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <slot name="btns"></slot>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
