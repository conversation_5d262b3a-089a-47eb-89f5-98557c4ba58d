<template>
  <div id="cardManagement" class="cardManagement">
    <div class="header">
      <div>
        <span class="txt" v-if="getCurrentStation.merchant_type == 2"
          >油站名称</span
        >
        <el-select
          class="select-box"
          v-model="stationId"
          @change="changeData"
          placeholder="请选择"
          style="width: 120px"
          v-if="getCurrentStation.merchant_type == 2"
        >
          <el-option
            v-for="(item, index) in stationOptions"
            :key="index"
            :label="item.stname"
            :value="item.stid"
          >
          </el-option>
        </el-select>
        <span class="txt">车队名称</span>
        <el-select
          class="select-box"
          filterable
          v-model="cardCompanyValue"
          @change="changeData('all')"
          placeholder="请选择"
          style="width: 120px"
        >
          <el-option
            v-for="(item, index) in cardCompanyInfoOptions"
            :key="index"
            :label="item.CompanyName"
            :value="item.ID"
          >
          </el-option>
        </el-select>
        <span class="txt">卡名称</span>
        <el-select
          class="select-box"
          v-model="cardThemeValue"
          @change="changeData('all')"
          placeholder="请选择"
          style="width: 120px"
        >
          <el-option
            v-for="(item, index) in cardThemeOptions"
            :key="index"
            :label="item.Name"
            :value="item.ID"
          >
          </el-option>
        </el-select>
        <span class="txt">卡组</span>
        <el-select
          class="select-box"
          v-model="cardGroupValue"
          @change="changeData('all')"
          placeholder="请选择"
          style="width: 120px"
        >
          <el-option
            v-for="(item, index) in cardGroupOptions"
            :key="index"
            :label="item.CustomerGroupName"
            :value="item.ID"
          >
          </el-option>
        </el-select>
        <span class="txt">卡类型</span>
        <el-select
          class="select-box"
          v-model="cardTypeValue"
          @change="changeData"
          placeholder="请选择"
          style="width: 120px"
        >
          <el-option
            v-for="(item, index) in cardTypeOptions"
            :key="index"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
      <div>
        <span class="txt">卡状态</span>
        <el-select
          class="select-box"
          v-model="cardStateValue"
          @change="changeData"
          placeholder="请选择"
          style="width: 138px"
        >
          <el-option
            v-for="(item, index) in cardStateOptions"
            :key="index"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <span class="txt">查询类型</span>
        <el-radio-group v-model="searchTypeVlaue">
          <el-radio label="1">手机号</el-radio>
          <el-radio label="0">卡号</el-radio>
          <el-radio label="2">卡面卡号</el-radio>
          <el-radio label="4">持卡人</el-radio>
        </el-radio-group>

        <div class="search">
          <el-input
            @change="changeData"
            v-model="inputText"
            style="width: 210px"
            :placeholder="
              searchTypeVlaue == '0'
                ? '请输入卡号'
                : searchTypeVlaue == '1'
                ? '请输入手机号'
                : searchTypeVlaue == '2'
                ? '请输入卡面卡号'
                : '请输入持卡人名称'
            "
            clearable
          ></el-input>
          <el-button type="primary" @click="changeData">查询</el-button>
        </div>
      </div>
    </div>
    <div class="editAll">
      <div class="checkAll">
        <el-button
          type="primary"
          :disabled="isDisabled"
          @click="ShowEditAll"
          >批量编辑</el-button
        >
        <el-radio v-if="showCheckAll" v-model="isCheckAll" label="0" style="margin-left:20px" @change="changeThisPage">选择本页</el-radio>
        <el-radio v-if="showCheckAll" v-model="isCheckAll" label="1" @change="changeAllPage">选择全部页面</el-radio>
        <span style="margin-left:10px;display: inline-block;border-right: 2px solid  #32AF50;height: 12px;margin-right:10px">  </span>
         已选中
        <span style="font-size:14px;color: #32AF50;" v-if="showCheckAll && isCheckAll == '1'">{{ allLength }}</span>
        <span style="font-size:14px;color: #32AF50;" v-else>{{ multipleSelection.length }}</span> 张
      </div>

      <el-button
        type="primary"
        :disabled="tableData.length == 0"
        @click="exportUserCardList"
        v-show="isTotalReportForm"
        >下载数据</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      class="table-data"
      ref="multipleTable"
      :data="tableData"
      tooltip-effect="dark"
      stripe
      style="width: 100%"
      :row-key="getRowKeys"
      :row-style="rowStyle"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        fixed
        type="selection"
        :selectable="selectable"
        :reserve-selection="true"
        align="left"
        width="60"
      >
      </el-table-column>
      <el-table-column
        prop="CardNO"
        align="left"
        label="卡号"
        min-width="190"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="卡面卡号"
        min-width="190"
        prop="CardNumber"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="手机"
        min-width="140"
        prop="Phone"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        prop="CardName"
        align="left"
        label="卡名称"
        min-width="210"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="持卡人"
        min-width="120"
        prop="cardholder_name"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="车牌号"
        min-width="120"
        prop="CarNumber"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        prop="SUMAmount"
        align="left"
        label="总余额（元）"
        min-width="120"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        v-if="showLockPrice"
        prop="CardSumLiter"
        align="left"
        label="剩余升数"
        min-width="120"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        prop="Amount"
        align="left"
        label="本金（元）"
        min-width="120"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        prop="GiveAmount"
        align="left"
        label="赠金（元）"
        min-width="120"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        prop="CustomerGroup"
        align="left"
        label="卡组"
        min-width="160"
        :formatter="formatterCardCellval"
      >
      </el-table-column>
      <el-table-column
        prop="Remark"
        align="left"
        label="优惠信息"
        :width="setColumnWidth()"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        v-if="getCurrentStation.merchant_type == 2 && this.isGroupSettle == 0"
        prop="StationName"
        align="left"
        label="开户油站"
        min-width="150"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="卡类型"
        min-width="120"
        prop="CardType"
        :formatter="formatterCellval"
      >
        <template slot-scope="scope">{{
          getCardType(scope.row.CardType)
        }}</template>
      </el-table-column>
      <el-table-column
        align="center"
        label="状态"
        prop="State"
        min-width="120"
        :formatter="formatterCellval"
      >
        <template slot-scope="scope">
          <div
            class="state"
            :class="{ active: getCardState(scope.row.State) == '已激活' }"
          >
            {{ getCardState(scope.row.State) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="领卡时间"
        min-width="200"
        prop="CreateTime"
        :formatter="formatterCellval"
      >
        <template slot-scope="scope">{{
          $moment(scope.row.CreateTime).format("YYYY-MM-DD HH:mm:ss")
        }}</template>
      </el-table-column>
      <el-table-column
        type="index"
        align="center"
        prop="IDNumBer"
        width="200"
        label="备注"
      >
        <template slot-scope="scope" class="remark">
          <el-input
            style="width: 90%"
            type="textarea"
            maxlength="20"
            show-word-limit
            v-show="scope.row.showRemark"
            v-model="scope.row.IDNumBer"
            placeholder="请输入备注"
            @blur="orderChange(scope.row, scope.$index)"
            @keyup.enter.native="$event.target.blur"
          ></el-input>
          <span v-show="!scope.row.showRemark">{{
            scope.row.IDNumBer ? scope.row.IDNumBer : ""
          }}</span>
          <i
            v-show="!scope.row.showRemark && scope.row.State != 112"
            class="el-icon-edit-outline edit-icon"
            @click="showRemarkInput(scope.row)"
          ></i>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        prop="name"
        align="center"
        label="操作"
        min-width="120"
      >
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="checkDetail(scope.row)"
            >查看</el-button
          >
          <el-button
            v-if="!(scope.row.State == '111' || scope.row.State == '112')"
            size="mini"
            type="text"
            @click="goToEdit(scope.row)"
            >编辑</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 页码 -->
    <div class="page_content">
      <el-pagination
        class="page_left"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-size="pageSize"
        layout="prev, pager, next"
        :total="total"
      >
      </el-pagination>
      <el-pagination
        class="page_right"
        @size-change="handleSizeChange"
        :page-sizes="[10, 15, 20, 30]"
        :page-size="pageSize"
        layout="total, sizes"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 编辑卡详情 -->
    <el-dialog
      :close-on-click-modal="false"
      class="dialog"
      title="编辑卡详情"
      append-to-body
      :visible.sync="cardEditDialogVisible"
      width="640px"
    >
      <div class="main">
        <div class="item first-item">
          <p>卡号：{{ cardEditNo }}</p>
          <p v-if="cardEditNumber">卡面号：{{ cardEditNumber }}</p>
        </div>
        <div class="item">
          <div class="left">
            <span class="tab">状态</span>
            <el-select
              v-model="cardStateEditValue"
              :disabled="
                cardStateEditValue == 20 ||
                cardStateEditValue == 30 ||
                cardStateEditValue == 115
              "
              placeholder="请选择"
              style="width: 188px"
            >
              <el-option
                v-for="(item, index) in cardStateEditOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
              >
              </el-option>
            </el-select>
          </div>
          <div class="right">
            <span class="tab">卡名称</span>
            <el-select
              v-model="cardThemeEditValue"
              placeholder="请选择"
              style="width: 188px"
              :disabled="cardRuleDetail.market_type == 2"
            >
              <el-option
                v-for="(item, index) in cardThemeEditOptions"
                :key="index"
                :label="item.Name"
                :disabled="item.ShowRestrictionType != show_restriction_type"
                :value="item.ID"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="item">
          <div class="left">
            <span class="tab">持卡人</span>
            <el-input
              v-model="cardEditName"
              placeholder="请输入持卡人"
              style="width: 188px"
            ></el-input>
          </div>
          <div class="right">
            <span class="tab">持卡人手机</span>
            <el-input
              v-model="cardEditPhone"
              oninput="value=value.replace(/[^\d]/g,'')"
              maxlength="11"
              placeholder="请输入手机号"
              style="width: 188px"
            ></el-input>
          </div>
        </div>
        <div class="item">
          <div class="left">
            <span class="tab">车牌号</span>
            <el-input
              v-model="cardEditCarNumber"
              placeholder="请输入车牌号"
              style="width: 188px"
            ></el-input>
          </div>
          <div class="right">
            <span class="tab">身份证</span>
            <el-input
              v-model="cardEditICID"
              placeholder="请输入身份证"
              style="width: 188px"
            ></el-input>
          </div>
        </div>
        <div class="item">
          <div class="left">
            <span class="tab">车队名称</span>
            <el-select
              v-model="cardCompanyInfoEditValue"
              placeholder="请选择"
              style="width: 188px"
              @change="changeCompanyValue"
              clearable
              filterable
            >
              <el-option
                v-for="(item, index) in cardCompanyInfoEditOptions"
                :key="index"
                :label="item.CompanyName"
                :disabled="item.disabled"
                :value="Number(item.ID)"
              >
              </el-option>
            </el-select>
          </div>
          <div class="right">
            <span class="tab">卡类型</span>
            <el-select
              v-model="cardTypeEditValue"
              disabled
              style="width: 188px"
            >
              <el-option
                v-for="(item, index) in cardTypeEditOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="item">
          <div class="left">
            <span class="tab" style="width: 84px">持实体卡免密</span>
            <el-radio-group
              v-model="radioEditValue"
              :disabled="radioEditValue == 2"
            >
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </div>
        </div>
        <div class="item">
          <span class="tab">优惠信息</span>
            <el-input
              type="textarea"
               maxlength="100"
               show-word-limit
              placeholder="请输入优惠信息"
              v-model="dis_remark">
            </el-input>
        </div>
        <div class="item">
          <div class="left">
            <el-checkbox v-model="checked"
              >重置密码(重置后密码为123456)</el-checkbox
            >
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="cardEditDialogVisible = false"
          >取 消</el-button
        >
        <el-button size="mini" type="primary" @click="edit">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 查看卡详情 -->
    <el-dialog
      :close-on-click-modal="false"
      class="dialog"
      title="查看卡详情"
      append-to-body
      :visible.sync="cardCheckDialogVisible"
      width="720px"
    >
      <div class="main">
        <div class="item first-item">
          <p>卡号：{{ cardNo }}</p>
          <p v-if="cardNumber">卡面号：{{ cardNumber }}</p>
        </div>
        <div class="check-box">
          <div class="left">
            <p>
              <span class="txt">卡状态：</span
              ><span class="state" :class="{ active: cardState == '已激活' }">{{
                cardState
              }}</span>
            </p>
            <p>
              <span class="txt">持卡人：</span
              ><span>{{ cardholder_name ? cardholder_name : "--" }}</span>
            </p>
            <p>
              <span class="txt">手机号：</span
              ><span>{{ cardPhone ? cardPhone : "--" }}</span>
            </p>
            <p>
              <span class="txt">身份证：</span
              ><span>{{ identify ? identify : "--" }}</span>
            </p>
            <p>
              <span class="txt">车牌号：</span
              ><span>{{ car_number ? car_number : "--" }}</span>
            </p>
            <p>
              <span class="txt">卡分组：</span><span>{{ cardGroup }}</span>
            </p>
            <p>
              <span class="txt">卡类型：</span><span>{{ cardType }}</span>
            </p>
            <p>
              <span class="txt">领卡时间：</span><span>{{ createTime }}</span>
            </p>
          </div>
          <div class="right">
            <p>
              <span class="txt">卡名称：</span><span>{{ cardName }}</span>
            </p>
            <p class="cartxt">
              <span class="txt">卡规则：</span
              ><el-button type="text" @click="checkCardRuleDetail"
                >点击查看卡规则</el-button
              >
            </p>
            <p v-if="cardTypeNumber != 1">
              <span class="txt">车队名称：</span
              ><span>{{ cardCompanyInfo }}</span>
            </p>
            <p v-if="cardIntState != 112">
              <span class="txt">总余额：</span><span>{{ cardAmount }}元</span>
            </p>
            <p v-if="cardIntState != 112">
              <span class="txt">本金：</span><span>{{ Amount }}元</span>
            </p>
            <p v-if="cardIntState != 112">
              <span class="txt">赠金：</span><span>{{ GiveAmount }}元</span>
            </p>
            <p class="cartxt" v-if="cardIntState != 112">
              <span class="txt">金额操作：</span
              ><el-button
                type="text"
                :disabled="cardAmount == 0"
                @click="showEmptyTipsDialog"
                >点击余额清零</el-button
              >
            </p>
            <p class="cartxt" v-if="cardIntState != 112">
              <span class="txt">卡状态操作：</span
              ><el-button
                type="text" class="logout_btn"
                @click="deregistrationCar"
                >注销卡</el-button
              >
            </p>
            <p class="cartxt" v-if="cardIntState == 112">
              <span class="txt">注销原因：</span
              ><span>{{ deregistrationReson }}</span>
            </p>
            <p class="cartxt" v-if="cardIntState == 112">
              <span class="txt">注销时间：</span
              ><span>{{ deregistrationDate }}</span>
            </p>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="cardCheckDialogVisible = false"
          >关 闭</el-button
        >
      </span>
    </el-dialog>
    <!-- 批量编辑卡详情 -->
    <el-dialog :close-on-click-modal="false" class="dialog" title="批量编辑卡详情" append-to-body
      :visible.sync="cardAllEditDialogVisible" width="640px">
      <div class="main">
        <p class="tips">
          已选中
          <span v-if="showCheckAll && isCheckAll == '1'">{{allLength}}</span>
          <span v-else>{{ multipleSelection.length }}</span>
          张
        </p>
        <div class="item">
          <div class="left">
            <span class="tab">状态</span>
            <el-select v-model="cardStateEditAllValue" :disabled="showCheckAll && isCheckAll == '1'" placeholder="请选择" style="width: 188px">
              <el-option v-for="(item, index) in cardStateEditOptions" :key="index" :label="item.label"
                :value="item.value" :disabled="item.disabled">
              </el-option>
            </el-select>
          </div>
          <div class="right">
            <span class="tab">卡名称</span>
            <el-select v-model="cardThemeEditAllValue" disabled placeholder="请选择" style="width: 188px">
              <el-option v-for="(item, index) in cardThemeEditOptions" :key="index" :label="item.Name" :value="item.ID">
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="item">
          <div class="left">
            <span class="tab">车队名称</span>
            <el-select v-model="cardCompanyInfoEditAllValue" :disabled="showCheckAll && isCheckAll == '1'" placeholder="请选择" style="width: 188px"
              @change="changeCompanyInfoEditAllValue">
              <el-option v-for="(item, index) in cardCompanyInfoEditOptions" :key="index" :label="item.CompanyName"
                :value="item.ID">
              </el-option>
            </el-select>
          </div>
          <div class="right">
            <span class="tab">卡类型</span>
            <el-select v-model="cardTypeEditAllValue" disabled style="width: 188px">
              <el-option v-for="(item, index) in cardTypeEditOptions" :key="index" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="item">
          <div class="left">
            <span class="tab" style="width: 84px">持实体卡免密</span>
            <el-radio-group v-model="radioEditAllValue" :disabled="!hasNeedPassword">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
            <el-tooltip style="margin-left: 20px" v-if="!hasNeedPassword" content="选中的卡中包含制卡规则不开通免密或者非实体卡"
              placement="right" effect="light">
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </div>
        </div>
        <div class="item remark">
          <span class="tab" style="margin-right: 10px;width: 80px">优惠信息</span>
          <el-input type="textarea" maxlength="100" show-word-limit placeholder="请输入优惠信息" v-model="allDis_remark" style="margin-left:10px">
          </el-input>
        </div>
        <!-- <div class="item">
            <div class="left">
                <span class="tab">分组</span>
                <el-select v-model="cardGroupEditValue" placeholder="请选择" style="width:188px">
                    <el-option
                    v-for="(item,index) in cardGroupEditOptions"
                    :key="index"
                    :label="item.CustomerGroupName"
                    :value="item.ID">
                    </el-option>
                </el-select>
            </div>
        </div> -->
        <div class="item">
          <div class="left">
            <el-checkbox :disabled="showCheckAll && isCheckAll == '1'" v-model="allchecked">重置密码(重置后密码为123456)</el-checkbox>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="cardAllEditDialogVisible = false">取 消</el-button>
        <el-button size="mini" type="primary" :disabled="false" @click="editAll">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 查看卡规则详情 -->
    <el-dialog
      :close-on-click-modal="false"
      class="dialog cardRuleDialog"
      title="查看卡规则详情"
      append-to-body
      :visible.sync="cardRuleDialogVisible"
      width="720px"
    >
      <div class="main">
        <div class="check-box">
          <div class="left">
            <p>
              <span class="txt">卡名称：</span>{{ cardRuleDetail.rule_name }}
            </p>
            <p>
              <span class="txt">起止时间：</span
              ><span>{{ cardRuleDetail.time }}</span>
            </p>
            <p>
              <span class="txt">规则描述：</span
              ><span>{{ cardRuleDetail.description }}</span>
            </p>
            <p>
              <span class="txt">开票方式：</span
              ><span>{{ cardRuleDetail.invoice_open_type }}</span>
            </p>
            <p>
              <span class="txt">默认规则：</span
              ><span>{{ cardRuleDetail.priority }}</span>
            </p>
            <p>
              <span class="txt">状态：</span
              ><span>{{ cardRuleDetail.state }}</span>
            </p>
            <p>
              <span class="txt">充值：</span
              ><span>{{ cardRuleDetail.allow_charge }}</span>
            </p>
            <p>
              <span class="txt">充值本金：</span
              ><span>{{ cardRuleDetail.card_charge_money }}</span>
            </p>
            <p>
              <span class="txt">卡面值：</span
              ><span>{{ cardRuleDetail.card_initial_money }}</span>
            </p>
            <p>
              <span class="txt">适用类型：</span
              ><span>{{ cardRuleDetail.cus_type }}</span>
            </p>
          </div>
          <div class="right">
            <p>
              <span class="txt">制卡费：</span
              ><span>{{ cardRuleDetail.card_printing_cost }}</span>
            </p>
            <p>
              <span class="txt">充值上限：</span
              ><span>{{ cardRuleDetail.charge_max }}</span>
            </p>
            <p>
              <span class="txt">充值下限：</span
              ><span>{{ cardRuleDetail.charge_min }}</span>
            </p>
            <p>
              <span class="txt">余额上限：</span
              ><span>{{ cardRuleDetail.balance_max }}</span>
            </p>
            <p>
              <span class="txt">限制类型：</span
              ><span
                >{{ cardRuleDetail.charge_money_limit_type
                }}<span
                  v-if="cardRuleDetail.charge_money_limit_type != '不限制'"
                  >{{ cardRuleDetail.charge_money_limit_data }}</span
                ></span
              >
            </p>
            <p>
              <span class="txt">优惠券抵扣：</span
              ><span>{{ cardRuleDetail.is_allow_coupon }}</span>
            </p>
            <p>
              <span class="txt">持卡免密：</span
              ><span>{{ cardRuleDetail.is_need_password }}</span>
            </p>
            <p>
              <span class="txt">消费次数限制：</span
              ><span>每次最多{{ cardRuleDetail.consumption_limit }}次</span>
            </p>
            <p>
              <span class="txt">有效期：</span
              ><span v-if="cardRuleDetail.expire_value == 0">永久有效</span
              ><span v-else>{{ cardRuleDetail.expire_value }}个月</span>
            </p>
            <p>
              <span class="txt">卡介质：</span
              ><span>{{ cardRuleDetail.show_restriction_type }}</span>
            </p>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="cardRuleDialogVisible = false"
          >关 闭</el-button
        >
      </span>
    </el-dialog>
    <!-- 余额清零 -->
    <el-dialog
      :close-on-click-modal="false"
      class="dialog empty-dialog"
      title="余额清零"
      append-to-body
      :visible.sync="emptyDialogVisible"
      width="670px"
    >
      <div class="main">
        <div class="check-box">
          <div class="left">
            <p>
              <span class="txt">卡 号：</span
              ><span>{{ cardNo ? cardNo : "--" }}</span>
            </p>
            <p>
              <span class="txt">卡面号：</span
              ><span>{{ cardNumber ? cardNumber : "--" }}</span>
            </p>
            <p>
              <span class="txt">持卡人：</span
              ><span>{{ cardholder_name ? cardholder_name : "--" }}</span>
            </p>
            <p>
              <span class="txt">手机号：</span
              ><span>{{ cardPhone ? cardPhone : "--" }}</span>
            </p>
            <p>
              <span class="txt">身份证：</span
              ><span>{{ identify ? identify : "--" }}</span>
            </p>
            <p>
              <span class="txt">车牌号：</span
              ><span>{{ car_number ? car_number : "--" }}</span>
            </p>
          </div>
          <div class="right">
            <p>
              <span class="txt">卡类型：</span><span>{{ cardType }}</span>
            </p>
            <p>
              <span class="txt">卡名称：</span><span>{{ cardName }}</span>
            </p>
            <p>
              <span class="txt">总余额：</span
              ><span style="font-weight: bold">{{ cardAmount }}</span
              >元
            </p>
            <p>
              <span class="txt">本金：</span
              ><span
                style="font-size: 20px; font-weight: bold; color: #32af50"
                >{{ Amount }}</span
              >元<span v-if="cardTypeNumber == 1"> - 现金退回</span>
            </p>
            <p>
              <span class="txt">赠金：</span
              ><span style="font-size: 20px; font-weight: bold">{{
                GiveAmount
              }}</span
              >元<span v-if="cardTypeNumber == 1"> - 自动扣除</span>
            </p>
          </div>
        </div>
        <div class="empty-money" v-if="cardTypeNumber != 1">
          <div class="line">
            <span class="tabs">余额返回母账：</span>本金
            <el-input
              class="input-bar"
              oninput="value=value.indexOf('.') > -1?value.slice(0, value.indexOf('.') + 3):value"
              v-model="money01"
              placeholder="金额"
            ></el-input
            >元，赠金
            <el-input
              class="input-bar"
              oninput="value=value.indexOf('.') > -1?value.slice(0, value.indexOf('.') + 3):value"
              v-model="money02"
              placeholder="金额"
            ></el-input
            >元
          </div>
          <div>
            <span class="tabs">余额退回持卡人：</span>本金
            <el-input
              class="input-bar"
              oninput="value=value.indexOf('.') > -1?value.slice(0, value.indexOf('.') + 3):value"
              v-model="money03"
              placeholder="金额"
            ></el-input
            >元，赠金
            <el-input
              class="input-bar"
              oninput="value=value.indexOf('.') > -1?value.slice(0, value.indexOf('.') + 3):value"
              v-model="money04"
              placeholder="金额"
            ></el-input
            >元
          </div>
          <div style="margin-top: 8px">
            <span style="margin-left: 186px">现金退回</span>
            <span style="margin-left: 104px">清零后扣除</span>
          </div>
        </div>
        <div class="tips-item">
          <i
            class="el-icon-warning"
            style="margin-right: 8px; font-size: 14px"
          ></i
          >余额清零后无法退回，请务必谨慎操作！
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="showEmptyDialogVisible"
          >余额清零</el-button
        >
        <el-button size="mini" @click="emptyDialogVisible = false"
          >关 闭</el-button
        >
      </span>
    </el-dialog>
    <!-- 余额清零 -->
    <el-dialog
      :close-on-click-modal="false"
      class="dialog emptyTips-dialog"
      :show-close="false"
      append-to-body
      :visible.sync="emptyTipsDialogVisible"
      width="400px"
    >
      <div class="emptyTips">
        <i class="el-icon-warning" style="font-size: 64px; color: #fa6400"></i>
        <p>清零金额退回不涉及线上退款</p>
        <p style="color: #fa6400">本金：{{ Amount }}元 — 现金方式退回持卡人</p>
        <p>赠金：{{ GiveAmount }}元 — 自动扣除</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button
          size="mini"
          type="primary"
          :disabled="!canClear"
          @click="showEmptyWarning"
          >确 认</el-button
        >
        <el-button
          size="mini"
          @click="
            emptyTipsDialogVisible = false;
            emptyDialogVisible = false;
          "
          >关 闭</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      @close="emptyTipsCompanyClose"
      class="dialog emptyTipsCompany-dialog"
      title="请确认余额退回方式"
      append-to-body
      :visible.sync="emptyTipsCompanyDialogVisible"
      width="480px"
    >
      <div class="emptyTips">
        <p class="point-txt">返回{{ cardCompanyInfo }}</p>
        <p
          style="
            margin-left: 14px;
            color: #333;
            font-weight: bold;
            font-size: 18px;
          "
        >
          本金：{{ money01 }}元 赠金：{{ money02 }}元
        </p>
        <p class="point-txt">退回持卡人</p>
        <p style="color: #fa6400; margin-left: 14px; font-weight: bold">
          本金：{{ money03 }}元 — 现金方式退回持卡人
        </p>
        <p style="margin-left: 14px; font-weight: bold; color: #333">
          赠金：{{ money04 }}元 — 自动扣除
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button
          size="mini"
          type="primary"
          :disabled="!canClear"
          @click="showEmptyWarning"
          >确 认</el-button
        >
        <el-button
          size="mini"
          @click="
            emptyTipsCompanyDialogVisible = false;
            emptyDialogVisible = false;
          "
          >关 闭</el-button
        >
      </span>
    </el-dialog>

    <!-- 不可注销确认提示 -->
    <el-dialog
      center
      title="提示"
      append-to-body
      :visible.sync="deregistrationDialogVisible"
      width="320px">
      <div class="logout_box">
        <img class="waring_icon" src="https://fs1.weicheche.cn/images/test/220701044620-87712.png" alt="">
        <div>
          该卡内有余额无法注销，
          请余额清零后再注销
        </div>
      </div>
    </el-dialog>

    <!-- 注销成功提示 -->
    <el-dialog
      center
      title="提示"
      append-to-body
      :visible.sync="deregistrationSuccessDialogVisible"
      width="320px" :before-close="closeSuccess">
      <div class="logout_box1">
        <img class="waring_icon1" src="https://fs1.weicheche.cn/images/test/220701060036-34012.png" alt="">
        <div>
          注销成功
        </div>
      </div>
    </el-dialog>

        <!-- 注销失败提示 -->
    <el-dialog
      center
      title="提示"
      append-to-body
      :visible.sync="deregistrationFailDialogVisible"
      width="320px">
      <div class="logout_box1">
        <img class="waring_icon1" src="https://fs1.weicheche.cn/images/test/220702044840-84153.png" alt="">
        <div>
          {{deregistrationFailDes}}
        </div>
      </div>
    </el-dialog>

    <!-- 注销确认提示 -->
    <el-dialog
      center
      title="提示"
      append-to-body
      :visible.sync="deregistrationNoticeDialogVisible"
      width="420px">
      <div class="logout_box1">
        <img class="waring_icon1" src="https://fs1.weicheche.cn/images/test/220701054606-27902.png" alt="">
        <div class="notice_des">
          注销后卡状态更新为已注销，且无法再次使用，
请确认是否继续注销操作？
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="confirmDeregistration">确认</el-button>
        <el-button size="mini" @click="deregistrationNoticeDialogVisible = false">取消</el-button>
        </span>
    </el-dialog>

    <!-- 卡注销 -->
    <el-dialog
      :close-on-click-modal="false"
      class="dialog empty-dialog"
      title="卡注销"
      append-to-body
      :visible.sync="deregistrationDialogVisible1"
      width="720px"
    >
      <div class="main">
        <div class="check-box">
          <div class="left">
            <p>
              <span class="txt">卡 号：</span
              ><span>{{ cardNo ? cardNo : "--" }}</span>
            </p>
            <p>
              <span class="txt">卡面号：</span
              ><span>{{ cardNumber ? cardNumber : "--" }}</span>
            </p>
            <p>
              <span class="txt">卡类型：</span><span>{{ cardType }}</span>
            </p>
            <p>
              <span class="txt">卡名称：</span><span>{{ cardName }}</span>
            </p>
            <p>
              <span class="txt">领卡时间：</span><span>{{ createTime }}</span>
            </p>

          </div>
          <div class="right">
            <p>
              <span class="txt">持卡人：</span
              ><span>{{ cardholder_name ? cardholder_name : "--" }}</span>
            </p>
            <p>
              <span class="txt">手机号：</span
              ><span>{{ cardPhone ? cardPhone : "--" }}</span>
            </p>

            <p>
              <span class="txt">总余额：</span
              ><span style="font-weight: bold">{{ cardAmount }}</span
              >元
            </p>
            <p>
              <span class="txt">身份证：</span
              ><span>{{ identify ? identify : "--" }}</span>
            </p>
            <p>
              <span class="txt">车牌号：</span
              ><span>{{ car_number ? car_number : "--" }}</span>
            </p>
          </div>
        </div>
        <div style="display: flex">
          <div style="width:80px">注销原因：</div>
          <el-input
            type="textarea"
            :rows="4"
            placeholder="请输入注销原因"
            v-model="deregistrationReson">
          </el-input>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="danger" @click="deregistrationAction"
          >注销</el-button
        >
        <el-button size="mini" @click="deregistrationDialogVisible1 = false"
          >取消</el-button
        >
      </span>
    </el-dialog>

     <!-- 安全验证 -->
    <el-dialog
        title="安全验证"
        class="safe-dialog"
        append-to-body
        :visible.sync="verifyDialogVisible"
        width="400px">
        <p v-if="getCurrentStation.merchant_type == 2">请输入集团管理员操作密码</p>
        <p v-else>请输入油站管理员操作密码</p>
        <!-- <p>
            <i class="el-icon-user"></i>
            <span style="padding-right:15px">{{manager}}</span>
            <span>超级管理员</span>
        </p> -->
        <el-input v-model="password" type="password" placeholder="请输入操作密码"></el-input>
        <span slot="footer" class="dialog-footer">
            <el-button size="mini" @click="verifyDialogVisible = false">取 消</el-button>
            <el-button size="mini" type="primary" :disabled="!password" @click="deregistration">确认通过</el-button>
        </span>
    </el-dialog>

    <!-- 下载中心提示 -->
    <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>
  </div>
</template>

<script>
//导包函数
import setColumnWidth from './formatTabel.js'
import DownloadTips from "./DownloadTips.vue";
import { mapGetters } from "vuex";
export default {
  name: "CardManager",
  components: {
    DownloadTips,
  },
  data() {
    return {
      isTotalReportForm: true,
      value: "",
      stationId: "0",
      stationOptions: [
        {
          stid: "0",
          stname: "全部",
        },
      ], //卡名称
      cardCompanyValue: "0",
      cardThemeOptions: [
        {
          ID: "0",
          Name: "全部",
        },
      ], //卡名称
      cardThemeValue: "0",
      cardGroupOptions: [
        {
          ID: "0",
          CustomerGroupName: "全部",
        },
      ], //卡分组
      cardGroupValue: "0",
      cardTypeOptions: [
        {
          value: "0",
          label: "全部",
        },
        {
          value: "1",
          label: "个人卡",
        },
        {
          value: "2",
          label: "车队卡",
        },
        {
          value: "3",
          label: "不记名卡",
        },
      ],
      cardTypeValue: "0",
      cardStateOptions: [
        {
          value: "0",
          label: "全部",
        },
        {
          value: "20",
          label: "已制卡（待激活）",
        },
        {
          value: "30",
          label: "待激活",
        },
        {
          value: "100",
          label: "已激活",
        },
        {
          value: "101",
          label: "已冻结",
        },
        {
          value: "110",
          label: "挂失",
        },
        {
          value: "111",
          label: "坏卡",
        },
        {
          value: "115",
          label: "已补卡",
        },
        {
          value: "112",
          label: "已注销",
        }
      ],
      cardStateValue: "0",
      searchTypeOptions: [
        {
          value: "0",
          label: "卡号",
        },
        {
          value: "1",
          label: "手机号",
        },
        {
          value: "2",
          label: "卡面卡号",
        },
      ],
      deregistrationDialogVisible: false,
      deregistrationDialogVisible1: false,
      verifyDialogVisible: false,
      deregistrationFailDes: '', //错误提示
      deregistrationFailDialogVisible: false,
      deregistrationNoticeDialogVisible: false,
      deregistrationSuccessDialogVisible: false,
      password: "", // 验证密码
      deregistrationReson: "", //注销原因
      searchTypeVlaue: "1",
      tableData: [],
      loading: true,
      multipleSelection: [], //选中的数据
      total: 0, //数据总条数
      pageSize: 10, //默认一页10条
      currentPage: 1, //当前默认第一页
      inputText: "", //搜索的内容
      cardEditDialogVisible: false, //编辑卡详情
      cardEditNo: "", //编辑卡号
      cardEditNumber: "", //编辑卡面号
      cardEditPhone: "", //编辑卡手机号
      cardRemark: "", //备注
      cardEditName: "", //编辑卡持卡人
      cardEditCarNumber: "", //编辑卡车牌号
      cardEditICID: "", //编辑身份证
      cardEditUID: "", //编辑UID,只限单张卡编辑使用
      cardEditParams: {
        cards_id: "", //卡信息
        info: {
          phone: "", //手机号
          rid: "", //卡名称id
          extend: "", //扩展规则，在卡名称id里面有个extend，传回来就好了
          company_id: "", //车队卡id
          customer_group_id: "", //客户组id
          car_number: "", //车牌号
          users: [{ id: "", name: "", icid: "" }], //持卡人信息
        },
      }, //编辑参数
      cardThemeEditOptions: [], //卡名称
      cardThemeEditValue: "",
      cardCompanyInfoOptions: [
        {
          ID: "0",
          CompanyName: "全部",
        },
      ], //车队信息
      cardCompanyInfoEditOptions: [], //车队信息
      cardCompanyInfoEditValue: "",
      cardGroupEditOptions: [], //卡分组
      cardGroupEditValue: "",
      cardTypeEditOptions: [
        {
          value: "1",
          label: "个人卡",
        },
        {
          value: "2",
          label: "车队卡",
        },
        {
          value: "3",
          label: "不记名卡",
        },
      ],
      cardTypeEditValue: "",
      radioEditValue: 2,
      cardStateEditOptions: [
        {
          value: "20",
          label: "已制卡（待激活）",
          disabled: true,
        },
        {
          value: "30",
          label: "待激活",
          disabled: true,
        },
        {
          value: "100",
          label: "已激活",
        },
        {
          value: "101",
          label: "已冻结",
        },
        {
          value: "110",
          label: "挂失",
        },
        {
          value: "111",
          label: "坏卡",
          disabled: true,
        },
        {
          value: "115",
          label: "已补卡",
          disabled: true,
        },
      ],
      cardStateEditValue: "",
      cardCheckDialogVisible: false, //查看卡详情
      CardID: "",
      UID: "",
      CompanyID: "",
      StationNO: "",
      cardNo: "", //卡号
      cardNumber: "", //卡面号
      cardState: "", //卡状态
      cardIntState: 0, //卡状态(字符串)
      cardPhone: "", //卡手机
      cardholder_name: "", //持卡人
      cardType: "", //卡类型
      identify: "", //身份证
      car_number: "", //车牌号
      cardTypeNumber: "", //卡类型
      cardThemeId: "", //卡主题ID
      cardGroup: "", //卡分组
      cardName: "", //卡名称
      cardText: "", //卡名称内容
      cardAmount: "", //卡余额
      Amount: "", //本金
      GiveAmount: "", //赠金
      cardCompanyInfo: "", //卡车队信息
      cardAllEditDialogVisible: false, //批量编辑卡详情
      createTime: "", //领取时间
      cardStateEditAllValue: "",
      cardTypeEditAllValue: "",
      radioEditAllValue: 0,
      cardThemeEditAllValue: "",
      cardCompanyInfoEditAllValue: "",
      checked: false, //重置密码勾选
      allchecked: false, //重置密码勾选
      dis_remark:'',  //编辑卡详情优惠规则
      allDis_remark:'',
      cardRuleDialogVisible: false, //卡规则详情
      cardRuleDetail: {}, //卡规则内容

      showDownloadTips: false,
      show_restriction_type: 0, //发卡介质，默认0，表示通用
      isDetail: true, //判断是点击查看还是编辑

      emptyDialogVisible: false, //余额清零
      emptyTipsDialogVisible: false, //余额清零提示
      emptyTipsCompanyDialogVisible: false, //余额清零提示
      money01: "0.00",
      money02: "0.00",
      money03: "0.00",
      money04: "0.00",
      canClear: true, //是否能清零
      isGroupSettle: 0,
      hasNeedPassword: true, //批量编辑，是否有规则不开通免密的卡
      isCheckAll:'', //选择本页0 选择全部页1
      showCheckAll:false, //全选按钮
      intersection:0,       //选择的交集
      allLength:0,          //全选页面的选中的卡量
      selectMultipleSelection:[], //假如选中了卡名称 车队名称 卡组 放入这个数组用来过滤
      filterMultiple:[],  //全选页面取消的数据
    };
  },
  async mounted() {
    let userInfo = localStorage.getItem("__userInfo__") || "";
    this.isTotalReportForm = JSON.parse(userInfo).group_id != 426;
    if (
      this.getCurrentStation.merchant_type == undefined ||
      this.getCurrentStation.merchant_type == 0
    ) {
      return false;
    }
    if (this.getCurrentStation.merchant_type == 2) {
      this.getStationList();
    }
    this.getCardThemeRuleList();
    await this.getCompanyList();
    this.getCustomerGroupList();
    if (this.$route.params.id) {
      this.cardCompanyValue = this.$route.params.id;
      this.changeData();
    } else {
      this.getUserCardList();
    }
    //函数防抖
    this.$watch(
      "money01",
      this.debounce((newValue) => {
        if (Number(newValue) > Number(this.Amount)) {
          this.money01 = this.Amount;
        }
        this.money03 = this.accSubtr(this.Amount, newValue);
      }, 500)
    );
    this.$watch(
      "money02",
      this.debounce((newValue) => {
        if (Number(newValue) > Number(this.GiveAmount)) {
          this.money02 = this.GiveAmount;
        }
        this.money04 = this.accSubtr(this.GiveAmount, newValue);
      }, 500)
    );
    this.$watch(
      "money03",
      this.debounce((newValue) => {
        if (Number(newValue) > Number(this.Amount)) {
          this.money03 = this.Amount;
        }
        this.money01 = this.accSubtr(this.Amount, newValue);
      }, 500)
    );
    this.$watch(
      "money04",
      this.debounce((newValue) => {
        if (Number(newValue) > Number(this.GiveAmount)) {
          this.money04 = this.GiveAmount;
        }
        this.money02 = this.accSubtr(this.GiveAmount, newValue);
      }, 500)
    );
    this.isGroupSettle = localStorage.getItem("__isGroupSettle__");
    console.log(this.isGroupSettle);
  },
  computed: {
    ...mapGetters(['showLockPrice']),
    ...mapGetters({
      getCurrentStation: "getCurrentStation",
    }),
    isDisabled:{
      get(){
        //(!showCheckAll && multipleSelection.length < 2) || (showCheckAll && allLength < 2)
        if(!this.showCheckAll && this.multipleSelection.length < 2){
          return true
        }else if(this.isCheckAll == '1' &&  this.allLength < 2){
          return true
        }else if(this.isCheckAll == '0' && this.multipleSelection.length < 2){
          return true
        }
      }
    }
  },
  methods: {
    //防抖函数
    debounce(func, delay) {
      let timer;
      return function (...args) {
        if (timer) {
          clearTimeout(timer);
        }
        timer = setTimeout(() => {
          func.apply(this, args);
        }, delay);
      };
    },
    formatterCardCellval(row, column, cellValue, index) {
      // console.log('卡组', cellValue);
      if (cellValue === undefined || cellValue === "" || cellValue === null || !cellValue.length) {
        return "--";
      } else {
        return cellValue.map(item => item.GroupName).join('/')
      }
    },
    //获取卡名称列表，只显示启用
    getCardThemeRuleList() {
      let that = this;
      that.$axios
        .post("/CardRule/getCardThemeRuleList", {
          state: 100,
          station_id:
            this.getCurrentStation && this.getCurrentStation.merchant_type == 2
              ? 0
              : this.getCurrentStation.merchant_id,
          page: 1,
          page_size: 1000,
        })
        .then((res) => {
          if (res.data.status == 200) {
            that.cardThemeOptions = [
              {
                ID: "0",
                Name: "全部",
              },
            ];
            that.cardThemeOptions = that.cardThemeOptions.concat(
              res.data.data.dt
            );
            that.cardThemeEditOptions = res.data.data.dt
            /*let time = this.$moment().unix()
            console.log('time',time)
            that.cardThemeEditOptions = [];
            if(res.data.data.dt && (res.data.data.dt.length > 0)) {
              res.data.data.dt.forEach(v => { // 只显示未过期的卡
                if(!v.CutOffTime) {
                  that.cardThemeEditOptions.push(v)
                }
                else if(this.$moment(v.CutOffTime).unix() > time) {
                  that.cardThemeEditOptions.push(v)
                }
              })
            }*/
            console.log('cardThemeEditOptions',that.cardThemeEditOptions)
          } else {
            that.$message.error(res.data.info);
          }
        });
    },
    //获取客户组数据列表
    getCustomerGroupList() {
      let that = this;
      that.$axios
        .post("/CustomerGroup/getCustomerGroupList", {
          page: 1,
          page_size: "500",
        })
        .then(function (res) {
          if (res.data.status == 200) {
            that.cardGroupOptions = [
              {
                ID: "0",
                CustomerGroupName: "全部",
              },
            ];
            that.cardGroupOptions = that.cardGroupOptions.concat(
              res.data.data.dt
            );
            that.cardGroupEditOptions = res.data.data.dt;
          } else {
            that.$message({
              message: res.data.info,
              type: "error",
            });
          }
        })
        .catch(function (error) {});
    },
    //获取车队信息列表
    async getCompanyList() {
      this.isGroupSettle = localStorage.getItem("__isGroupSettle__");
      let that = this;
      await that.$axios
        .post("/CompanyCard/getSimpleCompanyList", {
          page: 1,
          page_size: 1250,
          input: "",
        })
        .then(function (res) {
          if (res.data.status == 200) {
            that.cardCompanyInfoOptions = [
              {
                ID: "0",
                CompanyName: "全部",
              },
            ];
            that.cardCompanyInfoOptions = that.cardCompanyInfoOptions.concat(
              res.data.data.dt
            );
            that.cardCompanyInfoEditOptions = res.data.data.dt;
          } else {
            that.$message({
              message: res.data.info,
              type: "error",
            });
          }
        })
        .catch(function (error) {});
    },
    //获取表格数据
    getUserCardList() {
      this.loading = true;
      this.$axios
        .post("/Card/getUserCardList", {
          page: this.currentPage, //页码
          page_size: this.pageSize, //每页数量
          input: this.inputText, //输入
          input_type: this.searchTypeVlaue, //输入
          card_theme_id: this.cardThemeValue, //卡名称id
          card_type_id: this.cardTypeValue, //卡类型id
          customer_group_id: this.cardGroupValue, //分组id
          status: this.cardStateValue, //状态
          company_id: this.cardCompanyValue,
          stid: this.stationId,
        })
        .then((res) => {
          this.tableData = []; //清空
          this.loading = false;
          if (res.data.status == 200) {
            this.isGroupSettle = localStorage.getItem("__isGroupSettle__");
            console.log(this.isGroupSettle);
            let data = JSON.parse(
              JSON.stringify(res.data.data.dt ? res.data.data.dt : [])
            );
            data.map((item, index) => {
              return (item.showRemark = false);
            });
            this.tableData = data;
            this.total = res.data.data.TotalQty;
            //默认选中全部
            if(this.cardCompanyValue != '0' || this.cardThemeValue != '0' || this.cardGroupValue != '0'){
              if(this.isCheckAll == '1'){
                // this.handleSelectionChange()
                // this.tableData.forEach(item=>{
                //   this.$refs.multipleTable.toggleRowSelection(item,true)
                // })
                //选中过的将不再添加到multipleSelection,未选中的加进去
                this.tableData.forEach(item=>{
                  if(this.multipleSelection.some(row=> row.ID === item.ID)){
                    this.$refs.multipleTable.toggleRowSelection(item,false)
                  }else if(this.filterMultiple.some(row=> row === item.ID)){
                    this.$refs.multipleTable.toggleRowSelection(item,false)
                  }else{
                    console.log('debugger');
                    this.$refs.multipleTable.toggleRowSelection(item,true)
                  }
                })
              }
            }
          } else {
            this.total = 0;
            this.$message.error(res.data.info);
          }

          //声明一个数组用来装所有选中的ID
          let checkArr = this.multipleSelection.map(item=>item.ID)
          // console.log('checkArr',checkArr);
          //声明一个数组用来装当前页面的ID
          let nowCheckArr = this.selectMultipleSelection.map(item=>item.ID)
          console.log('nowCheckArr',nowCheckArr);

          let nowCheckArrSet = new Set(nowCheckArr)
          //声明一个数组用来装当前选中ID和所有ID的交集，判断是否当前页面全部选中
          let intersection = checkArr.filter(item=> nowCheckArrSet.has(item))
          this.intersection = intersection
          console.log('intersection',intersection);
          // if(this.inputText == ''){
            this.allLength = this.multipleSelection.length - this.intersection.length + this.total - this.filterMultiple.length
          // }
          //如果全选，并且选了车队名称，卡名称，卡组才可以全选全部页面
          // if(nowCheckArr.length && intersection.length == nowCheckArr.length){
          //   console.log('全选');
            if(this.cardCompanyValue != '0' || this.cardThemeValue != '0' || this.cardGroupValue != '0'){
              console.log('选了车队名称，卡名称，卡组');
              this.showCheckAll = true
            }else{
              this.showCheckAll = false
            }
          // }else{
          //   this.showCheckAll = false
          // }
        });
    },
    getAllLength(){
    },
    //切换页码
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getUserCardList();
    },
    //切换每页条数
    handleSizeChange(val) {
      this.pageSize = val;
      this.getUserCardList();
    },
    //改变类型
    changeData(val) {
      console.log('类型',val);
      this.currentPage = 1;
      if(this.cardCompanyValue != '0' || this.cardThemeValue != '0' || this.cardGroupValue != '0'){
        this.showCheckAll = true
      }else{
        this.showCheckAll = false
      }
      if(val == 'all' && this.isCheckAll == '1'){
        this.$refs.multipleTable.clearSelection();
        this.isCheckAll = '0'
        this.filterMultiple = []
        this.intersection = []
        this.multipleSelection = []
      }else{
        this.filterMultiple = []
        this.intersection = []
      }
      this.getUserCardList();
    },
    //获取选中数据
    handleSelectionChange(val) {
      console.log('选中的数据',val);
      this.multipleSelection = val;
      if(this.cardCompanyValue != '0' || this.cardThemeValue != '0' || this.cardGroupValue != '0'){
        this.selectMultipleSelection = val
      }
      this.$forceUpdate()
    },
    //获取车队信息
    getCompanyInfo(val) {
      let that = this;
      if (val != 0) {
        that.$axios
          .post("/CompanyCard/getCompanyInfo", {
            id: val,
          })
          .then(function (res) {
            if (res.status == 200) {
              that.cardCompanyInfo = res.data.data.company_name;
            }
          })
          .catch(function (error) {});
      } else {
        that.cardCompanyInfo = "该卡没有绑定车队";
      }
    },
    //查看详情
    checkDetail(val) {
      console.log(val)
      this.isDetail = true;
      this.$loading({
        lock: true,
        text: "数据加载中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.6)",
      });
      this.CardID = val.ID;
      this.UID = val.UID;
      this.CompanyID = val.CompanyID;
      this.StationNO = val.StationNO;
      this.cardNo = val.CardNO;
      this.cardNumber = val.CardNumber;
      this.cardIntState = val.State;
      this.deregistrationReson = val.IDNumBer;
      this.deregistrationDate = this.$moment(val.UpdateTime).format("YYYY-MM-DD HH:mm:ss");

      this.cardState = this.getCardState(val.State);
      this.cardPhone = val.Phone;
      this.Amount = val.Amount;
      this.GiveAmount = val.GiveAmount;
      this.cardholder_name = val.cardholder_name;
      this.cardTypeNumber = val.CardType;
      this.identify = val.icid;
      this.car_number = val.CarNumber;
      this.cardType = this.getCardType(val.CardType);
      this.createTime = val.CreateTime
      if (val.CustomerGroup.length > 0) {
        let myGroup = "";
        val.CustomerGroup.forEach((element, index) => {
          if (index == val.CustomerGroup.length - 1) {
            myGroup += element.GroupName;
          } else {
            myGroup += element.GroupName + ",";
          }
        });
        this.cardGroup = myGroup;
      } else {
        this.cardGroup = "未绑定分组";
      }
      this.cardName = val.CardName;
      let str = "";
      val.extend_remark.forEach((item, index) => {
        str += index + 1 + "." + item;
      });

      this.cardText = str;
      this.cardAmount = val.SUMAmount;
      this.getCompanyInfo(val.CompanyID);
      //获取卡规则详情
      this.cardThemeId = val.RID;
      this.getCardThemeRuleInfo(this.cardThemeId);
    },
    rowStyle(val) {
      var item = val.row

      if (item.State == 112) {
        return {background: '#F5F5F5',hover: 'none', color:'#777777'}
      }else {
        return {color:'#333333'}
      }
    },
    //获取卡状态
    getCardState(val) {
      let typeName = "";
      this.cardStateOptions.forEach((element) => {
        if (element.value == val) {
          typeName = element.label;
        }
      });
      return typeName;
    },
    //获取卡类型
    getCardType(val) {
      let typeName = "";
      this.cardTypeOptions.forEach((element) => {
        if (element.value == val) {
          typeName = element.label;
        }
      });
      return typeName;
    },
    //展示编辑窗口
    goToEdit(val) {
      this.isDetail = false;
      this.$loading({
        lock: true,
        text: "数据加载中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.6)",
      });
      this.checked = false;
      this.cardEditNo = val.CardNO;
      this.cardEditNumber = val.CardNumber;
      this.cardEditCarNumber = val.CarNumber;
      this.cardEditPhone = val.Phone;
      this.cardEditUID = val.UID;
      this.cardEditName = val.cardholder_name;
      this.cardEditICID = val.icid;
      this.cardThemeEditValue = val.RID;
      this.cardTypeNumber = val.CardType;
      this.cardRemark = val.IDNumBer;
      this.dis_remark = val.Remark
      if (val.CustomerGroupID) {
        this.cardGroupEditValue = val.CustomerGroupID;
      } else {
        this.cardGroupEditValue = "";
      }
      this.$nextTick(()=>{
        if (val.CompanyID != 0) {
          this.cardCompanyInfoEditValue = val.CompanyID;
          this.cardTypeEditValue = "2";
        } else {
          this.cardCompanyInfoEditValue = "";
          this.cardTypeEditValue = String(val.CardType);
        }
      })
      this.cardStateEditValue = String(val.State);
      this.radioEditValue = Number(val.ISNeedPassword);

      this.cardEditParams.cards_id = val.ID;
      this.cardEditParams.info.card_no = val.CardNO;
      this.getCardThemeRuleInfo(val.RID);
    },
    //编辑
    edit() {
      let that = this;
      const phoneReg = /^1[3456789]\d{9}$/;
      const icidReg =
        /^(?:(?:[1-9]\d{5}(?:18|19|20|3\d)\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[1-2]\d|3[0-1])\d{3}[0-9Xx])|(?:[A-Z]\d{9})|(?:[1|5|7][0-9]{6}\([0-9]\))|(?:[A-Z]{1,2}\d{6}\([0-9A]\)))$/;
      const carReg =
        /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳使领]))$/;
      if (this.cardEditPhone && !phoneReg.test(this.cardEditPhone)) {
        that.$message({
          message: "请填写正确的手机号码！",
          type: "error",
        });
        return;
      }
      if (this.cardEditICID && !icidReg.test(this.cardEditICID)) {
        that.$message({
          message: "请填写正确的身份证！",
          type: "error",
        });
        return;
      }
      if (this.cardEditCarNumber && !carReg.test(this.cardEditCarNumber)) {
        that.$message({
          message: "请填写正确的车牌号！",
          type: "error",
        });
        return;
      }
      that.cardEditParams.info.phone = that.cardEditPhone;
      that.cardEditParams.info.car_number = that.cardEditCarNumber;
      that.cardEditParams.info.rid = that.cardThemeEditValue;
      that.cardEditParams.info.users = [];
      that.cardEditParams.info.users.push({
        id: that.cardEditUID,
        name: that.cardEditName,
        icid: that.cardEditICID,
      });
      that.cardThemeEditOptions.forEach((element) => {
        if (that.cardThemeEditValue == element.ID) {
          that.cardEditParams.info.extend = element.Extend;
          that.cardEditParams.info.card_name = element.Name;
        }
      });
      that.cardEditParams.info.company_id = that.cardCompanyInfoEditValue;
      that.cardCompanyInfoEditOptions.forEach((element) => {
        if (that.cardCompanyInfoEditValue == element.ID) {
          that.cardEditParams.info.pay_method = element.PayMethod;
        }
      });

      if (that.cardCompanyInfoEditValue != 0) {
        that.cardEditParams.info.card_type = "2";
      } else {
        that.cardEditParams.info.card_type = "1";
      }

      that.cardEditParams.info.customer_group_id = "0";
      that.cardEditParams.info.remark = that.cardRemark;
      that.cardEditParams.info.dis_remark = that.dis_remark;
      that.cardEditParams.info.state = that.cardStateEditValue;
      that.cardEditParams.info.is_need_password = that.radioEditValue;
      if (that.checked) {
        that.cardEditParams.info.new_pwd = "123456";
      } else {
        //没有重置密码时，若有new_pwd将删除字段
        that.cardEditParams.info.new_pwd
          ? delete that.cardEditParams.info.new_pwd
          : "";
      }

      that.$axios
        .post("/Card/changeCardsInfo", that.cardEditParams)
        .then((res) => {
          if (res.data.status == 200) {
            that.cardEditDialogVisible = false;
            that.$message({
              message: "修改成功",
              type: "success",
            });
            that.getUserCardList();
          } else if (res.data.status == 253) {
            //253个人卡余额不能转车队卡
            that.cardCompanyInfoEditValue = "";
            that.cardTypeEditValue = String(this.cardTypeNumber);
            that.$message.error(res.data.info);
          } else {
            that.$message.error(res.data.info);
          }
        });
    },
    //显示批量编辑弹窗
    ShowEditAll() {
      this.cardAllEditDialogVisible = true;
      this.cardCompanyInfoEditAllValue = ""
      this.cardStateEditAllValue = ""
      this.cardThemeEditValue = "";
      this.cardTypeEditValue = "";
      this.cardStateEditValue = "";
      this.cardTypeEditAllValue = "";
      this.allDis_remark = ""
      this.hasNeedPassword = true;
      this.multipleSelection.forEach((element) => {
        //如果选中一张卡规则没开通免密，则全部不能设置
        if (element.ISNeedPassword == 2) {
          this.radioEditAllValue = 2;
          this.hasNeedPassword = false;
        }
      });
    },
    //改变车队信息
    changeCompanyInfoEditAllValue(val) {
      if (val) {
        this.cardTypeEditAllValue = "2";
      }
    },
    //批量编辑
    editAll() {
      let that = this;
      let cardIDs = [];
      let card_secret = [];
      let search_data = {
        set_card_name:this.cardThemeValue, //卡名称
        set_company_id:this.cardCompanyValue,  //车队ID
        set_card_type:this.cardTypeValue,   //卡类型
        set_customer_groupid:this.cardGroupValue, //卡组
        set_card_state:this.cardStateValue,}  //卡状态


      this.multipleSelection.forEach((element) => {
        cardIDs.push(element.ID);
        card_secret.push({
          UsableOilStation: element.UsableOilStation,
          ID: element.ID,
          CardNO: element.CardNO,
        });
      });
      let pay_method = "";
      that.cardCompanyInfoEditOptions.forEach((element) => {
        if (that.cardCompanyInfoEditAllValue == element.ID) {
          pay_method = element.PayMethod;
        }
      });
      let ID = "0";
      let Extend = "";
      let Name = "";
      that.cardThemeEditOptions.forEach((element) => {
        if (that.cardThemeEditAllValue == element.ID) {
          ID = element.ID;
          Extend = element.Extend;
          Name = element.Name;
        }
      });
      let is_remark_update = 0
      //如果需要全部页面修改，is_remark_update传1
      if(this.showCheckAll && that.isCheckAll == '1'){
        console.log('全部');
        is_remark_update = 1
      }
      //如果状态，车队名称，重置密码都没改，只改了备注，可以传is_remark_update
      if( this.cardStateEditAllValue == '' && this.cardCompanyInfoEditAllValue == ''&& this.allchecked == '' && this.allDis_remark != ''){
        console.log('都没改');
        is_remark_update = 1
      }
      let select_all = 0
      let data = {
          cards_id: cardIDs,
          info: {
            //均不是必填
            rid: ID, //卡名称id，若有下面两个必填
            extend: Extend, //卡名称扩展规则，在卡名称详情里有
            card_name: Name, //卡名称名字，在卡名称详情里有
            pay_method: pay_method,
            company_id: this.cardCompanyInfoEditAllValue
              ? this.cardCompanyInfoEditAllValue
              : 0, //车队id
            new_pwd: this.allchecked ? "123456" : "", //重置的新密码
            state: that.cardStateEditAllValue, //状态
            is_update_card_type: that.cardTypeEditAllValue ? 1 : 0, //是否修改卡类型
            card_type: that.cardTypeEditAllValue, //卡类型id，修改必填
            is_need_password: that.radioEditAllValue, //卡类型id，修改必填
            dis_remark:that.allDis_remark, //修改优惠信息
            select_all:select_all,
            is_remark_update: is_remark_update
          },
          card_secret: card_secret,
        }
        //选择全部页面全选,请求完部分后再请求全部
        if(this.showCheckAll && that.isCheckAll == '1'){
          let data2 = JSON.parse(JSON.stringify(data))
          data2.search_data = search_data
          data2.info.select_all = 1
          //取消的卡
          data2.no_cards_id = this.filterMultiple
          that.$axios.post("/Card/changeCardsInfo", data2)
          .then(res2=>{
            if(res2.data.status == 200){
              that.cardAllEditDialogVisible = false;
              that.$refs.multipleTable.clearSelection()
              that.multipleSelection = []
              that.isCheckAll = '0'
              that.filterMultiple = []
              that.intersection = []
              that.$message({
                message: "修改成功",
                type: "success",
              });
              that.getUserCardList();
            }else{
              that.$message.error(res2.data.info)
            }
          })
        }else{
          that.$axios
            .post("/Card/changeCardsInfo", data)
            .then((res) => {
              if (res.data.status == 200) {
                  that.$refs.multipleTable.clearSelection()
                  that.cardAllEditDialogVisible = false;
                  that.multipleSelection = []
                  that.isCheckAll = '0'
                  that.filterMultiple = []
                  that.intersection = []
                  that.$message({
                    message: "修改成功",
                    type: "success",
                  });
                that.getUserCardList();
              } else {
                that.$message.error(res.data.info);
              }
            });
        }
    },
    getRowKeys(row) {
      return row.ID;
    },
    //编辑卡信息，改变选择企业，卡类型自动切换成企业卡
    changeCompanyValue() {
      if (this.cardCompanyInfoEditValue) {
        this.cardTypeEditValue = "2";
      } else {
        this.cardTypeEditValue = String(this.cardTypeNumber);
      }
    },
    checkCardRuleDetail() {
      this.cardRuleDialogVisible = true;
    },
    //获取卡规则详情
    getCardThemeRuleInfo(id) {
      this.$axios
        .post("/CardRule/getCardThemeRuleInfo", {
          id: id,
        })
        .then((res) => {
          if (res.data.status == 200) {
            let data = res.data.data;
            this.show_restriction_type =
              data.public_config.show_restriction_type;
            this.cardRuleDetail.rule_name = data.public_config.rule_name;

            this.cardRuleDetail.use_station_list = "";
            this.cardRuleDetail.time =
              data.public_config.start_time +
              "至" +
              data.public_config.end_time;
            this.cardRuleDetail.description = data.public_config.description
              ? data.public_config.description
              : "-";
            this.cardRuleDetail.invoice_open_type =
              data.currency_rule.invoice_open_type == 0
                ? "充值开票"
                : data.currency_rule.invoice_open_type == 1
                ? "消费开票"
                : "增值税月底开票";
            this.cardRuleDetail.priority =
              data.public_config.priority == 1 ? "是" : "否";
            this.cardRuleDetail.state =
              data.public_config.state == 100 ? "启用" : "禁用";
            this.cardRuleDetail.allow_charge =
              data.public_config.allow_charge == 1 ? "允许" : "禁止";
            this.cardRuleDetail.card_charge_money =
              data.enable_rule.card_charge_money;
            this.cardRuleDetail.card_initial_money =
              data.enable_rule.card_initial_money;
            this.cardRuleDetail.card_printing_cost =
              data.enable_rule.card_printing_cost;
            this.cardRuleDetail.charge_max = data.recharge_rule.charge_max;
            this.cardRuleDetail.charge_min = data.recharge_rule.charge_min;
            this.cardRuleDetail.balance_max = data.recharge_rule.balance_max;
            this.cardRuleDetail.charge_money_limit_type =
              data.recharge_rule.charge_money_limit_type == 0
                ? "不限制"
                : data.recharge_rule.charge_money_limit_type == 1
                ? "整倍数"
                : "固定值";
            this.cardRuleDetail.charge_money_limit_data =
              data.recharge_rule.charge_money_limit_data;
            this.cardRuleDetail.is_allow_coupon =
              data.consume_rule.is_allow_coupon == 1 ? "允许" : "禁止";
            this.cardRuleDetail.is_need_password =
              data.consume_rule.is_need_password == 1 ? "允许" : "禁止";
            this.cardRuleDetail.consumption_limit =
              data.consume_rule.consumption_limit;

            this.cardRuleDetail.cus_type =
              data.public_config.cus_type == 1 ? "个人卡账" : "通用";
            this.cardRuleDetail.expire_value = data.public_config.expire_value;
            this.cardRuleDetail.market_type = data.public_config.market_type;
            this.cardRuleDetail.show_restriction_type =
              data.public_config.show_restriction_type == 0
                ? "通用"
                : data.public_config.show_restriction_type == 1
                ? "仅发行云端卡"
                : "仅发行实体卡";

            if (this.isDetail) {
              this.cardCheckDialogVisible = true;
            } else {
              this.cardEditDialogVisible = true;
            }
          } else {
            this.$message.error(res.data.info);
          }
          this.$loading().close();
        });
    },

    //下载数据
    exportUserCardList() {
      this.$axios
        .post("/Card/exportUserCardList", {
          page: this.currentPage, //页码
          page_size: this.pageSize, //每页数量
          input: this.inputText, //输入
          input_type: this.searchTypeVlaue, //输入
          card_theme_id: this.cardThemeValue, //卡名称id
          card_type_id: this.cardTypeValue, //卡类型id
          customer_group_id: this.cardGroupValue, //分组id
          status: this.cardStateValue, //状态
          company_id: this.cardCompanyValue,
          stid: this.stationId,
        })
        .then((res) => {
          if (res.data.status == 200) {
            this.showDownloadTips = true;
          } else {
            this.$message.error(res.data.info);
          }
        });
    },

    showEmptyDialogVisible() {
      if (this.cardTypeNumber == 1) {
        this.emptyTipsDialogVisible = true;
      } else {
        //防抖函数有时候界面停留久了会失效原因不明，再次做校验
        if (
          Number(this.money01) + Number(this.money03) != Number(this.Amount) ||
          Number(this.money02) + Number(this.money04) != Number(this.GiveAmount)
        ) {
          this.$message.error("金额分配有误！");
          return;
        }
        this.emptyTipsCompanyDialogVisible = true;
      }
      this.emptyDialogVisible = false;
    },
    //余额清零
    showEmptyWarning() {
      let params = {};
      this.canClear = false;
      if (this.cardTypeNumber != 1) {
        params = {
          CardID: this.CardID,
          CardNO: this.cardNo,
          UID: this.UID,
          CompanyID: this.CompanyID,
          StationNO: this.StationNO,
          BalanceClearAmount: this.money03,
          GiveBalanceClearAmount: this.money04,
          RefundAmount: this.money01,
          GiveRefundAmount: this.money02,
        };
      } else {
        params = {
          CardID: this.CardID,
          CardNO: this.cardNo,
          UID: this.UID,
          StationNO: this.StationNO,
          BalanceClearAmount: this.Amount,
          GiveBalanceClearAmount: this.GiveAmount,
        };
      }
      this.$axios.post("/Card/cardBalanceClear", params).then((res) => {
        this.canClear = true;
        if (res.data.status == 200) {
          this.cardRuleDialogVisible = false;
          this.emptyDialogVisible = false;
          this.emptyTipsCompanyDialogVisible = false;
          this.emptyTipsDialogVisible = false;
          this.cardCheckDialogVisible = false;
          this.$message.success("操作成功！");
          this.getUserCardList();
        } else {
          this.$message.error(res.data.info);
        }
      });
    },

    //修改备注
    orderChange(item, index) {
      this.tableData[index].showRemark = false;
      let that = this;
      this.$axios
        .post("/Card/changeCardsInfo", {
          cards_id: item.ID,
          info: {
            phone: item.Phone, //手机号
            car_number: item.CarNumber, //车牌号,
            rid: item.RID, //卡主题id，若有下面一个必填
            card_name: item.CardName, //卡主题名字，在卡主题详情里有
            extend: item.extend, //卡主题扩展规则，在卡主题详情里有
            company_id: item.CompanyID, //企业id
            customer_group_id: item.CustomerGroupID, //客户组id
            card_type: item.CardType, //卡类型id，修改必填
            state: item.State, //状态
            card_no: item.CardNO, //卡号
            users: [
              {
                id: item.UID,
                name: item.cardholder_name,
                icid: item.icid,
              },
            ],
            remark: item.IDNumBer.replace(/(\r\n|\n|\r)/gm, ""),
          },
        })
        .then(function (res) {
          if (res.data.status == 200) {
          } else {
            that.$message({
              message: res.data.info,
              type: "error",
            });
          }
        })
        .catch(function (error) {});
    },
    showRemarkInput(item) {
      console.log(item);
      this.$nextTick(() => {
        item.showRemark = true;
      });
    },
    //展示余额清零信息
    showEmptyTipsDialog() {
      this.emptyDialogVisible = true;
      if (this.cardTypeNumber != 1) {
        this.money01 = this.Amount;
        this.money02 = this.GiveAmount;
      }
    },
    // 是否可选
    selectable(val) {
      if (val.State == 112) {
        return false
      }else {
        return true
      }
    },
    // 注销卡
    deregistrationCar() {
      if (this.cardAmount > 0) {
        this.$message({
          message: '该卡内有余额无法注销，请余额清零后再注销 ',
          type: 'warning'
        });
      }else {
        this.deregistrationDialogVisible1 = true
        this.deregistrationReson = ""
      }
    },
    deregistrationAction() {
      this.deregistrationDialogVisible1 = false
      this.deregistrationNoticeDialogVisible = true
    },
    confirmDeregistration() {
      this.deregistrationNoticeDialogVisible = false
      this.verifyDialogVisible = true
      this.password = ""
    },
    closeSuccess() {
      this.deregistrationSuccessDialogVisible = false
      this.getUserCardList();
    },
    deregistration() {
      var that = this
      this.verifyDialogVisible = false
      this.$axios.post("Card/cardCancellation", {
        card_no: that.cardNo,
        remark: that.deregistrationReson,
        password: that.password
      }).then((res) => {
        console.log(res)
        if (res.status == 200 && res.data.status == 200) {
          that.deregistrationDialogVisible1 = false;
          that.cardCheckDialogVisible = false
          that.$message({
            message: '注销成功',
            type: 'success'
          });
          that.getUserCardList();
        }else {
          // that.deregistrationFailDes = res.data.info
          // that.deregistrationFailDialogVisible = true
          that.$message.error(res.data.info);
        }
      });
    },
    emptyTipsCompanyClose() {
      this.emptyDialogVisible = false;
      this.emptyTipsCompanyDialogVisible = false;
    },

    //获取可用油站
    getStationList() {
      let that = this;
      this.$axios.post("/Stations/getStationList", {}).then((res) => {
        if (res.status == 200) {
          that.stationOptions = [
            {
              stid: "0",
              stname: "全部",
            },
          ];
          that.stationOptions = that.stationOptions.concat(res.data.data);
        }
      });
    },
    changeAllPage(){
      console.log('multipleSelection',this.multipleSelection);
      console.log('所选长度',this.multipleSelection.length);
      console.log('交集长度',this.intersection.length);

      this.filterMultiple = []
      this.selectMultipleSelection = []
      // this.multipleSelection = this.tableData
      this.tableData.forEach(item=>{
        this.$refs.multipleTable.toggleRowSelection(item,true)
      })
      this.allLength = this.multipleSelection.length - this.intersection.length + this.total
      this.$forceUpdate()

      // console.log('所有长度',this.allLength);
    },
    changeThisPage(){
      this.filterMultiple = []
      this.selectMultipleSelection = []
      this.$refs.multipleTable.clearSelection()
      // this.multipleSelection = this.tableData
      this.tableData.forEach(item=>{
        this.$refs.multipleTable.toggleRowSelection(item,true)
      })
    },
    //调用计算table宽度的函数
    setColumnWidth() {
      return setColumnWidth(this.tableData,3)
    }
  },
  watch: {
    async getCurrentStation(newValue, oldValue) {
      if (newValue.merchant_type != 0 && newValue.value != oldValue.value) {
        if (newValue.merchant_type == 2) {
          this.getStationList();
        }
        this.getCardThemeRuleList();
        await this.getCompanyList();
        this.getCustomerGroupList();
        if (this.$route.params.id) {
          this.cardCompanyValue = this.$route.params.id;
          this.changeData();
        } else {
          this.getUserCardList();
        }
      }
    },
    $route(newValue) {
      if (this.$route.params.id) {
        this.cardCompanyValue = this.$route.params.id;
        this.changeData();
      }
    },
    multipleSelection(newValue,oldValue){
      //声明一个数组用来装所有选中的ID
      let checkArr = newValue.map(item=>item.ID)
      // console.log('checkArr',checkArr);

      //声明一个数组用来装旧数据
      let oldArr = oldValue.map(item=>item.ID)
      // console.log('oldArr',oldArr);


      //声明一个数组用来装当前页面的ID
      let nowCheckArr = this.selectMultipleSelection.map(item=>item.ID)

      let nowCheckArrSet = new Set(nowCheckArr)
      //声明一个数组用来装当前选中ID和所有ID的交集，判断是否当前页面全部选中
      let intersection = checkArr.filter(item=> nowCheckArrSet.has(item))
      this.intersection = intersection
      // console.log('intersection',intersection);


      //如果全选，并且选了车队名称，卡名称，卡组才可以全选全部页面
      // if(nowCheckArr.length && intersection.length == nowCheckArr.length){
        // console.log('全选');
        if(this.cardCompanyValue != '0' || this.cardThemeValue != '0' || this.cardGroupValue != '0'){
          // console.log('选了车队名称，卡名称，卡组');
          let diff = oldArr.filter(val => checkArr.indexOf(val) === -1)
          let noDiff = checkArr.filter(val => oldArr.indexOf(val) === -1)
          console.log('diff',diff);
          console.log('noDiff',noDiff);
          console.log('filterMultiple',this.filterMultiple);
          if(diff.length > 0){
            this.filterMultiple.push(...diff)
          }else if(!diff.length){
            noDiff.forEach(item=>{
              if(this.filterMultiple.indexOf(item) != -1){
                this.filterMultiple.splice(this.filterMultiple.indexOf(item), 1)
              }
            })
          }


          this.showCheckAll = true
        }else{
          this.showCheckAll = false
        }
      // }else{
      //   this.showCheckAll = false
      // }
      this.filterMultiple = Array.from(new Set(this.filterMultiple))
      //  if(this.inputText == ''){
         this.allLength = this.multipleSelection.length - this.intersection.length + this.total - this.filterMultiple.length

      //  }
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->

<style scoped>
@import "../assets/css/cardTheme.css";
@import "../assets/css/cardRule.css";
@import "../assets/css/cardManagement.css";
.edit-icon {
  font-size: 20px;
}
.edit-icon:hover {
  color: #32af50;
  cursor: pointer;
}
.empty-dialog .tips-item {
  position: absolute;
  width: 100%;
  left: 0;
  padding: 8px 0;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
  background: #fae1d7;
  color: #fa5a00;
}
.empty-dialog .input-bar {
  width: 96px;
  margin: 0 12px;
}
.empty-money {
  background: #f5f5f5;
  padding: 16px 30px;
  margin-bottom: 27px;
}
.empty-money .line {
  margin-bottom: 16px;
}
.empty-money .tabs {
  display: inline-block;
  width: 120px;
  text-align: right;
  margin-right: 6px;
}
.point-txt::before {
  content: "";
  display: inline-block;
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background: #32af50;
  margin-right: 8px;
}
</style>
<style>
.empty-dialog .el-dialog__body {
  padding: 0 20px 30px;
}
.emptyTips-dialog .el-dialog__header {
  border-bottom: 0;
  padding: 0;
}
.emptyTips-dialog .el-dialog__body {
  padding-top: 40px;
  padding-bottom: 22px;
}
.emptyTips-dialog .emptyTips {
  font-size: 16px;
  text-align: center;
  font-weight: bold;
}
.emptyTips-dialog .emptyTips p {
  margin: 5px 0;
}
.emptyTipsCompany-dialog .emptyTips {
  font-size: 16px;
  margin-left: 55px;
}
.empty-dialog .el-input__inner {
  color: #32af50;
  font-weight: bold;
}
.remark .el-input__count{
  background: 0;
  bottom: 0;
}
.logout_btn {
  color: #fa5a00 !important;
}
.logout_btn:hover {
  color: #fa5a00 !important;
  opacity: 0.8 !important;
}
.logout_box {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #333333;
  line-height: 24px;
}
.waring_icon {
  widows: 40px;
  height: 40px;
  margin-right: 10px;
}
.waring_icon1 {
  widows: 60px;
  height: 60px;
  margin-bottom: 20px;
}
.logout_box1 {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 16px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #333333;
  line-height: 24px;
}
.notice_des {
  text-align: center;
  margin-top: 15px;
}
</style>
