<template>
  <div id="refund" class="refund">
    <div class="header">
      <div class="classType">
        <el-radio-group v-model="typeClassValue" @change="changeClassTypeValue">
          <el-radio-button
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.value"
            >{{ item.label }}</el-radio-button
          >
        </el-radio-group>
        <el-select
          v-model="stationId"
          :multiple="typeClassValue == 1"
          clearable
          collapse-tags
          style="width:250px;margin-right:20px;"
          placeholder="请选择油站"
          v-if="getCurrentStation.merchant_type == 2 && update"
          @change="changeStationValue"
        >
          <el-option
            v-for="item in stationOptions"
            :key="item.stid"
            :label="item.stname"
            :value="item.stid"
          >
          </el-option>
        </el-select>
        <el-date-picker
          v-show="typeClassValue == 1"
          style="width:355px; margin-left:30px;"
          v-model="searchDate"
          :picker-options="startTimePicker"
          @change="handleChangeTpye"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
        <banci-date-time
          ref="banciRef"
          :stationValue="stationId"
          :dateValue="dateBanciValue"
          :picker-options="startTimePicker"
          @searchDate="searchBanciDate"
          @changeDate="changeDate"
          v-show="typeClassValue == 4"
        >
        </banci-date-time>
        <!-- <el-date-picker
                v-show="typeClassValue == 4"
                style="margin-right:15px"
                :default-value="nowDate"
                v-model="dateBanciValue"
                type="date"
                placeholder="选择日期"
                format="yyyy 年 MM 月 dd 日"
                value-format="yyyy-MM-dd"
                @change="BanciChange">
            </el-date-picker>
            <div class="lookClass" v-show="typeClassValue == 4">查看班次</div>
            <el-checkbox-group v-model="checkList" class="checkClass" v-show="typeClassValue == 4" @change="searchBanci">
                <el-checkbox v-for="item in classList" :key="item.index" :label="item">{{item.bcmc}}</el-checkbox>
            </el-checkbox-group>
            <span v-show="typeClassValue == 4 && !classList.length " class="Noclass">暂无班次</span> -->
      </div>
      <el-radio-group v-model="typeValue" @change="handleChangeLabelType">
        <el-radio-button
          v-for="item in tabList"
          :key="item.index"
          :label="item.value"
          >{{ item.label }}</el-radio-button
        >
      </el-radio-group>
      <div class="search-box">
        <div class="left">
          <span class="txt">查询类型</span>
          <el-radio-group v-model="searchTypeValue" @change="changeTypeValue">
            <el-radio label="1">手机号</el-radio>
            <el-radio label="2">订单号</el-radio>
            <el-radio label="3">卡号</el-radio>
            <el-radio label="4">付款方式</el-radio>
          </el-radio-group>
          <el-input
            v-if="searchTypeValue == 1"
            v-model="searchOrderPhone"
            style="margin-left: 20px;width:210px"
            placeholder="请输入手机号"
            clearable
          ></el-input>
          <el-input
            v-if="searchTypeValue == 2"
            v-model="searchOrderNumber"
            style="margin-left: 20px;width:210px"
            placeholder="请输入订单号"
            clearable
          ></el-input>
          <el-input
            v-if="searchTypeValue == 3"
            v-model="searchCardNumber"
            style="margin-left: 20px;width:210px"
            placeholder="请输入卡号"
            clearable
          ></el-input>
          <!-- <el-input v-if="searchTypeValue==4"  v-model="searchPayMethods" style="width:210px" placeholder="请选择付款方式" clearable></el-input> -->
          <el-select
            v-if="searchTypeValue == 4"
            v-model="searchPayMethods"
            multiple
            collapse-tags
            filterable
            style="margin-left: 20px; width:210px"
            placeholder="请选择付款方式"
          >
            <el-option
              v-for="item in PayMethodsOptions"
              :key="item.pay_way_id"
              :label="item.payment_name"
              :value="item.pay_way_id"
            >
            </el-option>
          </el-select>
          <el-button type="primary" @click="handleChangeTpye">查询</el-button>
          <div
            style="display:inline-block;margin-left: 10px;"
            v-if="checkList.length"
          >
            <span>时间范围:</span>
            <span class="Noclass">{{ searchDate ? searchDate[0] : "-" }}</span>
            至
            <span class="Noclass">{{ searchDate ? searchDate[1] : "-" }}</span>
          </div>
        </div>

        <el-button
          type="primary"
          :disabled="tableData.length == 0"
          @click="exportCompanyChargeOrders"
          >下载数据</el-button
        >
      </div>
    </div>

    <!-- 表格数据 -->
    <el-table
      class="refundData"
      v-loading="tableLoading"
      :data="tableData"
      stripe
      style="width: 100%"
    >
      <el-table-column
        fixed
        v-if="getCurrentStation.merchant_type == 2"
        align="left"
        prop="station_name"
        label="充值油站"
        width="160"
      >
      </el-table-column>
      <el-table-column
        width="190"
        label="卡号"
        :formatter="formatterCellval"
        prop="card_no"
      >
      </el-table-column>
      <el-table-column
        align="center"
        width="120"
        label="手机号"
        prop="phone"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="持卡人"
        min-width="100"
        prop="cardholder_name"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        v-if="typeValue == 2 || typeValue == 3 || typeValue == 4"
        align="center"
        prop="pay_money"
        width="120"
        label="退款金额(元)"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="update_date"
        v-if="typeValue == 2"
        label="退款申请时间"
        width="180"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="refund_datetime"
        v-if="typeValue == 3"
        label="退款时间"
        width="200"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="update_date"
        v-if="typeValue == 4"
        label="退款审核时间"
        width="200"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        v-if="typeValue == 3"
        align="center"
        prop="refund_way"
        width="120"
        label="退款方式"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="left"
        prop="orig_amount"
        width="120"
        label="充值本金(元)"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="left"
        prop="gift_amount"
        width="120"
        label="充值赠金(元)"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="left"
        prop="charge_money"
        width="120"
        label="充值总额(元)"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        v-if="flag"
        align="left"
        prop="fee"
        width="120"
        label="手续费(元)"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        v-if="flag"
        align="left"
        prop="settle_amt"
        width="120"
        label="实结金额(元)"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="left"
        width="210"
        label="卡名称"
        prop="card_name"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        width="190"
        key="2"
        label="车队名称"
        :formatter="formatterCellval"
        prop="company_name"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="order_code"
        width="190"
        label="订单号"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="pay_datetime"
        label="充值时间"
        width="180"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="charge_consume_type"
        width="100"
        label="充值类型"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="type"
        width="100"
        label="账户类型"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="pay_way"
        width="180"
        label="付款方式"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="source"
        width="100"
        label="数据来源"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="operator_name"
        width="100"
        label="员工名称"
        :formatter="formatterCellval"
      >
      </el-table-column>
      <el-table-column
        type="index"
        align="center"
        prop="remark"
        width="240"
        label="备注"
      >
        <template slot-scope="scope" class="remark">
          <el-input
            style="width:90%"
            type="textarea"
            maxlength="75"
            show-word-limit
            v-show="scope.row.showRemark"
            v-model="scope.row.remark"
            placeholder="请输入备注"
            @blur="orderChange(scope.row, scope.$index)"
            @keyup.enter.native="$event.target.blur"
          ></el-input>
          <span v-show="!scope.row.showRemark">{{
            scope.row.remark ? scope.row.remark : ""
          }}</span>
          <i
            v-show="!scope.row.showRemark"
            class="el-icon-edit-outline edit-icon"
            @click="showRemarkInput(scope.row)"
          ></i>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" width="180" label="操作">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.order_status == 1 && scope.row.is_superadmin == 1"
            @click="handleNoApprovalClick(scope.row, 1)"
            type="text"
            size="small"
            >退款</el-button
          >
          <el-button
            v-if="scope.row.order_status == 2 && scope.row.is_superadmin == 1"
            @click="handleRefundClick(scope.row)"
            type="text"
            size="small"
            >通过</el-button
          >
          <el-button
            v-if="scope.row.order_status == 2 && scope.row.is_superadmin == 1"
            @click="handleNoApprovalClick(scope.row, 2)"
            type="text"
            size="small"
            >不通过</el-button
          >
          <el-button
            @click="handleGZLDetailClick(scope.row.order_code)"
            type="text"
            size="small"
            >工作流明细</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 页码 -->
    <div class="page_content">
      <el-pagination
        class="page_left"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-size="pageSize"
        layout="prev, pager, next"
        :total="total"
      >
      </el-pagination>
      <el-pagination
        class="page_right"
        @size-change="handleSizeChange"
        :page-sizes="[10, 15, 20, 30]"
        :page-size="pageSize"
        layout="total, sizes"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 工作流明细 -->
    <el-dialog
      :close-on-click-modal="false"
      title="工作流明细"
      :visible.sync="GZLDetailDialogVisible"
      width="50%"
    >
      <el-table :data="approvalFlowTableData" style="width: 100%">
        <el-table-column
          align="center"
          prop="create_time"
          label="创建时间"
          :formatter="formatterCellval"
        >
        </el-table-column>
        <el-table-column
          align="center"
          prop="operator_name"
          label="操作"
          :formatter="formatterCellval"
        >
        </el-table-column>
        <el-table-column
          align="center"
          prop="status_change"
          label="状态变化"
          :formatter="formatterCellval"
        >
        </el-table-column>
        <el-table-column
          align="center"
          prop="remark"
          label="备注"
          :formatter="formatterCellval"
        >
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button
          size="mini"
          type="primary"
          @click="GZLDetailDialogVisible = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <!-- 审核不通过反馈弹窗 -->
    <el-dialog
      class="dialog"
      :close-on-click-modal="false"
      :visible.sync="reasonDialogVisible"
      width="520px"
    >
      <div slot="title">
        <span v-if="charge == 1" class="title-name">充值退款</span>
        <span v-if="charge == 2" class="title-name">审核不通过原因</span>
      </div>
      <div class="check-box" v-if="charge == 1">
        <div class="left">
          <p>
            <span class="txt">卡 号：</span
            ><span>{{ orderInfo.card_no ? orderInfo.card_no : "--" }}</span>
          </p>
          <p>
            <span class="txt">持卡人：</span
            ><span>{{
              orderInfo.cardholder_name ? orderInfo.cardholder_name : "--"
            }}</span>
          </p>
          <p>
            <span class="txt">手机号：</span
            ><span>{{ orderInfo.phone ? orderInfo.phone : "--" }}</span>
          </p>
          <p>
            <span class="txt">车队名称：</span
            ><span>{{
              orderInfo.company_name ? orderInfo.company_name : "--"
            }}</span>
          </p>
          <p>
            <span class="txt">订单号：</span
            ><span>{{
              orderInfo.order_code ? orderInfo.order_code : "--"
            }}</span>
          </p>
          <p>
            <span class="txt">付款方式：</span
            ><span style="font-weight:bold">{{ orderInfo.pay_way }}</span>
          </p>
        </div>
        <div class="right">
          <p>
            <span class="txt">充值时间：</span
            ><span>{{ orderInfo.pay_datetime }}</span>
          </p>
          <p>
            <span class="txt">充值总额：</span
            ><span style="font-weight:bold">{{ orderInfo.charge_money }}</span
            >元
          </p>
          <p>
            <span class="txt">充值本金：</span
            ><span style="font-weight:bold">{{ orderInfo.orig_amount }}</span
            >元
          </p>
          <p>
            <span class="txt">充值赠金：</span
            ><span style="font-weight:bold">{{ orderInfo.gift_amount }}</span
            >元
          </p>
          <p>
            <span class="txt">退款金额：</span
            ><span style="font-weight:bold">{{ orderInfo.pay_money }}</span
            >元
          </p>
        </div>
      </div>
      <el-radio-group v-model="reasonRadio">
        <el-radio label="其他">其他</el-radio>
      </el-radio-group>
      <el-input
        type="textarea"
        maxlength="75"
        show-word-limit
        style="margin-top:10px;height:80px"
        v-if="reasonRadio == '其他'"
        v-model="reason"
        :placeholder="charge == 1 ? '请输入退款说明' : '请输入审核不通过原因'"
      ></el-input>
      <div class="tips" v-if="charge == 1">
        <i class="el-icon-warning" style="color:#FA5A00;margin: 4px;"></i>
        <div>
          <p style="color:#FA5A00">
            充值退款原路返回仅支持付款方式为微信、支付宝的订单
          </p>
          <p>银行卡付款的订单退款申请成功后，原路返回请到银联APP操作</p>
          <p>现金等其他付款方式的订单申请成功后，由油站自行退款</p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="reasonDialogVisible = false"
          >取 消</el-button
        >
        <el-button
          size="mini"
          v-if="approvalDisable"
          type="primary"
          :loading="true"
          >处理中</el-button
        >
        <el-button size="mini" v-else type="primary" @click="approval">{{
          charge == 1 ? "发起退款" : "确定"
        }}</el-button>
      </span>
    </el-dialog>
    <!-- 审核通过 -->
    <el-dialog
      title="安全验证"
      class="safe-dialog"
      :visible.sync="dialogVisible"
      width="400px"
    >
      <p v-if="getCurrentStation.merchant_type == 2">
        请输入集团管理员操作密码
      </p>
      <p v-else>请输入油站管理员操作密码</p>
      <!-- <p>
            <i class="el-icon-user"></i>
            <span style="padding-right:15px">{{manager}}</span>
            <span>超级管理员</span>
        </p> -->
      <el-input
        v-model="password"
        type="password"
        placeholder="请输入操作密码"
      ></el-input>
      <div v-if="refundInfo.is_online == 1" class="tips-item">
        <i class="el-icon-warning" style="margin-right:8px;font-size:14px"></i
        >验证通过后，发起退款原路返回，请务必谨慎操作！
      </div>
      <div v-else class="tips-item">
        <i class="el-icon-warning" style="margin-right:8px;font-size:14px"></i
        >验证通过后，扣除卡充值金额，请自行退款给车主！
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogVisible = false">取 消</el-button>
        <el-button
          size="mini"
          v-if="approvalDisable"
          type="primary"
          :loading="true"
          >退款中</el-button
        >
        <el-button
          size="mini"
          v-else
          type="primary"
          :disabled="!password"
          @click="refundCompanyCharge"
          >确认通过</el-button
        >
      </span>
    </el-dialog>

    <!-- 道达尔集团取消安全验证 -->
    <el-dialog title="提示" :visible.sync="dialogVisible2" width="520px">
      <span>对订单进行退款，是否继续?</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible2 = false">取 消</el-button>
        <el-button type="primary" @click="refundCompanyCharge">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 下载中心提示 -->
    <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>
  </div>
</template>

<script>
const userInfo = JSON.parse(localStorage.getItem("__userInfo__"));

import DownloadTips from "./DownloadTips.vue";
import BanciDateTime from "./Banci/banciDateTime.vue";
import { mapGetters } from "vuex";
export default {
  name: "Refund",
  components: {
    DownloadTips,
    BanciDateTime
  },
  data() {
    return {
      isTotalReportForm: true,
      userInfo: userInfo,
      approvalDisable: false,
      dialogVisible: false, //审核通过弹窗
      dialogVisible2: false, //道达尔审核通过弹窗
      refundInfo: {},
      typeValue: 1,
      tabList: [
        {
          value: 1,
          label: "充值订单"
        },
        {
          value: 2,
          label: "退款审核中"
        },
        {
          value: 3,
          label: "已退款"
        },
        {
          value: 4,
          label: "退款失败"
        }
      ],
      typeClassValue: 1,
      dateBanciValue: "",
      typeOptions: [
        {
          value: 1,
          label: "按自然日期"
        },
        {
          value: 4,
          label: "按班结日期"
        }
      ],
      checkList: [],
      classList: [], //班次列表
      stationId: [], //选中油站
      stationOptions: [], //油站列表
      tableLoading: true,
      tableData: [],
      tableDetailData: [], //详情数据
      currentPage: 1,
      pageSize: 10,
      total: 0,
      GZLDetailDialogVisible: false, //工作流明细
      searchTypeValue: "1",
      searchOrderNumber: "", //单号
      searchOrderPhone: "", //手机号
      searchCardNumber: "", //卡号
      searchPayMethods: [], //付款方式
      searchDate: [], //时间
      reasonDialogVisible: false,
      orderInfo: {}, //存储选中的对象
      reasonRadio: "其他",
      reason: "",
      charge: 1, //退款为1，审批不通过为2
      approvalFlowTableData: [], //工作流明细

      showDownloadTips: false,
      password: "", //操作密码
      manager: "",
      PayMethodsOptions: [],
      update: true,
      nowEndTime: "",
      flag: 0 // 手续费、结算金额查看，0看不到 1看得到
    };
  },
  created() {
    this.seeFee(); //查看手续费
  },
  mounted() {
    this.$axios = this.$axios.create();
    let userInfo = JSON.parse(localStorage.getItem("__userInfo__"));
    if (userInfo) {
      this.manager = userInfo.name;
    this.isTotalReportForm = userInfo.group_id != 426;
    }
    //获取前一个月的时间
    let startDate = this.$moment(new Date())
      .subtract(1, "months")
      .format("YYYY-MM-DD HH:mm:ss");
    let endDate = this.$moment(new Date());
    this.searchDate.push(
      this.$moment(startDate).format("YYYY-MM-DD") + " 00:00:00"
    );
    this.searchDate.push(
      this.$moment(endDate).format("YYYY-MM-DD HH:mm:ss")
    );
    this.nowEndTime = this.$moment(endDate).format("YYYY-MM-DD HH:mm:ss").slice(11)

    if (
      this.getCurrentStation.merchant_type == undefined ||
      this.getCurrentStation.merchant_type == 0
    ) {
      return false;
    }
    this.dateBanciValue = this.nowDate;
    this.getCompanyChargeOrder();
    this.getPayMethod();

    this.getStationList();
    this.stationId = [];
    this.$nextTick(() => {
      if (this.getCurrentStation.merchant_type == 1) {
        this.stationId.push(this.getCurrentStation.merchant_id);
      }
    });
  },
  computed: {
    ...mapGetters({
      getCurrentStation: "getCurrentStation",
      getUserInfo: "getUserInfo"
    }),
    startTimePicker() {
      var _this = this;
      return {
        disabledDate(time) {
          let endTime = new Date();
          const start = new Date();
          if (_this.userInfo.has_mch_id === 0) {
            return time.getTime() > new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1).getTime()
          }
          //最大值三个月范围限制
          start.setTime(endTime.getTime() - 3600 * 1000 * 24 * 90);
          return (
            time.getTime() > endTime ||
            time.getTime() < start ||
            time.getTime() > Date.now()
          );
        }
      };
    },
    nowDate: function() {
      return this.$moment(new Date()).format("YYYY-MM-DD");
    }
  },
  methods: {
    changeDate(value) {
      // console.log('value',value);
      this.dateBanciValue = value;
    },
    searchBanciDate(value) {
      this.searchDate = value;
      this.getCompanyChargeOrder();
    },
    //根据班次搜索
    searchBanci(e) {
      console.log("banci", e);
      if (e.length) {
        //选择了班次
        let startTime = [];
        let endTime = [];
        //获取时间戳存入数组中
        startTime = e.map(item => new Date(item.stime).getTime());
        endTime = e.map(item => new Date(item.etime).getTime());
        //判断大小，选出最小值为开始时间 最大值为结束时间
        let searchEndTime = Math.max.apply(null, endTime);
        let searchStartTime = Math.min.apply(null, startTime);

        this.searchDate[0] = this.$moment(searchStartTime).format(
          "YYYY-MM-DD hh:mm:ss"
        );
        this.searchDate[1] = this.$moment(searchEndTime).format(
          "YYYY-MM-DD hh:mm:ss"
        );

        this.getCompanyChargeOrder();
      } else {
        //没有选择班次
        this.searchDate = [];
        let startDate = this.$moment(new Date(this.dateBanciValue));
        let endDate = this.$moment(new Date(this.dateBanciValue));
        this.searchDate.push(
          this.$moment(startDate).format("YYYY-MM-DD") + " 00:00:00"
        );
        this.searchDate.push(
          this.$moment(endDate).format("YYYY-MM-DD") + ` ${this.nowEndTime}`
        );
        this.getCompanyChargeOrder();
      }
    },
    //获取可用油站
    getStationList() {
      this.stationId = [];
      let that = this;
      this.$axios.post("/Stations/getStationList", {}).then(res => {
        if (res.status == 200) {
          that.stationOptions = [];
          res.data.data.forEach(item => {
            that.stationId.push(item.stid);
          });
          that.stationOptions = res.data.data;
        }
      });
    },
    //切换班次类型
    changeClassTypeValue(e) {
      console.log("e", e);
      if (e == 4) {
        this.stationId = "";
        this.$refs.banciRef.clearDate();
      } else {
        this.stationId = [];
        this.searchDate = [];
        let startDate = this.$moment(new Date())
          .subtract(1, "months")
          .format("YYYY-MM-DD HH:mm:ss");
        let endDate = this.$moment(new Date());
        this.searchDate.push(
          this.$moment(startDate).format("YYYY-MM-DD") + " 00:00:00"
        );
        this.searchDate.push(
          this.$moment(endDate).format("YYYY-MM-DD") + ` ${this.nowEndTime}`
        );
        this.getCompanyChargeOrder();
      }
      //判断是否是单站，单站默认选中油站
      if (this.getCurrentStation.merchant_type == 1) {
        this.stationId = this.getCurrentStation.merchant_id;
      }
    },
    //切换班次日期
    BanciChange(e) {
      console.log("e", e);
      this.searchDate = [];
      let startDate = this.$moment(new Date(this.dateBanciValue));
      let endDate = this.$moment(new Date(this.dateBanciValue));
      this.searchDate.push(
        this.$moment(startDate).format("YYYY-MM-DD") + " 00:00:00"
      );
      this.searchDate.push(
        this.$moment(endDate).format("YYYY-MM-DD") + ` ${this.nowEndTime}`
      );

      if (e && this.stationId.length > 1) {
        this.$message.error("查看班次只能选择一个油站");
      } else if (e && this.stationId.length < 1) {
        this.$message.error("请选择一个油站");
      } else if (!e) {
        this.$message.error("请选择时间");
      } else {
        this.getSettlementList();
      }
    },
    //班次接口
    getSettlementList() {
      this.checkList = [];
      this.classList = [];
      let data = {
        stid: this.stationId,
        sdate: this.dateBanciValue,
        edate: this.dateBanciValue
      };
      //获取班次
      this.$axios
        .post("/CardCharge/getSettlementList", data)
        .then(res => {
          if (res.data.status == 200) {
            if (
              Object.prototype.toString.call(res.data.data) == "[object Object]"
            ) {
              for (const key in res.data.data) {
                this.classList.push(res.data.data[key]);
              }
            } else {
              this.classList = res.data.data;
            }
          } else {
            this.$message.error(res.data.info);
          }
        })
        .catch(err => {
          console.log("err", err);
        });
    },
    //切换油站
    changeStationValue(val) {
      console.log("val", val);
      console.log("stationId", this.stationId);
      if (this.typeClassValue == 4 && val) {
        //根据开班日期
        this.$refs.banciRef.getBanci(val);
      } else {
        this.getCompanyChargeOrder();
      }
    },
    //获取充值订单列表
    getCompanyChargeOrder() {
      let that = this;
      that.tableLoading = true;
      if (that.searchDate[0].length == 10)
        that.searchDate[0] = that.searchDate[0] + " 00:00:00";
      if (that.searchDate[1].length == 10){
        if(that.searchDate[1] == that.$moment(new Date()).format('YYYY-MM-DD')){
          that.searchDate[1] = that.searchDate[1] + ` ${this.$moment().format('HH:mm:ss')}`;
        }else{
          that.searchDate[1] = that.searchDate[1] + " 23:59:59";
        }
      }
      that.$axios
        .post("/CardCharge/getCompanyChargeOrder", {
          phone: that.searchOrderPhone,
          order_code: that.searchOrderNumber,
          vipcard_no: that.searchCardNumber,
          pay_way: that.searchPayMethods,
          start_time: that.searchDate ? that.searchDate[0] : "",
          end_time: that.searchDate ? that.searchDate[1] : "",
          page: that.currentPage,
          page_size: that.pageSize,
          order_status: that.typeValue,
          mch_arr: this.userInfo.mch_arr,
          station_id: that.stationId,
        })
        .then(function(res) {
          that.tableLoading = false;
          if (res.data.status == 200) {
            //深拷贝
            let data = JSON.parse(
              JSON.stringify(
                res.data.data.order_list ? res.data.data.order_list : []
              )
            );
            data.map((item, index) => {
              return (item.showRemark = false);
            });
            that.tableData = data;
            that.total = res.data.data.total;
          } else {
            that.tableData = [];
            that.total = 0;
            that.$message({
              message: res.data.info,
              type: "error"
            });
          }
        })
        .catch(function(error) {});
    },
    //切换页码
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getCompanyChargeOrder();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getCompanyChargeOrder();
    },
    //切换标签
    handleChangeLabelType(val) {
      // this.searchDate = [];
      this.dateBanciValue = [];
      // if(val == 1){
      //     let startDate = this.$moment(new Date()).subtract(1,'months').format('YYYY-MM-DD HH:mm:ss');
      //     let endDate = this.$moment(new Date());
      //     this.searchDate.push(this.$moment(startDate).format('YYYY-MM-DD')+ ' 00:00:00');
      //     this.searchDate.push(this.$moment(endDate).format('YYYY-MM-DD')+ ' 23:59:59');
      // }
      this.currentPage = 1;
      this.getCompanyChargeOrder();
    },
    //改变数据
    handleChangeTpye() {
      this.currentPage = 1;
      if(!this.searchDate){
        this.tableData = []
        return
      }
      this.getCompanyChargeOrder();
    },
    changeTypeValue(val) {
      if (val == 1) {
        this.searchOrderNumber = "";
        this.searchCardNumber = "";
      }
      if (val == 2) {
        this.searchOrderPhone = "";
        this.searchCardNumber = "";
      }
      if (val == 3) {
        this.searchOrderNumber = "";
        this.searchOrderPhone = "";
      }
    },
    //显示原因弹窗
    handleNoApprovalClick(val, index) {
      this.reasonDialogVisible = true;
      this.orderInfo = val;
      this.charge = index;
      this.reason = "";
    },
    //审核不通过
    approval() {
      let that = this;
      that.approvalDisable = true;
      that.$axios
        .post(
          "/CardCharge/approval",
          {
            order_code: that.orderInfo.order_code,
            order_type: that.orderInfo.order_type,
            operate: this.charge,
            remark: that.reasonRadio == "其他" ? that.reason : that.reasonRadio
          },
          {
            headers: {
              "X-Requested-With": "XMLHttpRequest"
            }
          }
        )
        .then(function(res) {
          if (res.data.status == 200) {
            setTimeout(() => {
              that.approvalDisable = false;
              that.$message({
                message: "操作成功",
                type: "success"
              });
              that.reasonDialogVisible = false;
              that.getCompanyChargeOrder();
            }, 2000);
          } else {
            setTimeout(() => {
              that.$message({
                message: res.data.info,
                type: "error"
              });
              that.approvalDisable = false;
              that.reasonDialogVisible = false;
              that.getCompanyChargeOrder();
            }, 2000);
          }
        })
        .catch(function(error) {});
    },
    //审核通过
    refundCompanyCharge() {
      let that = this;
      that.approvalDisable = true;
      that.$axios
        .post(
          "/CardCharge/refundCompanyCharge",
          {
            order_code: that.refundInfo.order_code,
            order_type: that.refundInfo.order_type,
            password: that.password
          },
          {
            timeout: 20000,
            headers: {
              "X-Requested-With": "XMLHttpRequest"
            }
          }
        )
        .then(function(res) {
          //列表数据不能及时刷新
          if (res.data.status == 200) {
            setTimeout(() => {
              that.approvalDisable = false;
              that.$message({
                message: "退款成功",
                type: "success"
              });
              that.dialogVisible = false;
              that.dialogVisible2 = false;
              that.getCompanyChargeOrder();
            }, 2000);
          } else {
            that.approvalDisable = false;
            that.$message({
              message: res.data.info,
              type: "error"
            });
          }
        })
        .catch(function(error) {
          that.approvalDisable = false;
          that.$message({
            message: "网络错误",
            type: "error"
          });
        });
    },
    //展示审核通过弹窗
    handleRefundClick(val) {
      this.password = "";
      this.refundInfo = val;
      let userInfo = JSON.parse(window.localStorage.__userInfo__);
      let new_group_id = userInfo.new_group_id;
      if (new_group_id == 1 || new_group_id == 426) {
        //判断是否是道达尔集团
        this.dialogVisible2 = true;
      } else {
        this.dialogVisible = true;
      }
    },
    //查看工作流明细
    handleGZLDetailClick(order_code) {
      this.GZLDetailDialogVisible = true;
      let that = this;
      that.approvalFlowTableData = [];
      that.$axios
        .post("/CardCharge/approvalFlow", {
          order_code: order_code
        })
        .then(function(res) {
          if (res.data.status == 200) {
            that.approvalFlowTableData = res.data.data;
          } else {
            that.$message({
              message: res.data.info,
              type: "error"
            });
          }
        })
        .catch(function(error) {});
    },

    //下载数据
    exportCompanyChargeOrders() {
      //如果是武汉道达尔，只能下载近60天数据
      if(!this.isTotalReportForm) {
        let today = this.$moment(new Date()).format("YYYY-MM-DD") + ' 23:59:59'
        let startDate = (this.$moment(today).valueOf() - 3600 * 1000 * 24 * 60) / 1000 + 1
        let search = this.$moment(this.searchDate[0]).valueOf() / 1000
        if(search < startDate) {
          return this.$message.error('只能下载最近60天的数据')
        }
      }
      this.$axios
        .post("/CardCharge/exportCompanyChargeOrders", {
          phone: this.searchOrderPhone,
          order_code: this.searchOrderNumber,
          vipcard_no: this.searchCardNumber,
          start_time: this.searchDate[0],
          end_time: this.searchDate[1],
          page: this.currentPage,
          page_size: this.pageSize,
          order_status: this.typeValue,
          station_id: this.stationId,
          mch_arr: this.userInfo.mch_arr
        })
        .then(res => {
          if (res.data.status == 200) {
            this.showDownloadTips = true;
          } else {
            this.$message.error(res.data.info);
          }
        });
    },

    //修改备注
    orderChange(item, index) {
      this.tableData[index].showRemark = false;
      let that = this;
      this.$axios
        .post("/CardCharge/orderChange", {
          code: item.order_code,
          remark: item.remark.replace(/(\r\n|\n|\r)/gm, "")
        })
        .then(function(res) {
          if (res.data.status == 200) {
            // that.getCompanyChargeOrder();
          } else {
            that.$message({
              message: res.data.info,
              type: "error"
            });
          }
        })
        .catch(function(error) {});
    },
    showRemarkInput(item) {
      this.$nextTick(() => {
        item.showRemark = true;
      });
    },

    //获取付款方式列表
    getPayMethod() {
      let that = this;
      this.$axios
        .post("CardCharge/getOrderPayType")
        .then(function(res) {
          if (res.data.status == 200) {
            // that.getCompanyChargeOrder();
            that.PayMethodsOptions = res.data.data;
          } else {
            that.$message({
              message: res.data.info,
              type: "error"
            });
          }
        })
        .catch(function(error) {
          console.log(error);
        });
    },
    // 查看手续费、结算金额
    async seeFee() {
      try {
        const res = await this.$axios.post(
          "CardCharge/companyChargeFeeSettleAmt"
        );
        // console.log('res',res);
        if (res.data.status == 200) {
          this.flag = 1;
        }
      } catch (error) {
        console.log(error);
      }
    }
  },
  watch: {
    getCurrentStation(newValue, oldValue) {
      if (newValue.merchant_type != 0 && newValue.value != oldValue.value) {
        this.getStationList() // 获取油站
        this.getCompanyChargeOrder();
                this.getPayMethod()
      }
    },
    searchDate() {
      if(
        this.$moment(this.searchDate[1]).format('YYYY-MM-DD') === this.$moment().format('YYYY-MM-DD') &&
        this.$moment(this.searchDate[1]).unix() > this.$moment().unix()
      ){
        this.searchDate[1] = this.$moment().format('YYYY-MM-DD HH:mm:ss')
      }
    },
    //监听班次切换变化，让多选单选重新渲染，防止报错
    typeClassValue() {
      this.update = false;
      setTimeout(() => {
        this.update = true;
      }, 0);
    }
  }
};
</script>
<style scoped>
.refund .header {
  padding: 20px 0;
}
.classType {
  margin-bottom: 20px;
}

.lookClass,
.checkClass {
  display: inline-block;
}
.Noclass {
  font-size: 14px;
  color: #606266;
}
.search-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
}
.refundData td:first-child,
.refundData th:first-child {
  padding-left: 18px;
}
.remark {
  display: flex;
}
/* 页码 */
.page_content {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}
.edit-icon {
  font-size: 20px;
}
.edit-icon:hover {
  color: #32af50;
  cursor: pointer;
}

.dialog .check-box {
  display: flex;
  justify-content: space-between;
}
.dialog .check-box .left {
  width: 55%;
}
.dialog .check-box .right {
  width: 45%;
}
.dialog .check-box p {
  text-align: left;
  margin-bottom: 20px;
  color: #333;
  display: flex;
}
.safe-dialog .tips-item {
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  padding: 8px 0;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
  background: #fae1d7;
  color: #fa5a00;
}
.tips {
  margin-top: 5px;
  display: flex;
}
.tips p {
  margin: 0;
}
</style>
<style>
.dialog .el-dialog__body {
  padding: 15px 30px;
}
.safe-dialog .el-dialog__body {
  padding: 15px 90px 60px;
  position: relative;
}
.dialog .el-textarea__inner {
  height: 80px;
}
</style>
