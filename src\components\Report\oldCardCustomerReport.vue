<template>
  <div class="report">
    <div class="report-content">
      <div class="checkbox-box">
        <span>勾选后生成报表</span>
        <el-checkbox class="checkAll" :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
        <el-checkbox-group v-model="checkedReport" @change="handleCheckedReportChange">
          <el-checkbox v-for="(item,index) in reports" :label="item.value" :key="index">{{item.name}}</el-checkbox>
        </el-checkbox-group>
      </div>
      <el-select v-model="stationValue" multiple collapse-tags style="width:220px" placeholder="请选择油站" @change="getCheckedStation">
        <el-option
          v-for="(item,index) in stationOptions"
          :key="index"
          :label="item.stname"
          :value="item.stid">
        </el-option>
      </el-select>
      <el-date-picker
        v-model="dateValue"
        :clearable="false"
        value-format="yyyy-MM-dd HH:mm:ss"
        type="datetimerange"
        range-separator="至"
        :default-time="['00:00:00', '23:59:59']"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="changeDateValue">
      </el-date-picker>
      <el-select v-model="companyValue" filterable style="width:220px" placeholder="请选择车队" clearable @change="changeCompanyValue">
        <el-option
          v-for="(item,index) in companyOptions"
          :key="index"
          :label="item.CompanyName"
          :value="item.ID">
        </el-option>
      </el-select>
      <div class="search-box">
        <span class="txt">查询类型</span>
        <el-radio-group v-model="searchTypeVlaue">
          <el-radio label="1">手机号</el-radio>
          <el-radio label="2">卡号</el-radio>
          <el-radio label="3">卡面卡号</el-radio>
        </el-radio-group>
        <el-input  v-model="inputTxt" style="width:210px" :placeholder="searchTypeVlaue=='0'?'请输入卡号':searchTypeVlaue=='1'?'请输入手机号':'请输入卡面卡号'" clearable></el-input>
      </div>

      <div class="content_header">
        <el-button type="primary" @click="createReport">生成</el-button>
        <el-button type="primary" :disabled="tableData.length == 0" @click="printContent">打印</el-button>
        <el-button type="primary" :disabled="tableData.length == 0" @click="cardChargeDownload" v-show="isTotalReportForm">下载数据</el-button>
      </div>

      <div id="myTable" >
        <div class="tableData reportData" v-loading="loading">
          <div class="report_title">储值卡客户资金对账汇总表</div>
          <div class="report_header">
            <div v-if="isGroup">集团名称：{{getCurrentStation.label}}</div>
            <div v-else>油站名称：{{getCurrentStation.label}}</div>
            <div v-if="companyValue">车队：{{companyName}}</div>
            <div v-if="inputTxt">客户：{{customerName}}</div>
            <div>开始日期：{{start_time}}</div>
            <div>结束日期：{{end_time}}</div>
            <div>单位：元</div>
          </div>
          <!-- 储值卡客户资金对账汇总表 -->
          <el-table :data="tableData" border size="small" align="center">
            <el-table-column align="center" label="期初余额">
              <template slot-scope="scope">{{Number(scope.row.StartBalance).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" label="期间充值">
              <el-table-column align="center" prop="RechargeNumber" label="充值笔数"> </el-table-column>
              <el-table-column align="center" label="充值金额">
                <template slot-scope="scope">{{Number(scope.row.RechargeAmount).toFixed(2)}}</template>
              </el-table-column>
              <el-table-column align="center" label="充值本金">
                <template slot-scope="scope">{{Number(scope.row.RechargeCapital).toFixed(2)}}</template>
              </el-table-column>
              <el-table-column align="center" label="充值赠金">
                <template slot-scope="scope">{{Number(scope.row.RechargeBouns).toFixed(2)}}</template>
              </el-table-column>
            </el-table-column>
            <el-table-column align="center" label="期间消费">
              <el-table-column align="center" prop="ConsumeNumber" label="消费笔数"> </el-table-column>
              <el-table-column align="center" label="消费金额">
                <template slot-scope="scope">{{Number(scope.row.ConsumeAmount).toFixed(2)}}</template>
              </el-table-column>
              <el-table-column align="center" label="消费本金">
                <template slot-scope="scope">{{Number(scope.row.ConsumeCapital).toFixed(2)}}</template>
              </el-table-column>
              <el-table-column align="center" label="消费赠金">
                <template slot-scope="scope">{{Number(scope.row.ConsumeBouns).toFixed(2)}}</template>
              </el-table-column>
            </el-table-column>
            <el-table-column align="center" label="期间退款">
              <el-table-column align="center" label="充值退款">
                <el-table-column align="center" prop="RechargeRefundNumber" label="充值退款笔数"></el-table-column>
                <el-table-column align="center" label="充值退款金额">
                  <template slot-scope="scope">{{Number(scope.row.RechargeRefundAmount).toFixed(2)}}</template>
                </el-table-column>
                <el-table-column align="center" label="充值退款赠金">
                  <template slot-scope="scope">{{Number(scope.row.RechargeRefundBouns).toFixed(2)}}</template>
                </el-table-column>
                <el-table-column align="center" label="实退金额">
                  <template slot-scope="scope">{{Number(scope.row.RechargeRefundCapital).toFixed(2)}}</template>
                </el-table-column>
              </el-table-column>
              <el-table-column align="center" prop="recharge_refund_order_cnt" label="消费退款">
                <el-table-column align="center" prop="ConsumeRefundNumber" label="消费退款笔数"></el-table-column>
                <el-table-column align="center" label="消费退款金额">
                  <template slot-scope="scope">{{Number(scope.row.ConsumeRefundAmount).toFixed(2)}}</template>
                </el-table-column>
                <el-table-column align="center" label="消费退款本金">
                  <template slot-scope="scope">{{Number(scope.row.ConsumeRefundCapital).toFixed(2)}}</template>
                </el-table-column>
                <el-table-column align="center" label="消费退款赠金">
                  <template slot-scope="scope">{{Number(scope.row.ConsumeRefundBouns).toFixed(2)}}</template>
                  <!-- <el-table-column align="center" label="0#柴油">
                    <template >{{ }}</template>
                  </el-table-column>
                  <el-table-column align="center" label="92#汽油">
                    <template >{{ }}</template>
                  </el-table-column>
                  <el-table-column align="center" label="95#汽油">
                    <template >{{ }}</template>
                  </el-table-column>
                  <el-table-column align="center" label="98#汽油">
                    <template >{{ }}</template>
                  </el-table-column> -->
                </el-table-column>
              </el-table-column>
            </el-table-column>
            <el-table-column align="center" label="期间资金分配">
              <el-table-column align="center" label="母账划拨">
                <template slot-scope="scope">{{Number(scope.row.DivedeAmount).toFixed(2)}}</template>
              </el-table-column>
              <el-table-column align="center" label="子卡卡账返款">
                <template slot-scope="scope">{{Number(scope.row.RebateAmount).toFixed(2)}}</template>
              </el-table-column>
            </el-table-column>
            <el-table-column align="center" label="余额清零">
              <el-table-column align="center" prop="ClearNumber" label="清零笔数"></el-table-column>
              <el-table-column align="center" label="清零金额">
                <template slot-scope="scope">{{Number(scope.row.ClearAmount).toFixed(2)}}</template>
              </el-table-column>
              <el-table-column align="center" label="清零本金">
                <template slot-scope="scope">{{Number(scope.row.ClearCapital).toFixed(2)}}</template>
              </el-table-column>
              <el-table-column align="center" label="清零赠金">
                <template slot-scope="scope">{{Number(scope.row.ClearBouns).toFixed(2)}}</template>
              </el-table-column>
            </el-table-column>
            <el-table-column align="center" label="期末余额">
              <template slot-scope="scope">{{Number(scope.row.EndBalance).toFixed(2)}}</template>
            </el-table-column>
          </el-table>

          <p v-if="stationValue.length != stationOptions.length && isGroup" class="stations"><span>*</span>取数油站：{{stationName}}</p>
          <p class="stations">注：客户资金变动计入储值卡开户油站</p>

          <div class="des_bottom">
            <div>制表人：{{orderMaker}}</div>
            <div>制表时间:{{orderMakingTime}}</div>
            <div>签字:</div>
          </div>

          <!-- 储值卡客户充值明细表 -->
          <div class="report_title" v-if="showTable01">客户充值明细表</div>
          <el-table :data="tableData1" border size="small" align="center" v-if="showTable01">
            <el-table-column align="center" prop="stname" label="充值油站"></el-table-column>
            <el-table-column align="center" label="卡号">
              <template slot-scope="scope">{{scope.row.card_no?scope.row.card_no:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="卡面卡号">
              <template slot-scope="scope">{{scope.row.card_number?scope.row.card_number:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="卡名称">
              <template slot-scope="scope">{{scope.row.card_name?scope.row.card_name:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="手机号">
              <template slot-scope="scope">{{scope.row.phone?scope.row.phone:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="车队名称">
              <template slot-scope="scope">{{scope.row.company_name?scope.row.company_name:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" prop="card_type" label="卡类型"></el-table-column>
            <el-table-column align="center" prop="account_type" label="到账类型"></el-table-column>
            <el-table-column align="center" prop="payment_name" label="付款方式"></el-table-column>
            <el-table-column align="center" label="付款金额">
              <template slot-scope="scope">{{Number(scope.row.pay_money).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" label="充值金额">
              <template slot-scope="scope">{{(Number(scope.row.recharge_money)+Number(scope.row.give_money)).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" label="充值本金">
              <template slot-scope="scope">{{Number(scope.row.recharge_money).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" label="赠送金额">
              <template slot-scope="scope">{{Number(scope.row.give_money).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" prop="platform" label="充值方式"></el-table-column>
            <el-table-column align="center" prop="pay_date" label="交易时间"></el-table-column>
          </el-table>
          <!-- 储值卡客户消费明细报表 -->
          <div class="report_title" v-if="showTable02">客户消费明细表</div>
          <el-table :data="tableData2" border size="small" align="center" v-if="showTable02">
            <el-table-column align="center" prop="stname" label="付款油站"></el-table-column>
            <el-table-column align="center" label="卡号">
              <template slot-scope="scope">{{scope.row.card_no?scope.row.card_no:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="卡面卡号">
              <template slot-scope="scope">{{scope.row.card_number?scope.row.card_number:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="卡名称">
              <template slot-scope="scope">{{scope.row.card_name?scope.row.card_name:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="车牌号">
              <template slot-scope="scope">{{scope.row.car_number?scope.row.car_number:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="手机号">
              <template slot-scope="scope">{{scope.row.phone?scope.row.phone:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="车队名称">
              <template slot-scope="scope">{{scope.row.company_name?scope.row.company_name:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" prop="goods_name" label="商品名称"></el-table-column>
            <el-table-column align="center" label="数量(升)">
              <template slot-scope="scope">{{Number(scope.row.goods_number).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" label="订单原价">
              <template slot-scope="scope">{{Number(scope.row.goods_amount).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" label="支付金额">
              <template slot-scope="scope">{{Number(scope.row.pay_money).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" label="支付本金">
              <template slot-scope="scope">{{Number(scope.row.account_money).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" label="支付赠金">
              <template slot-scope="scope">{{Number(scope.row.give_money).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" prop="pay_date" label="交易时间"></el-table-column>
          </el-table>
          <!-- 储值卡客户充值退款明细表 -->
          <div class="report_title" v-if="showTable03">客户充值退款明细表</div>
          <el-table :data="tableData3" border size="small" align="center" v-if="showTable03">
            <el-table-column align="center" label="卡号">
              <template slot-scope="scope">{{scope.row.card_no?scope.row.card_no:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="卡面卡号">
              <template slot-scope="scope">{{scope.row.card_number?scope.row.card_number:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="卡名称">
              <template slot-scope="scope">{{scope.row.card_name?scope.row.card_name:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="手机号">
              <template slot-scope="scope">{{scope.row.phone?scope.row.phone:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="车队名称">
              <template slot-scope="scope">{{scope.row.company_name?scope.row.company_name:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" prop="order_code" label="交易单号"></el-table-column>
            <el-table-column align="center" label="付款金额">
              <template slot-scope="scope">{{Number(scope.row.pay_money).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" prop="account_type" label="退款账户"></el-table-column>
            <el-table-column align="center" prop="payment_name" label="退款方式"></el-table-column>
            <el-table-column align="center" label="实退金额">
              <template slot-scope="scope">{{Number(scope.row.pay_money).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" label="退款金额">
              <template slot-scope="scope">{{Number(scope.row.recharge_money).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" label="退款赠金">
              <template slot-scope="scope">{{Number(scope.row.give_money).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" prop="trade_time" label="退款时间"></el-table-column>
          </el-table>
          <!-- 储值卡客户消费退款明细报表 -->
          <div class="report_title" v-if="showTable04">客户消费退款明细表</div>
          <el-table :data="tableData4" border size="small" align="center" v-if="showTable04">
            <el-table-column align="center" label="卡号">
              <template slot-scope="scope">{{scope.row.card_no?scope.row.card_no:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="卡面卡号">
              <template slot-scope="scope">{{scope.row.card_number?scope.row.card_number:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="卡名称">
              <template slot-scope="scope">{{scope.row.card_name?scope.row.card_name:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="车牌号">
              <template slot-scope="scope">{{scope.row.car_number?scope.row.car_number:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="手机号">
              <template slot-scope="scope">{{scope.row.phone?scope.row.phone:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="车队名称">
              <template slot-scope="scope">{{scope.row.company_name?scope.row.company_name:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" prop="order_code" label="交易单号"></el-table-column>
            <el-table-column align="center" prop="goods_name" label="商品名称"></el-table-column>
            <el-table-column align="center" label="数量(升)">
              <template slot-scope="scope">{{Number(scope.row.goods_number).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" label="订单原价">
              <template slot-scope="scope">{{Number(scope.row.goods_amount).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" label="付款金额">
              <template slot-scope="scope">{{Number(scope.row.pay_money).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" label="退回金额">
              <template slot-scope="scope">{{Number(scope.row.pay_money).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" label="退回本金">
              <template slot-scope="scope">{{Number(scope.row.account_money).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" label="退回赠金">
              <template slot-scope="scope">{{Number(scope.row.give_money).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" prop="trade_time" label="退款时间"></el-table-column>
          </el-table>
          <!-- 储值卡客户资金分配明细表 -->
          <div class="report_title" v-if="showTable05">客户资金分配明细表</div>
          <el-table :data="tableData5" border size="small" align="center" v-if="showTable05">
            <el-table-column align="center" prop="transfer_type" label="分配类型"></el-table-column>
            <el-table-column align="center" label="卡号">
              <template slot-scope="scope">{{scope.row.card_no?scope.row.card_no:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="卡面卡号">
              <template slot-scope="scope">{{scope.row.card_number?scope.row.card_number:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="卡名称">
              <template slot-scope="scope">{{scope.row.card_name?scope.row.card_name:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="手机号">
              <template slot-scope="scope">{{scope.row.phone?scope.row.phone:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" label="车队名称">
              <template slot-scope="scope">{{scope.row.company_name?scope.row.company_name:"-"}}</template>
            </el-table-column>
            <el-table-column align="center" prop="stname" label="操作油站"></el-table-column>
            <el-table-column align="center" prop="account_type" label="操作对象"></el-table-column>
            <el-table-column align="center" label="变动前金额">
              <template slot-scope="scope">{{Number(scope.row.change_front_balance).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" label="操作金额">
              <template slot-scope="scope">{{Number(scope.row.change_money).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" label="变动后金额">
              <template slot-scope="scope">{{Number(scope.row.change_after_balance).toFixed(2)}}</template>
            </el-table-column>
            <el-table-column align="center" prop="trade_time" label="操作时间"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 下载中心提示 -->
    <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>
  </div>
</template>

<script>
import DownloadTips from '../DownloadTips.vue';
import {mapGetters} from 'vuex'
import axiosReport from 'axios'
export default {
  name: 'oldCardCustomerReport',
  components:{
    DownloadTips
  },
  data () {
    return {
      isTotalReportForm: true,
      checkAll: false,
      checkedReport: [],//勾选选项
      reports: [
        {name:"客户充值明细表",value:1},
        {name:"客户消费明细表",value:2},
        {name:"客户充值退款明细表",value:3},
        {name:"客户消费退款明细表",value:4},
        {name:"客户资金分配明细表",value:5},
      ],//勾选选项
      isIndeterminate: false,

      stationOptions:[],//油站列表
      stationValue:[],//选中油站

      dateValue:[],//选中时间

      companyOptions:[],//车队列表
      companyValue:"",//选中车队

      searchTypeVlaue:"1",//查询类型
      inputTxt:"",//搜索内容

      companyName:"",//车队名称
      customerName:"",//客户名称
      start_time:"",//开始时间
      end_time:"",//结束时间
      stationName:"",//所选油站名称

      tableData: [],//表格数据
      tableData1: [],//表格数据
      tableData2: [],//表格数据
      tableData3: [],//表格数据
      tableData4: [],//表格数据
      tableData5: [],//表格数据
      isGroup:true,//是否是集团账号
      showTable01:false,
      showTable02:false,
      showTable03:false,
      showTable04:false,
      showTable05:false,
      loading: false,//loading

      orderMaker:"",//制表人
      orderMakingTime:"",//制表时间

      params:{
        type:'',
        start_time:'',
        end_time:'',
        station_ids:[],
        company_id:'',
        query_type:'',
        content:'',
      },
      showDownloadTips:false
    }
  },
  mounted(){
    let userInfo = localStorage.getItem("__userInfo__") || "";
    this.isTotalReportForm = JSON.parse(userInfo).group_id != 426;
    if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
      return false;
    }
    if(this.getCurrentStation && this.getCurrentStation.merchant_type == 2){
      this.isGroup = true;
    }else{
      this.isGroup = false;
    }
    this.getStations();
    this.getCompanyList();
  },
  computed:{
    ...mapGetters({
      "getCurrentStation":"getCurrentStation"
    })
  },
  methods: {
    //全选
    handleCheckAllChange(val) {
      let allReport = [];
      this.reports.forEach((item)=>{
        allReport.push(item.value)
      })
      this.checkedReport = val ? allReport : [];
      this.isIndeterminate = false;
    },
    //多选
    handleCheckedReportChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.reports.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.reports.length;
    },

    //获取油站列表
    getStations(){
      let that = this;
      that.stationValue = [];
      that.$axios.post('/Stations/getStations',{}).then((res)=>{
        if(res.data.status == 200){
          that.stationOptions = res.data.data.station_info;
          that.stationOptions.forEach((item)=>{
            that.stationValue.push(item.stid);
          })
        }else{
          that.$message({
            message: res.data.info,
            type: 'error'
          });
        }
      })
    },

    //获取车队名称
    getCompanyName(val){
      this.companyOptions.forEach(item=>{
        if(val == item.ID){
          this.companyName = item.CompanyName;
        }
      })

    },

    //获取车队信息列表
    getCompanyList(){
      let that = this;
      that.$axios.post('/CompanyCard/getSimpleCompanyList', {
        page: 1,
        page_size: 800,
        input: "",
      })
        .then(function (res) {
          if(res.data.status == 200){
            that.companyOptions = res.data.data.dt;
            //默认选择数组第一个
            // if(that.companyOptions.length>0){
            //     that.companyValue = that.companyOptions[0].ID;
            // }
          }else{
            that.$message({
              message: res.data.info,
              type: 'error'
            });
          }
        })
    },

    //获取选择的油站名称
    getCheckedStation(){
      this.stationName = "";
      let len = this.stationValue.length;
      this.stationValue.forEach((item,index)=>{
        this.stationOptions.forEach((subitem)=>{
          if(subitem.stid == item){
            if(index == len-1){
              this.stationName += subitem.stname;

            }else{
              this.stationName += subitem.stname + "，";
            }
          }
        })
      })
    },

    //控制时间控件只能选择31天以内的日期
    changeDateValue(val){
      if(this.dateValue){
        let startTime = this.$moment(this.dateValue[0], 'YYYY-MM-DD HH:mm:ss').unix();
        let endTime = this.$moment(this.dateValue[1], 'YYYY-MM-DD HH:mm:ss').unix();
        if((endTime-startTime)/(60*60*24) > 31){
          this.$message({
            message: "只能获取一个月内的数据",
            type: 'warning'
          });
          this.dateValue[1] = this.$moment((startTime + (60*60*24)*31)*1000).format('YYYY-MM-DD HH:mm:ss');
        }
      }
    },

    //改变车队时，把表格车队名称设置为空
    changeCompanyValue(){
      this.companyName = "";
    },

    //生成报表
    async createReport(){
      if(this.stationValue.length == 0){
        this.$message({
          message: "请选择油站",
          type: 'error'
        });
        return;
      }
      if(!this.dateValue || this.dateValue.length == 0){
        this.$message({
          message: "请选择时间",
          type: 'error'
        });
        return;
      }
      if(!this.companyValue && !this.inputTxt){
        this.$message({
          message: "请选择车队或输入查询类型",
          type: 'error'
        });
        return;
      }

      this.loading = true;

      this.params.type = 6;
      this.params.start_time = this.dateValue[0];
      this.params.end_time = this.dateValue[1];
      this.params.station_ids = this.stationValue;
      this.params.company_id = this.companyValue ? this.companyValue : 0;
      this.params.query_type = Number(this.searchTypeVlaue);
      this.params.content = this.inputTxt;

      let total = await this.getCapitalDetails();
      let totalArr = [];
      if(total.data.status == 200){
        totalArr.push(total.data.data);
        this.tableData = totalArr;
      }else{
        this.$message({
          message: total.data.info,
          type: 'error'
        });
      }

      if(this.checkedReport.includes(1)){
        this.params.type = 1;
        let res = await this.getCapitalDetails();
        if(res.data.status == 200){
          //按时间排序
          let data = res.data.data.list.sort(function(a,b){
            return a.pay_date > b.pay_date ? 1 : -1
          });
          this.tableData1 = data;
        }else{
          this.$message({
            message: res.data.info,
            type: 'error'
          });
        }
      }
      if(this.checkedReport.includes(2)){
        this.params.type = 2;
        let res = await this.getCapitalDetails();
        if(res.data.status == 200){
          //按时间排序
          let data = res.data.data.list.sort(function(a,b){
            return a.pay_date > b.pay_date ? 1 : -1
          });
          this.tableData2 = data;
        }else{
          this.$message({
            message: res.data.info,
            type: 'error'
          });
        }
      }
      if(this.checkedReport.includes(3)){
        this.params.type = 3;
        let res = await this.getCapitalDetails();
        if(res.data.status == 200){
          //按时间排序
          let data = res.data.data.list.sort(function(a,b){
            return a.trade_time > b.trade_time ? 1 : -1
          });
          this.tableData3 = data;
        }else{
          this.$message({
            message: res.data.info,
            type: 'error'
          });
        }
      }
      if(this.checkedReport.includes(4)){
        this.params.type = 4;
        let res = await this.getCapitalDetails();
        if(res.data.status == 200){
          //按时间排序
          let data = res.data.data.list.sort(function(a,b){
            return a.trade_time > b.trade_time ? 1 : -1
          });
          this.tableData4 = data;
        }else{
          this.$message({
            message: res.data.info,
            type: 'error'
          });
        }
      }
      if(this.checkedReport.includes(5)){
        this.params.type = 5;
        let res = await this.getCapitalDetails();
        if(res.data.status == 200){
          //按时间排序
          let data = res.data.data.list.sort(function(a,b){
            return a.trade_time > b.trade_time ? 1 : -1
          });
          this.tableData5 = data;
        }else{
          this.$message({
            message: res.data.info,
            type: 'error'
          });
        }
      }
      this.loading = false;
      this.getCompanyName(this.companyValue);
      this.customerName = this.inputTxt;
      this.start_time = this.dateValue[0];
      this.end_time = this.dateValue[1];
      let userInfo = localStorage.getItem('__userInfo__');
      if(userInfo && (userInfo !== "" || userInfo !== "undefined")){
        this.orderMaker = JSON.parse(userInfo).name;
      }
      this.orderMakingTime = this.$moment().format("YYYY-MM-DD");
    },

    //生成明细表函数
    getCapitalDetails(){
      let fundSummaryAxios = axiosReport.create();
      return new Promise((resolve, reject)=>{
        fundSummaryAxios.post('/CardReportForm/getCapitalDetails', this.params, {timeout:30000}).then(function (res) {
          resolve(res);
        }).catch((err)=>{
          resolve(err);
        })
      })
    },

    //打印
    printContent(){
      let wpt = document.querySelector('#myTable');
      let newContent = wpt.innerHTML;
      let oldContent = document.body.innerHTML;
      document.body.innerHTML = newContent;
      let headerDom = document.getElementsByClassName("el-table__header");
      let bodyDom = document.getElementsByClassName("el-table__body");
      let wrapperDom = document.getElementsByClassName("el-table__body-wrapper");
      let emptyDom = document.getElementsByClassName("el-table__empty-block");
      for(let i=0;i<headerDom.length;i++){
        headerDom[i].style.width = "100%";
        headerDom[i].style["table-layout"] = "auto";
      }
      for(let i=0;i<bodyDom.length;i++){
        bodyDom[i].style.width = "100%";
        bodyDom[i].style["table-layout"] = "auto";
      }
      for(let i=0;i<wrapperDom.length;i++){
        wrapperDom[i].style["overflow-x"] = "hidden";
      }
      for(let i=0;i<emptyDom.length;i++){
        if(emptyDom[i]){
          emptyDom[i].style.width = "100%";
        }
      }
      window.print(); //打印方法
      history.go(0)
      document.body.innerHTML = oldContent;
    },
    //下载数据
    async cardChargeDownload(){
      this.params.type = this.checkedReport;
      await this.exportCapitalDetails();
      // if(this.tableData.length>0){
      //     this.params.type = 6;
      //     await this.exportCapitalDetails();
      // }
      // if(this.tableData1.length>0){
      //     this.params.type = 1;
      //     await this.exportCapitalDetails();
      // }
      // if(this.tableData2.length>0){
      //     this.params.type = 2;
      //     await this.exportCapitalDetails();
      // }
      // if(this.tableData3.length>0){
      //     this.params.type = 3;
      //     await this.exportCapitalDetails();
      // }
      // if(this.tableData4.length>0){
      //     this.params.type = 4;
      //     await this.exportCapitalDetails();
      // }
      // if(this.tableData5.length>0){
      //     this.params.type = 5;
      //     await this.exportCapitalDetails();
      // }
      this.showDownloadTips = true;
    },

    exportCapitalDetails(){
      let that = this;
      return new Promise((resolve, reject)=>{
        this.$axios.post('/CardReport/newCapitalDetailsDownload', this.params).then(function (res) {
          if(res.data.status == 200){
            resolve(res);
          }else{
            that.$message({
              message: res.data.info,
              type: 'error'
            });
            reject(res);
            return;
          }
        }).catch((err)=>{
          resolve(err);
        })
      })
    },

  },
  watch: {
    getCurrentStation(newValue,oldValue){
      if(newValue.merchant_type != 0 && newValue.value != oldValue.value){
        if(this.getCurrentStation && this.getCurrentStation.merchant_type == 2){
          this.isGroup = true;
        }else{
          this.isGroup = false;
        }
        this.getStations();
        this.getCompanyList();
        this.$parent.getOldReport()
      }
    },
    checkedReport(val){
      if(val.includes(1)){
        this.showTable01 = true;
      }else{
        this.showTable01 = false;
      }
      if(val.includes(2)){
        this.showTable02 = true;
      }else{
        this.showTable02 = false;
      }
      if(val.includes(3)){
        this.showTable03 = true;
      }else{
        this.showTable03 = false;
      }
      if(val.includes(4)){
        this.showTable04 = true;
      }else{
        this.showTable04 = false;
      }
      if(val.includes(5)){
        this.showTable05 = true;
      }else{
        this.showTable05 = false;
      }
    },
  },
}
</script>

<style scoped>
.report {
  position: relative;
  height: 100%;
  background:rgba(245,245,245,1);
  margin: 0px auto;
  font-size: 14px;
}
.report-content{
  background: #fff;
  padding:20px 0;

}
.report .checkbox-box{
  display: flex;
  align-content: center;
  margin-bottom: 20px;
}
.report .checkbox-box .checkAll{
  margin-right: 30px;
  margin-left: 30px;
}
.report .search-box{
  margin-top: 20px;
}
.report .content_header {
  margin-top: 20px;
}
.tableData{
  text-align: center;
}
.tableData .report_title {
  font-size: 24px;
  font-weight: bolder;
  margin: 40px auto 20px;
}
.tableData .report_header {
  display: flex;
  margin:10px 20px;
  font-size: 16px;
}
.tableData .report_header div{
  min-width: 100px;
  text-align: left;
  margin-right: 40px;
}
.tableData .header_table {
  width: 100%;
  border-right:1px solid #EBEEF5;
  border-bottom:1px solid #EBEEF5;
  margin-top: 20px;
}
.tableData .header_table td {
  border-left:1px solid #EBEEF5;
  border-top:1px solid #EBEEF5;
}
.tableData .header_table_row {
  height: 40px;
}
.tableData .table_des {
  margin-top: 40px;
  margin-bottom: 20px;
}
.tableData .table_des_text {
  text-align: left;
}
.stations{
  text-align: left;
}
.stations span{
  color: #32AF50;
  margin-right: 10px;
}
.des_bottom {
  display: flex;
  justify-content: flex-end;
  font-size: 14px;
  font-weight: 500;
  margin-top: 10px;
}
.des_bottom div{
  padding-right: 100px;
}
</style>
<style>
.reportData td:first-child, .reportData th:first-child{
  padding-left: 0 !important;
}
</style>
