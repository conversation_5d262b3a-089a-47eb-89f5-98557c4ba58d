webpackJsonp([23],{CP2S:function(e,t){},H2je:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a("//Fk"),o=a.n(n),l=a("Xxa5"),r=a.n(l),s=a("exGp"),i=a.n(s),u=a("Dd8w"),c=a.n(u),d=a("mtWM"),_=a.n(d),m=a("FZmr"),p=a("NYxO"),b={name:"CardCapitalDayReport",components:{DownloadTips:m.a},data:function(){return{isTotalReportForm:!0,reportType:"1",stationOptions:[],stationValue:[],dateDayValue:[],dateMonthValue:[],stationName:"",start_time:"",end_time:"",DailyTableData:[],isGroup:!0,loading:!0,orderMaker:"",orderMakingTime:"",params:{},isOnlyOneStation:!1,showDownloadTips:!1,is_bp:!1,isOilRefundBounsListNotNull:!1,isOilBounsListNotNull:!1,oilBounsList:[],oilRefundBounsList:[]}},mounted:function(){var e=localStorage.getItem("__userInfo__")||"";this.isTotalReportForm=426!=JSON.parse(e).group_id;var t=this.$moment().subtract(1,"days").format("YYYY-MM-DD");this.dateDayValue.push(t),this.dateDayValue.push(t);var a=this.$moment().month(this.$moment().month()-1).startOf("month").valueOf(),n=this.$moment().month(this.$moment().month()-1).endOf("month").valueOf();if(this.dateMonthValue.push(this.$moment(a).format("YYYY-MM-DD")),this.dateMonthValue.push(this.$moment(n).format("YYYY-MM-DD")),void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.getStations(),this.getCurrentStation&&2==this.getCurrentStation.merchant_type?this.isGroup=!0:this.isGroup=!1},computed:c()({},Object(p.c)({getCurrentStation:"getCurrentStation"})),methods:{changeReportType:function(){this.createReport()},getStations:function(){var e=this;this.stationValue=[],this.$axios.post("/Stations/getStations",{}).then(function(t){200==t.status&&(e.stationOptions=t.data.data.station_info,e.stationOptions.forEach(function(t){e.stationValue.push(t.stid)}),e.createReport())})},getCheckedStation:function(){var e=this;this.stationName="";var t=this.stationValue.length;this.stationValue.forEach(function(a,n){e.stationOptions.forEach(function(o){o.stid==a&&(e.stationName+=n==t-1?o.stname:o.stname+"，")})})},createReport:function(){var e=this;return i()(r.a.mark(function t(){var a,n,o,l,s;return r.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(e.oilRefundBounsList=[],e.oilBounsList=[],e.isOilRefundBounsListNotNull=!1,e.isOilBounsListNotNull=!1,e.loading=!0,e.params.datetype=e.reportType,e.params.type=1,0!=e.stationValue.length){t.next=10;break}return e.$message({message:"请选择油站",type:"error"}),t.abrupt("return");case 10:return a=e.$moment(e.dateMonthValue[1]).endOf("month").format("YYYY-MM-DD"),1==e.reportType?(e.params.start_time=e.dateDayValue[0]+" 00:00:00",e.params.end_time=e.dateDayValue[1]+" 23:59:59"):(e.params.start_time=e.dateMonthValue[0]+" 00:00:00",e.params.end_time=a+" 23:59:59"),e.params.station_ids=e.stationValue,t.next=15,e.getFundSummary();case 15:n=t.sent,e.loading=!1,200==n.data.status?(o=n.data.data.sort(function(e,t){return e.Date>t.Date?1:-1}),e.DailyTableData=o,o[0]&&(o[0].consume_bouns_oil_info&&(e.oilBounsList=o[0].consume_bouns_oil_info),o[0].consume_refund_bouns_oil_info&&(e.oilRefundBounsList=o[0].consume_refund_bouns_oil_info)),1374==(l=JSON.parse(window.localStorage.getItem("__userInfo__"))).new_group_id||1==l.new_group_id||1==l.isPlatform?(e.is_bp=!0,e.oilBounsList.length>0&&(e.isOilBounsListNotNull=!0),e.oilRefundBounsList.length>0&&(e.isOilRefundBounsListNotNull=!0)):e.is_bp=!1,1==e.reportType?(e.start_time=e.dateDayValue[0],e.end_time=e.dateDayValue[1]):(e.start_time=e.dateMonthValue[0],e.end_time=a),!(s=localStorage.getItem("__userInfo__"))||""===s&&"undefined"===s||(e.orderMaker=JSON.parse(s).name),e.orderMakingTime=e.$moment().format("YYYY-MM-DD")):(e.DailyTableData=[],e.$message({message:n.data.info,type:"error"})),e.$forceUpdate();case 19:case"end":return t.stop()}},t,e)}))()},getFundSummary:function(){var e=this,t=_.a.create();return new o.a(function(a,n){t.post("/CardReportForm/getFundSummary",e.params,{timeout:3e4}).then(function(e){a(e)}).catch(function(e){a(e)})})},printContent:function(){var e=document.querySelector("#myTable").innerHTML,t=document.body.innerHTML;document.body.innerHTML=e,document.getElementsByClassName("el-table__header")[0].style.width="100%",document.getElementsByClassName("el-table__header")[0].style["table-layout"]="auto",document.getElementsByClassName("el-table__body")[0].style.width="100%",document.getElementsByClassName("el-table__body")[0].style["table-layout"]="auto",window.print(),history.go(0),document.body.innerHTML=t},cardChargeDownload:function(){var e=this;this.$axios.post("/CardReportForm/exportFundSummary",this.params).then(function(t){200==t.data.status?e.showDownloadTips=!0:e.$message.error(t.data.info)})}},watch:{getCurrentStation:function(e,t){0!=e.merchant_type&&e.value!=t.value&&(this.getStations(),this.getCurrentStation&&2==this.getCurrentStation.merchant_type?this.isGroup=!0:this.isGroup=!1)}}},f={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"report"},[a("div",{staticClass:"report-content"},[a("el-radio-group",{on:{change:e.createReport},model:{value:e.reportType,callback:function(t){e.reportType=t},expression:"reportType"}},[a("el-radio-button",{attrs:{label:"1"}},[e._v("日报表")]),e._v(" "),a("el-radio-button",{attrs:{label:"2"}},[e._v("月报表")])],1),e._v(" "),a("div",{staticClass:"choice-box"},[a("div",{staticClass:"left"},[a("el-select",{staticStyle:{width:"250px","margin-right":"20px"},attrs:{multiple:"","collapse-tags":"",placeholder:"请选择油站"},on:{change:e.getCheckedStation},model:{value:e.stationValue,callback:function(t){e.stationValue=t},expression:"stationValue"}},e._l(e.stationOptions,function(e){return a("el-option",{key:e.stid,attrs:{label:e.stname,value:e.stid}})}),1),e._v(" "),a("el-date-picker",{directives:[{name:"show",rawName:"v-show",value:1==e.reportType,expression:"reportType==1"}],attrs:{clearable:!1,type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd"},model:{value:e.dateDayValue,callback:function(t){e.dateDayValue=t},expression:"dateDayValue"}}),e._v(" "),a("el-date-picker",{directives:[{name:"show",rawName:"v-show",value:2==e.reportType,expression:"reportType==2"}],attrs:{clearable:!1,type:"monthrange","range-separator":"至","start-placeholder":"开始月份","end-placeholder":"结束月份","default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd"},model:{value:e.dateMonthValue,callback:function(t){e.dateMonthValue=t},expression:"dateMonthValue"}}),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.createReport}},[e._v("生成")])],1),e._v(" "),a("div",{staticClass:"right"},[a("el-button",{attrs:{type:"primary",disabled:0==e.DailyTableData.length},on:{click:e.printContent}},[e._v("打印")]),e._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:e.isTotalReportForm,expression:"isTotalReportForm"}],attrs:{type:"primary",disabled:0==e.DailyTableData.length},on:{click:e.cardChargeDownload}},[e._v("下载数据")])],1)]),e._v(" "),a("div",{attrs:{id:"myTable"}},[a("div",{staticClass:"tableData reportData"},[a("div",{directives:[{name:"show",rawName:"v-show",value:1==e.reportType,expression:"reportType==1"}],staticClass:"report_title"},[e._v("储值卡资金对账日汇总表")]),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:2==e.reportType,expression:"reportType==2"}],staticClass:"report_title"},[e._v("储值卡资金对账月汇总表")]),e._v(" "),a("div",{staticClass:"report_header"},[e.isGroup?a("div",[e._v("集团名称："+e._s(e.getCurrentStation.label))]):a("div",[e._v("油站名称："+e._s(e.getCurrentStation.label))]),e._v(" "),a("div",[e._v("开始日期："+e._s(e.start_time))]),e._v(" "),a("div",[e._v("结束日期："+e._s(e.end_time))]),e._v(" "),a("div",[e._v("单位：元")])]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.DailyTableData,border:"",size:"small",align:"center"}},[a("el-table-column",{attrs:{align:"center",prop:"date",label:"日期",width:"100"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"期间充值"}},[a("el-table-column",{attrs:{align:"center",prop:"recharge_number",label:"充值笔数"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.recharge_amount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.recharge_capital).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.recharge_bouns).toFixed(2)))]}}])})],1),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"期间消费"}},[a("el-table-column",{attrs:{align:"center",prop:"consume_number",label:"消费笔数"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.consume_amount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.consume_capital).toFixed(2)))]}}])}),e._v(" "),e.is_bp&&e.isOilBounsListNotNull?a("el-table-column",{attrs:{align:"center",label:"消费赠金"}},[e._l(e.oilBounsList,function(t){return a("el-table-column",{key:t.index,attrs:{align:"center",label:t.oil_name,prop:t.prop,width:"120"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v(e._s(Number(a.row[t.prop]).toFixed(2)))]}}],null,!0)})}),e._v(" "),e.is_bp&&e.isOilBounsListNotNull?a("el-table-column",{attrs:{align:"center",label:"合计"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.consume_oil_total).toFixed(2)))]}}],null,!1,2399596939)}):e._e()],2):a("el-table-column",{attrs:{align:"center",label:"消费赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.consume_bouns).toFixed(2)))]}}])})],1),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"期间退款"}},[a("el-table-column",{attrs:{align:"center",label:"充值退款"}},[a("el-table-column",{attrs:{align:"center",prop:"recharge_refund_number",label:"充值退款笔数",width:"100"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值退款金额",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.recharge_refund_amount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值退款赠金",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.recharge_refund_bouns).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"实退金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.recharge_refund_capital).toFixed(2)))]}}])})],1),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费退款"}},[a("el-table-column",{attrs:{align:"center",prop:"consume_refund_number",label:"消费退款笔数",width:"100"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费退款金额",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.consume_refund_amount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费退款本金",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.consume_refund_capital).toFixed(2)))]}}])}),e._v(" "),e.is_bp&&e.isOilRefundBounsListNotNull?a("el-table-column",{attrs:{align:"center",label:"消费退款赠金"}},[e._l(e.oilRefundBounsList,function(t){return a("el-table-column",{key:t.index,attrs:{align:"center",label:t.oil_name,prop:t.prop,width:"120"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v(e._s(Number(a.row[t.prop]).toFixed(2)))]}}],null,!0)})}),e._v(" "),e.is_bp&&e.isOilRefundBounsListNotNull?a("el-table-column",{attrs:{align:"center",label:"合计"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.consume_refund_oil_total).toFixed(2)))]}}],null,!1,2586563194)}):e._e()],2):a("el-table-column",{attrs:{align:"center",label:"消费退款赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.consume_refund_bouns).toFixed(2)))]}}])})],1)],1),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"余额清零"}},[a("el-table-column",{attrs:{align:"center",prop:"clear_number",label:"清零笔数"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"清零金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.clear_amount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"清零本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.clear_capital_amount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"清零赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.clear_bouns_amount).toFixed(2)))]}}])})],1)],1)],1),e._v(" "),a("div",{staticClass:"table_des"},[a("div",{staticClass:"table_des_text"},[a("p",[e._v("注：")]),e._v(" "),a("div",[1==e.reportType?a("p",[e._v("1、统计油站每日产生的储值卡数据，含跨站消费、充值、退款数据。")]):e._e(),e._v(" "),2==e.reportType?a("p",[e._v("1、统计油站每月产生的储值卡数据，含跨站消费、充值、退款数据。")]):e._e(),e._v(" "),e.stationValue.length!=e.stationOptions.length&&e.isGroup?a("p",{staticClass:"stations"},[e._v("2、取数油站："+e._s(e.stationName))]):e._e()])])]),e._v(" "),a("div",{staticClass:"des_bottom"},[a("div",[e._v("制表人："+e._s(e.orderMaker))]),e._v(" "),a("div",[e._v("制表时间："+e._s(e.orderMakingTime))]),e._v(" "),a("div",[e._v("签字：")])])])],1),e._v(" "),a("download-tips",{attrs:{showDownloadTips:e.showDownloadTips},on:{"update:showDownloadTips":function(t){e.showDownloadTips=t},"update:show-download-tips":function(t){e.showDownloadTips=t}}})],1)},staticRenderFns:[]};var v=a("VU/8")(b,f,!1,function(e){a("PYTA"),a("CP2S")},"data-v-6f74f2f3",null);t.default=v.exports},PYTA:function(e,t){}});