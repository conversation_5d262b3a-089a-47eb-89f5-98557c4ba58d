webpackJsonp([7],{TmV0:function(t,e,a){a("fZOM"),t.exports=a("FeBl").Object.values},cMhO:function(t,e){},fZOM:function(t,e,a){var o=a("kM2E"),r=a("mbce")(!1);o(o.S,"Object",{values:function(t){return r(t)}})},gRE1:function(t,e,a){t.exports={default:a("TmV0"),__esModule:!0}},mbce:function(t,e,a){var o=a("+E39"),r=a("lktj"),n=a("TcQ7"),l=a("NpIQ").f;t.exports=function(t){return function(e){for(var a,i=n(e),s=r(i),u=s.length,c=0,d=[];u>c;)a=s[c++],o&&!l.call(i,a)||d.push(t?[a,i[a]]:i[a]);return d}}},yMQS:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=a("fZjL"),r=a.n(o),n=a("woOf"),l=a.n(n),i=a("Xxa5"),s=a.n(i),u=a("exGp"),c=a.n(u),d=a("Dd8w"),p=a.n(d),m=a("gRE1"),g=a.n(m),v=a("M4fF"),f=a("NYxO"),b=a("3pLw"),h=a("mw3O"),_=a.n(h),y=a("bOzO"),S={PENDING:{value:0,label:"审核中",optionLabel:"待审核"},APPROVED:{value:1,label:"审核通过",optionLabel:"已审核"},REJECTED:{value:2,label:"审核驳回"},SUCCESS:{value:3,label:"审核通过"},FAILED:{value:4,label:"审核通过-充值失败"}},C=g()(S).filter(function(t){return t.optionLabel}),w={name:"RechargeApproval",props:{hideStatusOptions:{type:Boolean,default:!1},includeSubmitterID:{type:Boolean,default:!1}},data:function(){var t=this;return{STATUS:S,STATUS_OPTIONS:C,status:S.PENDING.value,dataList:[],stationId:[],dateRange:[],companyId:[],stationLoading:!1,companyLoading:!1,stationOptions:[],companyOptions:[],pickerOptions:{disabledDate:function(e){return e>t.$moment().endOf("day").toDate()},onPick:function(e){var a=e.maxDate,o=e.minDate;t.pickerMinDate=o,t.pickerMaxDate=a},shortcuts:[{text:"最近一周",onClick:function(e){var a=t.$moment().toDate(),o=t.$moment().subtract(7,"days").toDate();e.$emit("pick",[o,a])}},{text:"最近一个月",onClick:function(e){var a=t.$moment().toDate(),o=t.$moment().subtract(1,"months").toDate();e.$emit("pick",[o,a])}},{text:"最近三个月",onClick:function(e){var a=t.$moment().toDate(),o=t.$moment().subtract(3,"months").toDate();e.$emit("pick",[o,a])}}]},pickerMinDate:null,pickerMaxDate:null,query:{page:1,size:10,total:0},loading:!1,dialogState:{visible:!1,actionType:null,bonusLoading:!1,data:{id:"",CompanyName:"",CompanyContacts:"",ContactsPhone:"",BJ_MZ:"",ChargeOilName:"",ChargeAmount:"",ChargeLiter:"",BonusAmount:"",Submitter:"",SubmitTime:"",StationName:"",remark:"",CardLockType:0}},security:{verifyDialogVisible:!1,password:"",loading:!1},workflowDetailDialog:{visible:!1,data:[],loading:!1,columns:[{prop:"OperateTime",label:"操作时间"},{prop:"OperatorName",label:"操作人员"},{prop:"StatusChange",label:"状态变化"},{prop:"Remark",label:"备注"}]},FIXED_PRICE_MODE:y.a}},computed:p()({},Object(f.d)(["isGroup"]),Object(f.c)(["getCurrentStation"]),{currentFixPriceMode:function(){return String((this.dialogState.data?this.dialogState.data.CardLockType:"")||this.FIXED_PRICE_MODE.No)}}),created:function(){this.debounceRemoteMethod=Object(v.debounce)(this.remoteMethod,300),this.hideStatusOptions&&(this.status=this.STATUS.REJECTED.value)},mounted:function(){this.getStationOptions(),this.handleSearch()},methods:{getStationOptions:function(){var t=this;return c()(s.a.mark(function e(){var a;return s.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.stationLoading){e.next=2;break}return e.abrupt("return");case 2:return t.stationLoading=!0,e.prev=3,e.next=6,t.$axios.post("/Stations/getStationList",{});case 6:200===(a=e.sent).status?t.stationOptions=a.data.data.map(function(t){return{stid:t.stid,stname:t.stname}}):t.$message.error(a.data.info||"获取油站列表失败"),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(3),console.error("获取油站列表失败:",e.t0),t.$message.error("获取油站列表失败,请重试");case 14:return e.prev=14,t.stationLoading=!1,e.finish(14);case 17:case"end":return e.stop()}},e,t,[[3,10,14,17]])}))()},handleStatusChange:function(){this.query.page=1,this.handleSearch()},handleSearch:function(){var t=this;return c()(s.a.mark(function e(){var a,o,r,n,l;return s.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t.loading=!0,e.prev=1,a={Page:t.query.page,PageSize:t.query.size,StationNoList:2==t.getCurrentStation.merchant_type?t.stationId&&t.stationId.length?t.stationId.join(","):void 0:t.getCurrentStation.merchant_id,CompanyIdList:t.companyId.join(","),QueryType:t.status},t.includeSubmitterID&&(o=JSON.parse(localStorage.getItem("__userInfo__")),a.SubmitterID=o&&o.adid?o.adid:""),t.dateRange&&2===t.dateRange.length&&(a.StartTime=t.$moment(t.dateRange[0]).format("YYYY-MM-DD 00:00:00"),a.EndTime=t.$moment(t.dateRange[1]).format("YYYY-MM-DD 23:59:59")),r=_.a.stringify(a,{arrayFormat:"repeat"}),e.next=8,Object(b.b)("/CompanyCard/getApplyRechargeList?"+r);case 8:n=e.sent,l=n.data,console.log("🚀 ~ file: RechargeApproval.vue:400 ~ handleSearch ~ res:",l,n),200===n.status?(t.dataList=l.list,t.query.total=l.page_info.TotalQty):t.$message.error(n.info||"获取数据失败"),e.next=18;break;case 14:e.prev=14,e.t0=e.catch(1),console.error("获取数据失败:",e.t0),t.$message.error("获取数据失败，请重试");case 18:return e.prev=18,t.loading=!1,e.finish(18);case 21:case"end":return e.stop()}},e,t,[[1,14,18,21]])}))()},remoteMethod:function(t){var e=this;return c()(s.a.mark(function a(){var o,r;return s.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(t){a.next=3;break}return e.companyOptions=[],a.abrupt("return");case 3:return e.companyLoading=!0,a.prev=4,a.next=7,e.$axios.post("/CompanyCard/getCompanyNameList",{input:t});case 7:200===(o=a.sent).data.status?(r=JSON.parse(o.data.data),e.companyOptions=r.map(function(t){return{CompanyID:t.CompanyID,CompanyName:t.CompanyName}})):e.$message.error(o.data.info||"搜索车队失败"),a.next=15;break;case 11:a.prev=11,a.t0=a.catch(4),console.error("搜索车队失败:",a.t0),e.$message.error("搜索车队失败,请重试");case 15:return a.prev=15,e.companyLoading=!1,a.finish(15);case 18:case"end":return a.stop()}},a,e,[[4,11,15,18]])}))()},handleSizeChange:function(t){this.query.size=t,this.query.page=1,this.handleSearch()},handleCurrentChange:function(t){this.query.page=t,this.handleSearch()},showWorkflowDetail:function(t){var e=this;return c()(s.a.mark(function a(){var o;return s.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return console.log("🚀 ~ showWorkflowDetail ~ row:",t),e.workflowDetailDialog.visible=!0,e.workflowDetailDialog.loading=!0,a.prev=3,a.next=6,e.$axios.get("/CompanyCard/getApplyRechargeInfo",{params:{id:t.ID}});case 6:200===(o=a.sent).data.status?e.workflowDetailDialog.data=o.data.data.map(function(t){return{OperateTime:t.OperateTime,OperatorName:t.OperatorName,StatusChange:t.StatusChange,Remark:t.Remark}}):e.$message.error(o.data.info||"获取工作流明细失败"),a.next=14;break;case 10:a.prev=10,a.t0=a.catch(3),console.error("获取工作流明细失败:",a.t0),e.$message.error("获取工作流明细失败，请重试");case 14:return a.prev=14,e.workflowDetailDialog.loading=!1,a.finish(14);case 17:case"end":return a.stop()}},a,e,[[3,10,14,17]])}))()},showApprovalDialog:function(t,e){if(console.log("🚀 ~ showApprovalDialog ~ this.isGroup:",this.isGroup),this.isGroup)this.$message.error("暂不支持集团");else{this.dialogState.visible=!0,this.dialogState.actionType=e;this.dialogState.data=l()({},{id:"",CompanyName:"",CompanyContacts:"",ContactsPhone:"",BJ_MZ:"",ChargeOilName:"",ChargeAmount:"",ChargeLiter:"",BonusAmount:"",Submitter:"",SubmitTime:"",StationName:"",remark:"",gift:"",CardLockType:0,ChargePrice:0},t||{},{id:t&&t.ID||"",CardLockType:t&&t.CardLockType||0,ChargePrice:t&&t.ChargePrice||0}),this.dialogState.bonusLoading=!0,this.getChargeBonus()}},handleApproval:function(){var t=this;return c()(s.a.mark(function e(){return s.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t.security.verifyDialogVisible=!0;case 1:case"end":return e.stop()}},e,t)}))()},getStatusLabel:function(t){var e=r()(S).find(function(e){return S[e].value===t});return e&&S[e]?S[e].label:t},verifyPassword:function(){var t=this;return c()(s.a.mark(function e(){var a,o,r;return s.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t.security.password){e.next=3;break}return t.$message.warning("请输入操作密码"),e.abrupt("return");case 3:if(!(t.security.password.length>64)){e.next=6;break}return t.$message.warning("操作密码不能超过64位"),e.abrupt("return");case 6:return e.prev=6,t.security.loading=!0,a=t.dialogState.actionType,o={id:t.dialogState.data.id,status:a,remark:t.dialogState.data.remark,password:t.security.password,company_id:t.dialogState.data.CompanyID,amount:t.dialogState.data.ChargeAmount,pay_way:1,CardLockType:t.dialogState.data.CardLockType||0,Bonus:t.dialogState.data.gift,oils:t.dialogState.data.ChargeOil,adid:function(){var t=localStorage.getItem("__userInfo__");if(t)try{return JSON.parse(t).adid||""}catch(t){return console.error("解析userInfo失败",t),""}return""}(),liters:t.dialogState.data.ChargeLiter,unit_price:t.dialogState.data.ChargePrice,other:t.dialogState.data},e.next=12,t.$axios.post("/CompanyCard/auditApplyRecharge",o);case 12:if(200!==(r=e.sent).data.status){e.next=22;break}return t.$message.success("操作成功"),t.dialogState.visible=!1,t.security.verifyDialogVisible=!1,t.security.password="",e.next=20,t.handleSearch();case 20:e.next=23;break;case 22:t.$message.error(r.data.info||"操作失败");case 23:e.next=29;break;case 25:e.prev=25,e.t0=e.catch(6),console.error("操作失败:",e.t0),t.$message.error("操作失败，请重试");case 29:return e.prev=29,t.security.loading=!1,e.finish(29);case 32:case"end":return e.stop()}},e,t,[[6,25,29,32]])}))()},getChargeBonus:function(){var t=this;if(this.dialogState.bonusLoading=!0,this.currentFixPriceMode!==y.a.No)return this.dialogState.data.gift="",void(this.dialogState.bonusLoading=!1);if(!this.dialogState.data.ChargeAmount)return this.dialogState.data.gift="",void(this.dialogState.bonusLoading=!1);var e=this.dialogState.data.CompanyID||"";this.$axios.post("/CompanyCard/getChargeAmount",{company_id:e,amount:this.dialogState.data.ChargeAmount,pay_way:1}).then(function(e){if(e&&e.data&&200===e.data.status){var a=e.data.data.donate_money?e.data.data.donate_money+"元":"",o=e.data.data.couponinfo,r="";r=o&&1==o.coupon_type?2==o.retail_type?o.price+"元油品券*"+o.count:3==o.retail_type?o.price+"元非油券*"+o.count:4==o.retail_type?o.price+"元服务券*"+o.count:o.price+"元赠金券*"+o.count:o&&3==o.coupon_type?2==o.retail_type?o.price+"折油品券*"+o.count:3==o.retail_type?o.price+"折非油券*"+o.count:4==o.retail_type?o.price+"折服务券*"+o.count:o.price+"折赠金券*"+o.count:o&&2==o.coupon_type?2==o.retail_type?"油品券*"+o.count:3==o.retail_type?"非油券*"+o.count:4==o.retail_type?"服务券*"+o.count:"赠金券*"+o.count:o&&0==o.coupon_type?"券包":"",t.dialogState.data.gift=a+(r?" + "+r:"")}else{t.dialogState.data.gift="";var n=e&&e.data&&e.data.info?"获取赠送金额失败："+e.data.info:"获取赠送金额失败";t.$message.error(n)}}).catch(function(e){console.log("请求异常:",e),t.dialogState.data.gift="",t.$message.error("获取赠送金额失败，请重试")}).finally(function(){t.$nextTick(function(){t.dialogState.bonusLoading=!1,console.log("Loading状态已设置为:",t.dialogState.bonusLoading)})})}},watch:{dateRange:function(t){if(t&&2===t.length){var e=this.$moment(t[0]),a=this.$moment(t[1]).diff(e,"months",!0);console.log("🚀 ~ dateRange ~ monthDiff:",a),a>3&&(this.dateRange=[],this.$message.error("选择的时间范围不能超过3个月"))}},getCurrentStation:function(t,e){e&&t.value===e.value||(this.companyId=[],this.companyOptions=[],this.query.page=1,this.handleSearch())}}},k={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"recharge-approval py-5"},[a("div",{staticClass:"flex items-center mb-5"},[2==t.getCurrentStation.merchant_type?a("div",{staticClass:"flex items-center mr-5"},[a("span",{staticClass:"mr-2.5"},[t._v("油站名称")]),t._v(" "),a("el-select",{directives:[{name:"loading",rawName:"v-loading",value:t.stationLoading,expression:"stationLoading"}],staticClass:"w-250px",attrs:{placeholder:"请选择",multiple:"",clearable:"","collapse-tags":""},model:{value:t.stationId,callback:function(e){t.stationId=e},expression:"stationId"}},t._l(t.stationOptions,function(t,e){return a("el-option",{key:e,attrs:{label:t.stname,value:t.stid}})}),1)],1):t._e(),t._v(" "),a("div",{staticClass:"flex items-center mr-5"},[a("span",{staticClass:"mr-2.5"},[t._v("提交日期范围")]),t._v(" "),a("el-date-picker",{staticClass:"w-280px",attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":t.pickerOptions,clearable:""},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1),t._v(" "),a("div",{staticClass:"flex items-center mr-5"},[a("span",{staticClass:"mr-2.5"},[t._v("车队名称")]),t._v(" "),a("el-select",{staticClass:"w-[200px]",attrs:{clearable:"",filterable:"",remote:"","reserve-keyword":"",placeholder:"请输入车队名称","remote-method":t.debounceRemoteMethod,loading:t.companyLoading,multiple:"","collapse-tags":""},model:{value:t.companyId,callback:function(e){t.companyId=e},expression:"companyId"}},t._l(t.companyOptions,function(t){return a("el-option",{key:t.CompanyID,attrs:{label:t.CompanyName,value:t.CompanyID}})}),1)],1),t._v(" "),a("el-button",{attrs:{type:"primary",disabled:t.loading},on:{click:t.handleSearch}},[t._v("查询")])],1),t._v(" "),a("div",[t.hideStatusOptions?t._e():a("el-radio-group",{on:{change:t.handleStatusChange},model:{value:t.status,callback:function(e){t.status=e},expression:"status"}},t._l(t.STATUS_OPTIONS,function(e){return a("el-radio-button",{key:e.value,attrs:{label:e.value}},[t._v("\n        "+t._s(e.optionLabel)+"\n      ")])}),1),t._v(" "),a("div",{staticClass:"mt-4"},[t.status===t.STATUS.PENDING.value?a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.dataList}},[a("el-table-column",{attrs:{prop:"CompanyName",label:"车队名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"CompanyContacts",label:"车队管理员"}}),t._v(" "),a("el-table-column",{attrs:{prop:"ContactsPhone",label:"管理员手机号码"}}),t._v(" "),a("el-table-column",{attrs:{prop:"BJ_MZ",label:"母账余额（元）"}}),t._v(" "),a("el-table-column",{attrs:{label:"母账升数（升）"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(0===e.row.MZ_Liters||null==e.row.MZ_Liters?"--":Number(e.row.MZ_Liters).toFixed(4))+"\n          ")]}}],null,!1,2715690950)}),t._v(" "),a("el-table-column",{attrs:{label:"充值油品"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(t.formatterCellval(e.row.ChargeOilName))+"\n          ")]}}],null,!1,2370095318)}),t._v(" "),a("el-table-column",{attrs:{label:"本次充值（元）"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(t.formatterCellval(e.row.ChargeAmount))+"\n          ")]}}],null,!1,3961013783)}),t._v(" "),a("el-table-column",{attrs:{label:"本次充值（升）"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(0===e.row.ChargeLiter||null==e.row.ChargeLiter?"--":Number(e.row.ChargeLiter).toFixed(4))+"\n          ")]}}],null,!1,1056217799)}),t._v(" "),a("el-table-column",{attrs:{prop:"Submitter",label:"提交人员"}}),t._v(" "),a("el-table-column",{attrs:{prop:"StationName",label:"操作油站"}}),t._v(" "),a("el-table-column",{attrs:{prop:"SubmitTime",label:"提交审核时间"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(a){return t.showApprovalDialog(e.row,t.STATUS.APPROVED.value)}}},[t._v("通过")]),t._v(" "),a("el-link",{attrs:{type:"danger",underline:!1},on:{click:function(a){return t.showApprovalDialog(e.row,t.STATUS.REJECTED.value)}}},[t._v("驳回")])]}}],null,!1,*********)})],1):t._e(),t._v(" "),t.status===t.STATUS.APPROVED.value||t.status===t.STATUS.REJECTED.value?a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList}},[a("el-table-column",{attrs:{prop:"CompanyName",label:"车队名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"CompanyContacts",label:"车队管理员"}}),t._v(" "),a("el-table-column",{attrs:{prop:"ContactsPhone",label:"管理员手机号码"}}),t._v(" "),a("el-table-column",{attrs:{prop:"BJ_MZ",label:"母账余额（元）"}}),t._v(" "),a("el-table-column",{attrs:{label:"母账升数（升）"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(0===e.row.MZ_Liters||null==e.row.MZ_Liters?"--":Number(e.row.MZ_Liters).toFixed(4))+"\n          ")]}}],null,!1,2715690950)}),t._v(" "),a("el-table-column",{attrs:{label:"充值油品"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(t.formatterCellval(e.row.ChargeOilName))+"\n          ")]}}],null,!1,2370095318)}),t._v(" "),a("el-table-column",{attrs:{label:"本次充值（元）"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(t.formatterCellval(e.row.ChargeAmount))+"\n          ")]}}],null,!1,3961013783)}),t._v(" "),a("el-table-column",{attrs:{label:"本次充值（升）"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(0===e.row.ChargeLiter||null==e.row.ChargeLiter?"--":Number(e.row.ChargeLiter).toFixed(4))+"\n          ")]}}],null,!1,1056217799)}),t._v(" "),a("el-table-column",{attrs:{label:"本次充值赠送"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(t.formatterCellval(e.row.Bonus))+"\n          ")]}}],null,!1,2970561444)}),t._v(" "),a("el-table-column",{attrs:{prop:"Submitter",label:"提交人员"}}),t._v(" "),a("el-table-column",{attrs:{prop:"StationName",label:"操作油站"}}),t._v(" "),a("el-table-column",{attrs:{prop:"SubmitTime",label:"提交审核时间"}}),t._v(" "),a("el-table-column",{attrs:{label:"审核结果"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.status||e.row.Status===t.STATUS.REJECTED.value?a("span",{staticStyle:{color:"#F56C6C"}},[t._v("\n              "+t._s(t.getStatusLabel(e.row.status||e.row.Status))+"\n            ")]):a("span",[t._v("\n              "+t._s(t.getStatusLabel(e.row.status||e.row.Status))+"\n            ")])]}}],null,!1,2942292958)}),t._v(" "),a("el-table-column",{attrs:{prop:"Auditor",label:"审核人员"}}),t._v(" "),a("el-table-column",{attrs:{prop:"AuditTime",label:"审核时间"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(a){return t.showWorkflowDetail(e.row)}}},[t._v("工作流明细")])]}}],null,!1,41091850)})],1):t._e(),t._v(" "),a("div",{staticClass:"mt-4 w-full flex justify-between items-center"},[a("el-pagination",{attrs:{"current-page":t.query.page,"page-size":t.query.size,layout:"prev, pager, next",total:t.query.total},on:{"current-change":t.handleCurrentChange}}),t._v(" "),a("el-pagination",{attrs:{"page-sizes":[10,20,50,100],"page-size":t.query.size,layout:"total, sizes",total:t.query.total},on:{"size-change":t.handleSizeChange}})],1)],1)],1),t._v(" "),a("el-dialog",{attrs:{title:"充值审核",visible:t.dialogState.visible,width:"600px"},on:{"update:visible":function(e){return t.$set(t.dialogState,"visible",e)}}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.dialogState.bonusLoading,expression:"dialogState.bonusLoading"}]},[a("el-form",{attrs:{"label-width":"120px"}},[a("el-row",{staticClass:"flex-wrap",attrs:{gutter:15,type:"flex"}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"车队名称："}},[a("span",[t._v(t._s(t.dialogState.data.CompanyName))])])],1),t._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"车队管理员："}},[a("span",[t._v(t._s(t.dialogState.data.CompanyContacts))])])],1),t._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"车队余额："}},[a("span",[t._v(t._s(t.dialogState.data.BJ_MZ))]),t._v("元\n            ")])],1),t._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"手机号："}},[a("span",[t._v(t._s(t.dialogState.data.ContactsPhone))])])],1),t._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"充值油品："}},[a("span",[t._v(t._s(t.formatterCellval(t.dialogState.data.ChargeOilName)))])])],1),t._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"本次充值(元)："}},[a("span",[t._v(t._s(t.formatterCellval(t.dialogState.data.ChargeAmount))+" 元")])])],1),t._v(" "),t.currentFixPriceMode!==t.FIXED_PRICE_MODE.No?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"本次充值(升)："}},[a("span",[t._v("\n                "+t._s(0===t.dialogState.data.ChargeLiter||null==t.dialogState.data.ChargeLiter?"--":Number(t.dialogState.data.ChargeLiter).toFixed(4))+" \n                "),0!==t.dialogState.data.ChargeLiter&&null!=t.dialogState.data.ChargeLiter?[t._v("升")]:t._e()],2)])],1):t._e(),t._v(" "),t.currentFixPriceMode!==t.FIXED_PRICE_MODE.No?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"油品单价："}},[a("span",[t._v(t._s(t.dialogState.data.ChargePrice)+"元/升")])])],1):t._e(),t._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"提交人员："}},[a("span",[t._v(t._s(t.dialogState.data.Submitter))])])],1),t._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"操作油站："}},[a("span",[t._v(t._s(t.dialogState.data.StationName))])])],1),t._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"操作时间："}},[a("span",[t._v(t._s(t.dialogState.data.SubmitTime))])])],1),t._v(" "),a("el-col",{attrs:{span:12}},[t.dialogState.data.gift?a("el-form-item",{attrs:{label:"充值赠送："}},[a("span",[t._v(t._s(t.dialogState.data.gift))])]):t._e()],1),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注："}},[a("el-input",{attrs:{type:"textarea",maxlength:"120",rows:3,placeholder:"请输入备注"},model:{value:t.dialogState.data.remark,callback:function(e){t.$set(t.dialogState.data,"remark",e)},expression:"dialogState.data.remark"}})],1)],1)],1)],1)],1),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{disabled:t.dialogState.bonusLoading},on:{click:function(e){t.dialogState.visible=!1}}},[t._v("取 消")]),t._v(" "),t.dialogState.actionType===t.STATUS.APPROVED.value?a("el-button",{attrs:{type:"primary",disabled:t.dialogState.bonusLoading},on:{click:t.handleApproval}},[t._v("审核通过")]):a("el-button",{attrs:{type:"danger",disabled:t.dialogState.bonusLoading},on:{click:t.handleApproval}},[t._v("审核驳回")])],1)]),t._v(" "),a("el-dialog",{staticClass:"safe-dialog",attrs:{title:"安全验证","append-to-body":"",visible:t.security.verifyDialogVisible,width:"400px"},on:{"update:visible":function(e){return t.$set(t.security,"verifyDialogVisible",e)}}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.security.loading,expression:"security.loading"}]},[2==t.getCurrentStation.merchant_type?a("p",[t._v("请输入集团管理员操作密码")]):a("p",[t._v("请输入油站管理员操作密码")]),t._v(" "),a("el-input",{attrs:{type:"password",placeholder:"请输入操作密码",maxlength:"64","show-word-limit":""},model:{value:t.security.password,callback:function(e){t.$set(t.security,"password",e)},expression:"security.password"}})],1),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini",disabled:t.loading},on:{click:function(){t.security.verifyDialogVisible=!1,t.security.password=""}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{size:"mini",type:"primary",disabled:!t.security.password||t.security.loading||t.loading},on:{click:t.verifyPassword}},[t._v("确认")])],1)]),t._v(" "),a("el-dialog",{attrs:{title:"工作流明细",visible:t.workflowDetailDialog.visible,width:"800px"},on:{"update:visible":function(e){return t.$set(t.workflowDetailDialog,"visible",e)}}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.workflowDetailDialog.loading,expression:"workflowDetailDialog.loading"}],attrs:{data:t.workflowDetailDialog.data,border:"",height:"400px"}},t._l(t.workflowDetailDialog.columns,function(t){return a("el-table-column",{key:t.prop,attrs:{prop:t.prop,label:t.label}})}),1),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){t.workflowDetailDialog.visible=!1}}},[t._v("确 定")])],1)],1)],1)},staticRenderFns:[]};var D=a("VU/8")(w,k,!1,function(t){a("cMhO")},"data-v-5aa4380e",null);e.default=D.exports}});