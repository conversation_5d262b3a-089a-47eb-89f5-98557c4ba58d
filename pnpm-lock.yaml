lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      lodash:
        specifier: ^4.17.21
        version: 4.17.21
      printjs:
        specifier: ^1.1.0
        version: 1.1.0
    devDependencies:
      '@iconify-json/mage':
        specifier: ^1.2.0
        version: 1.2.0
      '@playwright/test':
        specifier: ^1.41.0
        version: 1.47.2
      '@types/node':
        specifier: ^20.11.5
        version: 20.16.5

packages:

  '@iconify-json/mage@1.2.0':
    resolution: {integrity: sha512-s6hT2K52tM5oKAHo0qjm8ZUAPgRElxa4LiLH7GNZotJoXC3LDzRAB76Z/5HKEASoUwbtMidhcwFYF7AVZQfCBQ==}

  '@iconify/types@2.0.0':
    resolution: {integrity: sha512-+wluvCrRhXrhyOmRDJ3q8mux9JkKy5SJ/v8ol2tu4FVjyYvtEzkc/3pK15ET6RKg4b4w4BmTk1+gsCUhf21Ykg==}

  '@playwright/test@1.47.2':
    resolution: {integrity: sha512-jTXRsoSPONAs8Za9QEQdyjFn+0ZQFjCiIztAIF6bi1HqhBzG9Ma7g1WotyiGqFSBRZjIEqMdT8RUlbk1QVhzCQ==}
    engines: {node: '>=18'}
    hasBin: true

  '@types/node@20.16.5':
    resolution: {integrity: sha512-VwYCweNo3ERajwy0IUlqqcyZ8/A7Zwa9ZP3MnENWcB11AejO+tLy3pu850goUW2FC/IJMdZUfKpX/yxL1gymCA==}

  fsevents@2.3.2:
    resolution: {integrity: sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  playwright-core@1.47.2:
    resolution: {integrity: sha512-3JvMfF+9LJfe16l7AbSmU555PaTl2tPyQsVInqm3id16pdDfvZ8TTZ/pyzmkbDrZTQefyzU7AIHlZqQnxpqHVQ==}
    engines: {node: '>=18'}
    hasBin: true

  playwright@1.47.2:
    resolution: {integrity: sha512-nx1cLMmQWqmA3UsnjaaokyoUpdVaaDhJhMoxX2qj3McpjnsqFHs516QAKYhqHAgOP+oCFTEOCOAaD1RgD/RQfA==}
    engines: {node: '>=18'}
    hasBin: true

  printjs@1.1.0:
    resolution: {integrity: sha512-OgZScvRJFhBYh3vyTqU0fACbo7icAn15YmZL95SmX97hj8lxHjXsx3RtXOfB8fDR6Zu7XgfAH350hLOe49ixAw==}

  undici-types@6.19.8:
    resolution: {integrity: sha512-ve2KP6f/JnbPBFyobGHuerC9g1FYGn/F8n1LWTwNxCEzd6IfqTwUQcNXgEtmmQ6DlRrC1hrSrBnCZPokRrDHjw==}

snapshots:

  '@iconify-json/mage@1.2.0':
    dependencies:
      '@iconify/types': 2.0.0

  '@iconify/types@2.0.0': {}

  '@playwright/test@1.47.2':
    dependencies:
      playwright: 1.47.2

  '@types/node@20.16.5':
    dependencies:
      undici-types: 6.19.8

  fsevents@2.3.2:
    optional: true

  lodash@4.17.21: {}

  playwright-core@1.47.2: {}

  playwright@1.47.2:
    dependencies:
      playwright-core: 1.47.2
    optionalDependencies:
      fsevents: 2.3.2

  printjs@1.1.0: {}

  undici-types@6.19.8: {}
