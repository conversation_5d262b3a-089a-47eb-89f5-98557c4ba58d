webpackJsonp([22],{"3czE":function(t,e){},axwi:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=a("mvHQ"),s=a.n(i),o=a("Dd8w"),n=a.n(o),l=a("FZmr"),r=a("NYxO"),c={name:"TaxInvoice",components:{DownloadTips:l.a},data:function(){return{isTotalReportForm:!0,selectStatus:"全部",selectDate:[],radio:1,pageSize:10,currentPage:1,totalCount:0,inputValue:"",tableData:[],radio1:1,edit_date:null,textarea:"",carInput:"",dateDisable:!0,isMulti:!1,selectItem:null,selectItems:[],confirmDialogVisible:!1,editDialogVisible:!1,companys:[],company:"",invoiceCompanys:[],invoiceCompany:"",selectMotorcade:null,loading:!1,motorcades:[],pickerOptions:{disabledDate:function(t){return t.getTime()>Date.now()}},showDownloadTips:!1,typeRadio:2,fpstate:0,bpGroupId:1,currentGroupId:0}},mounted:function(){var t=localStorage.getItem("__userInfo__")||"";this.isTotalReportForm=426!=JSON.parse(t).group_id;var e=this.$moment(new Date).subtract(1,"months").format("YYYY-MM-DD HH:mm:ss"),a=this.$moment(new Date);if(this.selectDate.push(this.$moment(e).format("YYYY-MM-DD")+" 00:00:00"),this.selectDate.push(this.$moment(a).format("YYYY-MM-DD")+" 23:59:59"),void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;-1!=window.location.href.indexOf("card-admin.zhihuiyouzhan.com")?this.bpGroupId=1374:this.bpGroupId=1,this.getInvoiceList(),this.getInvoicesCompanyList(),this.getMotorcades()},computed:n()({},Object(r.c)({getCurrentStation:"getCurrentStation"})),methods:{modifyConfirm:function(t){1==t&&this.editConfirmAction(),this.confirmDialogVisible=!1,this.editDialogVisible=!1},invoiceStatusChange:function(t){this.dateDisable=0==t},editRow:function(t){this.selectItem=t,this.isMulti=!1,this.editDialogVisible=!0,t.is_bp_has_invoice?(this.dateDisable=!1,this.radio1=1):(this.dateDisable=!0,this.radio1=0),t.bp_invoice_open_time?this.edit_date=t.bp_invoice_open_time.substring(0,10):this.edit_date=null,t.bp_invoice_title&&t.bp_invoice_no?this.company=t.bp_invoice_title+"      "+t.bp_invoice_no:this.company="",this.textarea=t.bp_invoice_summ},searchAction:function(){this.currentPage=1,this.getInvoiceList()},editConfirm:function(){0==this.radio1?this.isMulti?this.selectItems[0].is_bp_has_invoice?this.confirmDialogVisible=!0:this.editConfirmAction():this.selectItem.is_bp_has_invoice?this.confirmDialogVisible=!0:this.editConfirmAction():this.editConfirmAction()},editConfirmAction:function(){var t=this;if(this.edit_date||this.dateDisable){var e={};if(this.isMulti)for(var a=0;a<this.selectItems.length;a++){var i=this.selectItems[a];e[i.TradeID]=s()({stid:i.StationNO,pay_time:i.TradeTime})}else e[this.selectItem.TradeID]=s()({stid:this.selectItem.StationNO,pay_time:this.selectItem.TradeTime});var o=t.edit_date?t.$moment(t.edit_date).valueOf()/1e3:"";0==this.radio1&&(o="");var n="",l=null;for(a=0;a<this.invoiceCompanys.length;a++)this.company==this.invoiceCompanys[a].showTitle&&(l=this.invoiceCompanys[a]);l&&(n=l.title+"      "+l.snno+"      "+l.addr+"      "+l.phone+"      "+l.bank_name+"      "+l.bank_no);var r={is_bp_has_invoice:this.radio1,bp_invoice_open_time:o,bp_invoice_summ:this.textarea,bp_invoice_info:n};t.editDialogVisible=!1,t.loading=!0,t.$axios.post("/bp/oinvoicesAdd",{codes:e,exts:r}).then(function(e){200==e.data.status?(setTimeout(function(){t.loading=!1,t.getInvoiceList()},3e3),t.$message.success("编辑成功")):(t.loading=!1,t.$message({message:e.data.info,type:"error"}))}).catch(function(e){t.loading=!1,t.$message.error("修改出错")})}else t.$message.error("请选择开票时间")},getInvoiceList:function(){var t=this,e="";"充值"==t.selectStatus?e="CZ":"消费"==t.selectStatus&&(e="XF");var a=t.selectDate?t.$moment(t.selectDate[0]).format("YYYY-MM-DD HH:mm:ss"):"",i=t.selectDate?t.$moment(t.selectDate[1]).format("YYYY-MM-DD HH:mm:ss"):"";t.loading=!0,t.$axios.post("/bp/oinvoices",{fptype:1,qtype:t.radio,cztype:e,query:t.inputValue,page:t.currentPage,start:a,end:i,pagesize:t.pageSize,company_id:t.selectMotorcade,fpstatus:t.typeRadio,fpstate:t.fpstate}).then(function(e){if(t.loading=!1,200==e.data.status){for(var a=e.data.data.dt,i=0;i<a.length;i++){var s=a[i];if(0==s.Money.indexOf("-")?s.money=s.Money.substr(1):s.money=s.Money,s.items&&s.items.length>0){var o=s.items[0];s.goods_name=o.goods_name,s.goods_number=parseFloat(o.goods_number).toFixed(2),s.goods_price=parseFloat(o.goods_price).toFixed(2),s.order_amount=parseFloat(s.order_amount).toFixed(2),s.market_price=o.market_price}s.bp_invoice_open_time?s.open_time=s.bp_invoice_open_time.substring(0,10):s.open_time=""}t.totalCount=e.data.data.TotalQty,t.currentGroupId=e.data.data.group_id,t.tableData=a}else t.$message({message:e.data.info,type:"error"})}).catch(function(e){t.loading=!1,t.$message.error("获取列表出错")})},outputAction:function(){var t=this,e="";"充值"==t.selectStatus?e="CZ":"消费"==t.selectStatus&&(e="XF");var a=t.selectDate?t.$moment(t.selectDate[0]).format("YYYY-MM-DD HH:mm:ss"):"",i=t.selectDate?t.$moment(t.selectDate[1]).format("YYYY-MM-DD HH:mm:ss"):"";t.loading=!0,t.$axios.get("/bp/export",{params:{fptype:1,qtype:t.radio,cztype:e,query:t.inputValue,page:t.currentPage,start:a,end:i,pagesize:t.pageSize,company_id:t.selectMotorcade,name:"专票",fpstatus:t.typeRadio,fpstate:t.fpstate}}).then(function(e){t.loading=!1,200==e.data.status?t.showDownloadTips=!0:t.$message({message:e.data.info,type:"error"})}).catch(function(e){t.$message({message:"导出出错",type:"error"}),t.loading=!1})},handleCurrentChange:function(t){this.currentPage=t,this.getInvoiceList()},handleSizeChange:function(t){this.pageSize=t,this.getInvoiceList()},multiEdit:function(){if(0!=this.selectItems.length)if(1!=this.selectItems.length){for(var t=this.selectItems[0].is_bp_has_invoice,e=!1,a=1;a<this.selectItems.length;a++){if(this.selectItems[a].is_bp_has_invoice!=t){e=!0;break}}e?this.$message.error("选中订单开票状态不一致"):(this.isMulti=!0,this.selectItem=null,this.editDialogVisible=!0,t?(this.radio1=1,this.dateDisable=!1):(this.radio1=0,this.dateDisable=!0))}else this.editRow(this.selectItems[0]);else this.$message.error("请至少选择一条记录")},handleSelectionChange:function(t){this.selectItems=t},checkSelectSet:function(t,e){return!(2==t.RKPType||1==t.RKPType&&"XF"!=t.Type||0==t.RKPType&&"CZ"!=t.Type&&"JH_CZ"!=t.Type)},getInvoicesCompanyList:function(){var t=this;t.loading=!0,t.$axios.get("/bp/invoices",{params:{page:1,pagesize:999}}).then(function(e){if(t.loading=!1,200==e.data.status){for(var a=e.data.data.data,i=0;i<a.length;i++){var s=a[i];s.showTitle=s.title+"      "+s.snno}t.invoiceCompanys=a}else t.$message({message:e.data.info,type:"error"})}).catch(function(e){t.loading=!1,t.$message.error("获取发票信息列表出错")})},getMotorcades:function(){var t=this;t.loading=!0,t.$axios.post("/oscard/getCompanies",{page:1,pagesize:999}).then(function(e){if(t.loading=!1,200==e.data.status){for(var a=[],i=0;i<e.data.data.length;i++){var s=e.data.data[i];s.id=s.ID,a.push(s)}t.motorcades=a}else t.$message({message:e.data.info,type:"error"})}).catch(function(e){t.loading=!1,t.$message.error("获取车队列表出错")})},transactionCellstyle:function(t){t.row,t.column,t.rowIndex;if(17!=t.columnIndex)return"text-align:center"},headerStyle:function(t){t.row,t.column,t.rowIndex;if(17!=t.columnIndex)return"text-align:center"},getDisabledInvoice:function(){1==this.fpstate?this.fpstate=0:this.fpstate=1,this.typeRadio=2,this.currentPage=1,this.getInvoiceList()},getAllData:function(){this.fpstate=0,this.currentPage=1,this.getInvoiceList()}},watch:{getCurrentStation:function(t,e){0!=t.merchant_type&&t.value!=e.value&&(this.getInvoiceList(),this.getInvoicesCompanyList(),this.getMotorcades())}}},d={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"tax"},[i("div",{staticClass:"header_input"},[i("div",[t._v("车队名称")]),t._v(" "),i("el-select",{staticClass:"motorcades_select",attrs:{filterable:"",clearable:"",placeholder:"请选择车队"},on:{change:t.searchAction},model:{value:t.selectMotorcade,callback:function(e){t.selectMotorcade=e},expression:"selectMotorcade"}},t._l(t.motorcades,function(t){return i("el-option",{key:t.ID,attrs:{label:t.CompanyName,value:t.ID}})}),1),t._v(" "),i("el-date-picker",{staticClass:"header_datePick",attrs:{type:"datetimerange","default-time":["00:00:00","23:59:59"],"range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:t.searchAction},model:{value:t.selectDate,callback:function(e){t.selectDate=e},expression:"selectDate"}})],1),t._v(" "),i("div",{staticClass:"header_search"},[i("div",{staticClass:"header_search_left"},[i("div",{staticClass:"search_title"},[t._v("查询客户")]),t._v(" "),i("div",[i("el-radio-group",{model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[i("el-radio",{attrs:{label:1}},[t._v("手机号")]),t._v(" "),i("el-radio",{attrs:{label:2}},[t._v("卡号")]),t._v(" "),i("el-radio",{attrs:{label:3}},[t._v("卡面卡号")])],1)],1),t._v(" "),i("el-input",{staticClass:"search_input",staticStyle:{width:"210px"},attrs:{placeholder:"2"==t.radio?"请输入卡号":"1"==t.radio?"请输入手机号":"请输入卡面卡号",clearable:""},model:{value:t.inputValue,callback:function(e){t.inputValue=e},expression:"inputValue"}}),t._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:t.searchAction}},[t._v("查询")])],1),t._v(" "),i("div",[t.currentGroupId==t.bpGroupId?i("span",{staticClass:"invoice-btn",class:{active:1==t.fpstate},on:{click:t.getDisabledInvoice}},[t._v("已开票退款订单")]):t._e(),t._v(" "),i("el-button",{directives:[{name:"show",rawName:"v-show",value:t.isTotalReportForm,expression:"isTotalReportForm"}],staticClass:"output_btn",attrs:{type:"primary"},on:{click:t.outputAction}},[t._v("下载数据")])],1)]),t._v(" "),0===t.fpstate?i("div",{staticClass:"edit_box"},[i("el-button",{attrs:{type:"primary"},on:{click:t.multiEdit}},[t._v("批量编辑")]),t._v(" "),i("div",{staticClass:"edit_des"},[t._v("选中"),i("span",{staticClass:"count"},[t._v(t._s(t.selectItems.length))]),t._v("条数据")]),t._v(" "),i("el-radio-group",{on:{change:t.searchAction},model:{value:t.typeRadio,callback:function(e){t.typeRadio=e},expression:"typeRadio"}},[i("el-radio",{attrs:{label:2}},[t._v("全部")]),t._v(" "),i("el-radio",{attrs:{label:0}},[t._v("仅看未开票")]),t._v(" "),i("el-radio",{attrs:{label:1}},[t._v("仅看已开票")])],1),t._v(" "),i("div",{staticClass:"invoice_notice"},[t._v("仅显示车队消费订单。")])],1):i("div",{staticClass:"edit_box"},[i("el-button",{attrs:{size:"mini"},on:{click:t.getAllData}},[t._v("返回")])],1),t._v(" "),i("div",{staticClass:"table_box"},[i("el-table",{staticStyle:{width:"100%"},attrs:{"cell-style":t.transactionCellstyle,"header-cell-style":t.headerStyle,data:t.tableData},on:{"selection-change":t.handleSelectionChange}},[i("el-table-column",{attrs:{fixed:"left",type:"selection",width:"55"}}),t._v(" "),i("el-table-column",{attrs:{prop:"StationName",label:"所属油站",width:"160"}}),t._v(" "),i("el-table-column",{attrs:{prop:"TradeID",label:"订单号",width:"180"}}),t._v(" "),i("el-table-column",{attrs:{prop:"CardNO",label:"卡号",width:"180",formatter:t.formatterCellval}}),t._v(" "),i("el-table-column",{attrs:{prop:"Phone",label:"手机号",width:"120"}}),t._v(" "),i("el-table-column",{attrs:{prop:"CardNumber",label:"卡面卡号",width:"100",formatter:t.formatterCellval}}),t._v(" "),i("el-table-column",{attrs:{prop:"bp_has_invoice_text",label:"是否开票"}}),t._v(" "),i("el-table-column",{attrs:{prop:"open_time",label:"开票时间",width:"110",formatter:t.formatterCellval}}),t._v(" "),i("el-table-column",{attrs:{prop:"goods_name",label:"商品名称",width:"150"}}),t._v(" "),i("el-table-column",{attrs:{prop:"goods_number",label:"数量"}}),t._v(" "),i("el-table-column",{attrs:{prop:"market_price",label:"单价"}}),t._v(" "),i("el-table-column",{attrs:{prop:"goods_amount",width:"120",label:"应付金额（元）"}}),t._v(" "),i("el-table-column",{attrs:{prop:"discount_unit_price",width:"120",label:"折后单价（元）"}}),t._v(" "),i("el-table-column",{attrs:{prop:"discount_amount",width:"120",label:"折后金额（元）"}}),t._v(" "),i("el-table-column",{attrs:{prop:"SFBJ",width:"120",label:"实付本金（元）"}}),t._v(" "),i("el-table-column",{attrs:{prop:"SKJ",width:"120",label:"实付赠金（元）"}}),t._v(" "),i("el-table-column",{attrs:{prop:"yh_amount",width:"120",label:"优惠金额（元）"}}),t._v(" "),i("el-table-column",{attrs:{prop:"CompanyName",label:"车队名称",width:"160"}}),t._v(" "),i("el-table-column",{attrs:{prop:"TradeTime",label:"交易时间",width:"140"}}),t._v(" "),i("el-table-column",{attrs:{prop:"bp_invoice_summ",width:"350",label:"备注",formatter:t.formatterCellval}}),t._v(" "),i("el-table-column",{attrs:{fixed:"right",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-button",{attrs:{type:"text",size:"small"},nativeOn:{click:function(a){return a.preventDefault(),t.editRow(e.row)}}},[t._v("\n                    编辑\n                    ")])]}}])})],1)],1),t._v(" "),i("div",{staticClass:"page_content"},[i("el-pagination",{staticClass:"page_left",attrs:{"current-page":t.currentPage,"page-size":t.pageSize,layout:"prev, pager, next",total:t.totalCount},on:{"current-change":t.handleCurrentChange}}),t._v(" "),i("el-pagination",{staticClass:"page_right",attrs:{"page-sizes":[10,15,20,30],"page-size":t.pageSize,layout:"total, sizes",total:t.totalCount},on:{"size-change":t.handleSizeChange}})],1),t._v(" "),i("el-dialog",{attrs:{title:"编辑",visible:t.editDialogVisible,width:"500px","close-on-click-modal":!1},on:{"update:visible":function(e){t.editDialogVisible=e}}},[i("div",[t.isMulti?i("div",{staticClass:"edit_count"},[t._v("已选中"),i("span",{staticClass:"select_count"},[t._v("3")]),t._v("笔订单")]):t._e(),t._v(" "),i("div",{staticClass:"edit_item"},[i("div",{staticClass:"edit_title"},[t._v("是否开票")]),t._v(" "),i("div",[i("el-radio-group",{attrs:{disabled:1==t.fpstate},on:{change:t.invoiceStatusChange},model:{value:t.radio1,callback:function(e){t.radio1=e},expression:"radio1"}},[i("el-radio",{attrs:{label:0}},[t._v("未开票")]),t._v(" "),i("el-radio",{attrs:{label:1}},[t._v("已开票")])],1)],1)]),t._v(" "),i("div",{staticClass:"edit_item"},[i("div",{staticClass:"edit_title"},[1==t.radio1?i("img",{staticClass:"mark_icon",attrs:{src:a("ooDP")}}):t._e(),t._v("\n                    开票时间\n                ")]),t._v(" "),i("div",[i("el-date-picker",{attrs:{disabled:1==t.fpstate||t.dateDisable,"picker-options":t.pickerOptions,type:"date",placeholder:"请选择开票时间"},model:{value:t.edit_date,callback:function(e){t.edit_date=e},expression:"edit_date"}})],1)]),t._v(" "),i("div",{staticClass:"edit_item"},[i("div",{staticClass:"edit_title"},[t._v("发票信息")]),t._v(" "),i("div",[i("el-select",{staticClass:"company_input",attrs:{filterable:"",placeholder:"请选择",disabled:1==t.fpstate},model:{value:t.company,callback:function(e){t.company=e},expression:"company"}},t._l(t.invoiceCompanys,function(t){return i("el-option",{key:t.id,attrs:{label:t.showTitle,value:t.showTitle}})}),1)],1)]),t._v(" "),i("div",{staticClass:"edit_item edit_input"},[i("div",{staticClass:"edit_title"},[t._v("备注")]),t._v(" "),i("div",[i("el-input",{staticClass:"textInput",attrs:{disabled:1==t.fpstate,type:"textarea",rows:4,maxlength:"180","show-word-limit":"",placeholder:"请输入内容"},model:{value:t.textarea,callback:function(e){t.textarea=e},expression:"textarea"}})],1)])]),t._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{size:"mini",type:"primary"},on:{click:t.editConfirm}},[t._v("确 定")]),t._v(" "),i("el-button",{attrs:{size:"mini"},on:{click:function(e){t.editDialogVisible=!1}}},[t._v("取 消")])],1)]),t._v(" "),i("el-dialog",{attrs:{title:"提示",visible:t.confirmDialogVisible,width:"350px","close-on-click-modal":!1,center:""},on:{"update:visible":function(e){t.confirmDialogVisible=e}}},[i("div",{staticClass:"confirmTips"},[t._v('请确认是否将"已开票"修改为"未开票？"')]),t._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.modifyConfirm(1)}}},[t._v("确 定")]),t._v(" "),i("el-button",{attrs:{size:"mini"},on:{click:function(e){return t.modifyConfirm(2)}}},[t._v("取 消")])],1)]),t._v(" "),i("download-tips",{attrs:{showDownloadTips:t.showDownloadTips},on:{"update:showDownloadTips":function(e){t.showDownloadTips=e},"update:show-download-tips":function(e){t.showDownloadTips=e}}})],1)},staticRenderFns:[]};var p=a("VU/8")(c,d,!1,function(t){a("3czE")},"data-v-6ff6d3ae",null);e.default=p.exports},ooDP:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAACJklEQVQ4T5WRy08TYRTFz5kZraQpHSKuOg0xbrpBURqJj2A7EJ9x69oQg8aVsnBjTEiMCzd2pwEluvYPwEfSVoxoRKuCG1yhMF2YQJjSEGnofNfMtJoi1IS7u/fc+8v5vkP8p35E0sd8uaOcn2y2xmZCMZrqB/Xnvi6iTlmlfHar3aaAhWh6UNO0Ef9IKRmMl3IPtwdotc9oOscDgCdn4yu5Z9sCFM3eg8COj7Wj9WTMff15S8D3qH3IIO8C4sDzRq3yxDt/0WlJWdylzwZ/sOYlrF+vnGAeOXEEunEZQIxYv8EFs29MIwYCukhViJeA3K9g5U0I5ld/XIHbGULrcYBXKTgJ0qjtqzEW2+zzAjwgGNtgUaQg4F5/RsgcyO5GXSBFQF0JUnAih3d7CCcMQ+sSkSSBTpAJAOH60SpEZgWYIVmoVtUXHauzVnlqacsYp7E/3G62j4Psrb1MJpbcxXMHMLP670duAjwF9KOm/YjkRQAv/HsAp0XkyVs3d+kC4DVCNgEc086QvCaCCqB6hJpQMEUiJCIZy80NNQPQMe2bJG8HtoFRaznrxwWnrW+EwGD9ObcsN3en7gx/HcxHU0ld0z/U41xcY6V73/LkvN8XW3rjCBmfQLb7fVWpZEcpX6glVK+5SCqx09DfA2gVUUOWm880WnXM9HVSuydAiVXVEyvnv20ABFbNVJcn+p7HpWx2GFCNgGFAG4ja/Rrlp+Xmp/9ovwETrNLINPJrWQAAAABJRU5ErkJggg=="}});