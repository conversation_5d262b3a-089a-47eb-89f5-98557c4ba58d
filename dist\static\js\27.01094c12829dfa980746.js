webpackJsonp([27],{"9Mo+":function(a,t){},fAcX:function(a,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s=e("Xxa5"),n=e.n(s),i=e("exGp"),o=e.n(i),l=e("Dd8w"),r=e.n(l),c=e("FZmr"),p=e("fTGR"),u=e("NYxO"),m=JSON.parse(localStorage.getItem("__userInfo__")),d={name:"GroupCapitalRecord",components:{DownloadTips:c.a,BanciDateTime:p.a},data:function(){return{userInfo:m,typeList:[{value:"",label:"全部"},{value:"CZ",label:"充值"},{value:"XF",label:"消费"},{value:"FP",label:"资金划拨"},{value:"FK",label:"资金返款"},{value:"TK",label:"充值退款"},{value:"XFCX",label:"消费退款"},{value:"QK",label:"余额清零"}],checkboxGroup:"",dateValue:[],companyOptions:[{CompanyName:"全部车队",ID:0}],companyValue:0,tableData:[],loading:!0,total:0,pageSize:10,currentPage:1,inputTxt:"",searchTypeVlaue:"1",stationOptions:[],stationValue:[],companyData:{CurrentBalance:"--",ConsumptionAmount:"--",RechargeAmount:"--",BalanceMZBJ:"--",BalanceMZSKJ:"--",BalanceZZBJ:"--",BalanceZZSKJ:"--",ConsumptionMZBJ:"--",ConsumptionZZBJ:"--",ConsumptionMZSKJ:"--",ConsumptionZZSKJ:"--",RechargeMZBJ:"--",RechargeZZBJ:"--",RechargeMZSKJ:"--",RechargeZZSKJ:"--",CreditTotalAmount:0,MarginMoney:0},showDownloadTips:!1,typeClassValue:1,dateBanciValue:"",typeOptions:[{value:1,label:"按自然日期"},{value:4,label:"按班结日期"}],checkList:[],classList:[],update:!0,hide_time_type:1}},mounted:function(){var a=this.$moment(new Date).subtract(1,"months").format("YYYY-MM-DD HH:mm:ss"),t=this.$moment(new Date);if(this.dateValue.push(this.$moment(a).format("YYYY-MM-DD")+" 00:00:00"),this.dateValue.push(this.$moment(t).format("YYYY-MM-DD")+" 23:59:59"),void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.dateBanciValue=this.nowDate,this.getCompanyList(),this.getStations(),this.getCompanyCapitalRecordList(),this.getGroupBaseInfo()},computed:r()({},Object(u.c)({getCurrentStation:"getCurrentStation"}),{nowDate:function(){return this.$moment(new Date).format("YYYY-MM-DD")},startTimePicker:function(){var a=this;return{disabledDate:function(t){if(0===a.userInfo.has_mch_id)return!1;var e=new Date,s=new Date;return s.setTime(e.getTime()-7776e6),t.getTime()>e||t.getTime()<s||t.getTime()>Date.now()}}}}),methods:{getGroupBaseInfo:function(){var a=this;return o()(n.a.mark(function t(){var e;return n.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,a.$axios.post("/Ostn/getGroupBaseInfo");case 3:if(200==(e=t.sent).data.status){t.next=6;break}return t.abrupt("return",a.$message.error(e.data.info));case 6:a.hide_time_type=e.data.data.hide_time_type||0,t.next=12;break;case 9:t.prev=9,t.t0=t.catch(0),a.$message.error("网络错误！");case 12:case"end":return t.stop()}},t,a,[[0,9]])}))()},changeDate:function(a){this.dateBanciValue=a},searchBanciDate:function(a){this.dateValue=a,this.getCompanyCapitalRecordList()},changeStationValue:function(a){console.log("val",a),4==this.typeClassValue&&a&&this.$refs.banciRef.getBanci(a)},changeClassTypeValue:function(a){if(console.log("e",a),4==a)this.stationValue="",this.$refs.banciRef.clearDate();else{this.dateValue=[];var t=this.$moment(new Date).subtract(1,"months").format("YYYY-MM-DD HH:mm:ss"),e=this.$moment(new Date);this.dateValue.push(this.$moment(t).format("YYYY-MM-DD")+" 00:00:00"),this.dateValue.push(this.$moment(e).format("YYYY-MM-DD")+" 23:59:59"),this.getCompanyCapitalRecordList()}1==this.getCurrentStation.merchant_type&&(this.stationValue=this.getCurrentStation.merchant_id)},getCompanyList:function(){var a=this;this.$axios.post("/CompanyCard/getSimpleCompanyList",{page:1,page_size:1250,input:"",state:0}).then(function(t){200==t.data.status?(a.companyOptions=[{CompanyName:"全部车队",ID:0}],a.companyOptions=a.companyOptions.concat(t.data.data.dt)):a.$message.error(t.data.info)})},getCompanyCapitalRecordList:function(){var a=this;a.loading=!0,a.companyData.CreditTotalAmount=0,a.companyData.MarginMoney=0,10==a.dateValue[0].length&&(a.dateValue[0]=a.dateValue[0]+" 00:00:00"),10==a.dateValue[1].length&&(a.dateValue[1]=a.dateValue[1]+" 23:59:59"),a.$axios.post("/CompanyCard/getCompanyCapitalRecordList",{Page:a.currentPage,PageSize:a.pageSize,QueryType:a.searchTypeVlaue,RequestStr:a.inputTxt,QueryStartTime:a.dateValue?a.dateValue[0]:"",QueryEndTime:a.dateValue?a.dateValue[1]:"",TradeType:a.checkboxGroup,CompanyID:a.companyValue,mch_arr:a.userInfo.mch_arr}).then(function(t){a.loading=!1,a.tableData=[],200==t.data.status?(a.tableData=t.data.data.list.dt,t.data.data.count?a.companyData=t.data.data.count:a.companyData={CurrentBalance:"--",ConsumptionAmount:"--",RechargeAmount:"--",BalanceMZBJ:"--",BalanceMZSKJ:"--",BalanceZZBJ:"--",BalanceZZSKJ:"--",ConsumptionMZBJ:"--",ConsumptionZZBJ:"--",ConsumptionMZSKJ:"--",ConsumptionZZSKJ:"--",RechargeMZBJ:"--",RechargeZZBJ:"--",RechargeMZSKJ:"--",RechargeZZSKJ:"--",CreditTotalAmount:0,MarginMoney:0},a.total=t.data.data.list.TotalQty):(a.total=0,a.$message.error(t.data.info))})},changeData:function(){this.currentPage=1,this.getCompanyCapitalRecordList()},changeType:function(a){this.currentPage=1,this.checkboxGroup=a,this.getCompanyCapitalRecordList()},handleCurrentChange:function(a){this.currentPage=a,this.getCompanyCapitalRecordList()},handleSizeChange:function(a){this.pageSize=a,this.getCompanyCapitalRecordList()},getStations:function(){var a=this;this.$axios.post("/Stations/getStations",{}).then(function(t){200==t.status&&(a.stationOptions=t.data.data.station_info)})},exportCompanyCapitalRecordList:function(){var a=this;this.$axios.post("/CompanyCard/exportCompanyCapitalRecordList",{Page:this.currentPage,PageSize:this.pageSize,QueryType:this.searchTypeVlaue,RequestStr:this.inputTxt,QueryStartTime:this.dateValue?this.dateValue[0]:"",QueryEndTime:this.dateValue?this.dateValue[1]:"",TradeType:this.checkboxGroup,CompanyID:this.companyValue,mch_arr:this.userInfo.mch_arr}).then(function(t){200==t.data.status?a.showDownloadTips=!0:a.$message.error(t.data.info)})}},watch:{getCurrentStation:function(a,t){0!=a.merchant_type&&a.value!=t.value&&(this.getCompanyList(),this.getStations(),this.getCompanyCapitalRecordList())},typeClassValue:function(){var a=this;this.update=!1,setTimeout(function(){a.update=!0},0)}}},h={render:function(){var a=this,t=a.$createElement,e=a._self._c||t;return e("div",{staticClass:"capitalRecord",attrs:{id:"GroupCapitalRecord"}},[0===a.userInfo.has_mch_id?e("div",{staticClass:"top-box"},[e("el-select",{staticStyle:{width:"240px"},attrs:{filterable:"","collapse-tags":"",placeholder:"请选择车队"},on:{change:a.changeData},model:{value:a.companyValue,callback:function(t){a.companyValue=t},expression:"companyValue"}},a._l(a.companyOptions,function(a){return e("el-option",{key:a.ID,attrs:{label:a.CompanyName,value:a.ID}})}),1),a._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:0==a.hide_time_type,expression:"hide_time_type == 0"}],staticClass:"data-box"},[e("div",{staticClass:"item"},[e("p",{staticClass:"title"},[a._v("消费金额(元)")]),a._v(" "),e("p",{staticClass:"number hasdata"},[a._v("\n          "+a._s("--"==a.companyData.ConsumptionAmount?a.companyData.ConsumptionAmount:Math.abs(a.companyData.ConsumptionAmount).toFixed(2))+"\n        ")]),a._v(" "),e("span",{staticClass:"line"}),a._v(" "),e("p",{staticClass:"tleft"},[a._v("\n          母账：本金"),e("span",{staticClass:"hasdata"},[a._v(a._s("--"==a.companyData.ConsumptionMZBJ?a.companyData.ConsumptionMZBJ:Math.abs(a.companyData.ConsumptionMZBJ).toFixed(2)))]),a._v("元 赠金"),e("span",{staticClass:"hasdata"},[a._v(a._s("--"==a.companyData.ConsumptionMZSKJ?a.companyData.ConsumptionMZSKJ:Math.abs(a.companyData.ConsumptionMZSKJ).toFixed(2)))]),a._v("元\n        ")]),a._v(" "),e("p",{staticClass:"tleft"},[a._v("\n          子账：本金"),e("span",{staticClass:"hasdata"},[a._v(a._s("--"==a.companyData.ConsumptionZZBJ?a.companyData.ConsumptionZZBJ:Math.abs(a.companyData.ConsumptionZZBJ).toFixed(2)))]),a._v("元 赠金"),e("span",{staticClass:"hasdata"},[a._v(a._s("--"==a.companyData.ConsumptionZZSKJ?a.companyData.ConsumptionZZSKJ:Math.abs(a.companyData.ConsumptionZZSKJ).toFixed(2)))]),a._v("元\n        ")]),a._v(" "),e("p",{staticClass:"tleft"},[a._v("\n          信用额度："),e("span",{staticClass:"hasdata"},[a._v(a._s(a.companyData.ConsumptionMZXYED?Math.abs(a.companyData.ConsumptionMZXYED):0))]),a._v("元\n        ")])]),a._v(" "),e("div",{staticClass:"box-line"}),a._v(" "),e("div",{staticClass:"item"},[e("p",{staticClass:"title"},[a._v("充值金额(元)")]),a._v(" "),e("p",{staticClass:"number hasdata"},[a._v(a._s(a.companyData.RechargeAmount))]),a._v(" "),e("span",{staticClass:"line"}),a._v(" "),e("p",{staticClass:"tleft"},[a._v("\n          母账：本金"),e("span",{staticClass:"hasdata"},[a._v(a._s(a.companyData.RechargeMZBJ))]),a._v("元 赠金"),e("span",{staticClass:"hasdata"},[a._v(a._s(a.companyData.RechargeMZSKJ))]),a._v("元\n        ")]),a._v(" "),e("p",{staticClass:"tleft"},[a._v("\n          子账：本金"),e("span",{staticClass:"hasdata"},[a._v(a._s(a.companyData.RechargeZZBJ))]),a._v("元 赠金"),e("span",{staticClass:"hasdata"},[a._v(a._s(a.companyData.RechargeZZSKJ))]),a._v("元\n        ")]),a._v(" "),e("p",{staticClass:"tleft"},[a._v("\n          信用额度："),e("span",{staticClass:"hasdata"},[a._v(a._s(a.companyData.RechargeMZXYED?Math.abs(a.companyData.RechargeMZXYED):0))]),a._v("元\n        ")])]),a._v(" "),e("div",{staticClass:"box-line"}),a._v(" "),e("div",{staticClass:"item"},[e("p",{staticClass:"title"},[a._v("当前余额(元)")]),a._v(" "),e("p",{staticClass:"number hasdata"},[a._v(a._s(a.companyData.CurrentBalance))]),a._v(" "),e("span",{staticClass:"line"}),a._v(" "),e("p",{staticClass:"tleft"},[a._v("\n          母账：本金"),e("span",{staticClass:"hasdata"},[a._v(a._s(a.companyData.BalanceMZBJ))]),a._v("元 赠金"),e("span",{staticClass:"hasdata"},[a._v(a._s(a.companyData.BalanceMZSKJ))]),a._v("元\n        ")]),a._v(" "),e("p",{staticClass:"tleft"},[a._v("\n          子账：本金"),e("span",{staticClass:"hasdata"},[a._v(a._s(a.companyData.BalanceZZBJ))]),a._v("元 赠金"),e("span",{staticClass:"hasdata"},[a._v(a._s(a.companyData.BalanceZZSKJ))]),a._v("元\n        ")]),a._v(" "),e("p",{staticClass:"tleft"},[a._v("\n          已用信用额度："),e("span",{staticClass:"hasdata"},[a._v(a._s(a.companyData.Balance_UseXYED?Number(a.companyData.Balance_UseXYED).toFixed(2):0))]),a._v("元\n        ")])]),a._v(" "),0==a.companyData.CreditTotalAmount&&0==a.companyData.MarginMoney||0==a.companyValue?a._e():e("div",{staticClass:"box-line"}),a._v(" "),0!=a.companyData.CreditTotalAmount&&0!=a.companyValue?e("div",{staticClass:"item"},[e("p",{staticClass:"title"},[a._v("信用额度(元)")]),a._v(" "),e("p",{staticClass:"number hasdata"},[a._v(a._s(a.companyData.CreditBalance))]),a._v(" "),e("span",{staticClass:"line"}),a._v(" "),e("p",[a._v("\n          初始额度："),e("span",{staticClass:"hasdata"},[a._v(a._s(a.companyData.CreditTotalAmount))]),a._v("元\n        ")])]):a._e(),a._v(" "),0!=a.companyData.MarginMoney&&0!=a.companyValue?e("div",{staticClass:"item"},[e("p",{staticClass:"title"},[a._v("保证金(元)")]),a._v(" "),e("p",{staticClass:"number hasdata"},[a._v("\n          "+a._s(Math.abs(a.companyData.MarginMoney).toFixed(2))+"\n        ")]),a._v(" "),e("span",{staticClass:"line"})]):a._e()])],1):a._e(),a._v(" "),e("div",{staticClass:"header"},[e("div",{staticClass:"group"},[e("div",{staticClass:"classType"},[e("el-radio-group",{on:{change:a.changeClassTypeValue},model:{value:a.typeClassValue,callback:function(t){a.typeClassValue=t},expression:"typeClassValue"}},a._l(a.typeOptions,function(t){return e("el-radio-button",{key:t.value,attrs:{label:t.value}},[a._v(a._s(t.label))])}),1),a._v(" "),2==a.getCurrentStation.merchant_type&&a.update&&4==a.typeClassValue?e("el-select",{staticStyle:{width:"220px","margin-right":"20px"},attrs:{multiple:1==a.typeClassValue,clearable:"","collapse-tags":"",placeholder:"请选择油站"},on:{change:a.changeStationValue},model:{value:a.stationValue,callback:function(t){a.stationValue=t},expression:"stationValue"}},a._l(a.stationOptions,function(a){return e("el-option",{key:a.stid,attrs:{label:a.stname,value:a.stid}})}),1):a._e(),a._v(" "),e("el-date-picker",{directives:[{name:"show",rawName:"v-show",value:1==a.typeClassValue,expression:"typeClassValue == 1"}],staticClass:"date-picker",attrs:{clearable:!1,"picker-options":a.startTimePicker,"value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"],type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:a.changeData},model:{value:a.dateValue,callback:function(t){a.dateValue=t},expression:"dateValue"}}),a._v(" "),e("banci-date-time",{directives:[{name:"show",rawName:"v-show",value:4==a.typeClassValue,expression:"typeClassValue == 4"}],ref:"banciRef",attrs:{stationValue:a.stationValue,dateValue:a.dateBanciValue,"picker-options":a.startTimePicker},on:{searchDate:a.searchBanciDate,changeDate:a.changeDate}})],1),a._v(" "),e("el-radio-group",{on:{change:a.changeData},model:{value:a.checkboxGroup,callback:function(t){a.checkboxGroup=t},expression:"checkboxGroup"}},a._l(a.typeList,function(t){return e("el-radio-button",{key:t.index,attrs:{label:t.value}},[e("span",{on:{click:function(e){return a.changeType(t.value)}}},[a._v(a._s(t.label))])])}),1)],1),a._v(" "),e("div",{staticClass:"select"},[e("div",{staticClass:"right"},[e("span",{staticClass:"txt"},[a._v("查询类型")]),a._v(" "),e("el-radio-group",{model:{value:a.searchTypeVlaue,callback:function(t){a.searchTypeVlaue=t},expression:"searchTypeVlaue"}},[e("el-radio",{attrs:{label:"1"}},[a._v("手机号")]),a._v(" "),e("el-radio",{attrs:{label:"0"}},[a._v("卡号")]),a._v(" "),e("el-radio",{attrs:{label:"2"}},[a._v("卡面卡号")]),a._v(" "),e("el-radio",{attrs:{label:"5"}},[a._v("订单号")])],1),a._v(" "),e("el-input",{staticStyle:{width:"210px"},attrs:{placeholder:"0"==a.searchTypeVlaue?"请输入卡号":"1"==a.searchTypeVlaue?"请输入手机号":"2"==a.searchTypeVlaue?"请输入卡面卡号":"请输入订单号",clearable:""},model:{value:a.inputTxt,callback:function(t){a.inputTxt=t},expression:"inputTxt"}}),a._v(" "),e("el-button",{attrs:{type:"primary"},on:{click:a.changeData}},[a._v("查询")]),a._v(" "),a.checkList.length?e("div",{staticStyle:{display:"inline-block","margin-left":"10px"}},[e("span",[a._v("时间范围:")]),a._v(" "),e("span",{staticClass:"Noclass"},[a._v(a._s(a.dateValue?a.dateValue[0]:"-"))]),a._v(" 至\n          "),e("span",{staticClass:"Noclass"},[a._v(a._s(a.dateValue?a.dateValue[1]:"-"))])]):a._e()],1),a._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:426!=a.userInfo.group_id,expression:"userInfo.group_id != 426"}]},[0===a.userInfo.has_mch_id?e("el-button",{attrs:{type:"primary",disabled:0==a.tableData.length},on:{click:a.exportCompanyCapitalRecordList}},[a._v("下载数据")]):a._e()],1)])]),a._v(" "),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:a.loading,expression:"loading"}],staticClass:"capitalRecordData",staticStyle:{width:"100%","margin-bottom":"20px"},attrs:{data:a.tableData}},[e("el-table-column",{attrs:{fixed:"",align:"center","min-width":"140",prop:"StationName",label:"操作油站"}}),a._v(" "),e("el-table-column",{attrs:{align:"left",label:"卡号","min-width":"200",prop:"CardNO",formatter:a.formatterCellval}}),a._v(" "),e("el-table-column",{attrs:{align:"center","min-width":"180",label:"卡面卡号",prop:"CardFaceNumber",formatter:a.formatterCellval}}),a._v(" "),e("el-table-column",{attrs:{align:"center","min-width":"140",label:"手机号",prop:"Phone",formatter:a.formatterCellval}}),a._v(" "),e("el-table-column",{attrs:{align:"center",label:"持卡人","min-width":"120",prop:"cardholder_name",formatter:a.formatterCellval}}),a._v(" "),e("el-table-column",{attrs:{align:"center","min-width":"140",label:"车牌号",prop:"CarNumber",formatter:a.formatterCellval}}),a._v(" "),e("el-table-column",{attrs:{align:"center","min-width":"140",label:"车队名称",prop:"CompanyName",formatter:a.formatterCellval}}),a._v(" "),""==a.checkboxGroup||"QK"==a.checkboxGroup?e("el-table-column",{attrs:{align:"center","min-width":"150",prop:"Type",label:"变动类型"}}):a._e(),a._v(" "),e("el-table-column",{attrs:{align:"left",prop:"Money","min-width":"120",label:"变动金额(元)"}}),a._v(" "),e("el-table-column",{attrs:{align:"left","min-width":"120",prop:"ChangeFrontBalance",label:"变动前金额(元)"}}),a._v(" "),e("el-table-column",{attrs:{align:"left","min-width":"120",prop:"ChangeAfterBalance",label:"变动后金额(元)"}}),a._v(" "),e("el-table-column",{attrs:{align:"center","min-width":"120",prop:"AmountType",label:"变动账户"}}),a._v(" "),e("el-table-column",{attrs:{align:"center",prop:"TradeID","min-width":"180",label:"订单号"}}),a._v(" "),e("el-table-column",{attrs:{align:"center",prop:"TradeTime","min-width":"180",label:"变动时间"},scopedSlots:a._u([{key:"default",fn:function(t){return[e("span",[a._v(a._s(a.$moment(t.row.TradeTime).format("YYYY-MM-DD HH:mm:ss")))])]}}])})],1),a._v(" "),e("div",{staticClass:"page_content"},[e("el-pagination",{staticClass:"page_left",attrs:{"current-page":a.currentPage,"page-size":a.pageSize,layout:"prev, pager, next",total:a.total},on:{"current-change":a.handleCurrentChange}}),a._v(" "),e("el-pagination",{staticClass:"page_right",attrs:{"page-sizes":[10,15,20,30],"page-size":a.pageSize,layout:"total, sizes",total:a.total},on:{"size-change":a.handleSizeChange}})],1),a._v(" "),e("download-tips",{attrs:{showDownloadTips:a.showDownloadTips},on:{"update:showDownloadTips":function(t){a.showDownloadTips=t},"update:show-download-tips":function(t){a.showDownloadTips=t}}})],1)},staticRenderFns:[]};var v=e("VU/8")(d,h,!1,function(a){e("9Mo+")},"data-v-7e2a3622",null);t.default=v.exports}});