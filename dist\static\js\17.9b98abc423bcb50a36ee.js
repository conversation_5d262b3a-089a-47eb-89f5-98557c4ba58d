webpackJsonp([17],{"P+VI":function(e,t){},VMAi:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a("Xxa5"),s=a.n(r),o=a("exGp"),i=a.n(o),n=a("mvHQ"),l=a.n(n),c=a("Dd8w"),h=a.n(c),p=a("FZmr"),d=a("fTGR"),m=a("NYxO"),u=JSON.parse(localStorage.getItem("__userInfo__")),g={name:"Refund",components:{DownloadTips:p.a,BanciDateTime:d.a},data:function(){return{isTotalReportForm:!0,userInfo:u,approvalDisable:!1,dialogVisible:!1,dialogVisible2:!1,refundInfo:{},typeValue:1,tabList:[{value:1,label:"充值订单"},{value:2,label:"退款审核中"},{value:3,label:"已退款"},{value:4,label:"退款失败"}],typeClassValue:1,dateBanciValue:"",typeOptions:[{value:1,label:"按自然日期"},{value:4,label:"按班结日期"}],checkList:[],classList:[],stationId:[],stationOptions:[],tableLoading:!0,tableData:[],tableDetailData:[],currentPage:1,pageSize:10,total:0,GZLDetailDialogVisible:!1,searchTypeValue:"1",searchOrderNumber:"",searchOrderPhone:"",searchCardNumber:"",searchPayMethods:[],searchDate:[],reasonDialogVisible:!1,orderInfo:{},reasonRadio:"其他",reason:"",charge:1,approvalFlowTableData:[],showDownloadTips:!1,password:"",manager:"",PayMethodsOptions:[],update:!0,nowEndTime:"",flag:0}},created:function(){this.seeFee()},mounted:function(){var e=this;this.$axios=this.$axios.create();var t=JSON.parse(localStorage.getItem("__userInfo__"));t&&(this.manager=t.name,this.isTotalReportForm=426!=t.group_id);var a=this.$moment(new Date).subtract(1,"months").format("YYYY-MM-DD HH:mm:ss"),r=this.$moment(new Date);if(this.searchDate.push(this.$moment(a).format("YYYY-MM-DD")+" 00:00:00"),this.searchDate.push(this.$moment(r).format("YYYY-MM-DD HH:mm:ss")),this.nowEndTime=this.$moment(r).format("YYYY-MM-DD HH:mm:ss").slice(11),void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.dateBanciValue=this.nowDate,this.getCompanyChargeOrder(),this.getPayMethod(),this.getStationList(),this.stationId=[],this.$nextTick(function(){1==e.getCurrentStation.merchant_type&&e.stationId.push(e.getCurrentStation.merchant_id)})},computed:h()({},Object(m.c)({getCurrentStation:"getCurrentStation",getUserInfo:"getUserInfo"}),{startTimePicker:function(){var e=this;return{disabledDate:function(t){var a=new Date,r=new Date;return 0===e.userInfo.has_mch_id?t.getTime()>new Date(new Date((new Date).toLocaleDateString()).getTime()+864e5-1).getTime():(r.setTime(a.getTime()-7776e6),t.getTime()>a||t.getTime()<r||t.getTime()>Date.now())}}},nowDate:function(){return this.$moment(new Date).format("YYYY-MM-DD")}}),methods:{changeDate:function(e){this.dateBanciValue=e},searchBanciDate:function(e){this.searchDate=e,this.getCompanyChargeOrder()},searchBanci:function(e){if(console.log("banci",e),e.length){var t,a;t=e.map(function(e){return new Date(e.stime).getTime()}),a=e.map(function(e){return new Date(e.etime).getTime()});var r=Math.max.apply(null,a),s=Math.min.apply(null,t);this.searchDate[0]=this.$moment(s).format("YYYY-MM-DD hh:mm:ss"),this.searchDate[1]=this.$moment(r).format("YYYY-MM-DD hh:mm:ss"),this.getCompanyChargeOrder()}else{this.searchDate=[];var o=this.$moment(new Date(this.dateBanciValue)),i=this.$moment(new Date(this.dateBanciValue));this.searchDate.push(this.$moment(o).format("YYYY-MM-DD")+" 00:00:00"),this.searchDate.push(this.$moment(i).format("YYYY-MM-DD")+" "+this.nowEndTime),this.getCompanyChargeOrder()}},getStationList:function(){this.stationId=[];var e=this;this.$axios.post("/Stations/getStationList",{}).then(function(t){200==t.status&&(e.stationOptions=[],t.data.data.forEach(function(t){e.stationId.push(t.stid)}),e.stationOptions=t.data.data)})},changeClassTypeValue:function(e){if(console.log("e",e),4==e)this.stationId="",this.$refs.banciRef.clearDate();else{this.stationId=[],this.searchDate=[];var t=this.$moment(new Date).subtract(1,"months").format("YYYY-MM-DD HH:mm:ss"),a=this.$moment(new Date);this.searchDate.push(this.$moment(t).format("YYYY-MM-DD")+" 00:00:00"),this.searchDate.push(this.$moment(a).format("YYYY-MM-DD")+" "+this.nowEndTime),this.getCompanyChargeOrder()}1==this.getCurrentStation.merchant_type&&(this.stationId=this.getCurrentStation.merchant_id)},BanciChange:function(e){console.log("e",e),this.searchDate=[];var t=this.$moment(new Date(this.dateBanciValue)),a=this.$moment(new Date(this.dateBanciValue));this.searchDate.push(this.$moment(t).format("YYYY-MM-DD")+" 00:00:00"),this.searchDate.push(this.$moment(a).format("YYYY-MM-DD")+" "+this.nowEndTime),e&&this.stationId.length>1?this.$message.error("查看班次只能选择一个油站"):e&&this.stationId.length<1?this.$message.error("请选择一个油站"):e?this.getSettlementList():this.$message.error("请选择时间")},getSettlementList:function(){var e=this;this.checkList=[],this.classList=[];var t={stid:this.stationId,sdate:this.dateBanciValue,edate:this.dateBanciValue};this.$axios.post("/CardCharge/getSettlementList",t).then(function(t){if(200==t.data.status)if("[object Object]"==Object.prototype.toString.call(t.data.data))for(var a in t.data.data)e.classList.push(t.data.data[a]);else e.classList=t.data.data;else e.$message.error(t.data.info)}).catch(function(e){console.log("err",e)})},changeStationValue:function(e){console.log("val",e),console.log("stationId",this.stationId),4==this.typeClassValue&&e?this.$refs.banciRef.getBanci(e):this.getCompanyChargeOrder()},getCompanyChargeOrder:function(){var e=this;e.tableLoading=!0,10==e.searchDate[0].length&&(e.searchDate[0]=e.searchDate[0]+" 00:00:00"),10==e.searchDate[1].length&&(e.searchDate[1]==e.$moment(new Date).format("YYYY-MM-DD")?e.searchDate[1]=e.searchDate[1]+" "+this.$moment().format("HH:mm:ss"):e.searchDate[1]=e.searchDate[1]+" 23:59:59"),e.$axios.post("/CardCharge/getCompanyChargeOrder",{phone:e.searchOrderPhone,order_code:e.searchOrderNumber,vipcard_no:e.searchCardNumber,pay_way:e.searchPayMethods,start_time:e.searchDate?e.searchDate[0]:"",end_time:e.searchDate?e.searchDate[1]:"",page:e.currentPage,page_size:e.pageSize,order_status:e.typeValue,mch_arr:this.userInfo.mch_arr,station_id:e.stationId}).then(function(t){if(e.tableLoading=!1,200==t.data.status){var a=JSON.parse(l()(t.data.data.order_list?t.data.data.order_list:[]));a.map(function(e,t){return e.showRemark=!1}),e.tableData=a,e.total=t.data.data.total}else e.tableData=[],e.total=0,e.$message({message:t.data.info,type:"error"})}).catch(function(e){})},handleCurrentChange:function(e){this.currentPage=e,this.getCompanyChargeOrder()},handleSizeChange:function(e){this.pageSize=e,this.getCompanyChargeOrder()},handleChangeLabelType:function(e){this.dateBanciValue=[],this.currentPage=1,this.getCompanyChargeOrder()},handleChangeTpye:function(){this.currentPage=1,this.searchDate?this.getCompanyChargeOrder():this.tableData=[]},changeTypeValue:function(e){1==e&&(this.searchOrderNumber="",this.searchCardNumber=""),2==e&&(this.searchOrderPhone="",this.searchCardNumber=""),3==e&&(this.searchOrderNumber="",this.searchOrderPhone="")},handleNoApprovalClick:function(e,t){this.reasonDialogVisible=!0,this.orderInfo=e,this.charge=t,this.reason=""},approval:function(){var e=this;e.approvalDisable=!0,e.$axios.post("/CardCharge/approval",{order_code:e.orderInfo.order_code,order_type:e.orderInfo.order_type,operate:this.charge,remark:"其他"==e.reasonRadio?e.reason:e.reasonRadio},{headers:{"X-Requested-With":"XMLHttpRequest"}}).then(function(t){200==t.data.status?setTimeout(function(){e.approvalDisable=!1,e.$message({message:"操作成功",type:"success"}),e.reasonDialogVisible=!1,e.getCompanyChargeOrder()},2e3):setTimeout(function(){e.$message({message:t.data.info,type:"error"}),e.approvalDisable=!1,e.reasonDialogVisible=!1,e.getCompanyChargeOrder()},2e3)}).catch(function(e){})},refundCompanyCharge:function(){var e=this;e.approvalDisable=!0,e.$axios.post("/CardCharge/refundCompanyCharge",{order_code:e.refundInfo.order_code,order_type:e.refundInfo.order_type,password:e.password},{timeout:2e4,headers:{"X-Requested-With":"XMLHttpRequest"}}).then(function(t){200==t.data.status?setTimeout(function(){e.approvalDisable=!1,e.$message({message:"退款成功",type:"success"}),e.dialogVisible=!1,e.dialogVisible2=!1,e.getCompanyChargeOrder()},2e3):(e.approvalDisable=!1,e.$message({message:t.data.info,type:"error"}))}).catch(function(t){e.approvalDisable=!1,e.$message({message:"网络错误",type:"error"})})},handleRefundClick:function(e){this.password="",this.refundInfo=e;var t=JSON.parse(window.localStorage.__userInfo__).new_group_id;1==t||426==t?this.dialogVisible2=!0:this.dialogVisible=!0},handleGZLDetailClick:function(e){this.GZLDetailDialogVisible=!0;var t=this;t.approvalFlowTableData=[],t.$axios.post("/CardCharge/approvalFlow",{order_code:e}).then(function(e){200==e.data.status?t.approvalFlowTableData=e.data.data:t.$message({message:e.data.info,type:"error"})}).catch(function(e){})},exportCompanyChargeOrders:function(){var e=this;if(!this.isTotalReportForm){var t=this.$moment(new Date).format("YYYY-MM-DD")+" 23:59:59",a=(this.$moment(t).valueOf()-5184e6)/1e3+1;if(this.$moment(this.searchDate[0]).valueOf()/1e3<a)return this.$message.error("只能下载最近60天的数据")}this.$axios.post("/CardCharge/exportCompanyChargeOrders",{phone:this.searchOrderPhone,order_code:this.searchOrderNumber,vipcard_no:this.searchCardNumber,start_time:this.searchDate[0],end_time:this.searchDate[1],page:this.currentPage,page_size:this.pageSize,order_status:this.typeValue,station_id:this.stationId,mch_arr:this.userInfo.mch_arr}).then(function(t){200==t.data.status?e.showDownloadTips=!0:e.$message.error(t.data.info)})},orderChange:function(e,t){this.tableData[t].showRemark=!1;var a=this;this.$axios.post("/CardCharge/orderChange",{code:e.order_code,remark:e.remark.replace(/(\r\n|\n|\r)/gm,"")}).then(function(e){200==e.data.status||a.$message({message:e.data.info,type:"error"})}).catch(function(e){})},showRemarkInput:function(e){this.$nextTick(function(){e.showRemark=!0})},getPayMethod:function(){var e=this;this.$axios.post("CardCharge/getOrderPayType").then(function(t){200==t.data.status?e.PayMethodsOptions=t.data.data:e.$message({message:t.data.info,type:"error"})}).catch(function(e){console.log(e)})},seeFee:function(){var e=this;return i()(s.a.mark(function t(){return s.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.$axios.post("CardCharge/companyChargeFeeSettleAmt");case 3:200==t.sent.data.status&&(e.flag=1),t.next=10;break;case 7:t.prev=7,t.t0=t.catch(0),console.log(t.t0);case 10:case"end":return t.stop()}},t,e,[[0,7]])}))()}},watch:{getCurrentStation:function(e,t){0!=e.merchant_type&&e.value!=t.value&&(this.getStationList(),this.getCompanyChargeOrder(),this.getPayMethod())},searchDate:function(){this.$moment(this.searchDate[1]).format("YYYY-MM-DD")===this.$moment().format("YYYY-MM-DD")&&this.$moment(this.searchDate[1]).unix()>this.$moment().unix()&&(this.searchDate[1]=this.$moment().format("YYYY-MM-DD HH:mm:ss"))},typeClassValue:function(){var e=this;this.update=!1,setTimeout(function(){e.update=!0},0)}}},f={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"refund",attrs:{id:"refund"}},[a("div",{staticClass:"header"},[a("div",{staticClass:"classType"},[a("el-radio-group",{on:{change:e.changeClassTypeValue},model:{value:e.typeClassValue,callback:function(t){e.typeClassValue=t},expression:"typeClassValue"}},e._l(e.typeOptions,function(t){return a("el-radio-button",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])}),1),e._v(" "),2==e.getCurrentStation.merchant_type&&e.update?a("el-select",{staticStyle:{width:"250px","margin-right":"20px"},attrs:{multiple:1==e.typeClassValue,clearable:"","collapse-tags":"",placeholder:"请选择油站"},on:{change:e.changeStationValue},model:{value:e.stationId,callback:function(t){e.stationId=t},expression:"stationId"}},e._l(e.stationOptions,function(e){return a("el-option",{key:e.stid,attrs:{label:e.stname,value:e.stid}})}),1):e._e(),e._v(" "),a("el-date-picker",{directives:[{name:"show",rawName:"v-show",value:1==e.typeClassValue,expression:"typeClassValue == 1"}],staticStyle:{width:"355px","margin-left":"30px"},attrs:{"picker-options":e.startTimePicker,"value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"],type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:e.handleChangeTpye},model:{value:e.searchDate,callback:function(t){e.searchDate=t},expression:"searchDate"}}),e._v(" "),a("banci-date-time",{directives:[{name:"show",rawName:"v-show",value:4==e.typeClassValue,expression:"typeClassValue == 4"}],ref:"banciRef",attrs:{stationValue:e.stationId,dateValue:e.dateBanciValue,"picker-options":e.startTimePicker},on:{searchDate:e.searchBanciDate,changeDate:e.changeDate}})],1),e._v(" "),a("el-radio-group",{on:{change:e.handleChangeLabelType},model:{value:e.typeValue,callback:function(t){e.typeValue=t},expression:"typeValue"}},e._l(e.tabList,function(t){return a("el-radio-button",{key:t.index,attrs:{label:t.value}},[e._v(e._s(t.label))])}),1),e._v(" "),a("div",{staticClass:"search-box"},[a("div",{staticClass:"left"},[a("span",{staticClass:"txt"},[e._v("查询类型")]),e._v(" "),a("el-radio-group",{on:{change:e.changeTypeValue},model:{value:e.searchTypeValue,callback:function(t){e.searchTypeValue=t},expression:"searchTypeValue"}},[a("el-radio",{attrs:{label:"1"}},[e._v("手机号")]),e._v(" "),a("el-radio",{attrs:{label:"2"}},[e._v("订单号")]),e._v(" "),a("el-radio",{attrs:{label:"3"}},[e._v("卡号")]),e._v(" "),a("el-radio",{attrs:{label:"4"}},[e._v("付款方式")])],1),e._v(" "),1==e.searchTypeValue?a("el-input",{staticStyle:{"margin-left":"20px",width:"210px"},attrs:{placeholder:"请输入手机号",clearable:""},model:{value:e.searchOrderPhone,callback:function(t){e.searchOrderPhone=t},expression:"searchOrderPhone"}}):e._e(),e._v(" "),2==e.searchTypeValue?a("el-input",{staticStyle:{"margin-left":"20px",width:"210px"},attrs:{placeholder:"请输入订单号",clearable:""},model:{value:e.searchOrderNumber,callback:function(t){e.searchOrderNumber=t},expression:"searchOrderNumber"}}):e._e(),e._v(" "),3==e.searchTypeValue?a("el-input",{staticStyle:{"margin-left":"20px",width:"210px"},attrs:{placeholder:"请输入卡号",clearable:""},model:{value:e.searchCardNumber,callback:function(t){e.searchCardNumber=t},expression:"searchCardNumber"}}):e._e(),e._v(" "),4==e.searchTypeValue?a("el-select",{staticStyle:{"margin-left":"20px",width:"210px"},attrs:{multiple:"","collapse-tags":"",filterable:"",placeholder:"请选择付款方式"},model:{value:e.searchPayMethods,callback:function(t){e.searchPayMethods=t},expression:"searchPayMethods"}},e._l(e.PayMethodsOptions,function(e){return a("el-option",{key:e.pay_way_id,attrs:{label:e.payment_name,value:e.pay_way_id}})}),1):e._e(),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.handleChangeTpye}},[e._v("查询")]),e._v(" "),e.checkList.length?a("div",{staticStyle:{display:"inline-block","margin-left":"10px"}},[a("span",[e._v("时间范围:")]),e._v(" "),a("span",{staticClass:"Noclass"},[e._v(e._s(e.searchDate?e.searchDate[0]:"-"))]),e._v("\n          至\n          "),a("span",{staticClass:"Noclass"},[e._v(e._s(e.searchDate?e.searchDate[1]:"-"))])]):e._e()],1),e._v(" "),a("el-button",{attrs:{type:"primary",disabled:0==e.tableData.length},on:{click:e.exportCompanyChargeOrders}},[e._v("下载数据")])],1)],1),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],staticClass:"refundData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:""}},[2==e.getCurrentStation.merchant_type?a("el-table-column",{attrs:{fixed:"",align:"left",prop:"station_name",label:"充值油站",width:"160"}}):e._e(),e._v(" "),a("el-table-column",{attrs:{width:"190",label:"卡号",formatter:e.formatterCellval,prop:"card_no"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",width:"120",label:"手机号",prop:"phone",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"持卡人","min-width":"100",prop:"cardholder_name",formatter:e.formatterCellval}}),e._v(" "),2==e.typeValue||3==e.typeValue||4==e.typeValue?a("el-table-column",{attrs:{align:"center",prop:"pay_money",width:"120",label:"退款金额(元)",formatter:e.formatterCellval}}):e._e(),e._v(" "),2==e.typeValue?a("el-table-column",{attrs:{align:"center",prop:"update_date",label:"退款申请时间",width:"180",formatter:e.formatterCellval}}):e._e(),e._v(" "),3==e.typeValue?a("el-table-column",{attrs:{align:"center",prop:"refund_datetime",label:"退款时间",width:"200",formatter:e.formatterCellval}}):e._e(),e._v(" "),4==e.typeValue?a("el-table-column",{attrs:{align:"center",prop:"update_date",label:"退款审核时间",width:"200",formatter:e.formatterCellval}}):e._e(),e._v(" "),3==e.typeValue?a("el-table-column",{attrs:{align:"center",prop:"refund_way",width:"120",label:"退款方式",formatter:e.formatterCellval}}):e._e(),e._v(" "),a("el-table-column",{attrs:{align:"left",prop:"orig_amount",width:"120",label:"充值本金(元)",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{attrs:{align:"left",prop:"gift_amount",width:"120",label:"充值赠金(元)",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{attrs:{align:"left",prop:"charge_money",width:"120",label:"充值总额(元)",formatter:e.formatterCellval}}),e._v(" "),e.flag?a("el-table-column",{attrs:{align:"left",prop:"fee",width:"120",label:"手续费(元)",formatter:e.formatterCellval}}):e._e(),e._v(" "),e.flag?a("el-table-column",{attrs:{align:"left",prop:"settle_amt",width:"120",label:"实结金额(元)",formatter:e.formatterCellval}}):e._e(),e._v(" "),a("el-table-column",{attrs:{align:"left",width:"210",label:"卡名称",prop:"card_name",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{key:"2",attrs:{align:"center",width:"190",label:"车队名称",formatter:e.formatterCellval,prop:"company_name"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"order_code",width:"190",label:"订单号",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"pay_datetime",label:"充值时间",width:"180",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"charge_consume_type",width:"100",label:"充值类型",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"type",width:"100",label:"账户类型",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"pay_way",width:"180",label:"付款方式",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"source",width:"100",label:"数据来源",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"operator_name",width:"100",label:"员工名称",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{attrs:{type:"index",align:"center",prop:"remark",width:"240",label:"备注"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{directives:[{name:"show",rawName:"v-show",value:t.row.showRemark,expression:"scope.row.showRemark"}],staticStyle:{width:"90%"},attrs:{type:"textarea",maxlength:"75","show-word-limit":"",placeholder:"请输入备注"},on:{blur:function(a){return e.orderChange(t.row,t.$index)}},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:t.target.blur.apply(null,arguments)}},model:{value:t.row.remark,callback:function(a){e.$set(t.row,"remark",a)},expression:"scope.row.remark"}}),e._v(" "),a("span",{directives:[{name:"show",rawName:"v-show",value:!t.row.showRemark,expression:"!scope.row.showRemark"}]},[e._v(e._s(t.row.remark?t.row.remark:""))]),e._v(" "),a("i",{directives:[{name:"show",rawName:"v-show",value:!t.row.showRemark,expression:"!scope.row.showRemark"}],staticClass:"el-icon-edit-outline edit-icon",on:{click:function(a){return e.showRemarkInput(t.row)}}})]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",fixed:"right",width:"180",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.order_status&&1==t.row.is_superadmin?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.handleNoApprovalClick(t.row,1)}}},[e._v("退款")]):e._e(),e._v(" "),2==t.row.order_status&&1==t.row.is_superadmin?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.handleRefundClick(t.row)}}},[e._v("通过")]):e._e(),e._v(" "),2==t.row.order_status&&1==t.row.is_superadmin?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.handleNoApprovalClick(t.row,2)}}},[e._v("不通过")]):e._e(),e._v(" "),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.handleGZLDetailClick(t.row.order_code)}}},[e._v("工作流明细")])]}}])})],1),e._v(" "),a("div",{staticClass:"page_content"},[a("el-pagination",{staticClass:"page_left",attrs:{"current-page":e.currentPage,"page-size":e.pageSize,layout:"prev, pager, next",total:e.total},on:{"current-change":e.handleCurrentChange}}),e._v(" "),a("el-pagination",{staticClass:"page_right",attrs:{"page-sizes":[10,15,20,30],"page-size":e.pageSize,layout:"total, sizes",total:e.total},on:{"size-change":e.handleSizeChange}})],1),e._v(" "),a("el-dialog",{attrs:{"close-on-click-modal":!1,title:"工作流明细",visible:e.GZLDetailDialogVisible,width:"50%"},on:{"update:visible":function(t){e.GZLDetailDialogVisible=t}}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.approvalFlowTableData}},[a("el-table-column",{attrs:{align:"center",prop:"create_time",label:"创建时间",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"operator_name",label:"操作",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"status_change",label:"状态变化",formatter:e.formatterCellval}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"remark",label:"备注",formatter:e.formatterCellval}})],1),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){e.GZLDetailDialogVisible=!1}}},[e._v("确 定")])],1)],1),e._v(" "),a("el-dialog",{staticClass:"dialog",attrs:{"close-on-click-modal":!1,visible:e.reasonDialogVisible,width:"520px"},on:{"update:visible":function(t){e.reasonDialogVisible=t}}},[a("div",{attrs:{slot:"title"},slot:"title"},[1==e.charge?a("span",{staticClass:"title-name"},[e._v("充值退款")]):e._e(),e._v(" "),2==e.charge?a("span",{staticClass:"title-name"},[e._v("审核不通过原因")]):e._e()]),e._v(" "),1==e.charge?a("div",{staticClass:"check-box"},[a("div",{staticClass:"left"},[a("p",[a("span",{staticClass:"txt"},[e._v("卡 号：")]),a("span",[e._v(e._s(e.orderInfo.card_no?e.orderInfo.card_no:"--"))])]),e._v(" "),a("p",[a("span",{staticClass:"txt"},[e._v("持卡人：")]),a("span",[e._v(e._s(e.orderInfo.cardholder_name?e.orderInfo.cardholder_name:"--"))])]),e._v(" "),a("p",[a("span",{staticClass:"txt"},[e._v("手机号：")]),a("span",[e._v(e._s(e.orderInfo.phone?e.orderInfo.phone:"--"))])]),e._v(" "),a("p",[a("span",{staticClass:"txt"},[e._v("车队名称：")]),a("span",[e._v(e._s(e.orderInfo.company_name?e.orderInfo.company_name:"--"))])]),e._v(" "),a("p",[a("span",{staticClass:"txt"},[e._v("订单号：")]),a("span",[e._v(e._s(e.orderInfo.order_code?e.orderInfo.order_code:"--"))])]),e._v(" "),a("p",[a("span",{staticClass:"txt"},[e._v("付款方式：")]),a("span",{staticStyle:{"font-weight":"bold"}},[e._v(e._s(e.orderInfo.pay_way))])])]),e._v(" "),a("div",{staticClass:"right"},[a("p",[a("span",{staticClass:"txt"},[e._v("充值时间：")]),a("span",[e._v(e._s(e.orderInfo.pay_datetime))])]),e._v(" "),a("p",[a("span",{staticClass:"txt"},[e._v("充值总额：")]),a("span",{staticStyle:{"font-weight":"bold"}},[e._v(e._s(e.orderInfo.charge_money))]),e._v("元\n        ")]),e._v(" "),a("p",[a("span",{staticClass:"txt"},[e._v("充值本金：")]),a("span",{staticStyle:{"font-weight":"bold"}},[e._v(e._s(e.orderInfo.orig_amount))]),e._v("元\n        ")]),e._v(" "),a("p",[a("span",{staticClass:"txt"},[e._v("充值赠金：")]),a("span",{staticStyle:{"font-weight":"bold"}},[e._v(e._s(e.orderInfo.gift_amount))]),e._v("元\n        ")]),e._v(" "),a("p",[a("span",{staticClass:"txt"},[e._v("退款金额：")]),a("span",{staticStyle:{"font-weight":"bold"}},[e._v(e._s(e.orderInfo.pay_money))]),e._v("元\n        ")])])]):e._e(),e._v(" "),a("el-radio-group",{model:{value:e.reasonRadio,callback:function(t){e.reasonRadio=t},expression:"reasonRadio"}},[a("el-radio",{attrs:{label:"其他"}},[e._v("其他")])],1),e._v(" "),"其他"==e.reasonRadio?a("el-input",{staticStyle:{"margin-top":"10px",height:"80px"},attrs:{type:"textarea",maxlength:"75","show-word-limit":"",placeholder:1==e.charge?"请输入退款说明":"请输入审核不通过原因"},model:{value:e.reason,callback:function(t){e.reason=t},expression:"reason"}}):e._e(),e._v(" "),1==e.charge?a("div",{staticClass:"tips"},[a("i",{staticClass:"el-icon-warning",staticStyle:{color:"#FA5A00",margin:"4px"}}),e._v(" "),a("div",[a("p",{staticStyle:{color:"#FA5A00"}},[e._v("\n          充值退款原路返回仅支持付款方式为微信、支付宝的订单\n        ")]),e._v(" "),a("p",[e._v("银行卡付款的订单退款申请成功后，原路返回请到银联APP操作")]),e._v(" "),a("p",[e._v("现金等其他付款方式的订单申请成功后，由油站自行退款")])])]):e._e(),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:function(t){e.reasonDialogVisible=!1}}},[e._v("取 消")]),e._v(" "),e.approvalDisable?a("el-button",{attrs:{size:"mini",type:"primary",loading:!0}},[e._v("处理中")]):a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:e.approval}},[e._v(e._s(1==e.charge?"发起退款":"确定"))])],1)],1),e._v(" "),a("el-dialog",{staticClass:"safe-dialog",attrs:{title:"安全验证",visible:e.dialogVisible,width:"400px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[2==e.getCurrentStation.merchant_type?a("p",[e._v("\n      请输入集团管理员操作密码\n    ")]):a("p",[e._v("请输入油站管理员操作密码")]),e._v(" "),a("el-input",{attrs:{type:"password",placeholder:"请输入操作密码"},model:{value:e.password,callback:function(t){e.password=t},expression:"password"}}),e._v(" "),1==e.refundInfo.is_online?a("div",{staticClass:"tips-item"},[a("i",{staticClass:"el-icon-warning",staticStyle:{"margin-right":"8px","font-size":"14px"}}),e._v("验证通过后，发起退款原路返回，请务必谨慎操作！\n    ")]):a("div",{staticClass:"tips-item"},[a("i",{staticClass:"el-icon-warning",staticStyle:{"margin-right":"8px","font-size":"14px"}}),e._v("验证通过后，扣除卡充值金额，请自行退款给车主！\n    ")]),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),e.approvalDisable?a("el-button",{attrs:{size:"mini",type:"primary",loading:!0}},[e._v("退款中")]):a("el-button",{attrs:{size:"mini",type:"primary",disabled:!e.password},on:{click:e.refundCompanyCharge}},[e._v("确认通过")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"提示",visible:e.dialogVisible2,width:"520px"},on:{"update:visible":function(t){e.dialogVisible2=t}}},[a("span",[e._v("对订单进行退款，是否继续?")]),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisible2=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.refundCompanyCharge}},[e._v("确 定")])],1)]),e._v(" "),a("download-tips",{attrs:{showDownloadTips:e.showDownloadTips},on:{"update:showDownloadTips":function(t){e.showDownloadTips=t},"update:show-download-tips":function(t){e.showDownloadTips=t}}})],1)},staticRenderFns:[]};var _=a("VU/8")(g,f,!1,function(e){a("P+VI"),a("wMiC")},"data-v-87ac4122",null);t.default=_.exports},wMiC:function(e,t){}});