<template>
    <div id="cardRule" class="cardRule">
        <div class="table-box" v-show="showTableData">

            <!-- 标签切换 -->
            <div class="tab-box">
                <div class="item">
                    <span class="tab">状态</span>
                    <el-radio-group v-model="state" @change="stateChange">
                        <el-radio-button v-for="item in stateList" :key="item.key" :label="item.value">{{item.label}}</el-radio-button>
                    </el-radio-group>
                </div>
                <!-- <div class="item">
                     <span class="tab">类型</span>
                     <el-radio-group v-model="cardType" @change="typeChange">
                         <el-radio-button v-for="item in typeList" :key="item.key" :label="item.value">{{item.label}}</el-radio-button>
                     </el-radio-group>
                </div> -->
                <div class="item">
                    <span class="tab">可用油站</span>
                    <el-radio-group v-model="station_id" @change="stationChange">
                        <el-radio-button :label="0">全部</el-radio-button>
                        <el-radio-button v-for="item in stationList" :key="item.key" :label="item.stid">{{item.stname}}</el-radio-button>
                    </el-radio-group>
                </div>
            </div>

            <div style="display:flex;justify-content: space-between;">
              <div><el-button type="primary" @click="goToCreateRule" style="border: none;">创建通用规则</el-button>
                <el-button v-if="showLockRule" @click="goToCreateLockRule">创建锁价规则</el-button>
                </div>

                <div>
                    <el-input
                        placeholder="请输入规则名称"
                        style="width:200px;margin:0;"
                        prefix-icon="el-icon-search"
                        v-model="searchTxt"
                        clearable>
                    </el-input>
                    <el-button type="primary" @click="search">查询</el-button>
                </div>
            </div>

            <!-- 表格数据 -->
            <el-table
            v-loading="loading"
            class="cardRuleListTable"
            :data="tableData">
                <el-table-column
                prop="ID"
                align="left" min-width="80"
                label="ID">
                </el-table-column>
                <el-table-column
                align="center"
                header-align="center" min-width="120"
                label="状态">
                    <template slot-scope="scope">
                        <span :class="{redfont:scope.row.State != 100}">{{scope.row.State == 100 ? '启用' : '禁用'}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                prop="Name"
                align="left" min-width="180"
                label="名称">
                </el-table-column>
                <el-table-column
                prop="Priority"
                align="center"
                header-align="center" min-width="120"
                label="优先级">
                </el-table-column>
                <el-table-column
                align="center"
                header-align="center" min-width="180"
                label="计算公式">
                    <template slot-scope="scope">
                        <span style="white-space: pre-line;" v-html="scope.row.CalculationFormulaInfo"></span>
                    </template>
                </el-table-column>
                <el-table-column
                prop="TimeRuleInfo"
                align="center"
                header-align="center" min-width="360"
                label="时间规则">
                </el-table-column>
                <el-table-column
                prop="address"
                align="center"
                header-align="center" min-width="360"
                label="起止时间">
                    <template slot-scope="scope">
                        {{scope.row.StartingTime + '至' + scope.row.CutOffTime}}
                    </template>
                </el-table-column>
                <el-table-column
                prop="address"
                align="center" min-width="120" fixed="right"
                header-align="center"
                label="操作">
                    <template slot-scope="scope">
                        <el-button @click="flag=0;modifyRule(scope.row)" type="text" size="small">修改</el-button>
                        <el-button v-if = "scope.row.State == 100" @click="disableRule(scope.row)" type="text" size="small">禁用</el-button>
                        <el-button v-if = "scope.row.State != 100" @click="enableRule(scope.row)" type="text" size="small">启用</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 页码 -->
            <div class="page_content">
                <el-pagination class="page_left"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-size="pageSize"
                    layout="prev, pager, next"
                    :total="totalNumber">
                </el-pagination>
                <el-pagination class="page_right"
                    @size-change="handleSizeChange"
                    :page-sizes="[10, 15, 20, 30]"
                    :page-size="pageSize"
                    layout="total, sizes"
                    :total="totalNumber">
                </el-pagination>
            </div>
        </div>
        <div v-show="!showTableData">
            <el-steps class="card-step" :active="stepActive" finish-status="success" simple style="margin-top: 20px">
                <el-step title="基本信息" ></el-step>
                <el-step title="使用条件" ></el-step>
                <el-step title="时间规则" ></el-step>
                <el-step title="充值赠送" ></el-step>
            </el-steps>
                <!-- 基础信息 -->
                <el-form v-show="showBaseForm" :model="public_config" :rules="public_config_rules" ref="baseForm" label-width="108px" class="demo-ruleForm card-form base-form">
                    <el-form-item label="充值规则名称" prop="rule_name">
                        <el-input v-model="public_config.rule_name" placeholder="请输入规则名称" style="width:250px"></el-input>
                    </el-form-item>
                    <el-form-item label="起止时间" prop="date">
                        <el-date-picker v-model="public_config.date" :default-time="['00:00:00', '23:59:59']" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 360px;"></el-date-picker>
                    </el-form-item>
                    <el-form-item label="规则说明" prop="description">
                        <el-input type="textarea" v-model="public_config.description" placeholder="请输入规则说明"  :rows="5" style="width: 360px; " maxlength="100" show-word-limit></el-input>
                    </el-form-item>
                    <el-form-item label="可用油站" prop="stationList">
                        <el-cascader v-model="public_config.stationList" @change="getStationIdList" :options="stationList" :props="stationListProps" collapse-tags  style="width:250px">
                        </el-cascader>
                    </el-form-item>
                    <el-form-item label="优先级" required>
                        <el-select v-model="public_config.priority" placeholder="请选择" style="width:250px">
                            <el-option v-for="item in 10" :key="item.index" :label="item" :value="item"></el-option>
                        </el-select>
                        <i style="display:inline-block;line-height:20px;position:relative;top:10px">优先级：决定规则执行的顺序。选项为1-10，数值越小优先级越高；同等优先级的情况下，按照ID越小越优先执行。<br>建议：设置规则时从10优先级设置，方便以后增加新的高优先级规则。</i>
                    </el-form-item>
                    <el-form-item>
                        <el-button @click="resetForm()">取消</el-button>
                        <el-button type="primary" @click="baseInfoNext('baseForm')">下一步</el-button>
                    </el-form-item>
                </el-form>
                <!-- 使用条件 -->
                <div v-show="showConditionForm" class="card-form recharge-form">
                    <p class="title">使用类型</p>
                    <el-form :model="extend_rule" class="card-form rule-form">
                        <el-form-item label="客户类型:"  required>
                            <el-radio-group v-model="extend_rule.account_type" @change="changeAccountType">
                                <el-radio label="3" :disabled="isLockPrice">按会员等级</el-radio>
                                <el-radio label="1">按制卡规则</el-radio>
                                <el-radio label="0" :disabled="isLockPrice">按车队客户</el-radio>
                                <el-radio label="2" :disabled="isLockPrice">按卡组</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-form>
                    <p class="title" v-show="extend_rule.account_type==3">勾选规则对应的会员等级</p>
                    <el-form :model="extend_rule" class="card-form rule-form" v-show="extend_rule.account_type==3">
                        <el-form-item label="会员等级:" class="last-item" required>
                            <el-checkbox :indeterminate="isClassIndeterminate" v-model="checkClassAll" @change="handleCheckClassAllChange">全选</el-checkbox>
                            <el-checkbox-group style="margin-left:81px" v-model="extend_rule.GradeStr" @change="handleCheckedClassChange">
                                <el-checkbox v-for="item in classList" :label="item.id" :key="item.id">{{item.level_name}}</el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                    </el-form>
                    <p class="title" v-show="extend_rule.account_type==0">勾选规则对应的车队</p>
                    <el-form :model="extend_rule" class="card-form rule-form" v-show="extend_rule.account_type==0">
                        <el-form-item label="车队名称:" class="last-item" required>
                            <el-checkbox :indeterminate="isCompanyIndeterminate" v-model="checkCompanyAll" @change="handleCheckCompanyAllChange">全选</el-checkbox>
                            <el-checkbox-group style="margin-left:81px" v-model="extend_rule.company_id" @change="handleCheckedCompanyChange">
                                <el-checkbox v-for="item in companyList" :label="item.ID" :key="item.ID">{{item.CompanyName}}</el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                    </el-form>
                    <p class="title" v-show="extend_rule.account_type==1">勾选规则对应的卡</p>
                    <el-form :model="extend_rule" class="card-form rule-form" v-show="extend_rule.account_type==1">
                        <el-form-item label="锁价卡类型:" class="last-item" required v-if="isLockPrice">
                          <el-radio-group v-model="extend_rule.market_type" @change="handleCheckedMarketType">
                            <el-radio label="2" :disabled="!isCreate">定额卡</el-radio>
                            <el-radio label="3" :disabled="!isCreate">定升卡</el-radio>
                          </el-radio-group>
                          <i class="el-icon-warning warning-icon" style="margin-left:30px"></i>锁价类型选中提交后不可更改
                        </el-form-item>
                        <el-form-item label="卡名称:" class="last-item" required v-if="!isLockPrice">
                            <el-checkbox :indeterminate="isThemeIndeterminate" v-model="checkThemeAll" @change="handleCheckThemeAllChange">全选</el-checkbox>
                            <el-checkbox-group style="margin-left:110px" v-model="extend_rule.card_theme" @change="handleCheckedThemeChange">
                                <el-checkbox v-for="item in cardThemeRuleList" :label="item.ID" :key="item.ID">{{item.Name}}</el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                        <el-form-item label="卡名称:" class="last-item" required v-if="isLockPrice">
                            <el-checkbox :indeterminate="isThemeIndeterminate" v-model="checkThemeAll" @change="handleCheckThemeAllChange">全选</el-checkbox>
                            <el-checkbox-group style="margin-left:110px" v-model="extend_rule.card_theme" @change="handleCheckedThemeChange">
                                <el-checkbox v-for="item in cardThemeRuleList" :label="item.ID" :key="item.ID">{{item.Name}}</el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                    </el-form>
                    <p class="title" v-show="extend_rule.account_type==2">勾选规则对应的卡组</p>
                    <el-form :model="extend_rule" class="card-form rule-form" v-show="extend_rule.account_type==2">
                        <el-form-item label="卡组名称:" class="last-item" required>
                            <el-checkbox :indeterminate="isCustomerGroupIndeterminate" v-model="checkCustomerGroupAll" @change="handleCheckCustomerGroupAllChange">全选</el-checkbox>
                            <el-checkbox-group style="margin-left:100px" v-model="extend_rule.customer_group_id" @change="handleCheckedCustomerGroupChange">
                                <el-checkbox v-for="item in customerGroupList" :label="item.ID" :key="item.ID">{{item.CustomerGroupName}}</el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                    </el-form>
                    <p class="title" v-show="extend_rule.account_type==1">卡使用范围</p>
                    <el-form :model="extend_rule" class="card-form rule-form" v-show="extend_rule.account_type==1">
                        <el-form-item label="卡类型:" prop="card_type" required>
                            <el-checkbox-group v-model="extend_rule.card_type">
                                <el-checkbox label="1">个人卡</el-checkbox>
                                <el-checkbox label="2" :disabled="isLockPrice">车队卡</el-checkbox>
                                <el-checkbox label="3" :disabled="isLockPrice">不记名卡</el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                        <!-- <el-form-item label="客户分类:" prop="customer_id">
                            <el-radio-group v-model="extend_rule.customer_id_type" class="customer-type" @change="customerIdTypeChange">
                                <el-radio-button label="L">等级</el-radio-button>
                                <el-radio-button label="G">客户组</el-radio-button>
                                <el-radio-button label="C">车队客户</el-radio-button>
                                <el-radio-button label="C">车队客户</el-radio-button>
                            </el-radio-group>
                            <br>
                            <el-select v-model="extend_rule.customer_id" placeholder="请选择" class="customer-select">
                                <el-option
                                v-for="item in options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item> -->
                    </el-form>
                    <p class="title">高级设置</p>
                    <el-form :model="extend_rule" class="card-form rule-form">
                        <el-form-item label="付款方式:" required>
                            <el-checkbox :indeterminate="isPayWayIndeterminate" v-model="checkPayWayAll" @change="handleCheckPayWayAllChange">全选</el-checkbox>
                            <el-checkbox-group style="margin-left:110px" v-model="extend_rule.pay_way" @change="handleCheckedPayWayChange">
                                <el-checkbox v-for="item in payWayList" :label="item.BH" :key="item.key" >{{item.MC}}</el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                        <el-form-item label="专享设置:">
                        <el-radio-group v-model="extend_rule.is_birthday">
                            <el-radio label="0">无</el-radio>
                            <el-radio label="1">生日专享</el-radio>

                        </el-radio-group>
                        <el-tooltip placement="right" effect="light" popper-class="card-tooltip">
                            <div slot="content">生日信息来源于公众号个人中心的个人资料设置。若勾选，则<br>用户生日当天享受本条规则。</div>
                            <i class="el-icon-question" style="font-size: 18px;"></i>
                        </el-tooltip>
                        </el-form-item>
                        <el-form-item label="充值专享:">
                        <el-radio-group v-model="extend_rule.first_recharge">
                            <el-radio label="0">无</el-radio>
                            <el-radio label="1">首次充值专享</el-radio>

                        </el-radio-group>
                        <!-- <el-tooltip placement="right" effect="light" popper-class="card-tooltip">
                            <div slot="content">生日信息来源于公众号个人中心的个人资料设置。若勾选，则<br>用户生日当天享受本条规则。</div>
                            <i class="el-icon-question"></i>
                        </el-tooltip> -->
                        </el-form-item>
                    </el-form>
                    <div class="btn-box">
                        <el-button @click="conditionBefore()">上一步</el-button>
                        <el-button type="primary" @click="conditionNext()">下一步</el-button>
                    </div>
                </div>
                <!-- 时间规则 -->
                <div v-show="showtimeRuleForm" class="card-form spend-form">
                    <el-form ref="form" :model="time_rule" label-width="80px" class="time-form">
                        <el-form-item label="时间类型" style="margin-bottom: 0;">
                            <el-radio-group v-model="time_rule.type">
                                <el-radio :label="0" style="min-width: 80px">无限制</el-radio>
                                <el-radio :label="1" style="min-width: 80px">每日</el-radio>
                                <el-radio :label="2" style="min-width: 80px">每周</el-radio>
                                <el-radio :label="3" style="min-width: 80px">每月</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-form>
                    <el-repeat-time-picker
                        @change = "checkTime"
                        xstyle="width: 280px;"
                        style="margin-left: 128px;"
                        value-format="HH:mm:ss"
                        :max-length="5"
                        v-model="time_rule.selectedTime"
                        :type="time_rule.type">
                    </el-repeat-time-picker>
                    <div class="time-btn-box">
                        <el-button @click="timeRuleBefore()">上一步</el-button>
                        <el-button type="primary" @click="timeRuleNext('timeRuleForm')">下一步</el-button>
                    </div>
                </div>
                <!-- 充值赠送 -->
                <div v-show="showgivenForm" class="card-form rule-form given-form">
                    <el-radio-group v-model="charge_give_rule.type" @change="changeRadioValue" v-if="!isLockPrice">
                    <el-form :model="charge_give_rule" class="card-form" :rules="chargeGiveRules" ref="chargeGive">
                        <el-form-item label="充值赠送类型:">
                            <el-row class="card-radio-group">
                                <el-radio label="0">固定赠送</el-radio>
                                <span>充值大于等于</span>
                                <el-form-item prop="guding_min_price">
                                    <el-input onkeyup="value=value.replace(/[^\d\.]/g,'')" v-model="charge_give_rule.guding.min_price" ref="guding_min_price" style="width:88px"></el-input>
                                </el-form-item>
                                <span>元，小于</span>
                                <el-form-item prop="guding_max_price">
                                    <el-input onkeyup="value=value.replace(/[^\d\.]/g,'')" v-model="charge_give_rule.guding.max_price" ref="guding_max_price" style="width:88px"></el-input>
                                </el-form-item>
                                <span>元时，赠送</span>
                                <el-form-item prop="guding_give_money">
                                    <el-input onkeyup="value=value.replace(/[^\d\.]/g,'')" v-model="charge_give_rule.guding.give" ref="guding_give_money" style="width:88px"></el-input>
                                </el-form-item>
                                <span>元。</span>
                                <span v-if="charge_give_rule.type == 0" class="add-coupons active">需要叠加送券？<span class="add" @click="showGudingCoupons = true">添加券</span></span>
                                <span v-else class="add-coupons">需要叠加送券？<span class="add">添加券</span></span>
                            </el-row>
                            <el-row class="card-radio-group" v-show="charge_give_rule.type == 0 && showGudingCoupons">
                                <el-form-item style="margin-left: 208px;margin-top: 20px;" prop="gudingCouponsData">
                                    <el-cascader
                                    filterable
                                    @change="addTableData"
                                    v-model="charge_give_rule.gudingCouponsData"
                                    :options="couponsOptions"
                                    :props="couponsProps"
                                    ref="myGudingCascader"
                                    collapse-tags
                                    ></el-cascader>
                                </el-form-item>
                            </el-row>
                        </el-form-item>

                        <el-form-item class="item item04" v-show="charge_give_rule.type == 0 && gudingCouponsTableData.length">
                            <el-table
                            :data="gudingCouponsTableData"
                            style="width:100%">
                                <el-table-column
                                    prop="coupon_id"
                                    align="center"
                                    label="券ID">
                                </el-table-column>
                                <el-table-column
                                    prop="coupon_name"
                                    align="center"
                                    label="券名">
                                </el-table-column>
                                <el-table-column
                                    align="center"
                                    label="券额">
                                    <template slot-scope="scope">
                                      <span v-if="scope.row.price_type != 5">{{ scope.row.price_rule.price?scope.row.price_rule.price:scope.row.price_rule.discount?scope.row.price_rule.discount+'折':scope.row.price_rule.min_price+'-'+scope.row.price_rule.max_price }}</span>
                                      <span v-else>{{ scope.row.price_rule.price}}元/升</span>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    label="数量"
                                    align="center">
                                    <template slot-scope="scope">
                                        <el-input-number v-model="scope.row.num" size="small" :step="1" step-strictly :min="1" @change="changeNumber(scope.row)"></el-input-number>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    label="操作"
                                    align="center">
                                    <template slot-scope="scope">
                                        <el-button @click="handleClick(scope.row,0)" type="text" size="small">删除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-form-item>
                    </el-form>
                    <el-form :model="charge_give_rule" class="card-form" :rules="chargeGiveRules" ref="chargeGive1">
                        <el-form-item>
                            <el-row style="margin-left: 100px;" class="card-radio-group">
                                <el-radio label="1">比例赠送</el-radio>
                                <span>充值大于等于</span>
                                <el-form-item prop="bili_min_price">
                                    <el-input onkeyup="value=value.replace(/[^\d\.]/g,'')" v-model="charge_give_rule.bili.min_price" ref="bili_min_price" style="width:88px"></el-input>
                                </el-form-item>
                                <span>元，小于</span>
                                <el-form-item prop="bili_max_price">
                                    <el-input onkeyup="value=value.replace(/[^\d\.]/g,'')" v-model="charge_give_rule.bili.max_price" ref="bili_max_price" style="width:88px"></el-input>
                                </el-form-item>
                                <span>元时，赠送</span>
                                <el-form-item prop="bili_give_money">
                                    <el-input onkeyup="value=value.replace(/[^\d\.]/g,'')" v-model="charge_give_rule.bili.give" ref="bili_give_money" style="width:88px"></el-input>
                                </el-form-item>
                                <span>%。</span>
                                <span v-if="charge_give_rule.type == 1" class="add-coupons active">需要叠加送券？<span class="add" @click="showGudingCoupons = true">添加券</span></span>
                                <span v-else class="add-coupons">需要叠加送券？<span class="add">添加券</span></span>
                            </el-row>
                            <el-row class="card-radio-group" v-show="charge_give_rule.type == 1 && showGudingCoupons">
                                <el-form-item style="margin-left: 208px;margin-top: 20px;" prop="addCouponsData">
                                    <el-cascader
                                    filterable
                                    @change="addTableData"
                                    v-model="charge_give_rule.addCouponsData"
                                    :options="couponsOptions"
                                    :props="couponsProps"
                                    ref="myAddCascader"
                                    collapse-tags
                                    ></el-cascader>
                                </el-form-item>
                            </el-row>
                        </el-form-item>
                        <el-form-item class="item item04" v-show="charge_give_rule.type == 1 && addCouponsTableData.length">
                            <el-table
                            :data="addCouponsTableData"
                            style="width:100%">
                                <el-table-column
                                    prop="coupon_id"
                                    align="center"
                                    label="券ID">
                                </el-table-column>
                                <el-table-column
                                    prop="coupon_name"
                                    align="center"
                                    label="券名">
                                </el-table-column>
                                <el-table-column
                                    align="center"
                                    label="券额">
                                    <template slot-scope="scope">
                                      <span v-if="scope.row.price_type != 5">{{ scope.row.price_rule.price?scope.row.price_rule.price:scope.row.price_rule.discount?scope.row.price_rule.discount+'折':scope.row.price_rule.min_price+'-'+scope.row.price_rule.max_price }}</span>
                                      <span v-else>{{ scope.row.price_rule.price}}元/升</span>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    label="数量"
                                    align="center">
                                    <template slot-scope="scope">
                                        <el-input-number v-model="scope.row.num" size="small" :step="1" step-strictly :min="1" :max="100" @change="changeNumber(scope.row)"></el-input-number>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    label="操作"
                                    align="center">
                                    <template slot-scope="scope">
                                        <el-button @click="handleClick(scope.row,1)" type="text" size="small">删除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-form-item>
                    </el-form>
                    <el-form :model="charge_give_rule" class="card-form" :rules="chargeGiveRules" ref="chargeGive2">
                        <el-form-item class="item item03">
                            <el-radio label="2">充值送券</el-radio>
                            <span>充值大于等于</span>
                            <el-form-item>
                                <el-input v-model="charge_give_rule.coupons.min_price" ref="coupons_min_price" style="width:88px"></el-input>
                            </el-form-item>
                            <span>元，小于</span>
                            <el-form-item>
                                <el-input v-model="charge_give_rule.coupons.max_price" ref="coupons_max_price" style="width:88px"></el-input>
                            </el-form-item>
                            <span>元时，</span>
                            <el-form-item prop="couponsData">
                                <el-cascader
                                filterable
                                @change="addTableData"
                                v-model="charge_give_rule.couponsData"
                                :options="couponsOptions"
                                :props="couponsProps"
                                ref="myCascader"
                                collapse-tags
                                ></el-cascader>
                            </el-form-item>
                        </el-form-item>
                        <el-form-item class="item item04" v-show="charge_give_rule.type == 2 && couponsTableData.length">
                            <el-table
                            :data="couponsTableData"
                            style="width:100%">
                                <el-table-column
                                    prop="coupon_id"
                                    align="center"
                                    label="券ID">
                                </el-table-column>
                                <el-table-column
                                    prop="coupon_name"
                                    align="center"
                                    label="券名">
                                </el-table-column>
                                <el-table-column
                                    align="center"
                                    label="券额">
                                    <template slot-scope="scope">
                                        <span v-if="scope.row.price_type != 5">{{ scope.row.price_rule.price?scope.row.price_rule.price:scope.row.price_rule.discount?scope.row.price_rule.discount+'折':scope.row.price_rule.min_price+'-'+scope.row.price_rule.max_price }}</span>
                                        <span v-else>{{ scope.row.price_rule.price}}元/升</span>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    label="数量"
                                    align="center">
                                    <template slot-scope="scope">
                                        <el-input-number v-model="scope.row.num" size="small" :step="1" step-strictly :min="1" :max="100" @change="changeNumber(scope.row)"></el-input-number>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    label="操作"
                                    align="center">
                                    <template slot-scope="scope">
                                        <el-button @click="handleClick(scope.row,2)" type="text" size="small">删除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-form-item>


                    </el-form>
                    </el-radio-group>
                    <!-- 锁价卡赠送类型 -->
                    <el-form :model="charge_give_rule" class="card-form" :rules="chargeGiveRules" ref="chargeGive" v-if="isLockPrice">
                      <el-form-item label="充值赠送:"  class="item03" style="margin-left: 0;">
                        <span v-if="extend_rule.market_type == 2">销售单价</span>
                        <span v-if="extend_rule.market_type == 3">销售油量</span>
                        <el-form-item class="item05">
                          <el-input v-if="extend_rule.market_type == 2" v-model="UnitPrice" style="width:88px" @input="calculationRules"></el-input>
                          <el-input v-if="extend_rule.market_type == 3" v-model="sellPrice" style="width:88px" @input="calculationRules"></el-input>
                        </el-form-item>
                        <span v-if="extend_rule.market_type == 2">元/升，</span>
                        <span v-if="extend_rule.market_type == 3">升，</span>
                        <span>销售价格</span>
                        <el-form-item class="item05">
                          <el-input v-model="SumPrice" style="width:88px" @input="calculationRules"></el-input>
                        </el-form-item>
                        <span>元，</span>
                        <span v-if="extend_rule.market_type == 2">赠送金额</span>
                        <span v-if="extend_rule.market_type == 3">赠送油量</span>
                        <el-form-item class="item05">
                          <el-input v-model="giveNumber" style="width:88px" @input="calculationRules"></el-input>
                        </el-form-item>
                        <span v-if="extend_rule.market_type == 2">元，</span>
                        <span v-if="extend_rule.market_type == 3">升</span>
                        <span v-if="extend_rule.market_type == 2">折后单价</span>
                        <el-form-item class="item05" v-if="extend_rule.market_type == 2">
                          <el-input v-model="salePrice" style="width:88px;color:#32af50" :disabled="true"></el-input>
                        </el-form-item>
                        <span v-if="extend_rule.market_type == 2">元/升</span>
                      </el-form-item>
                      <p v-if="extend_rule.market_type == 3" class="ruleItem">
                        油量 <span class="dataBase">{{(+sellPrice + +giveNumber).toFixed(2)}}</span> 升，
                        本金 <span class="dataBase">{{SumPrice}}</span> 元，
                        赠金 <span class="dataBase">{{givePrice}}</span> 元，
                        销售单价：<span class="dataBase">{{sellPrice == 0 || SumPrice == 0 ? 0 : (SumPrice/sellPrice).toFixed(2)}}</span> 元/升，
                        折后单价 <span class="dataBase">{{salePrice}}</span> 元/升
                        </p>
                    </el-form>

                    <!-- 充值送券次数限制-->
                    <el-form class="card-form" :model="recharge" :rules="rechargeRules" ref="recharge">
                      <el-form-item label="赠送次数限制:" class="item03" style="margin-left: 0;"  v-show="!isLockPrice">
                        <span>账户/卡</span>
                        <el-form-item prop="num" class="item05">
                          <el-input v-model="recharge.num" style="width:88px"></el-input>
                        </el-form-item>

                        <span>个</span>
                        <el-form-item prop="type" class="item05">
                          <el-select v-model="recharge.type" style="width:88px">
                            <el-option
                              v-for="item in couponDeliveryOptions"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value">
                            </el-option>
                          </el-select>
                        </el-form-item>
                        <span>内最多可享受</span>
                        <el-form-item prop="count" class="item05">
                          <el-input v-model="recharge.count"  style="width:88px"></el-input>
                        </el-form-item>
                        <span>次充值赠送，</span>
                        <span>本赠送活动最多可享受</span>
                        <el-form-item prop="total" class="item05">
                          <el-input v-model="recharge.total"  style="width:88px"></el-input>
                        </el-form-item>
                        <span>次。</span>
                        <span>填0为不限制</span>
                        <el-tooltip placement="right" effect="light" popper-class="card-rule-tooltip">
                            <div slot="content">
                              <p>例：账户/卡1个自然月内最多可享受3次充值赠送，本赠送活动最多可享受6次。</p>
                              <p>
                                账户/卡在2月29日充值第一次享受本规则，则2.29-3.30的前3次充值都可享受本充值规则的赠送。
                                赠送周期为2.29-3.30，3.31-4.29以此类推到规则生效时间的最后一天。赠送次数累计6次后，不再赠送。
                              </p>
                              </div>
                            <i class="el-icon-question" style="font-size: 18px;"></i>
                        </el-tooltip>
                      </el-form-item>
                      <el-form-item class="last-form-item">
                            <el-button @click="givenBefore">上一步</el-button>
                            <el-button :disabled="btnDisabled" v-if="isCreate" type="primary" @click="create">创建</el-button>
                            <el-button :disabled="btnDisabled" v-else type="primary" @click="save">确认</el-button>
                      </el-form-item>
                  </el-form>
                </div>
        </div>
    </div>
</template>

<script>
import {mapGetters} from 'vuex'
export default {
    name: 'CardRule',
    data() {
        var checkGudingMinPrice = (rule, value, callback) => {
            if(this.charge_give_rule.type == 0){
                if (String(this.charge_give_rule.guding.min_price) != "") {
                    callback();
                }else{
                    callback(new Error('请输入数值'));
                }
            }
        };
        var checkGudingMaxPrice = (rule, value, callback) => {
            if(this.charge_give_rule.type == 0){
                if (String(this.charge_give_rule.guding.max_price) != "") {
                    if(Number(this.charge_give_rule.guding.max_price) > Number(this.charge_give_rule.guding.min_price)){
                        callback();
                    }else{
                        callback(new Error('最小值必须小于最大值'));
                    }
                }else{
                    callback(new Error('请输入数值'));
                }
            }
        };
        var checkGudingGiveMoney = (rule, value, callback) => {
            if(this.charge_give_rule.type == 0){
                if (String(this.charge_give_rule.guding.give) != "") {
                    callback();
                }else{
                    callback(new Error('请输入数值'));
                }
            }
        };
        var checkBiliMinPrice = (rule, value, callback) => {
            if(this.charge_give_rule.type == 1){
                if (String(this.charge_give_rule.bili.min_price) != "") {
                    callback();
                }else{
                    callback(new Error('请输入数值'));
                }
            }
        };
        var checkBiliMaxPrice = (rule, value, callback) => {
            if(this.charge_give_rule.type == 1){
                if (String(this.charge_give_rule.bili.max_price) != "") {
                    if(Number(this.charge_give_rule.bili.max_price) > Number(this.charge_give_rule.bili.min_price)){
                        callback();
                    }else{
                        callback(new Error('最小值必须小于最大值'));
                    }
                }else{
                    callback(new Error('请输入数值'));
                }
            }
        };
        var checkBiliGiveMoney = (rule, value, callback) => {
            if(this.charge_give_rule.type == 1){
                if (String(this.charge_give_rule.bili.give) != "") {
                    callback();
                }else{
                    callback(new Error('请输入数值'));
                }
            }
        };
        var checkCouponsMinPrice = (rule, value, callback) => {
            if(this.charge_give_rule.type == 1){
                if (String(this.charge_give_rule.coupons.min_price) != "") {
                    callback();
                }else{
                    callback(new Error('请输入数值'));
                }
            }
        };
        var checkCouponsMaxPrice = (rule, value, callback) => {
            if(this.charge_give_rule.type == 1){
                if (String(this.charge_give_rule.coupons.max_price) != "") {
                    if(Number(this.charge_give_rule.coupons.max_price) > Number(this.charge_give_rule.coupons.min_price)){
                        callback();
                    }else{
                        callback(new Error('最小值必须小于最大值'));
                    }
                }else{
                    callback(new Error('请输入数值'));
                }
            }
        };
        var checkCouponsData = (rule, value, callback) => {
            if(this.charge_give_rule.type == 2){
                if (value.length>0) {
                    callback();
                }else{
                    callback(new Error('请选择营销券'));
                }
            }
        };
        // var checkLockPrice = (rule, value, callback)=>{
        //     let regNumber=/^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/;
        //     if(regNumber.test(this.sellPrice) == false){
        //         callback(new Error('请输入正数'))
        //     }
        //     if(regNumber.test(this.UnitPrice) == false){
        //         callback(new Error('请输入正数'))
        //     }
        // };
        // var checkGiveNumber = (rule, value, callback)=>{
        //     let regNumber=/^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/;
        //     if(regNumber.test(this.giveNumber) == false){
        //         callback(new Error('请输入正数'))
        //     }
        // }
        // var checkSumPrice = (rule, value, callback)=>{
        //     let regNumber=/^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/;
        //     if(regNumber.test(this.SumPrice) == false){
        //         callback(new Error('请输入正数'))
        //     }
        // }
        return {
            showLockRule:false,
            isCreate:true,//这个字段判断是创建还是修改
            cardId:"",//规则id
            state:0,//当前状态默认全部为0
            stateList:[
                {
                    value:0,
                    label:"全部"
                },
                {
                    value:100,
                    label:"启用"
                },
                {
                    value:101,
                    label:"禁用"
                },
            ],
            // typeList:[
            //     {
            //         value:0,
            //         label:'全部'
            //     },
            //     {
            //         value:1,
            //         label:'通用规则'
            //     },
            //     {
            //         value:2,
            //         label:'锁价规则'
            //     }
            // ],
            // cardType:0,//当前类型默认为0  0：全部，1：通用规则，2：锁价规则（定额、定升)
            station_id:0,//当前油站id默认全部0
            currentPage:1,//当前页面
            showTableData:true,//默认显示表格数据
            pageSize:10,
            totalNumber:1,//数据总条数
            tableData: [],
            loading:true,
            stepActive:0,//默认显示第一个步
            showBaseForm:false,//默认显示基础信息
            showConditionForm:false,//使用条件默认隐藏
            showtimeRuleForm:false,//时间规则默认隐藏
            showgivenForm:false,//充值赠送默认隐藏
            public_config:{
                rule_name:"",
                date:[],
                description:"",
                stationList:[],
                priority:"10"
            },//基础信息数据
            public_config_rules:{
                rule_name:[
                    { required: true, message: '请输入规则名称', trigger: 'blur' },
                    { max: 50, message: '名称不能超过50个字符', trigger: 'blur' }
                ],
                date: [
                    { required: true, message: '请选择日期', trigger: 'blur' }
                ],
                stationList: [
                    { required: true, message: '请选择油站', trigger: 'change' }
                ],
                description:[
                    {max: 100, message: '规则不能超过100个字符', trigger: 'blur' }
                ]
            },
            stationList:[],//可用油站列表
            stationListProps:{
                multiple: true,
                label: "stname",
                value: "stid",
            },
            oilList:[],//可用项目列表
            oilListProps:{
                multiple: true,
                label: "name",
                value: "oil_id",
                children: "parent_arr"
            },
            extend_rule:{
                card_theme:[],
                customer_group_id:[],
                card_type:[],
                customer_id_type:"L",
                customer_id:"",
                pay_way:[],
                is_birthday:"0",
                first_recharge: "0",
                account_type:"1",
                company_id:[],
                GradeStr:[],

                market_type: "2", //锁价卡类型
            },//使用条件数据
            extendRules:{
                customer_id_type:[
                    { required: true, message: '请选择', trigger: 'blur' }
                ]
            },
            cardThemeRuleList:[],//卡名称列表
            allCardThemeRuleList:[],//所有名称列表
            customerGroupList:[],//卡组列表
            companyList:[],//车队列表
            classList:[],//等级列表
            payWayList:[],//付款方式列表
            options:[],//客户分类选择数据
            couponsOptions: [],//营销券列表
            couponsProps: {
                multiple: true,
                label:"coupon_name",
                value:"coupon_id"
            },//营销券列表
            timeRuleForm:{

            },//时间规则数据
            time_rule:{
                type:0,
                selectedTime:[],
            },
            isCheckTime:false,//检查时间规则合格的变量
            charge_give_rule:{
                type:"0",
                guding:{
                    max_price:"",
                    min_price:"",
                    give:""
                },
                bili:{
                    max_price:"",
                    min_price:"",
                    give:""
                },
                coupons:{
                    max_price:"",
                    min_price:"",
                    give:""
                },
                gudingCouponsData:[],
                addCouponsData:[],
                couponsData:[],
            },
            chargeGiveRules:{
                guding_max_price:[
                    {validator: checkGudingMaxPrice, trigger: 'blur'}
                ],
                guding_min_price:[
                    {validator: checkGudingMinPrice, trigger: 'blur'}
                ],
                guding_give_money:[
                    {validator: checkGudingGiveMoney, trigger: 'blur'}
                ],
                bili_max_price:[
                    {validator: checkBiliMaxPrice, trigger: 'blur'}
                ],
                bili_min_price:[
                    {validator: checkBiliMinPrice, trigger: 'blur'}
                ],
                bili_give_money:[
                    {validator: checkBiliGiveMoney, trigger: 'blur'}
                ],
                coupons_max_price:[
                    {validator: checkCouponsMaxPrice, trigger: 'blur'}
                ],
                coupons_min_price:[
                    {validator: checkCouponsMinPrice, trigger: 'blur'}
                ],
                couponsData:[
                    {validator: checkCouponsData, trigger: 'change'}
                ],
                // lockPriceRule:[
                //     {validator: checkLockPrice, trigger: 'blur'}
                // ],
                // lockSumPrice:[
                //     {validator: checkSumPrice, trigger: 'blur'}
                // ],
                // lockGiveNumber:[
                //     {validator: checkGiveNumber, trigger: 'blur'}
                // ],
            },
            couponsTableData:[],//选中的营销券数据列表
            gudingCouponsTableData:[],//选中的营销券数据列表
            addCouponsTableData:[],//选中的营销券数据列表
            btnDisabled:false,//创建按钮禁止，默认可以点击

            isPayWayIndeterminate:false,//付款方式全选
            checkPayWayAll:false,
            isThemeIndeterminate:false,//卡主题全选
            checkThemeAll:false,
            isCompanyIndeterminate:false,//车队卡全选
            checkCompanyAll:false,
            isCustomerGroupIndeterminate:false,//卡组全选
            checkCustomerGroupAll:false,
            isClassIndeterminate:false,//等级全选
            checkClassAll:false,

            showGudingCoupons:false,
            searchTxt:"",//搜索关键词

            //送券规则限制
            recharge:{
              num: "0",
              type: "month",
              count: "0",
              total: "0"
            },
            rechargeRules: {
              num: [
                { required: true, message: '输入用户个数', trigger: 'blur' },
                { pattern: /^(0|[1-9]\d?|1000)$/, message: '范围在0-1000', trigger: 'blur'}
              ],
              type: [
                { required: true, message: '请选择类型', trigger: 'blur' }
              ],
              count: [
                { required: true, message: '请输入充值赠送次数', trigger: 'blur' },
                { pattern: /^(0|[1-9]\d?|1000)$/, message: '范围在0-1000', trigger: 'blur'}
              ],
              total: [
                { required: true, message: '请输入最多赠送次数', trigger: 'blur' },
                { pattern: /^(0|[1-9]\d?|1000)$/, message: '范围在0-1000', trigger: 'blur'}
              ]
            },
            couponDeliveryOptions: [
              {
                value: 'month',
                label: '自然月'
              }, {
                value: 'week',
                label: '自然周'
              }, {
                value: 'day',
                label: '自然日'
              }
            ], //送券次数限制类型选项

            isLockPrice: false, //锁价卡规则
            sellPrice: 0, //销售油量
            giveNumber: 0, //赠送金额
            SumPrice: 0, //销售总价
            UnitPrice: 0,//销售单价
            // salePrice:0,//折后单价
            flag: 0, // 修改的判断
            newThemeList: [],//卡名称
        }
    },
    async mounted() {
        if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
            return false;
        }
        this.getGroupBaseInfo();
        this.getStationList();
        this.getCouponsList();
        this.getCardRuleLists();//获取卡规则列表
        this.getChargeRuleTem();//获取支付方式
        await this.getCardThemeRuleList();//初始化卡名称列表
        this.getCustomerGroupList();//初始化卡组列表
        this.getCompanyList();//初始化卡名称列表
        console.log(this.$route.hash);
        if(this.$route.hash == '#goToCreateLockRule'){
            this.goToCreateLockRule()
        }
    },
    activated(){
        console.log('hash',this.$route.hash);
        console.log('query',this.$route.query);
        let id = this.$route.query.id
        this.showBaseForm = true;
        this.showConditionForm = false;
        this.showtimeRuleForm = false;
        this.showgivenForm = false;
        this.stepActive = 0
        if(this.$route.hash == '#goToCreateLockRule' && !id){
            this.goToCreateLockRule()
        }else if(this.$route.hash == '#goToCreateLockRule' && id){
            this.flag = 1
            this.modifyRule(id)
        }
    },
    computed:{
        ...mapGetters({
            "getCurrentStation":"getCurrentStation"
        }),
        salePrice(){ // 折后单价
            if(this.extend_rule.market_type == 3){
                if(Number(this.sellPrice) + Number(this.giveNumber) == 0 ){
                    return 0
                }else{
                    return (+this.SumPrice/(+this.sellPrice+ +this.giveNumber)).toFixed(2)
                }
            }else{ //定额：销售总价?【（销售总价+赠送金额）?销售单价】
                if(Number(this.UnitPrice) == 0 || (Number(this.SumPrice) + Number(this.giveNumber))/ Number(this.UnitPrice) == 0 ){
                    return 0
                }else{
                    return (Number(this.SumPrice) /( (Number(this.SumPrice) + Number(this.giveNumber))/ Number(this.UnitPrice) )).toFixed(2)
                }
            }
        },
        givePrice(){ // 赠金
            if(+this.sellPrice == 0 || this.SumPrice / this.sellPrice == 0){
                return 0
            }else{
                return (this.giveNumber*(this.SumPrice/this.sellPrice).toFixed(2)).toFixed(2)
            }
        }
    },
    methods: {
      async getGroupBaseInfo() {
        try {
          const res = await this.$axios.post('/Ostn/getGroupBaseInfo')
          if(res.data.status != 200) return this.$message.error(res.data.info)
          this.showLockRule = res.data.data.is_show_create_lock_price == 1
        } catch (e) {
          this.showLockRule = false
          this.$message.error('网络错误！')
        }
      },
        //获取卡规则列表
        getCardRuleLists(){
            this.loading = true;
            this.$axios.post("/CardRule/getCardChargeRuleList",{
                state:this.state,
                station_id:this.station_id,
                page:this.currentPage,
                page_size:this.pageSize,
                ruleName:this.searchTxt,
                RuleType:this.cardType,
                accurate_name:3, //0:查询充值直降、锁价规则 1：查询充值直降 2：查询锁价规则 3：查询通用规则
            }).then((res)=>{
                this.loading = false;
                if(res.data.status == 200){
                    this.tableData = res.data.data.dt;
                    this.totalNumber = res.data.data.TotalQty;
                }else{
                    this.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
        },
        //页面切换
        handleCurrentChange(val){
            this.currentPage = val;
            this.getCardRuleLists();
        },
        handleSizeChange(val){
            this.pageSize = val;
            this.getCardRuleLists();
        },
        //状态切换
        stateChange(val){
            this.currentPage = 1;
            this.getCardRuleLists();
        },
        //油站切换
        stationChange(val){
            this.currentPage = 1;
            this.getCardRuleLists();
        },
        //类型切换
        // typeChange(val){
        //     this.currentPage = 1;
        //     this.getCardRuleLists();
        // },
        //修改规则
        async modifyRule(id){
            await this.getCardThemeRuleList()
            console.log('flag',this.flag)
            if(this.flag==0){
                this.cardId = id.ID;
                console.log('cardThemeRuleList111',this.cardThemeRuleList)
                this.cardThemeRuleList = this.cardThemeRuleList.filter(v => v.Type == id.Type)
                console.log('cardThemeRuleList111',this.cardThemeRuleList)
                this.newThemeList = this.cardThemeRuleList
            } else{
                this.cardId = id;
            }
            console.log('id',id)
            console.log(this.cardId)
            this.isCreate = false;
            this.$axios.post('/CardRule/getCardChargeRuleInfo',{
            id:this.cardId
            }).then((res)=>{
                if(res.data.status == 200){
                    let data = res.data.data;

                    //基础信息数据填充
                    this.public_config.rule_name = data.public_config.rule_name;
                    this.public_config.description = data.public_config.description;
                    this.public_config.priority = data.public_config.priority;
                    this.public_config.date = [data.public_config.start_time,data.public_config.end_time];
                    this.public_config.stationList = [];//清空数据
                    if(data.public_config.use_station_list && data.public_config.use_station_list.length > 0){
                        data.public_config.use_station_list.forEach((element)=>{
                            this.public_config.stationList.push([element])
                        })
                    }else{
                        this.public_config.stationList = [];
                    }

                    //使用条件数据填充
                    //卡主题
                    if(data.extend_rule.card_theme.length == this.cardThemeRuleList.length){
                        this.checkThemeAll = true;
                        this.isThemeIndeterminate = false;
                    }else{
                        this.isThemeIndeterminate = true;
                        this.checkThemeAll = false;
                    }
                    this.extend_rule.card_theme = data.extend_rule.card_theme;
                    //卡组
                    let customerArarry = [];
                    if(data.extend_rule.customer_group_id){
                        data.extend_rule.customer_group_id.map((item)=>{
                            customerArarry.push(Number(item));
                        })
                    }
                    if(customerArarry.length == this.customerGroupList.length){
                        this.checkCustomerGroupAll = true;
                        this.isCustomerGroupIndeterminate = false;
                    }else{
                        this.isCustomerGroupIndeterminate = true;
                        this.checkCustomerGroupAll = false;
                    }
                    this.extend_rule.customer_group_id = customerArarry;
                    //车队
                    let companyArarry = [];
                    if(data.extend_rule.company_id){
                        data.extend_rule.company_id.map((item)=>{
                            companyArarry.push(Number(item));
                        })
                    }
                    if(companyArarry.length == this.companyList.length){
                        this.checkCompanyAll = true;
                        this.isCompanyIndeterminate = false;
                    }else{
                        this.isCompanyIndeterminate = true;
                        this.checkCompanyAll = false;
                    }
                    this.extend_rule.company_id = companyArarry;
                    //会员等级
                    let gradeArarry = [];
                    if(data.extend_rule.gradeStr){
                        data.extend_rule.gradeStr.map((item)=>{
                            gradeArarry.push(Number(item));
                        })
                    }
                    if(gradeArarry.length == this.classList.length){
                        this.checkClassAll = true;
                        this.isClassIndeterminate = false;
                    }else{
                        this.isClassIndeterminate = true;
                        this.checkClassAll = false;
                    }
                    this.extend_rule.GradeStr = gradeArarry;

                    this.extend_rule.card_type = data.extend_rule.card_type;
                    this.extend_rule.customer_id = data.extend_rule.customer_id;
                    this.extend_rule.customer_id_type = data.extend_rule.customer_id_type;
                    this.extend_rule.is_birthday = String(data.extend_rule.is_birthday);
                    //判断首次充值字段是否为空
                    if(data.extend_rule.first_recharge !== undefined){
                      this.extend_rule.first_recharge = String(data.extend_rule.first_recharge);
                    }
                    this.extend_rule.account_type = String(data.extend_rule.account_type);
                    if(data.extend_rule.pay_way){
                        if(data.extend_rule.pay_way.length == this.payWayList.length){
                            this.checkPayWayAll = true;
                            this.isPayWayIndeterminate = false;
                        }else{
                            this.checkPayWayAll = false;
                            this.isPayWayIndeterminate = true;
                        }
                        this.extend_rule.pay_way = data.extend_rule.pay_way;
                    }else{
                        this.extend_rule.pay_way = [];
                    }

                    //时间规则数据填充
                    this.time_rule.selectedTime = [];
                    if(data.time_rule.type == 1){
                        this.time_rule.type = 0;
                        this.time_rule.rule = [];
                    }
                    if(data.time_rule.type == 2){
                        this.time_rule.type = 1;
                        data.time_rule.rule.forEach((element)=>{
                            this.time_rule.selectedTime.push({
                                time:[
                                    element.start_time,
                                    element.end_time
                                ]
                            })
                        })
                    };
                    if(data.time_rule.type == 3){
                        this.time_rule.type = 2;
                        data.time_rule.rule.forEach((element)=>{
                            this.time_rule.selectedTime.push({
                                date:element.date,
                                time:[
                                    element.start_time,
                                    element.end_time
                                ]
                            })
                        })
                    };
                    if(data.time_rule.type == 4){
                        this.time_rule.type = 3;
                        data.time_rule.rule.forEach((element)=>{
                            this.time_rule.selectedTime.push({
                                date:element.date,
                                time:[
                                    element.start_time,
                                    element.end_time
                                ]
                            })
                        })
                    };

                    //充值赠送数据填充
                    this.charge_give_rule.type = String(data.charge_give_rule.type);
                    if(data.charge_give_rule.data.market_type != 1){
                      this.isLockPrice = true;
                    }
                    if(data.charge_give_rule.data.market_type == 1){
                      let arr = this.allCardThemeRuleList;
                      this.cardThemeRuleList = arr;
                      this.isLockPrice = false;
                    }
                    if(data.charge_give_rule.type == 0){
                        this.charge_give_rule.guding.max_price = data.charge_give_rule.data.max_price;
                        this.charge_give_rule.guding.min_price = data.charge_give_rule.data.min_price;
                        this.charge_give_rule.guding.give = data.charge_give_rule.data.give.number;

                        //锁价卡编辑时的回显
                        if(this.isLockPrice){
                            this.sellPrice = this.charge_give_rule.guding.max_price;
                            this.SumPrice = data.charge_give_rule.data.SumPrice;
                            this.UnitPrice = (+data.charge_give_rule.data.UnitPrice).toFixed(2);
                            this.extend_rule.market_type = data.charge_give_rule.data.market_type;
                            this.giveNumber = data.charge_give_rule.data.give.number;
                            // this.salePrice = (this.SumPrice/(this.sellPrice+this.giveNumber)).toFixed(2)
                            this.handleCheckedMarketType();
                          }
                    }
                    if(data.charge_give_rule.type == 1){
                        this.charge_give_rule.bili.max_price = data.charge_give_rule.data.max_price;
                        this.charge_give_rule.bili.min_price = data.charge_give_rule.data.min_price;
                        this.charge_give_rule.bili.give = data.charge_give_rule.data.give.number;
                    }
                    if(data.charge_give_rule.type == 2){
                        this.charge_give_rule.coupons.max_price = data.charge_give_rule.data.max_price;
                        this.charge_give_rule.coupons.min_price = data.charge_give_rule.data.min_price;
                        this.charge_give_rule.coupons.give = data.charge_give_rule.data.give.number;
                    }
                    this.handelCouponlist(data.charge_give_rule.data.give.couponlist , data.charge_give_rule.type)

                    //充值送券规则限制
                    if(data.charge_give_rule.recharge !== undefined){
                      this.recharge.num = data.charge_give_rule.recharge.num;
                      this.recharge.type = data.charge_give_rule.recharge.type;
                      this.recharge.count = data.charge_give_rule.recharge.count;
                      this.recharge.total = data.charge_give_rule.recharge.total;
                    }


                    this.showTableData = false;
                    this.showBaseForm = true;

                    // 编辑回显判断是否是锁价卡规则
                }else{
                    this.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
        },
        //处理修改返回的券信息，渲染到页面
        handelCouponlist(data,type){
            let coupons = [];
            let couponsArray = []; //渲染选中列表所需参数
            this.charge_give_rule.gudingCouponsData = [];
            this.charge_give_rule.addCouponsData = [];
            this.charge_give_rule.couponsData = [];
            data.forEach((element)=>{
                coupons.push(element.coupon_id);
                couponsArray.push([element.coupon_id]);
                this.couponsOptions.forEach((sub)=>{
                    if(sub.coupon_id == element.coupon_id){
                        sub.num = element.coupon_num;
                    }
                })
            })
            if(type == 0){
                this.charge_give_rule.gudingCouponsData = coupons;
            }else if (type == 1){
                this.charge_give_rule.addCouponsData = coupons;
            }else{
                this.charge_give_rule.couponsData = coupons;
            }
            this.addTableData(couponsArray);
        },
        //禁用规则
        disableRule(val){
            this.$confirm('是否禁用“'+val.Name+'”？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$axios.post("/CardRule/changeCardChargeRuleState",{
                    state:101,
                    id:val.ID,
                }).then((res)=>{
                    if(res.status == 200){
                        this.getCardRuleLists();
                    }
                })
            }).catch(() => {
            });
        },
        //启用规则
        enableRule(val){
            this.$confirm('是否启用“'+val.Name+'”？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$axios.post("/CardRule/changeCardChargeRuleState",{
                    state:100,
                    id:val.ID,
                }).then((res)=>{
                    if(res.status == 200){
                        this.getCardRuleLists();
                    }
                })
            }).catch(() => {
            });
        },
        //去创建规则
        goToCreateRule(){
            this.showTableData = false;
            this.showBaseForm = true;
            this.isCreate = true;
            this.isLockPrice = false;

            this.clearFormData();//清空数据
            this.extend_rule.card_type = ["1","2","3"];
            this.cardThemeRuleList = [...this.allCardThemeRuleList];
            this.cardThemeRuleList = this.cardThemeRuleList.filter(item => item.Type == 1)
            this.$forceUpdate()
        },
        //创建锁价规则
        goToCreateLockRule(){
            this.showTableData = false;
            this.showBaseForm = true;
            this.isCreate = true;
            this.isLockPrice = true;

            console.log(this.extend_rule.card_type);
            this.clearFormData();//清空数据
            this.extend_rule.card_type = ["1"];
            this.extend_rule.market_type = '2' //创建锁价默认进入的时候为定额
            let arr = this.allCardThemeRuleList;
            this.cardThemeRuleList = arr;
            this.cardThemeRuleList = this.cardThemeRuleList.filter(item => item.Type == 2)
            console.log(this.cardThemeRuleList);
            this.$forceUpdate()
        },
        //取消
        resetForm(){
            this.showBaseForm = false;
            this.showTableData = true;
            if(this.$route.hash == '#goToCreateLockRule'){
                this.$router.push('/MarketingRules')
            }
        },
        //基础信息下一步
        baseInfoNext(formName){
            console.log('hash',this.$route.hash)
            if(this.newThemeList.length > 0 && !this.$route.hash){
                this.cardThemeRuleList = this.newThemeList
                console.log('cardThemeRuleList222',this.cardThemeRuleList);
            }
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.stepActive = 1;
                    this.showBaseForm = false;
                    this.showConditionForm = true;
                }
            })
        },
        //获取可用油站
        getStationList(){
            this.$axios.post('/Stations/getStationList',{}).then((res)=>{
                if(res.status == 200){
                    this.stationList = res.data.data;
                    this.stationList.forEach((element)=>{
                        this.public_config.stationList.push([element.stid]);
                    });
                }
            })
        },
        //获取卡列表,根据选择的可用油站获取相应的列表
        getStationIdList(val){
            let stationIdList = [];//当前选择的可用油站id集合
            val.forEach((element)=>{
                stationIdList.push(element[0]);
            });
        },
        //获取卡列表,根据选择的可用油站获取相应的列表
        async getCardThemeRuleList(){
            /*接口做了分页，暂时定义了500条数据 */
            let that = this;
            await that.$axios.post('/CardRule/getCardThemeRuleList',{
                state:100,
                station_id:0,
                page:1,
                page_size:500,
            }).then((res)=>{
                if(res.data.status == 200){
                    that.cardThemeRuleList = res.data.data.dt;
                    that.allCardThemeRuleList = res.data.data.dt;
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
        },
        //获取卡列表,根据选择的可用油站获取相应的列表
        getCustomerGroupList(){
            /*接口做了分页，暂时定义了500条数据 */
            let that = this;
            that.$axios.post('/CustomerGroup/getCustomerGroupList',{
                page:1,
                page_size:500,
            }).then((res)=>{
                if(res.data.status == 200){
                    that.customerGroupList = res.data.data.dt;
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
        },
        //获取车队信息列表
        getCompanyList(){
            let that = this;
            that.$axios.post('/CompanyCard/getSimpleCompanyList', {
                page: 1,
                page_size: 1250,
                input: "",
                state:100
            })
            .then(function (res) {
                if(res.data.status == 200){
                    that.companyList = res.data.data.dt;
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
            });
        },
        //获取付款方式列表
        getChargeRuleTem(){
            this.$axios.post('/CardRule/getChargeRuleTem',{}).then((res)=>{
                if(res.data.status == 200){
                    this.payWayList = res.data.data.pay_way;
                    this.classList = res.data.data.level;
                }else{
                    this.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
        },
        //切换客户分类渲染
        customerIdTypeChange(val){
            this.getCustomerIdList(val);
        },
        //渲染客户分类内容
        getCustomerIdList(val){

        },
        //使用条件上一步
        conditionBefore(){
            this.stepActive = 0;
            this.showBaseForm = true;
            this.showConditionForm = false;
        },
        //使用条件下一步
        conditionNext(){
            if(this.extend_rule.account_type == 3 && this.extend_rule.GradeStr.length == 0){
                this.$message.error('请勾选规则对应的会员等级！');
                return;
            }
            if(this.extend_rule.account_type == 0 && this.extend_rule.company_id.length == 0){
                this.$message.error('请勾选规则对应的车队！');
                return;
            }
            if(this.extend_rule.account_type == 1){
                if(this.extend_rule.card_theme.length == 0){
                    this.$message.error('请勾选规则对应的卡！');
                    return;
                }
                if(this.extend_rule.card_type.length == 0){
                    this.$message.error('请勾选卡类型！');
                    return;
                }
            }

            if(this.extend_rule.account_type == 2 && this.extend_rule.customer_group_id.length == 0){
                this.$message.error('请勾选规则对应的卡组！');
                return;
            }
            if(this.payWayList.length > 0 && this.extend_rule.pay_way.length == 0){
                this.$message.error('请勾选付款方式！');
                return;
            }
            this.stepActive = 2;
            this.showConditionForm = false;
            this.showtimeRuleForm = true;
        },
        //检查时间合法性
        checkTime(val){
            this.isCheckTime = val;
        },
        //时间规则上一步
        timeRuleBefore(){
            this.stepActive = 1;
            this.showConditionForm = true;
            this.showtimeRuleForm = false;
        },
        //时间规则下一步
        timeRuleNext(){
            if(this.isCheckTime){
                this.stepActive = 3;
                this.showtimeRuleForm = false;
                this.showgivenForm = true;
            }else{
                this.$message.error('请选择完整时间和日期！');
            }
        },
        //充值赠送上一步
        givenBefore(){
            this.stepActive = 2;
            this.showtimeRuleForm = true;
            this.showgivenForm = false;
        },
        //获取营销券列表
        getCouponsList(){
            this.$axios.post("/GameActivity/getCouponsByStid",{}).then((res)=>{
                if(res.status == 200){
                    this.couponsOptions = res.data.data;

                    console.log(this.couponsOptions);
                    this.couponsOptions.forEach((element)=>{
                        element.num = 1;
                        element.coupon_name = element.coupon_name+" "+element.coupon_id;
                    })
                }
            });

        },
        //选择营销券,渲染营销券表格数据
        addTableData(val){
            let arr = [];
            let nodesData = "";
            //用nexttick确保界面渲染后再赋值
            this.$nextTick(()=>{
                if(this.charge_give_rule.type == 2){
                    nodesData = this.$refs['myCascader'].getCheckedNodes();
                }else if(this.charge_give_rule.type == 0){
                    nodesData = this.$refs['myGudingCascader'].getCheckedNodes();
                }else{
                    nodesData = this.$refs['myAddCascader'].getCheckedNodes();
                }
                nodesData.forEach((element)=>{
                    arr.push(JSON.parse(JSON.stringify(element.data)));
                })
                let idList = [];
                val.forEach((element)=>{
                    idList.push(element[0]);
                })
                arr.forEach((element,index)=>{
                    if(!idList.includes(element.coupon_id)){
                        arr.splice(index,1);
                    }
                })
                if(arr.length>0){
                    this.showGudingCoupons = true;
                }
                if(this.charge_give_rule.type == 2){
                    this.couponsTableData = arr;
                }else if(this.charge_give_rule.type == 1){
                    this.addCouponsTableData = arr;
                }else{
                    this.gudingCouponsTableData = arr;
                }
            });
        },
        //判断添加的券，更新券列表里面num的值
        changeNumber(val){
            this.couponsOptions.forEach((sub)=>{
                if(sub.coupon_id == val.coupon_id){
                    sub.num = val.num;
                }
            })
        },
        //营销券列表删除功能
        handleClick(val,type){
            let id = val.coupon_id;
            if(type==2){
                this.charge_give_rule.couponsData = [];
                this.couponsTableData.forEach((element,index)=>{
                    if(id == element.coupon_id){
                        this.couponsTableData.splice(index,1)
                    }
                })
                this.couponsTableData.forEach((element)=>{
                    this.charge_give_rule.couponsData.push([element.coupon_id])
                })
            }else if(type==1){
                this.charge_give_rule.addCouponsData = [];
                this.addCouponsTableData.forEach((element,index)=>{
                    if(id == element.coupon_id){
                        this.addCouponsTableData.splice(index,1)
                    }
                })
                this.addCouponsTableData.forEach((element)=>{
                    this.charge_give_rule.addCouponsData.push([element.coupon_id])
                })
            }else{
                this.charge_give_rule.gudingCouponsData = [];
                this.gudingCouponsTableData.forEach((element,index)=>{
                    if(id == element.coupon_id){
                        this.gudingCouponsTableData.splice(index,1)
                    }
                })
                this.gudingCouponsTableData.forEach((element)=>{
                    this.charge_give_rule.gudingCouponsData.push([element.coupon_id])
                })
            }

        },
        //创建
        create(){
          let formName="";
              if(this.charge_give_rule.type == 0){
                  formName = 'chargeGive';
              }else if(this.charge_give_rule.type == 1){
                  formName = 'chargeGive1';
              }else if(this.charge_give_rule.type == 2){
                  formName = 'chargeGive2';
              }
              this.$refs[formName].validate((valid) => {
                  if (valid) {
                    this.$refs['recharge'].validate((valid) => {
                      if(valid) {
                        this.btnDisabled = true;
                        let params={
                          public_config : {
                              id:"",
                              rule_name : "",
                              start_time :"",
                              end_time : "",
                              description : "",
                              state : "",
                              use_station_list : [],
                              priority : ""
                          },
                          extend_rule : {
                              card_theme : [],
                              customer_group_id : [],
                              card_type : [],
                              customer_id_type : "",
                              customer_id : "",
                              pay_way : [],
                              is_birthday : "",
                              first_recharge: "",
                              account_type:"",
                              company_id: [],
                              GradeStr: []
                          },
                          time_rule : {
                              rule : [],
                              type : ""
                          },
                          charge_give_rule :{
                              type : "",
                              data : ""
                          },
                          recharge:{
                            num: "",
                            type: "month",
                            count: "",
                            total: ""
                          },
                      }
                      //修改规则多了id参数
                      if(!this.isCreate){
                          params.public_config.id = this.cardId;
                      }

                      //基本信息赋值
                      params.public_config.rule_name = this.public_config.rule_name;
                      params.public_config.start_time = this.public_config.date[0];
                      params.public_config.end_time = this.public_config.date[1];
                      params.public_config.description = this.public_config.description;
                      params.public_config.state = 100;
                      params.public_config.priority = Number(this.public_config.priority);
                      let stations = [];
                      this.public_config.stationList.forEach((element)=>{
                          stations.push(element[0]);
                      });
                      params.public_config.use_station_list = stations;

                      //使用条件赋值
                      params.extend_rule.card_theme = this.extend_rule.card_theme;
                      params.extend_rule.customer_group_id = this.extend_rule.customer_group_id;
                      params.extend_rule.company_id = this.extend_rule.company_id;
                      params.extend_rule.GradeStr = this.extend_rule.GradeStr;
                      params.extend_rule.card_type = this.extend_rule.card_type;
                      params.extend_rule.customer_id_type = this.extend_rule.customer_id_type;
                      params.extend_rule.customer_id = this.extend_rule.customer_id;
                      params.extend_rule.pay_way = this.extend_rule.pay_way;
                      params.extend_rule.is_birthday = Number(this.extend_rule.is_birthday);
                      params.extend_rule.first_recharge = Number(this.extend_rule.first_recharge);
                      params.extend_rule.account_type = Number(this.extend_rule.account_type);

                      //避免把多余选中的值传给后端
                      if(this.extend_rule.account_type == "0"){
                          params.extend_rule.card_theme = [];
                          params.extend_rule.card_type = [];
                          params.extend_rule.customer_group_id = [];
                          params.extend_rule.GradeStr = [];
                      }else if(this.extend_rule.account_type == "1"){
                          params.extend_rule.company_id = [];
                          params.extend_rule.customer_group_id = [];
                          params.extend_rule.GradeStr = [];
                      }else if(this.extend_rule.account_type == "2"){
                          params.extend_rule.card_theme = [];
                          params.extend_rule.card_type = [];
                          params.extend_rule.company_id = [];
                          params.extend_rule.GradeStr = [];
                      }else{
                          params.extend_rule.card_theme = [];
                          params.extend_rule.card_type = [];
                          params.extend_rule.company_id = [];
                          params.extend_rule.customer_group_id = [];
                      }

                      //时间规则赋值
                      if(this.time_rule.type == 0){
                          params.time_rule.type = 1;
                          params.time_rule.rule = "";
                      }
                      if(this.time_rule.type == 1){
                          params.time_rule.type = 2;
                          this.time_rule.selectedTime.forEach((element)=>{
                              params.time_rule.rule.push({
                                  start_time:element.time[0],
                                  end_time:element.time[1]
                              })
                          })
                      }
                      if(this.time_rule.type == 2){
                          params.time_rule.type = 3;
                          this.time_rule.selectedTime.forEach((element)=>{
                              params.time_rule.rule.push({
                                  date:element.date,
                                  start_time:element.time[0],
                                  end_time:element.time[1]
                              })
                          })
                      }
                      if(this.time_rule.type == 3){
                          params.time_rule.type = 4;
                          this.time_rule.selectedTime.forEach((element)=>{
                              params.time_rule.rule.push({
                                  date:element.date,
                                  start_time:element.time[0],
                                  end_time:element.time[1]
                              })
                          })
                      }

                      //充值赠送赋值
                      params.charge_give_rule.type = this.charge_give_rule.type;
                      if(this.charge_give_rule.type == 0){
                          let couponList = [];
                          this.gudingCouponsTableData.forEach((element)=>{
                              couponList.push({
                                  coupon_id:element.coupon_id,
                                  coupon_num:element.num,
                              })
                          });
                          params.charge_give_rule.data = {
                              max_price:this.charge_give_rule.guding.max_price,
                              min_price:this.charge_give_rule.guding.min_price,
                              SumPrice: 0,
                              UnitPrice: 0,
                              market_type: 1,
                              give:{
                                  number:this.charge_give_rule.guding.give,
                                  couponlist:couponList
                              }
                          }
                          if(this.isLockPrice){
                            params.charge_give_rule.data = {
                              max_price:this.sellPrice,
                              min_price:this.sellPrice,
                              SumPrice: this.SumPrice,
                              UnitPrice: this.UnitPrice,
                              market_type: this.extend_rule.market_type,
                              give:{
                                  number:this.giveNumber,
                                  couponlist:couponList
                              }
                            }
                          }
                      }
                      if(this.charge_give_rule.type == 1){
                          let couponList = [];
                          this.addCouponsTableData.forEach((element)=>{
                              couponList.push({
                                  coupon_id:element.coupon_id,
                                  coupon_num:element.num,
                              })
                          });
                          params.charge_give_rule.data = {
                              max_price:this.charge_give_rule.bili.max_price,
                              min_price:this.charge_give_rule.bili.min_price,
                              SumPrice: 0,
                              UnitPrice: 0,
                              market_type: 1,
                              give:{
                                  number:this.charge_give_rule.bili.give,
                                  couponlist:couponList
                              }
                          }
                      }
                      if(this.charge_give_rule.type == 2){
                          let couponList = [];
                          this.couponsTableData.forEach((element)=>{
                              couponList.push({
                                  coupon_id:element.coupon_id,
                                  coupon_num:element.num,
                              })
                          });
                          // params.charge_give_rule.data = couponList;
                          params.charge_give_rule.data = {
                              max_price:this.charge_give_rule.coupons.max_price,
                              min_price:this.charge_give_rule.coupons.min_price,
                              SumPrice: 0,
                              UnitPrice: 0,
                              market_type: 1,
                              give:{
                                  number:"",
                                  couponlist:couponList
                              }

                          };
                      }
                      params.charge_give_rule.recharge = {
                        num: this.recharge.num,
                        type: this.recharge.type,
                        count: this.recharge.count,
                        total: this.recharge.total
                      }

                      this.$axios.post("/CardRule/setCardChargeRule",params).then((res)=>{
                          if(res.data.status == 200){
                              if(this.isCreate){
                                  this.$message.success("创建成功");
                              }else{
                                  this.$message.success("修改成功");
                              }
                              this.showTableData = true;
                              this.showBaseForm = false;
                              this.showConditionForm = false;
                              this.showtimeRuleForm = false;
                              this.showgivenForm = false;
                              this.btnDisabled = false;
                              this.currentPage = 1;
                              if(this.$route.hash == '#goToCreateLockRule'){
                                this.$router.push('/MarketingRules')
                            }else{
                                this.getCardRuleLists();

                            }

                          }else{
                              this.btnDisabled = false;
                              this.$message.error(res.data.info);
                          }
                      })
                      }
                    })

                  }
              })
        },
        //保存
        save() {


              let formName="";
              if(this.charge_give_rule.type == 0){
                  formName = 'chargeGive';
              }else if(this.charge_give_rule.type == 1){
                  formName = 'chargeGive1';
              }else if(this.charge_give_rule.type == 2){
                  formName = 'chargeGive2';
              }
              this.$refs[formName].validate((valid) => {
                  if (valid) {
                    this.$refs['recharge'].validate((valid) => {
                      if (!valid) return
                      if(valid) {
                        this.btnDisabled = true;
                        let params={
                          public_config : {
                            id:"",
                            rule_name : "",
                            start_time :"",
                            end_time : "",
                            description : "",
                            state : "",
                            use_station_list : [],
                            priority : ""
                          },
                          extend_rule : {
                            card_theme : [],
                            customer_group_id : [],
                            card_type : [],
                            customer_id_type : "",
                            customer_id : "",
                            pay_way : [],
                            is_birthday : "",
                            first_recharge: "",
                            account_type:"",
                            company_id: [],
                            GradeStr: []
                          },
                          time_rule : {
                            rule : [],
                            type : ""
                          },
                          charge_give_rule :{
                            type : "",
                            data : ""
                          },
                          recharge:{
                            num: "",
                            type: "month",
                            count: "",
                            total: ""
                          },
                        }
                        //修改规则多了id参数
                        if(!this.isCreate){
                          params.public_config.id = this.cardId;
                        }

                        //基本信息赋值
                        params.public_config.rule_name = this.public_config.rule_name;
                        params.public_config.start_time = this.public_config.date[0];
                        params.public_config.end_time = this.public_config.date[1];
                        params.public_config.description = this.public_config.description;
                        params.public_config.state = 100;
                        params.public_config.priority = Number(this.public_config.priority);
                        let stations = [];
                        this.public_config.stationList.forEach((element)=>{
                          stations.push(element[0]);
                        });
                        params.public_config.use_station_list = stations;

                        //使用条件赋值
                        params.extend_rule.card_theme = this.extend_rule.card_theme;
                        params.extend_rule.customer_group_id = this.extend_rule.customer_group_id;
                        params.extend_rule.company_id = this.extend_rule.company_id;
                        params.extend_rule.GradeStr = this.extend_rule.GradeStr;
                        params.extend_rule.card_type = this.extend_rule.card_type;
                        params.extend_rule.customer_id_type = this.extend_rule.customer_id_type;
                        params.extend_rule.customer_id = this.extend_rule.customer_id;
                        params.extend_rule.pay_way = this.extend_rule.pay_way;
                        params.extend_rule.is_birthday = Number(this.extend_rule.is_birthday);
                        params.extend_rule.first_recharge = Number(this.extend_rule.first_recharge);
                        params.extend_rule.account_type = Number(this.extend_rule.account_type);

                        //避免把多余选中的值传给后端
                        if(this.extend_rule.account_type == "0"){
                          params.extend_rule.card_theme = [];
                          params.extend_rule.card_type = [];
                          params.extend_rule.customer_group_id = [];
                          params.extend_rule.GradeStr = [];
                        }else if(this.extend_rule.account_type == "1"){
                          params.extend_rule.company_id = [];
                          params.extend_rule.customer_group_id = [];
                          params.extend_rule.GradeStr = [];
                        }else if(this.extend_rule.account_type == "2"){
                          params.extend_rule.card_theme = [];
                          params.extend_rule.card_type = [];
                          params.extend_rule.company_id = [];
                          params.extend_rule.GradeStr = [];
                        }else{
                          params.extend_rule.card_theme = [];
                          params.extend_rule.card_type = [];
                          params.extend_rule.company_id = [];
                          params.extend_rule.customer_group_id = [];
                        }

                        //时间规则赋值
                        if(this.time_rule.type == 0){
                          params.time_rule.type = 1;
                          params.time_rule.rule = "";
                        }
                        if(this.time_rule.type == 1){
                          params.time_rule.type = 2;
                          this.time_rule.selectedTime.forEach((element)=>{
                            params.time_rule.rule.push({
                              start_time:element.time[0],
                              end_time:element.time[1]
                            })
                          })
                        }
                        if(this.time_rule.type == 2){
                          params.time_rule.type = 3;
                          this.time_rule.selectedTime.forEach((element)=>{
                            params.time_rule.rule.push({
                              date:element.date,
                              start_time:element.time[0],
                              end_time:element.time[1]
                            })
                          })
                        }
                        if(this.time_rule.type == 3){
                          params.time_rule.type = 4;
                          this.time_rule.selectedTime.forEach((element)=>{
                            params.time_rule.rule.push({
                              date:element.date,
                              start_time:element.time[0],
                              end_time:element.time[1]
                            })
                          })
                        }

                        //充值赠送赋值
                        params.charge_give_rule.type = this.charge_give_rule.type;
                        if(this.charge_give_rule.type == 0){
                          let couponList = [];
                          this.gudingCouponsTableData.forEach((element)=>{
                            couponList.push({
                              coupon_id:element.coupon_id,
                              coupon_num:element.num,
                            })
                          });
                          params.charge_give_rule.data = {
                            max_price:this.charge_give_rule.guding.max_price,
                            min_price:this.charge_give_rule.guding.min_price,
                            SumPrice: 0,
                            UnitPrice: 0,
                            market_type: 1,
                            give:{
                              number:this.charge_give_rule.guding.give,
                              couponlist:couponList
                            }
                          }
                          if(this.isLockPrice){
                            params.charge_give_rule.data = {
                              max_price:this.sellPrice,
                              min_price:this.sellPrice,
                              SumPrice: this.SumPrice,
                              UnitPrice: this.UnitPrice,
                              market_type: this.extend_rule.market_type,
                              give:{
                                number:this.giveNumber,
                                couponlist:couponList
                              }
                            }
                          }
                        }
                        if(this.charge_give_rule.type == 1){
                          let couponList = [];
                          this.addCouponsTableData.forEach((element)=>{
                            couponList.push({
                              coupon_id:element.coupon_id,
                              coupon_num:element.num,
                            })
                          });
                          params.charge_give_rule.data = {
                            max_price:this.charge_give_rule.bili.max_price,
                            min_price:this.charge_give_rule.bili.min_price,
                            SumPrice: 0,
                            UnitPrice: 0,
                            market_type: 1,
                            give:{
                              number:this.charge_give_rule.bili.give,
                              couponlist:couponList
                            }
                          }
                        }
                        if(this.charge_give_rule.type == 2){
                          let couponList = [];
                          this.couponsTableData.forEach((element)=>{
                            couponList.push({
                              coupon_id:element.coupon_id,
                              coupon_num:element.num,
                            })
                          });
                          // params.charge_give_rule.data = couponList;
                          params.charge_give_rule.data = {
                            max_price:this.charge_give_rule.coupons.max_price,
                            min_price:this.charge_give_rule.coupons.min_price,
                            SumPrice: 0,
                            UnitPrice: 0,
                            market_type: 1,
                            give:{
                              number:"",
                              couponlist:couponList
                            }

                          };
                        }
                        params.charge_give_rule.recharge = {
                          num: this.recharge.num,
                          type: this.recharge.type,
                          count: this.recharge.count,
                          total: this.recharge.total
                        }

                        this.$axios.post("/CardRule/setCardChargeRule",params).then((res)=>{
                          if(res.data.status == 200){
                            if(this.isCreate){
                              this.$message.success("创建成功");
                            }else{
                              this.$message.success("修改成功");
                            }
                            this.showTableData = true;
                            this.showBaseForm = false;
                            this.showConditionForm = false;
                            this.showtimeRuleForm = false;
                            this.showgivenForm = false;
                            this.btnDisabled = false;
                            this.currentPage = 1;
                            if(this.$route.hash == '#goToCreateLockRule'){
                              this.$router.push('/MarketingRules')
                            }else{
                              this.getCardRuleLists();
                            }
                          }else{
                            this.btnDisabled = false;
                            this.$message.error(res.data.info);
                          }
                        })
                      }


                    })

                  }
              })





        },
        //清空所以表数据
        clearFormData(){
            //清空基础设置
            this.public_config.rule_name = "";
            this.public_config.date = [];
            this.public_config.description = "";
            this.public_config.stationList = [];
            this.stationList.forEach((element)=>{
                this.public_config.stationList.push([element.stid]);
            });
            this.public_config.priority = "10";

            //清空使用条件
            this.extend_rule.card_theme = [];
            this.extend_rule.customer_group_id = [];
            this.extend_rule.company_id = [];
            this.extend_rule.GradeStr = [];
            this.extend_rule.card_type = [];
            this.extend_rule.customer_id_type = "L";
            this.extend_rule.pay_way = [];
            this.extend_rule.is_birthday = "0";
            this.extend_rule.first_recharge = "0";
            this.extend_rule.account_type = "1";

            //清空时间规则
            this.time_rule.type = 0;
            this.time_rule.selectedTime = [];

            //清空充值赠送
            this.charge_give_rule.type = "0";
            this.charge_give_rule.coupons.max_price = "";
            this.charge_give_rule.coupons.min_price = "";
            this.charge_give_rule.couponsData = [];
            this.charge_give_rule.guding.max_price = "";
            this.charge_give_rule.guding.min_price = "";
            this.charge_give_rule.guding.give = "";
            this.charge_give_rule.bili.max_price = "";
            this.charge_give_rule.bili.min_price = "";
            this.charge_give_rule.bili.give = "";

            //清空充值送券规则限制
            this.recharge.num = "0";
            this.recharge.type = "month";
            this.recharge.count = "0";
            this.recharge.total = "0";

            this.couponsTableData = [];
            this.addCouponsTableData = [];
            this.gudingCouponsTableData = [];
            this.charge_give_rule.gudingCouponsData = [];
            this.charge_give_rule.addCouponsData = [];
            this.showGudingCoupons = false;

            this.couponsOptions.forEach((element)=>{
                element.num = 1;
            })

        },
        //改变账户类型
        changeAccountType(val){
            if(val == 0 && this.charge_give_rule.type == 2){
                this.charge_give_rule.type = "0";
            }
        },
        //卡主题全选
        handleCheckThemeAllChange(val){
            let arr = [];
            this.cardThemeRuleList.forEach(element => {
                arr.push(element.ID)
            })
            this.extend_rule.card_theme = val ? arr : [];
            this.isThemeIndeterminate = false;
        },
        handleCheckedThemeChange(value){
            let checkedCount = value.length;
            this.checkThemeAll = checkedCount === this.cardThemeRuleList.length;
            this.isThemeIndeterminate = checkedCount > 0 && checkedCount < this.cardThemeRuleList.length;
        },
        //等级全选
        handleCheckClassAllChange(val){
            let arr = [];
            this.classList.forEach(element => {
                arr.push(element.id);
            })
            this.extend_rule.GradeStr = val ? arr : [];
            this.isClassIndeterminate = false;
        },
        handleCheckedClassChange(value){
            let checkedCount = value.length;
            this.checkClassAll = checkedCount === this.classList.length;
            this.isClassIndeterminate = checkedCount > 0 && checkedCount < this.classList.length;
        },
        //车队卡全选
        handleCheckCompanyAllChange(val){
            let arr = [];
            this.companyList.forEach(element => {
                arr.push(element.ID)
            })
            this.extend_rule.company_id = val ? arr : [];
            this.isCompanyIndeterminate = false;
        },
        handleCheckedCompanyChange(value){
            let checkedCount = value.length;
            this.checkCompanyAll = checkedCount === this.companyList.length;
            this.isCompanyIndeterminate = checkedCount > 0 && checkedCount < this.companyList.length;
        },
        //卡组全选
        handleCheckCustomerGroupAllChange(val){
            let arr = [];
            this.customerGroupList.forEach(element => {
                arr.push(element.ID)
            })
            this.extend_rule.customer_group_id = val ? arr : [];
            this.isCustomerGroupIndeterminate = false;
        },
        handleCheckedCustomerGroupChange(value){
            let checkedCount = value.length;
            this.checkCustomerGroupAll = checkedCount === this.customerGroupList.length;
            this.isCustomerGroupIndeterminate = checkedCount > 0 && checkedCount < this.customerGroupList.length;
        },
        //付款方式全选
        handleCheckPayWayAllChange(val){
            let arr = [];
            this.payWayList.forEach(element => {
                arr.push(element.BH)
            })
            this.extend_rule.pay_way = val ? arr : [];
            this.isPayWayIndeterminate = false;
        },
        handleCheckedPayWayChange(value){
            let checkedCount = value.length;
            this.checkPayWayAll = checkedCount === this.payWayList.length;
            this.isPayWayIndeterminate = checkedCount > 0 && checkedCount < this.payWayList.length;
        },
        //模糊搜索
        search(){
            this.currentPage = 1;
            this.getCardRuleLists();
        },
        changeRadioValue(val){
            this.$refs.chargeGive.resetFields()
            this.$refs.chargeGive1.resetFields()
            this.$refs.chargeGive2.resetFields()
            this.couponsOptions.forEach((element)=>{
                element.num = 1;
            })
            if(val == "0"){
                this.charge_give_rule.bili.max_price = "";
                this.charge_give_rule.bili.min_price = "";
                this.charge_give_rule.bili.give = "";
                this.charge_give_rule.coupons.max_price = "";
                this.charge_give_rule.coupons.min_price = "";
                this.charge_give_rule.couponsData = [];
                this.charge_give_rule.addCouponsData = [];
                this.couponsTableData = [];
                this.addCouponsTableData = [];
                this.showGudingCoupons = false;
            }else if(val == "1"){
                this.charge_give_rule.coupons.max_price = "";
                this.charge_give_rule.coupons.min_price = "";
                this.charge_give_rule.couponsData = [];
                this.charge_give_rule.gudingCouponsData = [];
                this.couponsTableData = [];
                this.gudingCouponsTableData = [];
                this.charge_give_rule.guding.max_price = "";
                this.charge_give_rule.guding.min_price = "";
                this.charge_give_rule.guding.give = "";
                this.showGudingCoupons = false;
            }else if(val == "2"){
                this.charge_give_rule.guding.max_price = "";
                this.charge_give_rule.guding.min_price = "";
                this.charge_give_rule.guding.give = "";
                this.charge_give_rule.bili.max_price = "";
                this.charge_give_rule.bili.min_price = "";
                this.charge_give_rule.bili.give = "";
                this.showGudingCoupons = false;
                this.gudingCouponsTableData = [];
                this.addCouponsTableData = [];
                this.charge_give_rule.gudingCouponsData = [];
                this.charge_give_rule.addCouponsData = [];
            }
        },

        //计算规则
        calculationRules(){
          //定额计算
          if(this.extend_rule.market_type == 2){
            let sell = (Number(this.SumPrice)+Number(this.giveNumber))/Number(this.sellPrice);
            console.log(sell);
            // if(sell == 0 || sell == NaN || +this.SumPrice == 0){
            //     this.UnitPrice == 0
            // }else{
            //     this.UnitPrice = (Number(this.SumPrice) / sell).toFixed(2)
            //     }
          }
          //定升计算
          if(this.extend_rule.market_type == 3){
            this.UnitPrice = (Number(this.SumPrice) / (Number(this.sellPrice)+Number(this.giveNumber))).toFixed(2)
            this.sellPrice = this.regNumber(this.sellPrice)
          }
            this.SumPrice = this.regNumber(this.SumPrice)
            this.giveNumber = this.regNumber(this.giveNumber)
        },
        regNumber(num){ //替换负数和开头为0的数
            let str = num + ''
		    let len1 = str.substr(0, 1)
		    let len2 = str.substr(1, 1)
		    //如果第一位是0，第二位不是点，就用数字把点替换掉
		    if (str.length > 1 && len1 == 0 && len2 != ".") {
			    str = str.substr(1, str.length-1)
		    }
            str = str.replace(/[^\d^\.]+/g, '')
            return str
        },
        handleCheckedMarketType(){
          let arr = this.allCardThemeRuleList;
          this.cardThemeRuleList = arr;
          this.cardThemeRuleList = this.cardThemeRuleList.filter(item => item.Type == this.extend_rule.market_type)
          console.log(this.cardThemeRuleList);
        }
    },
    watch:{
        getCurrentStation(newValue,oldValue){
            if(newValue.merchant_type != 0 && newValue.value != oldValue.value){
                this.showLockRule = false
                this.getGroupBaseInfo();
                this.getStationList();
                this.getCouponsList();
                this.getCardRuleLists();//获取卡规则列表
                this.getChargeRuleTem();//获取支付方式
                this.getCardThemeRuleList();//初始化卡名称列表
                this.getCustomerGroupList();//初始化卡组列表
                this.getCompanyList();//初始化卡名称列表
            }
        }
    }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->

<style scoped>
    @import '../assets/css/cardTheme.css';
    @import '../assets/css/cardRule.css';
    .add-coupons{
        color: #999;
    }
    .add-coupons.active{
        color: #2c3e50;
    }
    .add-coupons.active .add{
        color: #32af50;
        cursor: pointer;
    }
</style>
<style>
.cardRuleListTable .redfont{
    color: red;
}
.given-form .item03 .el-form-item__content{
    display: flex;
    align-items: center;
}
.given-form .el-form-item{
    margin-bottom: 0;
}
.card-rule-tooltip{
  width: 12%;
}
.item05{
  margin: 0 5px;
}
.card-form .el-form-item__label{
    width:110px
}
.card-form .ruleItem{
    font-size: 14px;
    margin-left:110px
}
.card-form .ruleItem .dataBase{
    color:#32af50
}
.item05 .el-form-item__content .el-input.is-disabled .el-input__inner{
    color:#32af50;
    font-weight: 700;
}
</style>
