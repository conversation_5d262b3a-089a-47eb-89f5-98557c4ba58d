<template>
  <div id="navigation">
    <div class="wrap-top">
      <div class="menu-logo">
        <img class="logo" src="../assets/logo.png" alt="" />
        <span class="menu-logo-test">加油卡系统后台</span>
      </div>
      <div class="wrap-header">
        <el-cascader
          placeholder="请输入搜索的油站名称"
          :show-all-levels="false"
          :options="options"
          :props="{ checkStrictly: true }"
          ref="stationCascader"
          collapse-tags
          filterable
          clearable
          v-model="currentStationValue"
          @change="handleSelectStation"
        >
        </el-cascader>
      </div>
      <ul class="wrap-top-info">
        <li @click="downloadTool" class="downloadTool">
          <i class="el-icon-download"></i>下载制卡工具
        </li>
        <li class="on"><a href="">加油卡系统后台</a></li>
        <li v-show="showMP"><a @click="goToMp">会员营销系统后台</a></li>
        <li @mouseenter="showInfo" @mouseleave="hideInfo">
          | &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<i class="el-icon-user"></i
          >{{ name ? name : "未登录"
          }}<i class="el-icon-arrow-down el-icon--right"></i>
          <div class="superHeader_superTop_right_nav" v-show="isShowInfo">
            <div class="superHeader_superTop_right_nav_triangle"></div>
            <div class="superHeader_superTop_right_userinfo">
              <div class="superHeader_superTop_right_logo">
                {{ currentStation.label[0] }}
              </div>
              <div class="superHeader_superTop_right_userinfoContent">
                <div class="superHeader_superTop_right_userinfoContent_nameTop">
                  <div class="superHeader_superTop_right_userinfoContent_name">
                    {{ name }}
                  </div>
                  >
                </div>
                <div
                  class="superHeader_superTop_right_userinfoContent_stationLocal"
                >
                  {{ currentStation.label }}
                </div>
              </div>
            </div>

            <a
              class="superHeader_superTop_right_nav_block"
              target="_blank"
              :href="myUrl + 'Message/show'"
            >
              <span>消息中心</span>
            </a>
            <a
              class="superHeader_superTop_right_nav_block"
              target="_blank"
              :href="myUrl + 'Setting/personal'"
            >
              <span>系统管理</span>
            </a>
            <a class="superHeader_superTop_right_nav_block" @click="logout">
              <span>退出</span>
            </a>
          </div>
        </li>
      </ul>
    </div>

    <div>
      <div class="menu">
        <div style="width:100%">
          <el-menu
            :default-active="$route.path"
            router
            class="el-menu-vertical-demo card-menu"
          >
            <template v-for="item in menuList">
              <el-submenu
                v-show="item.entity && item.childs && item.entity.show"
                :index="item.entity.path"
                :key="item.path"
              >
                <template slot="title">
                  <i :class="item.entity.icon"></i>
                  <span>{{ item.entity.name }}</span>
                </template>
                <div v-for="(subitem, index) in item.childs" :key="index">
                  <el-menu-item
                    v-show="subitem.entity.show && !subitem.childs"
                    :index="subitem.entity.path"
                    @click="jump(subitem)"
                  >
                    <span>{{ subitem.entity.name }}</span>
                  </el-menu-item>
                  <el-submenu
                    v-show="subitem.entity.show && subitem.childs"
                    :index="subitem.entity.path"
                    :key="subitem.index"
                  >
                    <template slot="title">
                      <span>{{ subitem.entity.name }}</span>
                    </template>
                    <div
                      v-for="(sub2item, index) in subitem.childs"
                      :key="index"
                    >
                      <el-menu-item
                        v-show="sub2item.entity.show"
                        :index="sub2item.entity.path"
                        @click="jump(sub2item)"
                      >
                        <span>{{ sub2item.entity.name }}</span>
                      </el-menu-item>
                    </div>
                  </el-submenu>
                </div>
              </el-submenu>
              <el-menu-item
                v-show="item.entity && !item.childs && item.entity.show"
                @click="jump(item)"
                :key="item.index"
                :index="item.entity.path"
              >
                <i :class="item.entity.icon"></i>
                <span>{{ item.entity.name }}</span>
              </el-menu-item>
            </template>
          </el-menu>
        </div>
      </div>

      <!-- 主要内容 -->
      <div class="wrap-main">
        <el-tabs
          class="main-tabs"
          v-model="editableTabsValue"
          @tab-click="clickTab"
          type="card"
          :closable="editableTabs && editableTabs.length > 1"
          @tab-remove="removeTab"
        >
          <el-tab-pane
            v-for="item in editableTabs"
            :key="item.name"
            :label="item.title"
            :name="item.name"
          >
          </el-tab-pane>
        </el-tabs>
        <div class="wrap-menu">
          <el-breadcrumb separator-class="el-icon-arrow-right">
            <el-breadcrumb-item
              v-for="(item, index) in breadcrumb.prev"
              :key="index"
              >{{ item }}</el-breadcrumb-item
            >
            <el-breadcrumb-item>{{ breadcrumb.current }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="wrap-content">
          <keep-alive :include="cacheArray">
            <router-view
              @currentNav="currentNav"
              :currentStation="currentStation"
            ></router-view>
          </keep-alive>
        </div>
      </div>
    </div>

    <div class="sign-up">
      <p>©2014-{{ year }} zhihuiyouzhan.com 粤ICP备14049534号-1</p>
      <p>Powered By Shenzhen Wecar Technology Co., Ltd.</p>
    </div>
    <password-expire-check ref="passwordCheck" />
  </div>
</template>

<script>
import eventBus from "@/components/eventBus.js";
import ssoUrlObj from "../assets/js/url.js";
import { mapGetters, mapActions } from "vuex";
import PasswordExpireCheck from './PasswordExpireCheck.vue'

export default {
  name: "Navigation",
  components: {
    PasswordExpireCheck
  },
  data() {
    return {
      name: "",
      currentStation: {
        merchant_type: "",
        merchant_id: "",
        value: "",
        label: "",
        pid: "",
        group_id:''
      },
      currentStationValue: [],
      currentIndex: "/index",
      isShowInfo: false,
      breadcrumb: {
        current: "首页",
        prev: []
      },
      menuList:  [
        {
          entity: {
            name: "首页",
            path: "/index",
            icon: "el-icon-house",
            prev: [],
            show: true
          }
        },
        {
          entity: {
            name: "卡管理",
            path: "/CardManager",
            icon: "el-icon-bank-card",
            prev: [],
            show: true
          }
        },
        {
          entity: {
            name: "订单管理",
            path: "/3",
            icon: "el-icon-notebook-1",
            prev: [],
            show: false
          },
          childs: [
            {
              entity: {
                name: "充值管理",
                path: "/Refund",
                icon: "el-icon-menu",
                prev: ["订单管理"],
                show: false
              }
            },
            {
              entity: {
                name: "资金流水",
                path: "/CapitalRecord",
                icon: "el-icon-menu",
                prev: ["订单管理"],
                show: true
              }
            }
          ]
        },

        {
          entity: {
            name: "报表管理",
            path: "/4",
            icon: "el-icon-data-line",
            prev: [],
            show: false
          },
          childs: [
            {
              entity: {
                name: "车队充值开票汇总报表",
                path: "/FleetTopUpInvoiceReport",
                icon: "el-icon-menu",
                prev: ["报表管理"],
                show: false
              }
            },
            {
              entity: {
                name: "充值资金日报表",
                path: "/Report",
                icon: "el-icon-menu",
                prev: ["报表管理"],
                show: true
              }
            },
            {
              entity: {
                name: "充值收付款流水表",
                path: "/RechargeRecodeList",
                icon: "el-icon-menu",
                prev: ["报表管理"],
                show: true
              }
            },
            {
              entity: {
                name: "消费明细报表",
                path: "/SpendDetailReport",
                icon: "el-icon-menu",
                prev: ["报表管理"],
                show: false
              }
            },
            {
              entity: {
                name: "新开卡用户统计报表",
                path: "/newUserReport",
                icon: "el-icon-menu",
                prev: ["报表管理"],
                show: true
              }
            },
            {
              entity: {
                name: "资金进销存报表",
                path: "/CapitalJXC",
                icon: "el-icon-menu",
                prev: ["报表管理"],
                show: false
              }
            },
            {
              entity: {
                name: "车队进销存报表",
                path: "/fleetJXC",
                icon: "el-icon-menu",
                prev: ["报表管理"],
                show: false
              }
            },
            {
              entity: {
                name: "车队开票汇总报表",
                path: "/fleetInvoiceReport",
                icon: "el-icon-menu",
                prev: ["报表管理"],
                show: false
              }
            },
            {
              entity: {
                name: "资金对账报表",
                path: "/4-1",
                icon: "",
                show: false
              },
              childs: [
                {
                  entity: {
                    name: "资金对账汇总表",
                    path: "/CardCapitalDayReport",
                    prev: ["资金对账报表"],
                    show: true
                  }
                },
                {
                  entity: {
                    name: "油站对账汇总表",
                    path: "/CardStationReport",
                    prev: ["资金对账报表"],
                    show: true
                  }
                }
              ]
            },
            {
              entity: {
                name: "客户资金对账报表",
                path: "/CardCustomerReport",
                icon: "el-icon-menu",
                prev: ["报表管理"],
                show: true
              }
            },
            {
              entity: {
                name: "法体资金对账报表",
                path: "/FleetCapitalChangeDetailReport",
                icon: "el-icon-menu",
                prev: ["报表管理"],
                show: this.$store.getters.showFleetCapitalChangeDetailReport
              }
            },
            {
              entity: {
                name: "客户消费变动明细表",
                path: "/CustomerSpendReport",
                icon: "el-icon-menu",
                prev: ["报表管理"],
                show: false
              }
            },

            {
              entity: {
                name: "车队油品销售报表",
                path: "/customerOilReport",
                icon: "el-icon-menu",
                prev: ["报表管理"],
                show: false
              }
            },
            {
              entity: {
                name: "站间清结算报表",
                path: "/BetweenStationsReport",
                icon: "el-icon-menu",
                prev: ["报表管理"],
                show: false
              }
            },
            {
              entity: {
                name: "集团清结算报表",
                path: "/GroupReport",
                icon: "el-icon-menu",
                prev: ["报表管理"],
                show: false
              }
            },
            {
              entity: {
                name: "定升车队进销存汇总表",
                path: "/InventorySummary",
                icon: "el-icon-menu",
                prev: ["报表管理"],
                show: true
              }
            }
          ]
        },

        {
          entity: {
            name: "车队管理",
            path: "/6",
            icon: "el-icon-office-building",
            prev: [],
            show: false
          },
          childs: [
            {
              entity: {
                name: "车队信息",
                path: "/EnterpriseInformationManagement",
                icon: "el-icon-menu",
                prev: ["车队管理"],
                show: true
              }
            },
            {
              entity: {
                name: "车队资金管理",
                path: "/CardTransfer",
                icon: "el-icon-menu",
                prev: ["车队管理"],
                show: true
              }
            },
            {
              entity: {
                name: "车队资金流水",
                path: "/GroupCapitalRecord",
                icon: "el-icon-menu",
                prev: ["车队管理"],
                show: false
              }
            },
            {
              entity: {
                name: "车队充值审核",
                path: "/RechargeApproval",
                icon: "el-icon-menu",
                prev: ["车队管理"],
                show: this.showRechargeApproval && this.$store.getters.rechargeApprovalPrivilege.is_has == 1
              }
            }
          ]
        },

        {
          entity: {
            name: "卡组管理",
            path: "/CustomerGroupManagement",
            icon: "el-icon-money",
            prev: [],
            show: false
          }
        },

        {
          entity: {
            name: "发票管理",
            path: "/8",
            icon: "el-icon-tickets",
            prev: [],
            show: false
          },
          childs: [
            {
              entity: {
                name: "普通发票管理",
                path: "/RegularInvoice",
                icon: "el-icon-menu",
                prev: ["发票管理"],
                show: true
              }
            },
            {
              entity: {
                name: "增值税专用发票管理",
                path: "/TaxInvoice",
                icon: "el-icon-menu",
                prev: ["发票管理"],
                show: true
              }
            },
            {
              entity: {
                name: "客户发票信息管理",
                path: "/CustomerInvoices",
                icon: "el-icon-menu",
                prev: ["发票管理"],
                show: true
              }
            }
          ]
        },

        {
          entity: {
            name: "规则管理",
            path: "/9",
            icon: "el-icon-document",
            prev: [],
            show: false
          },
          childs: [
            {
              entity: {
                name: "充值营销规则",
                path: "/MarketingRules",
                icon: "el-icon-menu",
                prev: ["规则管理"],
                show: false
              }
            },
            {
              entity: {
                name: "单价锁定",
                path: "/unitPriceLock",
                icon: "el-icon-menu",
                prev: ["充值营销规则"],
                show: false
              }
            },
            {
              entity: {
                name: "充值规则",
                path: "/CardRule",
                icon: "el-icon-menu",
                prev: ["规则管理"],
                show: true
              }
            },
            {
              entity: {
                name: "制卡规则",
                path: "/CardTheme",
                icon: "el-icon-menu",
                prev: ["规则管理"],
                show: true
              }
            }
          ]
        },

        {
          entity: {
            name: "卡系统配置",
            path: "/Setting",
            icon: "el-icon-setting",
            prev: [],
            show: false
          }
        },

        {
          entity: {
            name: "下载中心",
            path: "/DownloadList",
            icon: "el-icon-download",
            prev: [],
            show: false
          }
        },
        {
          entity: {
            name: "操作日志",
            path: "/12",
            icon: "el-icon-notebook-2",
            prev: [],
            show: false
          },
          childs: [
            {
              entity: {
                name: "平台操作日志",
                path: "/CardLog",
                icon: "el-icon-menu",
                prev: ["操作日志"],
                show: true
              }
            },
            {
              entity: {
                name: "手工账务处理日志",
                path: "/HandMadeLog",
                icon: "el-icon-menu",
                prev: ["操作日志"],
                show: true
              }
            }
          ]
        }
      ], //初始菜单
      options: [],
      value: "",
      new_group_id: -1, //消费明细表只有0,1和3才显示出来0是平台，1是钓鱼岛集团，3是u1
      isGroupSettle: 0, //设置了集团清结算为1，未设置为0
      groupSettleStid: -1,
      showMP: true, //是否显示商户平台
      showYZ: true, //是否显示
      showFJ: true, //是否显示
      year: 2017, // 报备截止年份
      new_customer_report: true, // 显示车队油品销售报表
      recharge_batch: true, // 显示充值营销规则
      show_company_invoice: false, // 显示车队进销存报表
      show_customerSpendReport: false, // 显示客户消费变动明细表
      show_fleet_topup_invoice: false  // 显示车队充值开票汇总
    };
  },
  async created() {
    this.year = new Date().getFullYear();
  },
  async mounted() {
    let that = this;
    //初始化赋值
    let userInfo = localStorage.getItem("__userInfo__") || "";
    if (userInfo && userInfo != "" && userInfo != "undefined") {
      that.name = JSON.parse(userInfo).name;
      await that.getCompany();
    }
    let router = this._getRouterDetail(this.$route.path);
    if(!router){
      return;
    }
    this.breadcrumb = {
      current: router.name,
      prev: router.prev
    };
    this.changeEditableTabs({
      title: this.getRouterName(this.$router.currentRoute.path),
      name: this.tabIndex,
      router: this.$router.currentRoute.path,
      current: router.name,
      prev: router.prev
    });
    this.changeCacheArray(this.$router.currentRoute.path.substr(1));
  },
  computed: {
    myUrl(){
      const lh = window.location.href;
      if (lh.indexOf("localhost:8080") != -1) {
        return this.urlObj.local;
      } else if (lh.indexOf("test-card-admin.wcc.cn") != -1) {
        return this.urlObj.test;
      } else if (lh.indexOf("preview-card-admin.weicheche.cn") != -1) {
        return this.urlObj.preview;
      } else if (lh.indexOf("card-admin.zhihuiyouzhan.com") != -1) {
        return this.urlObj.product;
      } else {
        return this.urlObj.product;
      }
    },
    currentRoute() {
      return this.$route.path;
    },
    editableTabsValue: {
      get() {
        return this.$store.getters.getEditableTabsValue;
      },
      set(newValue) {
        this.changeEditableTabsValue(newValue);
      }
    },
    editableTabs: {
      get() {
        return this.$store.getters.getEditableTabs;
      },
      set(newValue) {
        this.$store.commit("SETEDITABLETABS", newValue);
      }
    },
    tabIndex: {
      get() {
        return this.$store.getters.getTabIndex;
      },
      set(newValue) {
        this.$store.commit("SETTABINDEX", newValue);
      }
    },
    cacheArray: {
      get() {
        return this.$store.getters.getCacheArray;
      },
      set(newValue) {
        this.$store.commit("SETCACHEARRAY", newValue);
      }
    },
    ...mapGetters({
      getCurrentStation: "getCurrentStation"
    }),
    ...mapGetters(['showLockPrice', 'showRechargeApproval'])
  },

  methods: {
    ...mapActions({
      changeCurrentStation: "changeCurrentStation",
      changeEditableTabsValue: "changeEditableTabsValue",
      changeEditableTabs: "changeEditableTabs",
      changeCacheArray: "changeCacheArray"
    }),
    async fetchRoleJurisdiction() {
      try {
        let res = await this.$axios.get('/CardHome/getRoleJurisdiction');
        res = res.data;
        if (res.status === 200) {
          this.$store.commit('SET_ROLE_JURISDICTION', res.data);
        } else {
          let errorMessage = '获取角色权限失败，请稍后重试';
          if (res.info) {
            errorMessage = `获取角色权限失败，${res.info}`;
          } else if (res.message) {
            errorMessage = `获取角色权限失败，${res.message}`;
          }
          console.error('获取角色权限失败: HTTP status code is not 200', res);
          this.$message.error(errorMessage);
        }
      } catch (error) {
        let errorMessage = '获取角色权限失败，请稍后重试';
        if (error.message) {
          errorMessage = error.message;
        }
        console.error('获取角色权限失败:', error);
        this.$message.error(errorMessage);
      }
    },
    //获取菜单
    getMenu() {
      let user = localStorage.getItem("__userInfo__") || "";
      let userInfo = JSON.parse(user);
      this.new_group_id = user ? userInfo.new_group_id : -1;
      if(userInfo.group_id == 1545){ //中国国际能源金泰山加油站
        this.showMP = false
      }
      if(userInfo.group_id == 1321){ //中由石化亚珠站集团
        this.showYZ = false
      }

      // 最终菜单判断赋值
      this.menuList = [
          {
            entity: {
              name: "首页",
              path: "/index",
              icon: "el-icon-house",
              prev: [],
              show: true
            }
          },
          {
            entity: {
              name: "卡管理",
              path: "/CardManager",
              icon: "el-icon-bank-card",
              prev: [],
              show: true
            }
          },
          {
            entity: {
              name: "订单管理",
              path: "/3",
              icon: "el-icon-notebook-1",
              prev: [],
              show: this.showYZ
            },
            childs: [
              {
                entity: {
                  name: "充值管理",
                  path: "/Refund",
                  icon: "el-icon-menu",
                  prev: ["订单管理"],
                  show: this.showFJ
                }
              },
              {
                entity: {
                  name: "资金流水",
                  path: "/CapitalRecord",
                  icon: "el-icon-menu",
                  prev: ["订单管理"],
                  show: true
                }
              }
            ]
          },

          {
            entity: {
              name: "报表管理",
              path: "/4",
              icon: "el-icon-data-line",
              prev: [],
              show: userInfo.has_mch_id === 0 && this.showYZ && this.showFJ
            },
            childs: [
              {
                entity: {
                  name: "车队充值开票汇总报表",
                  path: "/FleetTopUpInvoiceReport",
                  icon: "el-icon-menu",
                  prev: ["报表管理"],
                  show: this.show_fleet_topup_invoice
                }
              },
              {
                entity: {
                  name: "充值资金日报表",
                  path: "/Report",
                  icon: "el-icon-menu",
                  prev: ["报表管理"],
                  show: true
                }
              },
              {
                entity: {
                  name: "充值收付款流水表",
                  path: "/RechargeRecodeList",
                  icon: "el-icon-menu",
                  prev: ["报表管理"],
                  show: true
                }
              },
              {
                entity: {
                  name: "消费明细报表",
                  path: "/SpendDetailReport",
                  icon: "el-icon-menu",
                  prev: ["报表管理"],
                  show: this.showMP
                }
              },
              {
                entity: {
                  name: "新开卡用户统计报表",
                  path: "/newUserReport",
                  icon: "el-icon-menu",
                  prev: ["报表管理"],
                  show: true
                }
              },
              {
                entity: {
                  name: "资金进销存报表",
                  path: "/CapitalJXC",
                  icon: "el-icon-menu",
                  prev: ["报表管理"],
                  show: this.showMP
                }
              },
              {
                entity: {
                  name: "车队进销存报表",
                  path: "/fleetJXC",
                  icon: "el-icon-menu",
                  prev: ["报表管理"],
                  show: this.show_company_invoice
                }
              },
              {
                entity: {
                  name: "车队开票汇总报表",
                  path: "/fleetInvoiceReport",
                  icon: "el-icon-menu",
                  prev: ["报表管理"],
                  show: this.$store.state.groupBaseInfo.show_company_invoice_report === 1
                }
              },
              {
                entity: {
                  name: "资金对账报表",
                  path: "/4-1",
                  icon: "",
                  show: this.showMP
                },
                childs: [
                  {
                    entity: {
                      name: "资金对账汇总表",
                      path: "/CardCapitalDayReport",
                      prev: ["资金对账报表"],
                      show: true
                    }
                  },
                  {
                    entity: {
                      name: "油站对账汇总表",
                      path: "/CardStationReport",
                      prev: ["资金对账报表"],
                      show: true
                    }
                  }
                ]
              },
              {
                entity: {
                  name: "客户资金对账报表",
                  path: "/CardCustomerReport",
                  icon: "el-icon-menu",
                  prev: ["报表管理"],
                  show: true
                }
              },
              {
                entity: {
                  name: "法体资金对账报表",
                  path: "/FleetCapitalChangeDetailReport",
                  icon: "el-icon-menu",
                  prev: ["报表管理"],
                  show: this.$store.getters.showFleetCapitalChangeDetailReport
                }
              },
              {
                entity: {
                  name: "客户消费变动明细表",
                  path: "/CustomerSpendReport",
                  icon: "el-icon-menu",
                  prev: ["报表管理"],
                  show: this.show_customerSpendReport
                }
              },

              {
                entity: {
                  name: "车队油品销售报表",
                  path: "/customerOilReport",
                  icon: "el-icon-menu",
                  prev: ["报表管理"],
                  show: this.new_customer_report
                }
              },
              {
                entity: {
                  name: "站间清结算报表",
                  path: "/BetweenStationsReport",
                  icon: "el-icon-menu",
                  prev: ["报表管理"],
                  show: this.new_group_id == 0 || this.isGroupSettle == 0
                }
              },
              {
                entity: {
                  name: "集团清结算报表",
                  path: "/GroupReport",
                  icon: "el-icon-menu",
                  prev: ["报表管理"],
                  show: this.new_group_id == 0 || this.isGroupSettle == 1
                }
              },
              {
                entity: {
                  name: "定升车队进销存汇总表",
                  path: "/InventorySummary",
                  icon: "el-icon-menu",
                  prev: ["报表管理"],
                  show: this.showLockPrice
                }
              }
            ]
          },

          {
            entity: {
              name: "车队管理",
              path: "/6",
              icon: "el-icon-office-building",
              prev: [],
              show: this.showFJ
            },
            childs: [
              {
                entity: {
                  name: "车队信息",
                  path: "/EnterpriseInformationManagement",
                  icon: "el-icon-menu",
                  prev: ["车队管理"],
                  show: true
                }
              },
              {
                entity: {
                  name: "车队资金管理",
                  path: "/CardTransfer",
                  icon: "el-icon-menu",
                  prev: ["车队管理"],
                  show: true
                }
              },
              {
                entity: {
                  name: "车队资金流水",
                  path: "/GroupCapitalRecord",
                  icon: "el-icon-menu",
                  prev: ["车队管理"],
                  show: this.showYZ
                }
              },
              {
                entity: {
                  name: "客户类型",
                  path: "/CustomerType",
                  icon: "el-icon-menu",
                  prev: ["车队管理"],
                  show: true
                }
              },
              {
                entity: {
                  name: "车队充值审核",
                  path: "/RechargeApproval",
                  icon: "el-icon-menu",
                  prev: ["车队管理"],
                  show: this.showRechargeApproval && this.$store.getters.rechargeApprovalPrivilege.is_has == 1
                }
              }
            ]
          },

          {
            entity: {
              name: "卡组管理",
              path: "/CustomerGroupManagement",
              icon: "el-icon-money",
              prev: [],
              show: this.showFJ
            }
          },

          {
            entity: {
              name: "发票管理",
              path: "/8",
              icon: "el-icon-tickets",
              prev: [],
              show: this.showYZ && this.showFJ
            },
            childs: [
              {
                entity: {
                  name: "普通发票管理",
                  path: "/RegularInvoice",
                  icon: "el-icon-menu",
                  prev: ["发票管理"],
                  show: true
                }
              },
              {
                entity: {
                  name: "增值税专用发票管理",
                  path: "/TaxInvoice",
                  icon: "el-icon-menu",
                  prev: ["发票管理"],
                  show: true
                }
              },
              {
                entity: {
                  name: "客户发票信息管理",
                  path: "/CustomerInvoices",
                  icon: "el-icon-menu",
                  prev: ["发票管理"],
                  show: true
                }
              }
            ]
          },

          {
            entity: {
              name: "规则管理",
              path: "/9",
              icon: "el-icon-document",
              prev: [],
              show: this.showFJ
            },
            childs: [
              {
                entity: {
                  name: "充值营销规则",
                  path: "/MarketingRules",
                  icon: "el-icon-menu",
                  prev: ["规则管理"],
                  show: this.recharge_batch
                }
              },
              {
                entity: {
                  name: "单价锁定",
                  path: "/unitPriceLock",
                  icon: "el-icon-menu",
                  prev: ["充值营销规则"],
                  show: false
                }
              },
              {
                entity: {
                  name: "充值规则",
                  path: "/CardRule",
                  icon: "el-icon-menu",
                  prev: ["规则管理"],
                  show: true
                }
              },
              {
                entity: {
                  name: "制卡规则",
                  path: "/CardTheme",
                  icon: "el-icon-menu",
                  prev: ["规则管理"],
                  show: true
                }
              }
            ]
          },

          {
            entity: {
              name: "卡系统配置",
              path: "/Setting",
              icon: "el-icon-setting",
              prev: [],
              show: this.showFJ
            }
          },

          {
            entity: {
              name: "下载中心",
              path: "/DownloadList",
              icon: "el-icon-download",
              prev: [],
              show: this.showFJ
            }
          },
          {
            entity: {
              name: "操作日志",
              path: "/12",
              icon: "el-icon-notebook-2",
              prev: [],
              show: this.showFJ
            },
            childs: [
              {
                entity: {
                  name: "平台操作日志",
                  path: "/CardLog",
                  icon: "el-icon-menu",
                  prev: ["操作日志"],
                  show: true
                }
              },
              {
                entity: {
                  name: "手工账务处理日志",
                  path: "/HandMadeLog",
                  icon: "el-icon-menu",
                  prev: ["操作日志"],
                  show: true
                }
              }
            ]
          }
        ];
      this.$forceUpdate()
    },
    // 菜单权限控制
    async getGroupBaseInfo() {
      try {
        const res = await this.$axios.post('/mock/Ostn/getGroupBaseInfo')
        if(res.data.status != 200) return this.$message.error(res.data.info)

        this.$store.commit('SET_GROUP_BASE_INFO',res.data.data)

        this.new_customer_report = res.data.data.company_oil_report == 1  // 1 显示车队油品销售报表

        this.recharge_batch = res.data.data.recharge_batch != 0 // 0 不显示充值营销规则

        this.isGroupSettle = res.data.data.is_group_settle; // 1 显示集团清结算报表; 0 显示站间清结算

        this.show_company_invoice = res.data.data.show_company_invoice == 1 // 1 显示车队进销存报表

        this.show_customerSpendReport = res.data.data.show_customerSpendReport == 1 // 1 显示客户消费变动明细表

        this.show_fleet_topup_invoice = res.data.data.show_fleet_topup_invoice == 1 // 1 显示车队充值开票汇总

        localStorage.setItem("__isGroupSettle__", res.data.data.is_group_settle)
        localStorage.setItem("__groupSettle__", res.data.data.group_settle);

        const {data} = res.data;
        if(data && 'is_group' in data){
          const {is_group='0'} = data;
          this.$store.state.isGroup = is_group;
        }

        // 检查密码过期状态
        this.$nextTick(() => {
          this.$refs.passwordCheck.checkPasswordStatus()
        })

        await this.fetchRoleJurisdiction();

        this.getMenu()
        this.$forceUpdate()
      } catch (e) {
        console.log("=>(Navigation.vue:1091) e", e);
        this.$message.error('网络错误！')
      }
    },
    //会员营销系统跳转
    goToMp() {
      const lh = window.location.href;
      if (lh.indexOf("localhost:8080") != -1) {
        window.open(this.urlObj.local);
      } else if (lh.indexOf("test-card-admin.wcc.cn") != -1) {
        window.open(this.urlObj.test);
      } else if (lh.indexOf("preview-card-admin.weicheche.cn") != -1) {
        window.open(this.urlObj.preview);
      } else if (lh.indexOf("card-admin.zhihuiyouzhan.com") != -1) {
        window.open(this.urlObj.product);
      } else {
        window.open(this.urlObj.product);
      }
    },
    //接收子传过来的currentNav
    currentNav(val) {
      this.breadcrumb = val;
    },

    //路由跳转
    jump(val) {
      this.breadcrumb = {
        current: val.entity.name,
        prev: val.entity.prev
      };

      //判断缓存数组是否存在组件
      let name = val.entity.path.substr(1);
      let currentName = this.cacheArray.find(element => element == name);
      if (!currentName) {
        this.changeCacheArray(name);
      }

      let newTabName = ++this.tabIndex + "";
      //判断点击的菜单是否存在editableTabs中
      let currentTab = this.editableTabs.find(
        element => element.title == val.entity.name
      );
      if (currentTab) {
        this.changeEditableTabsValue(currentTab.name);
        return;
      } else {
        this.changeEditableTabs({
          title: val.entity.name,
          name: newTabName,
          router: val.entity.path,
          current: val.entity.name,
          prev: val.entity.prev
        });
        this.changeEditableTabsValue(newTabName);
      }
    },

    _getRouterDetail(path) {
      let router = undefined;
      this.menuList.forEach(item => {
        if (item.childs && item.childs.length > 0) {
          item.childs.forEach(childsItem => {
            if (path == childsItem.entity.path) {
              router = childsItem.entity;
            } else {
              if (childsItem.childs && childsItem.childs.length > 0) {
                childsItem.childs.forEach(childsItem1 => {
                  if (path == childsItem1.entity.path) {
                    router = childsItem1.entity;
                  }
                });
              }
            }
          });
        } else {
          if (path == item.entity.path) {
            router = item.entity;
          }
        }
      });
      return router;
    },

    showInfo() {
      this.isShowInfo = true;
    },
    hideInfo() {
      this.isShowInfo = false;
    },
    handleClick(command) {
      if (command == "out") {
        this.logout();
      }
    },
    //退出登录
    logout() {
      let that = this;
      that.$axios
        .post("/ssoLogin/logout", {})
        .then(function(res) {
          if (res.data.status == 200) {
            // 清除密码过期提醒缓存
            localStorage.removeItem('pwd_expire_reminder')
            localStorage.removeItem("__userInfo__");
            sessionStorage.removeItem("__userInfo__");
            localStorage.removeItem("options");
            localStorage.removeItem("currentStation");
            let url = window.location.href.split("?")[0];
            const lh = window.location.href;
            if (lh.indexOf("localhost:8080") != -1) {
              window.location.href =
                ssoUrlObj.ssoTest +
                "/v1/login?redirect=" +
                encodeURIComponent(url);
            } else if (lh.indexOf("test-card-admin.wcc.cn") != -1) {
              window.location.href =
                ssoUrlObj.ssoTest +
                "/v1/login?redirect=" +
                encodeURIComponent(url);
            } else if (lh.indexOf("preview-card-admin.weicheche.cn") != -1) {
              window.location.href =
                ssoUrlObj.ssoPreview +
                "/v1/login?redirect=" +
                encodeURIComponent(url);
            } else if (lh.indexOf("card-admin.zhihuiyouzhan.com") != -1) {
              window.location.href =
                ssoUrlObj.ssoProduct +
                "/v1/login?redirect=" +
                encodeURIComponent(url);
            } else {
              window.location.href =
                ssoUrlObj.ssoTest +
                "/v1/login?redirect=" +
                encodeURIComponent(url);
            }
          } else {
            that.$message({
              message: res.data.info,
              type: "error"
            });
          }
        })
        .catch(function(error) {});
    },

    //车队名单
    async getCompany() {
      let userInfo = localStorage.getItem("__userInfo__") || "";
      if (userInfo && userInfo != "" && userInfo != "undefined") {
        this.groupSettleStid = JSON.parse(userInfo).group_settle_stid;
      }
      let that = this;
      await that.$axios
        .post("/Ostn/getMerchants", {})
        .then(async function(res) {
          if (res.data.status == 200) {
            that.options = res.data.data.merchantList;
            that.currentStation = {...res.data.data.current,group_id:res.data.data.group_id};
            localStorage.setItem("options", JSON.stringify(that.options));
            localStorage.setItem(
              "currentStation",
              JSON.stringify(that.currentStation)
            );
            that.changeCurrentStation(that.currentStation);

            //解决下拉无法自动选中问题
            var parentNodeList = getParentNodes(
              that.currentStation.value,
              that.options
            );
            var nodes = [];
            function getParentNodes(id, tree) {
              _getParentNodes([], id, tree);
              return nodes;
            }

            function _getParentNodes(his, targetId, tree) {
              tree.some(list => {
                const children = list.children || [];
                if (list.value === targetId) {
                  nodes = his;
                  return true;
                } else if (children.length > 0) {
                  const history = [...his];
                  history.push(list);
                  return _getParentNodes(history, targetId, children);
                }
              });
            }
            var _station = [];
            parentNodeList.forEach(function(element) {
              _station.push(element.value);
            });
            _station.push(that.currentStation.value);
            that.currentStationValue = _station;
            that.getGroupBaseInfo()
            that.$forceUpdate();
          } else {
            that.$message({
              message: res.data.info,
              type: "error"
            });
          }
        })
        .catch(function(error) {});
    },

    //选择油站
    handleSelectStation() {
      var that = this;
      localStorage.removeItem("options");
      this.$nextTick(async function() {
        if (this.$refs.stationCascader.getCheckedNodes()[0]) {
          var currentNode = this.$refs.stationCascader.getCheckedNodes()[0]
            .data; //获取当前选择节点
          let params = {
            merchant_type: currentNode.merchant_type,
            merchant_id: currentNode.merchant_id,
            value: currentNode.value,
            label: currentNode.label,
            pid: currentNode.pid
          };
          await that.$axios
            .post("/Ostn/switchOstn", params)
            .then(async function(res) {
              if (res.data.status == 200) {
                //切换油站，保留当前路由-start
                that.editableTabs = [];
                that.cacheArray = [];

                let router = that._getRouterDetail(that.$route.path);
                that.changeEditableTabs({
                  title: that.getRouterName(that.$router.currentRoute.path),
                  name: that.tabIndex,
                  router: that.$router.currentRoute.path,
                  current: router.name,
                  prev: router.prev
                });
                that.changeCacheArray(that.$router.currentRoute.path.substr(1));
                that.changeEditableTabsValue(that.tabIndex);
                //切换油站，保留当前路由-end

                that.changeCurrentStation({...params,group_id:currentNode.group_id});

                that.getGroupBaseInfo()
                //隐藏选择油站下拉浮层
                that.$refs.stationCascader.dropDownVisible = false;
              } else {
                that.$message({
                  message: res.data.info,
                  type: "error"
                });
              }
            })
            .catch(function(error) {});
        }
      });
    },

    //下载制卡小工具
    downloadTool() {
      window.location.href =
        "https://wcc-app-public.oss-cn-shenzhen.aliyuncs.com/制卡工具.rar";
    },

    //移除tab
    removeTab(targetName) {
      let tabs = this.editableTabs;
      if (tabs.length == 1) {
        return;
      }
      //移除tab删除缓存组件
      tabs.forEach((tab, index) => {
        if (tab.name == targetName) {
          let name = tab.router.substr(1);
          this.cacheArray.remove(name);
        }
      });
      if (this.editableTabsValue === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.name === targetName) {
            let nextTab = tabs[index + 1] || tabs[index - 1];
            if (nextTab) {
              this.changeEditableTabsValue(nextTab.name);
              this.breadcrumb = {
                current: nextTab.current,
                prev: nextTab.prev
              };
              this.$router.push(nextTab.router).catch(err => {});
            }
          }
        });
      }

      this.editableTabs = tabs.filter(tab => tab.name !== targetName);
    },
    //点击tab
    clickTab(val) {
      this.editableTabs.forEach((tab, index) => {
        if (tab.title === val.label) {
          this.breadcrumb = {
            current: tab.current,
            prev: tab.prev
          };
          this.$router.push(tab.router).catch(err => {});
        }
      });
    },
    //根据路由，获取菜单名称
    getRouterName(path) {
      let name = undefined;
      this.menuList.forEach(item => {
        if (item.childs && item.childs.length > 0) {
          item.childs.forEach(childsItem => {
            if (path == childsItem.entity.path) {
              name = childsItem.entity.name;
            } else {
              if (childsItem.childs && childsItem.childs.length > 0) {
                childsItem.childs.forEach(childsItem1 => {
                  if (path == childsItem1.entity.path) {
                    name = childsItem1.entity.name;
                  }
                });
              }
            }
          });
        } else {
          if (path == item.entity.path) {
            name = item.entity.name;
          }
        }
      });
      return name;
    }
  },
  watch: {
    getCurrentStation(newValue, oldValue) {
      if (newValue.merchant_type == 0) {
        this.$message({
          type: "error",
          message: "暂不支持平台！"
        });
        return false;
      }
      if (newValue.merchant_type != 0 && newValue.value != oldValue.value) {
      }
    },
    //监听路由切换到对应的tab
    currentRoute(newValue) {
      this.editableTabs.forEach(tab => {
        if (tab.router == newValue) {
          this.editableTabsValue = tab.name;
        }
      });
      let router = this._getRouterDetail(this.$route.path);
      this.breadcrumb = {
        current: router.name,
        prev: router.prev
      };

      //判断缓存数组是否存在组件
      let name = newValue.substr(1);
      let currentName = this.cacheArray.find(element => element == name);
      if (!currentName) {
        this.changeCacheArray(name);
      }

      let newTabName = ++this.tabIndex + "";
      let currentTab = this.editableTabs.find(
        element => element.router == newValue
      );
      if (!currentTab) {
        this.changeEditableTabs({
          title: router.name,
          name: newTabName,
          router: newValue,
          current: router.name,
          prev: router.prev
        });
        this.changeEditableTabsValue(newTabName);
      }
    }
  }
};
</script>

<style scoped>
.wrap-header {
  display: inline-block;
}
.wrap-top {
  height: 57px;
  background: #ffffff;

  border-bottom: 1px solid #eaeaea;
  position: relative;
}
.wrap-top-info {
  margin: 0px;
  padding: 0px;
  float: right;
}
.wrap-top-info ul {
  margin: 0px;
  padding: 0px;
}
.wrap-top-info li {
  display: inline-block;
  line-height: 57px;
  padding: 0px 10px;
  color: #999999;
  font-size: 14px;
  cursor: pointer;
}
.wrap-top-info li.on a {
  color: #32af50;
  font-weight: bold;
}
.wrap-top-info li > a,
.wrap-top-info li span {
  display: inline-block;
  padding: 0 10px;
  text-decoration: none;
  color: #999999;
}

.menu {
  width: 188px;
  position: absolute;
}
.el-submenu .el-menu-item {
  min-width: 188px !important;
}

.wrap-menu {
  padding: 0 16px 0;
  box-sizing: content-box;
}

.menu-logo {
  width: 200px;
  height: 60px;
  line-height: 60px;
  position: relative;
  display: inline-block;
  box-sizing: content-box;
  text-align: left;
}
.menu-logo-test {
  height: 60px;
  line-height: 60px;
  font-size: 15px;
  display: inline-block;
  margin-left: 70px;
  font-weight: bolder;
}
.menu-logo .logo {
  width: 36px;
  height: 36px;
  position: absolute;
  left: 10px;
  top: 10px;
  margin-left: 10px;
  margin-top: 2px;
}
.card-menu {
  width: 100%;
}

.card-menu > li {
  border-bottom: 1px solid #eaeaea;
}
.wrap-main {
  margin-left: 188px;
  background: #fff;
}

.wrap-content {
  padding: 0px 0 20px 0;
  background: #fff;
  margin: 0 16px 10px 16px;
}

.el-menu-item.is-active {
  font-weight: bolder;
}

.superHeader_superTop_right_nav {
  display: block;
  width: 250px;
  height: auto;
  position: absolute;
  z-index: 98;
  top: 56px;
  right: 0;
  padding: 0 20px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  border: 1px solid #eaeaea;
  border-radius: 2px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  background: #fff;
  cursor: default;
  z-index: 9999;
}

.superHeader_superTop_right_nav_triangle {
  position: absolute;
  border-color: #fff transparent transparent transparent;
  border-style: solid;
  border-width: 10px 8px 0 8px;
  width: 0px;
  height: 0px;
  top: -10px;
  right: 8%;
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
.superHeader_superTop_right_userinfo {
  display: box;
  display: -webkit-box;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  height: 120px;
  width: 100%;
  padding: 30px 0 30px 10px;
  border-bottom: 1px solid #eaeaea;
}
.superHeader_superTop_right_logo {
  display: block;
  width: 60px;
  height: 60px;
  line-height: 60px;
  text-align: center;
  border-radius: 50%;
  overflow: hidden;
  background: #d8d8d8;
  font-size: 24px;
  color: #fff;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
}
.superHeader_superTop_right_logo img {
  display: block;
  width: 100%;
  height: 100%;
}
.superHeader_superTop_right_userinfoContent {
  display: box;
  display: -webkit-box;
  box-flex: 1;
  -webkit-box-flex: 1;
  box-orient: vertical;
  -webkit-box-orient: vertical;
  padding-left: 10px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
}
.superHeader_superTop_right_userinfoContent_nameTop {
  display: box;
  display: -webkit-box;
  box-pack: justify;
  -webkit-box-pack: justify;
  box-align: center;
  -webkit-box-align: center;
  width: 100%;
  height: 30px;
}
.superHeader_superTop_right_userinfoContent_name,
.superHeader_superTop_right_userinfoContent_nameChange {
  white-space: nowrap;
  max-width: 130px;
  display: block;
  font-size: 12px;
  color: #333;
  line-height: 1;
}
.superHeader_superTop_right_userinfoContent_nameChange {
  color: #32af50;
  cursor: pointer !important;
}
.superHeader_superTop_right_userinfoContent_nameChange:hover {
  color: #00c520;
}
.superHeader_superTop_right_userinfoContent_stationLocal {
  display: block;
  height: 30px;
  line-height: 30px;
  font-size: 12px;
  color: #999;
}
.superHeader_superTop_right_nav_block {
  display: block;
  font-size: 14px;
  color: #333;
  height: 58px;
  line-height: 58px;
  text-indent: 10px;
  border-bottom: 1px solid #eaeaea;
  cursor: pointer !important;
  transition: 0.3s;
  -webkit-transition: 0.3s;
}
.superHeader_superTop_right_nav_block:hover span {
  color: #32af50;
  font-weight: 400;
}
.superHeader_superTop_right_nav_block:nth-last-of-type(1) {
  border-bottom: none;
}
.superHeader_superTop_right_nav_block span {
  position: relative;
  font-weight: 400;
  transition: 0.3s;
  -webkit-transition: 0.3s;
}
.superHeader_superTop_right_nav_block[itnews="1"] span:after {
  position: absolute;
  content: "";
  width: 8px;
  height: 8px;
  background: #e11946;
  border-radius: 50%;
  left: 105%;
  top: -50%;
}

a {
  text-decoration: none;
  -webkit-tap-highlight-color: transparent;
}

.el-breadcrumb {
  font-size: 12px !important;
}

.el-table td,
.el-table th {
  padding: 8px 0;
}
.downloadTool:hover {
  color: #32af50;
}
.sign-up {
  margin-left: 220px;
  margin-right: 20px;
  width: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #cacaca;
  font-size: 12px;
}
</style>
<style>
.main-tabs .el-tabs__nav-scroll {
  background: #f5f5f5;
}
.main-tabs .el-tabs__item {
  background: #e7e7e7;
  height: 32px;
  line-height: 15px;
  border-left: 1px solid #999 !important;
  border-top: 8px solid transparent !important;
  border-bottom: 8px solid transparent !important;
}
.main-tabs .el-tabs__item:first-child {
  border-left: 0 !important;
}
.main-tabs .el-tabs__item.is-active {
  background: #fff;
  border-left: 0 !important;
}
.main-tabs .el-tabs__item.is-active + .el-tabs__item {
  border-left: 0 !important;
}
</style>
