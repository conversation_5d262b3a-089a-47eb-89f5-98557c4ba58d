<template>
    <div id="ruleDetail" class="ruleDetail">
        <el-form class="discountRuleDetail" ref="form" :model="form" label-width="230px">
            <!-- <el-form-item label="优惠类型">
                
            </el-form-item> -->
            <el-form-item label="规则名称">
                {{form.rule_name}}
            </el-form-item>
            <el-form-item label="规则ID">
                {{form.id}}
            </el-form-item>
            <el-form-item label="规则时间">
                {{form.ruleTime}}
            </el-form-item>
            <el-form-item label="规则油站">
                {{form.stname}}
            </el-form-item>
            <el-form-item label="支付方式">
                {{form.pay_way}}
            </el-form-item>
            <el-form-item label="使用类型">
                {{useType}}
            </el-form-item>
            <el-form-item label="卡名称" v-if="showCardName">
                {{form.cardName}}
            </el-form-item>
            <el-form-item v-if="showCardName" label="卡类型">
                {{form.RuleCardType}}
            </el-form-item>
            <el-form-item label="用户限制" v-if="showCardName">
                {{form.account_type}}
            </el-form-item>
            <el-form-item v-if="RuleCustomerInfo && form.account_type=='会员等级'" label="会员等级名称">
                {{RuleCustomerInfo}}
            </el-form-item>
            <el-form-item v-else-if="RuleCustomerInfo && form.account_type=='卡组'" label="卡组名称">
                {{RuleCustomerInfo}}
            </el-form-item>
            <el-form-item v-else-if="RuleCustomerInfo && form.account_type=='车队客户'" label="车队卡名称">
                {{RuleCustomerInfo}}
            </el-form-item>

            <el-form-item label="优惠油品">
                {{form.oil_name}}
            </el-form-item>
            <el-form-item label="优惠规则">
                <div v-html="form.oil_discount_desc"></div>
            </el-form-item>
            <el-form-item v-if="form.IFBonus" label="是否与积分抵油互斥">
                {{form.IFBonus == 1 ? '不限制' : '不可共享'}}
            </el-form-item>
            <el-form-item v-if="form.IFVoucher" label="是否抵扣券共享">
                {{form.IFVoucher == 1 ? '不限制' : '不可共享'}}
            </el-form-item>
            <el-form-item v-if="form.share" label="是否与其他优惠共享">
                {{form.share == 1 ? '可共享' : '不可共享'}}
            </el-form-item>
            <el-form-item label="优先级">
                {{form.priority}}
            </el-form-item>
            <div style="padding:20px 230px">
                <el-button type="primary" @click="baseOut">关闭</el-button>
            </div>
        </el-form>
    </div>
</template>
<script>
export default {
    name:'ruleDetail',
    data() {
        return {
            form:{
                
            },
            showCardName:true,
            RuleCustomerInfo:'',
            useType:''
        }
    },
    methods: {
        //关闭
        baseOut(){
            this.form = {}
            this.$emit('baseOut')
        },
        getRule(id, oils, payType, station_list, cards_list, levels, customerGroupList, companyList){
            this.$axios.post('/CardRule/getCardChargeRuleInfo',{id,detail:1})
            .then(res=>{
                console.log('res',res);
                if(res.data.status == 200){
                    //规则名字
                    this.form.rule_name = res.data.data.public_config.rule_name
                    //规则id
                    this.form.id = res.data.data.public_config.id
                    //规则时间
                    this.form.ruleTime = res.data.data.public_config.start_time + ' 至 ' + res.data.data.public_config.end_time
                    //规则油站
                    if(res.data.data.extend_rule.oils_list){
                        let oilsId = res.data.data.extend_rule.oils_list.map(oil=>oil.OilsID)
                        let oil_name = []
                        oils.forEach(item=>{
                            if(oilsId.includes(item.oil_id)){
                                oil_name.push(item.oil_name)
                            }
                        })
                        this.form.oil_name = oil_name.join(' , ')
                    }

                    //支付方式
                    let payWay = res.data.data.extend_rule.pay_way
                    let pay_way = []
                    payType.forEach(item=>{
                        if(payWay.includes(item.BH)){
                            pay_way.push(item.MC)
                        }
                    })
                    this.form.pay_way = pay_way.join(' , ')

                    //规则油站
                    let oilStid = res.data.data.public_config.use_station_list
                    let stname = []
                    station_list.forEach(item=>{
                        if(oilStid.includes(String(item.stid))){
                            stname.push(item.stname)
                        }
                    })
                    this.form.stname = stname.join(' , ')

                    //卡名称
                    let card_theme = res.data.data.extend_rule.card_theme
                    let cardName = []
                    cards_list.forEach(item=>{
                        if(card_theme.includes(item.ID)){
                            cardName.push(item.Name)
                        }
                    })
                    this.form.cardName = cardName.join(' , ')

                    //卡类型 1个人卡，2车队卡，3不记名卡
                    let card_type = res.data.data.extend_rule.card_type
                    console.log('card_type',card_type);
                    card_type = card_type.map(item=>{
                        if(item == 1){
                            return '个人卡'
                        }else if(item == 2){
                            return '车队卡'
                        }else if(item == 3){
                            return '不记名卡'
                        }
                    })
                    console.log('card_type',card_type);
                    this.form.RuleCardType = card_type.join(' , ')
                    
                    //用户限制
                    if(res.data.data.extend_rule.account_type == ''){
                        this.form.account_type ='不限制'
                    }else if(res.data.data.extend_rule.account_type == 3){
                        this.form.account_type ='会员等级'
                        let myLevels = []
                        levels.forEach(item=>{
                            if(res.data.data.extend_rule.gradeStr.includes(String(item.id))){
                                myLevels.push(item.level_name)
                            }
                        })
                        this.RuleCustomerInfo = myLevels.join(',')
                    }else if(res.data.data.extend_rule.account_type == 2){
                        this.form.account_type ='卡组'
                        let myCustomerGroupList = []
                        customerGroupList.forEach(item=>{
                            if(res.data.data.extend_rule.customer_group_id.includes(String(item.ID))){
                                myCustomerGroupList.push(item.CustomerGroupName)
                            }
                        })
                        this.RuleCustomerInfo = myCustomerGroupList.join(',')
                    }else if(res.data.data.extend_rule.account_type == 0){
                        this.form.account_type ='车队客户'
                        let myCompanyList = []
                        companyList.forEach(item=>{
                            if(res.data.data.extend_rule.company_id.includes(String(item.ID))){
                                myCompanyList.push(item.CompanyName)
                            }
                        })
                        this.RuleCustomerInfo = myCompanyList.join(',')
                    }
                    if(res.data.data.extend_rule.account_type === '0'){
                        this.useType = '按车队客户'
                        this.showCardName = false
                    }else{
                         this.useType = '按制卡规则'
                         this.showCardName = true
                    }
                    //优惠规则
                    this.form.oil_discount_desc = res.data.data.extend_rule.oil_discount_desc
                    //优先级
                    this.form.priority = res.data.data.public_config.priority
                    //积分是否共享 0不限制 1不可共享
                    this.form.IFBonus = res.data.data.extend_rule.discount_config.IFBonus
                    //是否与抵扣券共享
                    this.form.IFVoucher = res.data.data.extend_rule.discount_config.IFVoucher
                    //是否与其他优惠共享
                    this.form.share = res.data.data.extend_rule.discount_config.share
                    this.$forceUpdate()
                }
            })
        },
    },
}
</script>
<style>
    .ruleDetail .el-form-item__label{
        color: #333;
        font-weight: bold;
        padding: 0 24px 0 0;
    }
</style>