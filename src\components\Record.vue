<template>
    <div class="record">
        <el-select
            filterable
            v-model="companyValue"
            @change="getCompanyManagerLog"
            collapse-tags
            style="width:220px"
            placeholder="请选择车队">
            <el-option
            v-for="item in companyOptions"
            :key="item.ID"
            :label="item.CompanyName"
            :value="item.ID">
            </el-option>
        </el-select>
        <el-date-picker
            style="margin-bottom:20px;"
            v-model="dateValue"
            @change="getCompanyManagerLog"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期">
        </el-date-picker>
        <div v-loading="loading">
            <div v-if="listData.length == 0" class="item no-data">
                暂无数据
            </div>
            <div v-else class="item" v-for="(item,index) in listData" :key="item.index" v-infinite-scroll="load" style="overflow:auto">
                <p class="title">{{item.date}}</p>
                <div class="line-data"  v-for="(subitem,subindex) in item.data" :key="subitem.index">
                    <div class="txt" @click="showDetail(index+'-'+subindex)">
                        <div>
                            <i class="el-icon-edit icon"></i>
                            <span>{{subitem.data}}</span>
                            <i v-if="subitem.type == 2" :class="'arrow'+index+'-'+subindex" class="el-icon-caret-top"></i>
                        </div>
                        <span>{{subitem.time}}</span>
                    </div>
                    <el-table
                        v-if="subitem.type == 2"
                        class="table-data"
                        :class="'table'+index+'-'+subindex"
                        :data="subitem.detail">
                        <el-table-column
                            prop="CardNO"
                            label="卡号">
                        </el-table-column>
                        <el-table-column
                            prop="CardFaceNumber"
                            label="卡面卡号">
                        </el-table-column>
                        <el-table-column
                            prop="TellPhone"
                            label="手机号">
                        </el-table-column>
                        <el-table-column
                            prop="TransferAmount"
                            label="划拨金额（元）">
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'Record',
    data () {
        return {
            companyOptions: [{
                CompanyName:"全部车队",
                ID:0,
            }],//车队列表
            companyValue: 0,//选中车队
            dateValue:"",
            listData:[],
            loading:false,
            tableData:[]
        }
    },
    mounted(){
        //获取当前时间
        let currentDateEnd = this.$moment().format('YYYY-MM-DD');
        this.dateValue = currentDateEnd;
        this.getCompanyList();
        this.getCompanyManagerLog();
    },
    methods:{
        //获取车队列表
        getCompanyList(){
            this.$axios.post('/CompanyCard/getCompanyList',{
                page: 1,
                page_size: '1000',
                input: "",
                state:0,
            }).then((res)=>{
                if(res.status == 200){
                    this.companyOptions = [{
                        CompanyName:"全部车队",
                        ID:0,
                    }];
                    this.companyOptions = this.companyOptions.concat(res.data.data.dt);
                }
            })
        },
        getCompanyManagerLog(){
            let that = this;
            that.loading = true;
            that.listData = [];
            if(that.dateValue){
                that.$axios.post('/CompanyManager/getCompanyManagerLog', {
                    start_time:that.$moment(that.dateValue+" 00:00:00").valueOf()/1000,
                    end_time:that.$moment(that.dateValue+" 23:59:59").valueOf()/1000
                })
                .then(function (res) {
                    that.loading = false;
                    if(res.data.status == 200){
                        that.listData = res.data.data;
                    }else{
                        that.$message({
                            message: res.data.info,
                            type: 'error'
                        });
                    }
                })
                .catch(function (error) {
                });
            }
        },
        //展示详情
        showDetail(index){
            let arrowDom = document.getElementsByClassName('arrow'+index);
            let tableDom = document.getElementsByClassName('table'+index);
            if(arrowDom[0].classList.contains("el-icon-caret-top")){
                arrowDom[0].classList.replace("el-icon-caret-top","el-icon-caret-bottom");
                tableDom[0].style.display="block";
            }else{
                arrowDom[0].classList.replace("el-icon-caret-bottom","el-icon-caret-top");
                tableDom[0].style.display="none";
            }
        },
        //滚动加载
        load(){
        }
    }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.record{
    padding: 20px;
}
.record .item{
    max-height: 746px;
}
.record .item .title{
    font-size: 16px;
    margin: 0;
    border-bottom: 1px solid #EAEAEA;
    padding: 20px 0;
}
.line-data .txt{
    padding: 20px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #EAEAEA;
}
.record .icon{
    margin-right: 24px;
}
.record .table-data{
    width: 93%;
    margin: 0 auto;
    display: none;
}
.record .no-data{
    text-align: center;
    padding: 20px 0;
}
</style>
