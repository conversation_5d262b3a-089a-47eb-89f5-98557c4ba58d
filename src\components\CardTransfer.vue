<template>
<div id="CardTransfer" class="CardTransfer">
    <div class="companyList" v-show="companyListShow">
        <div class="search flex items-center py-5 justify-between">
          <div>
            <el-select
              v-model="searchText"
              class="w-200px mr-10px"
              clearable
              @clear="searchAllCompany"
              filterable
              remote
              reserve-keyword
              placeholder="请输入车队名称"
              :remote-method="remoteMethod">
              <el-option
                v-for="item in companyOptions"
                :key="item.CompanyID"
                :label="item.CompanyName"
                :value="item.CompanyID">
              </el-option>
            </el-select>
            <el-button type="primary" @click="searchCompany" :disabled="!searchText">查询</el-button>
            <el-button class="ml-10px" @click="searchAllCompany">全部</el-button>
          </div>
            <el-button 
              type="primary" 
              @click="showWorkflowDetail"
              v-if="showRechargeApproval"
              class="ml-auto">
              我提交的
            </el-button>
        </div>
        <el-table
            class="cardTransferData"
            v-loading="companyLoading"
            :data="companyListData"
            style="width: 100%;margin-bottom:20px">
            <el-table-column
                align="left"
                prop="CompanyName"
                min-width="240"
                label="车队名称"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="left"
                min-width="180"
                label="所属油站">
                <template slot-scope="scope">{{ getStationName(scope.row.StationNO) }}</template>
            </el-table-column>
             <el-table-column
                align="center"
                prop="CompanyCardNum"
                min-width="120"
                label="卡数量">
            </el-table-column>
            <el-table-column
                align="left"
                prop="CompanyAccount"
                min-width="120"
                label="母账余额(元)"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="left"
                prop="CardSumAccount"
                min-width="120"
                label="子账余额(元)"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                prop="CompanyContacts"
                min-width="120"
                label="联系人"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                fixed="right"
                align="center"
                min-width="200"
                label="操作">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="goToRecharge(scope.row)">充值</el-button>
                    <el-button type="text" :disabled="scope.row.CompanyAccount == 0" size="small" @click="goToTransfer(scope.row)">资金划拨</el-button>
                    <el-button type="text" :disabled="scope.row.CardSumAccount == 0" size="small" @click="showCardList(scope.row)">资金返款</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 页码 -->
        <div class="page_content">
            <el-pagination class="page_left"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-size="pageSize"
                layout="prev, pager, next"
                :total="total">
            </el-pagination>
            <el-pagination class="page_right"
                @size-change="handleSizeChange"
                :page-sizes="[10, 15, 20, 30]"
                :page-size="pageSize"
                layout="total, sizes"
                :total="total">
            </el-pagination>
        </div>
    </div>
    <!-- 充值弹窗 -->
  <el-dialog
    :close-on-click-modal="false"
    title="车队账户充值"
    :visible.sync="rechargeDialogVisible"
    width="600px">
    <div class="card-info">
      <div class="item">
        <span class="tab">车队名称</span>
        <span>{{companyInfo.company_name}}</span>
      </div>
      <div class="item">
        <span class="tab">账户余额</span>
        <span>{{(Number(companyInfo.amount)+Number(companyInfo.give_amount)).toFixed(2)}}</span>
      </div>
      <div class="item">
        <span class="tab">信用功能</span>
        <!-- <span>{{companyInfo.CompanyCreditType}}<span v-if="companyInfo.CompanyCreditType != '不启用'">，额度{{companyInfo.CompanyCreditAccount}}元</span></span> -->
        <span>{{getCreditName(companyInfo.credit_type)}}<span v-if="companyInfo.credit_type != '0'">，额度{{companyInfo.credit_num}}元</span></span>
      </div>
      <div class="item">
        <span class="tab">本次充值</span>
        <div>
          <el-input-number 
            :disabled="rechargeLoading"
            :precision="2" 
            :min="0" 
            :max="*********" 
            :controls="false" 
            v-model="amount" 
            placeholder="请输入充值金额">
          </el-input-number>
          <p style="font-size:12px;display: flex;align-items: center;" v-show="giveAmount!=0 || couponInfo" v-if="!showRechargeApproval">
            <img src="../assets/images/given.png" class="given-icon" alt="">
            <span v-show="giveAmount!=0" style="color:#32af50">{{giveAmount}}元</span>
            <span style="color:#32af50" v-if="couponInfo"><span v-show="giveAmount!=0">&nbsp;+&nbsp;</span>{{couponInfo}}</span>
          </p>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="rechargeDialogVisible = false" :disabled="rechargeLoading">取 消</el-button>
        <el-button v-if="showRechargeApproval" type="primary" size="mini" :disabled="rechargeLoading" @click="submitRechargeApproval()">
          <i v-if="rechargeLoading" class="el-icon-loading"></i>
          提交审核
        </el-button>
        <el-button v-else type="primary" size="mini" :disabled="!transferAble || rechargeLoading" @click="chargeCompanyCard()">
          <i v-if="rechargeLoading" class="el-icon-loading"></i>
          确 定
        </el-button>
    </span>
  </el-dialog>

    <!--新版充值-->
    <el-dialog
      @opened="$nextTick(()=>$refs.form.resetFields())"
      @closed="fixedChargeInit"
      :close-on-click-modal="false"
      title="车队账户充值"
      top="50px"
      :visible.sync="dialogParams.showFixedCharge"
      width="900px">
        <div v-loading="dialogParams.oilLoading || dialogParams.charging">
            <el-form :rules="dialogParams.rules" ref="form" :model="dialogParams.query" label-width="100px">
            <el-form-item label="车队名称">
                <span>{{ companyInfo.company_name }}</span>
            </el-form-item>
            <el-form-item label="账户余额">
                <span>{{companyInfo.amount}}</span>
            </el-form-item>
            <el-form-item label="信用功能">
                <span>{{getCreditName(companyInfo.credit_type)}}<span v-if="companyInfo.credit_type != '0'">，额度{{companyInfo.credit_num}}元</span></span>
            </el-form-item>
            <el-form-item prop="oils" label="充值油品" v-if="companyInfo && companyInfo.oil_list">
                <el-radio-group class="space-y-2" v-model="dialogParams.query.oils">
                <el-radio v-for="item of companyInfo.oil_list" :key="item.oil_id" :label="item.oil_id">
                    {{ item.oil_name }}
                </el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item prop="amount" label="充值金额">
                <el-input-number :precision="2" :min="0" :max="*********" :controls="false" v-model="dialogParams.query.amount" placeholder="请输入充值金额" @change="automaticCalculation('amount')"></el-input-number>
                <span>元</span>
            </el-form-item>
            <el-form-item prop="unit_price" label="油品单价" v-if="currentFixPriceMode === FIXED_PRICE_MODE.FixedAmount">
                <el-input-number :precision="2" :min="0" :max="1000" :controls="false" v-model="dialogParams.query.unit_price" placeholder="请输入油品单价" @change="automaticCalculation('unit_price')"></el-input-number>
                <span>元/升</span>
            </el-form-item>
            <el-form-item prop="liters" label="油品升数">
                <el-input-number :precision="4" :min="0" :controls="false" v-model="dialogParams.query.liters" placeholder="请输入油品升数"  @change="automaticCalculation('liters')"></el-input-number>
                <span>升</span>
                <div class="p-0" v-if="currentFixPriceMode === FIXED_PRICE_MODE.FixedRise">油品单价换算为<span class="text-primary font-bold">{{dialogParams.query.unit_price}}</span>元/升</div>
            </el-form-item>
            <el-form-item label="备注">
                <el-input :rows="3" type="textarea" class="w-90%" show-word-limit :maxlength="200" v-model="dialogParams.query.remark" placeholder="请输入备注"/>
            </el-form-item>
            </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button size="mini" :disabled="dialogParams.charging" @click="dialogParams.showFixedCharge = false">取 消</el-button>
            <el-button type="primary" :disabled="dialogParams.charging" size="mini" @click="showRechargeApproval ? submitRechargeApproval() : chargeCompanyCard()">{{ showRechargeApproval ? '提交审核' : '确 定' }}</el-button>
        </span>
    </el-dialog>

    <div v-show="transferListShow"  v-loading="dialogParams.loadingInfo">
        <div style="padding:20px">
        <div class="header">
          <el-button size="mini" plain @click="goBack">返 回</el-button>
          <div class="downloads" v-if="!showLockPrice || currentFixPriceMode === FIXED_PRICE_MODE.No">
            <el-button type="text" @click="downloadTemplate" size="mini">下载导入模板</el-button>
            <el-divider direction="vertical"></el-divider>
            <el-upload
              class="upload" style="display:inline"
              action=""
              :multiple="false"
              :show-file-list="false"
              accept="csv, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
              :http-request="httpRequest">
              <el-button type="text" size="mini">批量导入划拨</el-button>
          </el-upload>
          </div>
        </div>
        <div class="company-info">
            <div class="line">
                <div class="item">
                    <span class="tab">车队名称</span>
                    <span>{{companyInfo.company_name}}</span>
                </div>
                <div class="item">
                    <span class="tab">所属油站</span>
                    <span>{{getStationName(companyInfo.station_id)}}</span>
                </div>
                <div class="item">
                    <span class="tab">联系人</span>
                    <span>{{companyInfo.company_contacts}}</span>
                </div>
            </div>
            <div class="line">
                <div class="item">
                    <span class="tab">本金</span>
                    <span>{{Number(companyInfo.amount).toFixed(2)}}元</span>
                </div>
                <div class="item">
                    <span class="tab">赠金</span>
                    <span>{{Number(companyInfo.give_amount).toFixed(2)}}元</span>
                </div>
                <div class="item" v-if="showLockPrice && currentFixPriceMode === FIXED_PRICE_MODE.FixedRise">
                    <span class="tab">母账升数</span>
                    <span>{{sumUnitLiter(dialogParams.item.companyBatches)}}升</span>
                </div>
                <div class="item">
                    <span class="tab">信用功能</span>
                    <!-- <span>{{companyInfo.CompanyCreditType}}</span> -->
                    <span>{{getCreditName(companyInfo.credit_type)}}</span>
                </div>
            </div>
        </div>
        <div class="tips">
            <div>
                <el-button type="primary" :disabled="multipleSelection.length<2" @click="showMoreTransfer">批量划拨</el-button>
                <span class="txt">选中{{multipleSelection.length}}张卡 </span>
            </div>
            <div class="search">
                <span>查询类型</span>
                <el-radio-group v-model="searchType" style="margin:0 15px">
                    <el-radio label="1">手机号</el-radio>
                    <el-radio label="0">卡号</el-radio>
                    <el-radio label="2">卡面卡号</el-radio>
                </el-radio-group>
                <div class="search">
                    <el-input style="width:210px;margin-right:10px"  v-model="searchCardText" :placeholder="searchType==0?'请输入卡号':searchType==1?'请输入手机号':'请输入卡面卡号'" clearable></el-input>
                    <el-button type="primary"  @click="searchCard">查询</el-button>
                </div>
            </div>
        </div>
        </div>
        <el-table
            v-loading="cardLoading"
            :element-loading-text="capitalAllocationContent"
            :data="cardListData"
            @selection-change="handleSelectionChange"
            style="width: 100%;margin-bottom:20px">
            <el-table-column
            type="selection"
            width="55">
            </el-table-column>
            <el-table-column
                align="left"
                prop="CardNO"
                label="卡号"
                width="180"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                prop="Phone"
                label="手机号"
                width="180"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                label="卡面卡号"
                prop="CardNumber"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                label="持卡人"
                prop="cardholder_name"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                label="车牌号"
                prop="CarNumber"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                prop="SUMAmount"
                label="卡账余额(元)">
            </el-table-column>
            <el-table-column
                v-if="showLockPrice && currentFixPriceMode === FIXED_PRICE_MODE.FixedRise"
                align="center"
                prop="CardSumLiter"
                label="子卡升数">
            </el-table-column>
            <el-table-column
                align="center"
                prop="Amount"
                label="本金(元)">
            </el-table-column>
            <el-table-column
                align="center"
                prop="GiveAmount"
                label="赠金(元)">
            </el-table-column>
            <el-table-column
                align="center"
                label="操作">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="showSingleTransfer(scope.row)">资金划拨</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 页码 -->
        <div class="page_content">
            <el-pagination class="page_left"
                @current-change="handleCardCurrentChange"
                :current-page="currentCardPage"
                :page-size="pageCardSize"
                layout="prev, pager, next"
                :total="totalCard">
            </el-pagination>
            <el-pagination class="page_right"
                @size-change="handleCardSizeChange"
                :page-sizes="[10, 15, 20, 30, 50, 100]"
                :page-size="pageCardSize"
                layout="total, sizes"
                :total="totalCard">
            </el-pagination>
        </div>
        <!-- 单人划拨 -->
        <el-dialog
            :destroy-on-close="true"
            v-loading="dialogParams.allocationLoading"
            @opened="allocationDialogInit"
            :close-on-click-modal="false"
            title="资金划拨"
            :visible.sync="singleDialogVisible"
            width="800px">
            <div class="card-info">
              <el-form ref="allocationForm" :model="dialogParams.query" label-width="90px" :rules="dialogParams.allocationRules">
                <el-form-item label="卡号">
                  <span>{{cardInfo.CardNO}}</span>
                </el-form-item>
                <el-form-item label="手机号">
                  <span>{{cardInfo.Phone}}</span>
                </el-form-item>
                <el-form-item label="卡面卡号">
                  <span>{{cardInfo.CardNumber || "--"}}</span>
                </el-form-item>
                <el-form-item label="卡账余额">
                 <span>{{cardInfo.SUMAmount}}元</span>
                </el-form-item>
                <template v-if="showLockPrice && currentFixPriceMode !== FIXED_PRICE_MODE.No">
                  <el-form-item label="划拨油品" prop="oils" required  v-loading="dialogParams.companyStationListLoading">
                    <p class="m-0" v-if="!dialogParams.companyStationList || dialogParams.companyStationList && dialogParams.companyStationList.length === 0">无可用油品</p>
                    <template v-else>
                      <el-radio-group v-model="dialogParams.query.oils"
                                      @change="$refs.allocationForm.validateField('amount')" class="space-y-2">
                        <el-radio class="block" :label="item.oil_id" :key="item.oil_id"
                                  v-for="item of dialogParams.companyStationList" required>
                          {{ item.oil_name }}
                          <span v-if="Object.keys(currentCompanyBatchInfo).length > 0">
                          (余额：{{ currentCompanyBatchInfo[item.oil_id] }}{{
                              currentFixPriceMode === FIXED_PRICE_MODE.FixedAmount ? '元' : '升'
                            }})
                        </span>
                        </el-radio>
                      </el-radio-group>
                    </template>
                  </el-form-item>
                  <el-form-item label="划拨资金" prop="amount">
                    <el-input-number v-model="dialogParams.query.amount" :controls="false" :min="0" :max="*********" :precision="currentFixPriceMode === FIXED_PRICE_MODE.FixedAmount ? 2 : 4" clearable placeholder="请输入划拨资金" class="w-160px"/>
                    <span class="pl-2">{{ currentFixPriceMode === FIXED_PRICE_MODE.FixedAmount ? '元' : '升' }}</span>
                  </el-form-item>
                </template>
                <template v-else>
                  <el-form-item label="划拨本金">
                    <el-input style="width:160px" v-model="PrincipalAmount" clearable
                              placeholder="请输入划拨本金"></el-input>
                  </el-form-item>
                  <el-form-item label="划拨赠金">
                    <el-input style="width:160px;margin-left: 4px" v-model="giveAmount" clearable
                              placeholder="请输入划拨赠金"></el-input>
                  </el-form-item>
                </template>
              </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" @click="singleDialogVisible = false">取 消</el-button>
                <el-button type="primary" size="mini" v-if="showLockPrice && currentFixPriceMode !== FIXED_PRICE_MODE.No" @click="allocationConfirm">确 定</el-button>
                <el-button type="primary" size="mini" v-else :disabled="!(PrincipalAmount && giveAmount)" @click="transferCompanyAccount">确 定</el-button>
            </span>
        </el-dialog>
        <!-- 批量划拨 -->
        <el-dialog
            :close-on-click-modal="false"
            title="资金批量划拨"
            :visible.sync="moreDialogVisible"
            :width="radioType==1?'720px':'940px'">
            <div style="margin-bottom:30px">
                <span>金额划拨</span>
                <el-radio-group v-model="radioType" @change="changeRadioType">
                    <el-radio label="1">划拨相同金额</el-radio>
                    <el-radio label="2">划拨不同金额</el-radio>
                </el-radio-group>
            </div>
            <p>已选中 <span>{{multipleSelection.length}}</span> 张卡</p>
            <el-table
                :data="multipleSelection"
                style="width: 100%">
                <el-table-column
                    align="center"
                    prop="CardNO"
                    label="卡号">
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="Phone"
                    label="手机号">
                </el-table-column>
                <el-table-column
                    align="center"
                    label="卡面卡号"
                    prop="CardNumber"
                    :formatter="formatterCellval">
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="Amount"
                    label="卡账余额(元)">
                </el-table-column>
                <el-table-column
                    v-if="radioType==2"
                    align="left"
                    width="350px"
                    label="划拨余额(元)">
                    <template slot-scope="scope">
                        <el-input style="width:145px" v-model="scope.row.PrincipalAmount" clearable placeholder="请输入划拨本金"></el-input>
                        <el-input style="width:145px" v-model="scope.row.giveAmount" clearable placeholder="请输入划拨赠金"></el-input>
                    </template>
                </el-table-column>
            </el-table>
            <div style="margin-top:24px" v-if="radioType==1">
                <span>单张卡划拨本金</span>
                <el-input style="width:145px" v-model="PrincipalAmount" clearable placeholder="请输入划拨本金"></el-input>元，
                <span>赠金</span>
                <el-input style="width:145px" v-model="giveAmount" clearable placeholder="请输入划拨赠金"></el-input>元
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size="mini" @click="moreDialogVisible = false">取 消</el-button>
                <el-button type="primary" v-if="radioType==1" :disabled="!(PrincipalAmount && giveAmount)" size="mini" @click="transferCompanyAccount">确 定</el-button>
                <el-button type="primary" v-if="radioType==2" size="mini" @click="transferCompanyAccount">确 定</el-button>
            </span>
        </el-dialog>
        <!-- 定升批量划拨 -->
      <el-dialog
        :destroy-on-close="true"
        v-loading="dialogParams.allocationLoading"
        @opened="allocationDialogInit"
        :close-on-click-modal="false"
        title="资金批量划拨"
        :visible.sync="dialogParams.showBatchAllocation"
        width="940px">
        <el-form ref="allocationBatchForm" :model="dialogParams.query" :rules="dialogParams.allocationRules">
          <ul class="space-y-4 pb-4 list-none">
            <li>
              <el-form-item prop="radioType">
                <span>金额划拨</span>
                <el-radio-group v-model="dialogParams.radioType" @change="changeRadioType($event)">
                  <el-radio :label="1">划拨相同金额</el-radio>
                  <el-radio :label="2">划拨不同金额</el-radio>
                </el-radio-group>
              </el-form-item>
            </li>
            <li v-loading="dialogParams.companyStationListLoading">
              <el-form-item prop="oils">
                <span>划拨油品</span>
                <p class="m-0" v-if="!dialogParams.companyStationList || dialogParams.companyStationList && dialogParams.companyStationList.length === 0">无可用油品</p>
                <template v-else>
                  <el-radio-group v-model="dialogParams.query.oils" @change="$refs.allocationBatchForm.validateField('amount')">
                    <el-radio :label="item.oil_id" :key="item.oil_id" v-for="item of dialogParams.companyStationList" required>
                      {{ item.oil_name }}
                      <span v-if="Object.keys(currentCompanyBatchInfo).length > 0">
                  (余额：{{ currentCompanyBatchInfo[item.oil_id] }}{{ currentFixPriceMode === FIXED_PRICE_MODE.FixedAmount ? '元' : '升' }})
                </span>
                    </el-radio>
                  </el-radio-group>
                </template>
              </el-form-item>
            </li>
          </ul>
          <p>已选中 <span>{{ multipleSelection.length }}</span> 张卡</p>
          <el-table
            :data="multipleSelection"
            style="width: 100%">
            <el-table-column
              align="center"
              prop="CardNO"
              label="卡号">
            </el-table-column>
            <el-table-column
              align="center"
              prop="Phone"
              label="手机号">
            </el-table-column>
            <el-table-column
              align="center"
              prop="CardNumber"
              label="卡面卡号">
            </el-table-column>
            <el-table-column
              align="center"
              prop="Amount"
              label="账户余额 ">
            </el-table-column>
            <el-table-column
              v-if="dialogParams.radioType===2"
              align="left"
              label="划拨余额">
              <template slot-scope="scope">
                <div class="flex items-center">
                  <el-input-number v-model="scope.row.TransferUnitLiter"
                            :controls="false" :min="0" :max="*********" :precision="currentFixPriceMode === FIXED_PRICE_MODE.FixedAmount ? 2 : 4" clearable :placeholder="`请输入${ currentFixPriceMode === FIXED_PRICE_MODE.FixedAmount ? '金额' : '升数'}`" class="w-160px"></el-input-number>
                  <span class="pl-2">{{ currentFixPriceMode === FIXED_PRICE_MODE.FixedAmount ? '元' : '升' }}</span></div>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top:24px" v-if="dialogParams.radioType===1">
            <el-form-item prop="amount">
              <span>每张卡划拨</span>
              <el-input-number v-model="dialogParams.query.amount" :controls="false" :min="0" :max="*********" :precision="currentFixPriceMode === FIXED_PRICE_MODE.FixedAmount ? 2 : 4" clearable :placeholder="`请输入${ currentFixPriceMode === FIXED_PRICE_MODE.FixedAmount ? '金额' : '升数'}`" class="w-160px"/>
              <span class="pl-2">{{ currentFixPriceMode === FIXED_PRICE_MODE.FixedAmount ? '元' : '升' }}</span>
            </el-form-item>
          </div>
        </el-form>
        <template #footer>
          <el-button size="mini" @click="dialogParams.showBatchAllocation = false">取 消</el-button>
          <el-button type="primary" size="mini" @click="allocationConfirm">确 定</el-button>
        </template>
      </el-dialog>




    </div>
    <div v-show="showCompany">
        <div style="padding:20px">
        <el-button plain size="mini" @click="goBack">返 回</el-button>
        <p style="font-size:16px; font-weight:bold">当前车队：{{companyName}}</p>
        <div style="margin-bottom:20px">
            <el-date-picker
                style="margin-right:20px"
                v-model="searchDate"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="{
                  disabledDate(time) {
                    return time.getTime() >= $moment().add(1,'days').startOf('day').valueOf();
                  }
                }"
                format="yyyy 年 MM 月 dd 日"
                value-format="yyyy-MM-dd"
                @change="getTransferDetailed">
            </el-date-picker>
            <span>查询类型</span>
            <el-radio-group v-model="searchType">
                <el-radio label="1">手机号</el-radio>
                <el-radio label="0">卡号</el-radio>
                <el-radio label="2">卡面卡号</el-radio>
            </el-radio-group>
            <el-input
                style="width:210px"
                :placeholder="searchType=='0'?'请输入卡号':searchType=='1'?'请输入手机号':'请输入卡面卡号'"
                v-model="searchTxt">
            </el-input>
            <el-button type="primary" :disabled="!searchTxt.length>0" @click="getTransferDetailed">查询</el-button>
        </div>
        <el-button type="primary" :disabled="multipleSelectionCard.length<2" @click="showBatchRefund">批量返款</el-button>
        <span>选中{{multipleSelectionCard.length}}张卡</span>
        </div>
        <el-table
            ref="multipleTable"
            v-loading="cardLoading"
            :data="cardList"
            style="width: 100%"
            @selection-change="handleSelectionCardChange">
            <el-table-column
                align="center"
                type="selection"
                width="55">
            </el-table-column>
            <el-table-column
                align="center"
                prop="CardNO"
                label="卡号"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                prop="Phone"
                label="手机号"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                prop="CardNumber"
                label="卡面卡号"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                label="持卡人"
                prop="cardholder_name"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                prop="CarNumber"
                label="车牌号"
                :formatter="formatterCellval">
            </el-table-column>
            <el-table-column
                align="center"
                prop="ReceiveAmount"
                label="划拨金额(元)">
            </el-table-column>
            <el-table-column
                align="center"
                prop="ReceiveGiveAmount"
                label="赠送金额(元)">
            </el-table-column>
            <el-table-column
                align="center"
                label="划拨时间">
                <template slot-scope="scope">{{ $moment(scope.row.CreateTime).format("YYYY-MM-DD HH:mm:ss") }}</template>
            </el-table-column>
            <el-table-column
                align="center"
                label="操作">
                <template slot-scope="scope"><el-button type="text" @click="showRefund(scope.row)">资金返款</el-button></template>
            </el-table-column>
        </el-table>

        <!-- 页码 -->
        <div class="page_content">
            <el-pagination class="page_left"
                @current-change="handleTransferCurrentChange"
                :current-page="currentPageTransfer"
                :page-size="pageSizeTransfer"
                layout="prev, pager, next"
                :total="totalTransfer">
            </el-pagination>
            <el-pagination class="page_right"
                @size-change="handleTransferSizeChange"
                :page-sizes="[10, 15, 20, 30, 50, 100]"
                :page-size="pageSizeTransfer"
                layout="total, sizes"
                :total="totalTransfer">
            </el-pagination>
        </div>
    </div>
    <!-- 返款弹窗 -->
    <el-dialog
        :close-on-click-modal="false"
        title="子卡资金返款"
        :visible.sync="dialogVisible"
        width="942px">
        <p>选中<span style="color:#32AF50">{{multipleSelectionCard.length}}</span>张卡</p>
        <el-table
            v-loading="dialogParams.companyStationListLoading"
            :data="multipleSelectionCard"
            style="width: 100%">
            <el-table-column
                align="center"
                prop="CardNO"
                label="卡号">
            </el-table-column>
            <el-table-column
                align="center"
                prop="Phone"
                label="手机号">
            </el-table-column>
            <el-table-column
                align="center"
                prop="CardNumber"
                label="卡面卡号"
                :formatter="formatterCellval">
            </el-table-column>
            <template v-if="showLockPrice && currentFixPriceMode !== FIXED_PRICE_MODE.No ">
              <el-table-column
                align="center"
                prop="ChargeOil"
                label="划拨油品">
                <template #default="{row}">
                  <span>{{getOilName(row.ChargeOil)}}</span>
                </template>
              </el-table-column>
              <el-table-column
                v-if="currentFixPriceMode === FIXED_PRICE_MODE.FixedRise"
                align="center"
                prop="UnitLiter"
                label="划拨升数">
              </el-table-column>
              <el-table-column
                v-else-if="currentFixPriceMode === FIXED_PRICE_MODE.FixedAmount"
                align="center"
                prop="ReceiveAmount"
                label="划拨金额">
              </el-table-column>
            </template>
            <el-table-column
                v-else
                align="center"
                prop="ReceiveAmount"
                label="划拨余额(元)">
            </el-table-column>
            <el-table-column
                v-if="!showLockPrice && currentFixPriceMode !== FIXED_PRICE_MODE.No"
                align="center"
                prop="ReceiveGiveAmount"
                label="赠送余额(元)">
            </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false;" size="mini">取 消</el-button>
            <el-button type="primary" @click="refund" size="mini">确认返款</el-button>
        </span>
    </el-dialog>

    <div v-if="showRechargeApprovalView">
      <div style="padding:20px">
        <el-button plain size="mini" @click="goBack">返 回</el-button>
        <span style="margin-left: 20px;">我提交的充值审核订单</span>
      </div>
      <recharge-approval :includeSubmitterID="true" :hideStatusOptions="true" />
    </div>
</div>
</template>

<script>
import {mapGetters,mapState} from 'vuex'
import axios from 'axios'
import XLSX from 'xlsx'
import BigNumber from "bignumber.js";
import {FIXED_PRICE_MODE} from "../utils/contants/fixedPrice";
import {getCompanyCardStations, getCompanyDetail, transferCompanyAccountV2} from "../api/CompanyCard";
import companyCard from "../mixins/companyCard";
import RechargeApproval from '../views/RechargeApproval/RechargeApproval.vue'

export default {
    components: {
      RechargeApproval
    },
    mixins: [companyCard],
    name:"CardTransfer",
    data(){
        return{
            companyListShow:true,//默认显示车队列表
            transferListShow:false,//划拨列表
            showRechargeApprovalView: false, // 新增控制变量
            searchText:"",
            stationList:[],//油站列表
            companyLoading:true,
            companyListData:[],//车队列表数据
            currentPage:1,
            pageSize:10,
            total:0,
            company_id:"65",//车队id
            companyName:"65",//车队id
            companyInfo:{
            },//展示的车队信息
            searchType:"1",//查询类型
            searchCardText:"",
            cardListData:[],//车队卡列表
            multipleSelection:[],//多选数据
            currentCardPage:1,
            pageCardSize:10,
            totalCard:0,
            orig_amount:"",
            giveAmount:'',//赠金
            PrincipalAmount:'',//本金
            cardInfo:{
            },//展示的车队信息
            singleDialogVisible:false,//单人资金划拨
            transfer_info:[],//划拨参数
            moreDialogVisible:false,//多人划拨
            radioType:"1",//默认划拨相同金额
            transferType:1,//1表示单人划拨，2表示批量划拨同样金额，3表示批量划拨不同金额
            rechargeDialogVisible:false,//充值弹窗
            rechargeLoading: false, // 旧版充值loading状态
            amount:"",//充值金额
            showCompany:false,
            searchDate:[],//查询时间
            searchTxt:"",
            dialogVisible:false,//显示弹窗
            cardLoading:false,
            cardList:[],//卡列表
            currentPageTransfer:1,
            pageSizeTransfer:10,
            totalTransfer:0,
            multipleSelectionCard:[],
            transferAble:false,//是否允许
            couponInfo:"",//充值提示券信息
            fullscreenLoading: false,
            capitalAllocationContent:'',//资金划拨加载提示文字,
            timeout:null,//防抖时间
            companyOptions: [], // 车队列表
            dialogParams:{
              charging:false,
              rules: {
                  oils: [
                      { required: true, message: '请选择充值油品', trigger: 'change' }
                  ],
                  amount: [
                      { required: true, message: '请输入充值金额', trigger: 'blur' },
                      { type: 'number', min: 1, message: '充值金额必须大于等于1', trigger: 'blur' }
                  ],
                  unit_price: [
                      { required: true, message: '请输入油品单价', trigger: 'blur' },
                      { required: true, type: 'number', min: 0.01, message: '油品单价必须大于0', trigger: 'blur' }
                  ],
                  liters: [
                      { required: true, message: '请输入油品升数', trigger: 'change' },
                      { required: true, type:'number',min:1,message: '油品升数必须大于等于1', trigger: 'blur' },
                      { validator: (rule, value, callback) =>{
                        if(this.currentFixPriceMode === FIXED_PRICE_MODE.FixedRise){
                          callback();
                          return;
                        }
                        const { amount, unit_price } = this.dialogParams.query;
                        value = new BigNumber(value).toFixed(4);
                        const calculatedLiters = new BigNumber(amount || 0).div(new BigNumber(unit_price || 1)).toFixed(4);
                        if (value !== calculatedLiters) {
                            callback(new Error('油品升数与充值金额和油品单价不匹配'));
                        } else {
                            callback();
                        }
                      }, trigger: 'change' }
                  ]
              },
              showFixedCharge:false,
              showBatchAllocation:false,
              radioType:1,
              oilLoading:false,
              oilList:[],
              item:{},
              query:{
                oils:'',
                liters:0,
                unit_price:0,
                amount:0,
                remark:'',
              },
              companyStationList:[],
              companyStationListLoading:false,
              allocationRules: {
                oils: [
                  { required: true, message: '请选择划拨油品', trigger: 'change' }
                ],
                amount: [
                  { required: true, message: '请输入划拨资金', trigger: 'blur' },
                  { validator: (rule, value, callback)=> {
                    if (value === null || value === '' || value === undefined) {
                      callback(new Error('划拨资金不能为空'));
                    } else if (value <= 0) {
                      callback(new Error('划拨资金必须大于0'));
                    } else if (value < 0.1 && this.currentFixPriceMode === FIXED_PRICE_MODE.FixedRise) {
                      callback(new Error('划拨升数不能小于0.1'));
                    }  else {
                      const selectedOilId = this.dialogParams.query.oils;
                      const balance = this.currentCompanyBatchInfo[selectedOilId];
                      if (balance !== undefined && value > balance) {
                        callback(new Error(`划拨总额不能大于当前余额（当前剩余：${balance}${this.currentFixPriceMode === FIXED_PRICE_MODE.FixedAmount ? '元' : '升'}）`));
                      } else {
                        if(this.dialogParams.showBatchAllocation && this.dialogParams.radioType === 1){
                          const totalAmount = BigNumber(this.multipleSelection.length).times(value).toFixed(this.currentFixPriceMode === FIXED_PRICE_MODE.FixedAmount?2:4);
                          if (BigNumber(totalAmount).gt(balance)) {
                            callback(new Error(`划拨总额不能大于当前余额（当前剩余：${balance}${this.currentFixPriceMode === FIXED_PRICE_MODE.FixedAmount ? '元' : '升'}）`));
                            return;
                          }
                        }
                        callback();
                      }
                    }
                  }, trigger: 'blur' }
                ],
              },
              allocationLoading:false,
              loadingInfo:false,
              refundOilList:[]
            }
        }
    },
    mounted(){
        //函数防抖
        this.$watch('amount', this.debounce((newQuery) => {
            if(!this.showRechargeApproval){
                this.getChargeAmount(newQuery)
            }
            this.$emit('amount', newQuery)
        }, 500))
        // this.$watch('orig_amount', this.debounce((newQuery) => {
        //     this.getSingleGiveAmount(newQuery)
        //     this.$emit('orig_amount', newQuery)
        // }, 500))

        if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
            return false;
        }
        this.getStationList();
        this.getCompanyList();
    },
    computed:{
      currentFixPriceMode(){
        if(!this.dialogParams.item){
          return String(FIXED_PRICE_MODE.No)
        }
        return String(this.dialogParams.item.CardLockType);
      },
      currentCompanyBatchInfo(){
        const res = {}
        if(this.currentFixPriceMode === FIXED_PRICE_MODE.No || !this.dialogParams.item || !this.dialogParams.item.companyBatches || this.dialogParams.item.companyBatches.length === 0){
          return res
        }
        let key = 'UnitLiter'
        if(this.currentFixPriceMode === FIXED_PRICE_MODE.FixedAmount){
          key = 'RechargeAmount';
        }
        this.dialogParams.item.companyBatches.forEach(item => {
          console.log("=>(CardTransfer.vue:749) item", item.OilNo,item[key]);
          if(item.OilNo in res){
            res[item.OilNo] = BigNumber(item[key]).plus(res[item.OilNo]||0).toFixed(key === 'UnitLiter'?4:2)
            return;
          }
          res[item.OilNo] = BigNumber(item[key]||0).toFixed(key === 'UnitLiter'?4:2);
        })
        return res;
      },
      ...mapState(['isGroup']),
      ...mapGetters(['showLockPrice','showRechargeApproval']),
      ...mapGetters({
          "getCurrentStation":"getCurrentStation",
      })
    },
    watch:{
        getCurrentStation(newValue,oldValue){
            if(newValue.merchant_type != 0 && newValue.value != oldValue.value){
                this.getStationList();
                this.currentPage = 1;
                this.getCompanyList();
            }
        }
    },
    methods:{
        async submitRechargeApproval(){
          // 如果是新版dialog，先进行表单校验
          if (this.dialogParams.showFixedCharge) {
            await this.$refs.form.validate();
          }

          let rechargeAmount = this.dialogParams.showFixedCharge ? this.dialogParams.query.amount : this.amount;

          if (!(rechargeAmount > 0)) {
            this.$message.error('本次充值金额必须大于0');
            return;
          }

          // 设置loading状态
          if (this.dialogParams.showFixedCharge) {
            this.dialogParams.charging = true;
          } else {
            this.rechargeLoading = true;
          }
          
          const userinfo = JSON.parse(localStorage.getItem('__userInfo__'));
          let oils_name = '';
          if (this.dialogParams.query.oils && this.companyInfo.oil_list) {
            const selectedOil = this.companyInfo.oil_list.find(item =>
              String(item.oil_id) === String(this.dialogParams.query.oils)
            );
            oils_name = selectedOil ? selectedOil.oil_name : '';
          }

          const params = {
            CardLockType: this.dialogParams.item.CardLockType,
            adid: (userinfo && userinfo.adid) || '',
            company_id: this.company_id,
            company_name: this.companyInfo.company_name,
            liters: this.dialogParams.query.liters,
            oils: this.dialogParams.query.oils,
            oils_name: oils_name,
            pay_way: 1,
            remark: this.dialogParams.query.remark,
            unit_price: this.dialogParams.query.unit_price,
            amount: rechargeAmount
          };
          
          this.$axios.post('/CompanyCard/subApplyRecharge', params)
            .then(res => {
              if(res.data.status === 200) {
                this.$message.success('提交成功');
                // 关闭对应的dialog
                if (this.dialogParams.showFixedCharge) {
                  this.dialogParams.showFixedCharge = false;
                  this.dialogParams.charging = false;
                } else {
                  this.rechargeDialogVisible = false;
                  this.rechargeLoading = false;
                }
              } else {
                this.$message.error(res.data.info || '提交失败');
              }
            })
            .catch(err => {
              this.$message.error('请求失败，请稍后重试');
            })
            .finally(() => {
              if (this.dialogParams.showFixedCharge) {
                this.dialogParams.charging = false;
              } else {
                this.rechargeLoading = false;
              }
            });
        },
        showBatchRefund(){
          this.dialogVisible = true;
          this.showLockPrice && this.getRefundOilList()
        },
        getOilName(oilId){
          if(!oilId || !this.dialogParams.refundOilList || this.dialogParams.refundOilList && this.dialogParams.refundOilList.length === 0){
            return oilId;
          }
          const oil = this.dialogParams.refundOilList.find(item => item.oil_id === oilId);
          if(!oil){
            return oilId;
          }
          return oil.oil_name;
        },
        allocationDialogInit(){
          if(this.currentFixPriceMode !== FIXED_PRICE_MODE.No || !this.showLockPrice){
            this.getCompanyStationList()
            this.fixedChargeInit();
            this.$nextTick(()=>{
              this.$refs.allocationForm && this.$refs.allocationForm.resetFields();
              this.$refs.allocationBatchForm && this.$refs.allocationBatchForm.resetFields();
            })
            if(this.dialogParams.showBatchAllocation){
              this.multipleSelection.forEach(item => {
                item.TransferUnitLiter = 0
              })
            }
          }
        },
        fixedChargeInit(){
            this.dialogParams.query = {
              oils:'',
              liters:0,
              unit_price:0,
              amount:0,
              remark:'',
            }
        },
        automaticCalculation(field) {
          console.log("=>(CardTransfer.vue:999) this.currentFixPriceMode === FIXED_PRICE_MODE.FixedRise", this.currentFixPriceMode);
            if(this.currentFixPriceMode === FIXED_PRICE_MODE.FixedRise){
              let { amount='', liters='' } = this.dialogParams.query;
              // 输入合法性检查和预处理
              amount = this.sanitizeInput(amount) || 0;
              liters = this.sanitizeInput(liters) || 1;
              // 更新输入框的值
              if (new BigNumber(amount).isEqualTo(0) && new BigNumber(liters).isEqualTo(0)) {
                // 如果 amount 或 liters 中有一个为 0，不能计算 unit_price
                return;
              }
              const unitPrice = new BigNumber(amount ).dividedBy(liters);
              if(unitPrice.isFinite()){
                this.dialogParams.query.unit_price = unitPrice.toFixed(8);
              }
              return;
            }
            // 定额卡，输入油品升数不处理计算
            if(this.currentFixPriceMode === FIXED_PRICE_MODE.FixedAmount && field === 'liters'){
              return;
            }
            let { amount='', unit_price='' } = this.dialogParams.query;

            // 输入合法性检查和预处理
            amount = this.sanitizeInput(amount);
            unit_price = this.sanitizeInput(unit_price);

            // 使用 BigNumber 进行精确计算
            const amountBN = new BigNumber(amount || 0);
            const unitPriceBN = new BigNumber(unit_price || 1);
            const litersBN = amountBN.div(unitPriceBN);

            this.dialogParams.query.liters = 0;
            // 更新输入框的值
            if(litersBN.isFinite()){
                this.dialogParams.query.liters = litersBN.toFixed(4);
            }
        },
      sanitizeInput(value) {
        // 预处理非法字符，移除除数字和小数点之外的字符
        let sanitizedValue = value.toString().replace(/[^0-9.]/g, '');

        // 检查并处理多个小数点的情况
        const parts = sanitizedValue.split('.');
        if (parts.length > 2) {
          sanitizedValue = parts[0] + '.' + parts.slice(1).join('');
        }

        return sanitizedValue;
      },
        async remoteMethod(query) {
          try {
            if (!query) {
              return this.companyOptions = [];
            }
            const res = await this.$axios.post('/CompanyCard/getCompanyNameList', {
              input: query
            })
            console.log('搜索结果', res)
            if(res.data.status != 200) return this.$message.error(res.data.info)
            this.companyOptions = JSON.parse(res.data.data)
          }catch (e) {
            console.log(e)
            this.$message.error('查询车队名称异常')
          }
        },
        handleInput(item){
            if(this.timeout !== null) clearTimeout(this.timeout)
            this.timeout = setTimeout(() => {
                this.getGiveAmount(item);
            }, 500)
        },
        //防抖函数
        debounce(func, delay) {
            let timer
            return function (...args) {
                if (timer) {
                clearTimeout(timer)
                }
                timer = setTimeout(() => {
                func.apply(this, args)
                }, delay)
            }
        },
        //获取充值赠金
        getChargeAmount(amount){
            let that = this;
            that.transferAble = false;
            if(amount>0){
                that.$axios.post('/CompanyCard/getChargeAmount', {
                    company_id: that.company_id,
                    amount: amount,
                    pay_way: 1
                })
                .then(function (res) {
                    if(res.data.status == 200){
                        that.giveAmount =  res.data.data.donate_money;

                        let couponinfo = res.data.data.couponinfo;

                        if(couponinfo && couponinfo.coupon_type == 1){
                            if(couponinfo.retail_type == 2){
                                that.couponInfo = couponinfo.price + "元油品券*" + couponinfo.count;
                            }else if(couponinfo.retail_type == 3){
                                that.couponInfo = couponinfo.price + "元非油券*" + couponinfo.count;
                            }else if(couponinfo.retail_type == 4){
                                that.couponInfo = couponinfo.price + "元服务券*" + couponinfo.count;
                            }else{
                                that.couponInfo = couponinfo.price + "元赠金券*" + couponinfo.count;
                            }
                        }else if(couponinfo && couponinfo.coupon_type == 3){
                            if(couponinfo.retail_type == 2){
                                that.couponInfo = couponinfo.price + "折油品券*" + couponinfo.count;
                            }else if(couponinfo.retail_type == 3){
                                that.couponInfo = couponinfo.price + "折非油券*" + couponinfo.count;
                            }else if(couponinfo.retail_type == 4){
                                that.couponInfo = couponinfo.price + "折服务券*" + couponinfo.count;
                            }else{
                                that.couponInfo = couponinfo.price + "折赠金券*" + couponinfo.count;
                            }
                        }else if(couponinfo && couponinfo.coupon_type == 2){
                            if(couponinfo.retail_type == 2){
                                that.couponInfo = "油品券*" + couponinfo.count;
                            }else if(couponinfo.retail_type == 3){
                                that.couponInfo = "非油券*" + couponinfo.count;
                            }else if(couponinfo.retail_type == 4){
                                that.couponInfo = "服务券*" + couponinfo.count;
                            }else{
                                that.couponInfo = "赠金券*" + couponinfo.count;
                            }
                        }else if(couponinfo && couponinfo.coupon_type == 0){
                            that.couponInfo = "券包";
                        }else{
                            that.couponInfo = "";
                        }

                        that.transferAble = true;
                    }else{
                        that.$message({
                            message: res.data.info,
                            type: 'error'
                        });
                    }
                })
            }else{
                that.giveAmount =  0;
                that.couponInfo = "";
            }
        },
        //获取单人划拨赠金
        getSingleGiveAmount(amount){
            let that = this;
            that.transferAble = false;
            if(amount>0){
                that.$axios.post('/CompanyCard/getGiveAmount', {
                    id: that.company_id,
                    orig_amount: amount
                })
                .then(function (res) {
                    if(res.data.status == 200){
                        that.transferAble = true;
                        that.giveAmount =  res.data.data.GiveAmount;
                        that.PrincipalAmount =  res.data.data.PrincipalAmount;
                    }else{
                        that.$message({
                            message: res.data.info,
                            type: 'error'
                        });
                    }
                })
                .catch(function (error) {
                });
            }else{
                that.giveAmount =  0;
                that.couponInfo = "";
            }
        },
        //获取赠金
        getGiveAmount(item){
            let that = this;
            if(!item.input){
                that.$set(item,'giveAmount',0);
                that.$set(item,'PrincipalAmount',0);
                return;
            }
            return new Promise((resolve,reject)=>{
                that.$axios.post('/CompanyCard/getGiveAmount', {
                    id: that.company_id,
                    orig_amount: item.input
                })
                .then(function (res) {
                    if(res.data.status == 200){
                        that.$set(item,'giveAmount',res.data.data.GiveAmount)
                        that.$set(item,'PrincipalAmount',res.data.data.PrincipalAmount)
                        resolve(res);
                    }else{
                        that.$message({
                            message: res.data.info,
                            type: 'error'
                        });
                    }
                })
                .catch(function (error) {
                    reject(error)
                });
            })
        },
        //获取油站列表
        getStationList(){
            this.$axios.post('/Stations/getStationList',{}).then((res)=>{
                if(res.status == 200){
                    this.stationList = res.data.data;
                }
            })
        },
        async getCompanyStationList(stid = undefined) {
          try {
            this.dialogParams.companyStationListLoading = true;
            const res = await getCompanyCardStations(stid || this.companyInfo.station_id)
            console.log("=>(CardTransfer.vue:958) res", res);
            if(res.status !== 200){
              this.$message.error(res.info || '获取油站列表失败，请稍后重试');
              return;
            }
            if(!res.data){
              this.dialogParams.companyStationList = [];
              return
            }
            let result = res.data.oil_list || [];
            if(stid){
              return result;
            }
            if (result.length > 0) {
              result = result.filter(item => this.currentCompanyBatchInfo[item.oil_id])
            }
            this.dialogParams.companyStationList = result;
          } catch (e) {
            console.log("=>(CardTransfer.vue:960) 获取油站列表失败", e);
            this.$message.error('获取油站列表失败，请稍后重试')
          } finally {
            this.dialogParams.companyStationListLoading = false;
          }
        },
        //根据id获取油站名称
        getStationName(val){
            let typeName = "";
            this.stationList.forEach((element)=>{
                if(element.stid == val){
                    typeName =  element.stname;
                }
            })
            return typeName;
        },
        //根据id获取信用类型
        getCreditName(val){
            let typeName = "";
            let creditList = [{
                value:"0",
                label:"不启用"
            },{
                value:"1",
                label:"信贷"
            },{
                value:"2",
                label:"保证金"
            }]
            creditList.forEach((element)=>{
                if(element.value == val){
                    typeName =  element.label;
                }
            })
            return typeName;
        },
        //获取车队信息列表
        getCompanyList(){
            let that = this;
            that.companyLoading = true;
            that.$axios.post('/CompanyCard/getCompanyList', {
                page: that.currentPage,
                page_size: that.pageSize,
                input: that.searchText,
                state:100
            })
            .then(function (res) {
                that.companyLoading = false;
                if(res.data.status == 200){
                    that.companyListData = res.data.data.dt;
                    that.total = res.data.data.TotalQty;
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
            });
        },
        //车队列表切换页码
        handleCurrentChange(val){
            this.currentPage = val;
            this.getCompanyList();
        },
        handleSizeChange(val){
            this.pageSize = val;
            this.getCompanyList();
        },
        //搜索车队
        searchCompany(){
            this.currentPage = 1;
            this.getCompanyList();
        },
        //展示划拨界面
        goToTransfer(val){
            this.company_id = val.ID;
            this.companyListShow = false;
            this.transferListShow = true;
            this.showCompany = false;
            this.companyInfo = {};
            this.companyInfo.amount = 0;
            this.companyInfo.give_amount = 0;
            this.getCompanyInfo(val.ID);
            this.currentCardPage = 1
            this.pageCardSize = 10
            this.getUserCardList();
            this.dialogParams.item = val;
            this.searchCardText = ''
            this.searchType = '1'
        },
        //获取车队详情
        getCompanyInfo(id){
            let that = this;
            this.dialogParams.oilLoading = true;
            that.$axios.post('/CompanyCard/getCompanyInfo', {
                id: id
            })
            .then(function (res) {
                if(res.data.status == 200){
                    that.companyInfo = res.data.data;
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            }).finally(()=>{
              this.dialogParams.oilLoading = false;
            })
        },
        async getCurrentCompanyInfo() {
          if(!this.dialogParams.item || !this.dialogParams.item.ID){
            return;
          }
          try {
            this.dialogParams.loadingInfo = true;
            const res = await getCompanyDetail(this.dialogParams.item.ID)
            console.log("=>(CardTransfer.vue:1254) res", res);
            if(res.status !== 200){
              this.$message.error(res.info || '获取车队详情失败');
              return;
            }
            if(!res.data || !res.data.dt || res.data.dt.length===0){
              this.$message.error('获取车队详情失败，请刷新页面');
              return;
            }
            const [company] = res.data.dt;
            this.dialogParams.item = company;
          } catch (e) {
            console.log("=>(CardTransfer.vue:1267) 获取车队详情失败", e);
            this.$message.error('获取车队详情失败，请刷新页面');
          } finally {
            this.dialogParams.loadingInfo = false;
          }
        },
        //获取车队卡列表
        getUserCardList(){
            let that = this;
            that.cardLoading = true;
            that.cardListData = [];
            that.$axios.post('/Card/getUserCardList', {
                page: that.currentCardPage,
                page_size: that.pageCardSize,
                input:that.searchCardText,
                input_type:that.searchType,
                company_id: that.company_id,
            })
            .then(function (res) {
                that.cardLoading = false;
                if(res.data.status == 200){
                    that.cardListData = res.data.data.dt;
                    that.totalCard = res.data.data.TotalQty;
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
            });
        },
        //卡列表切换页码
        handleCardCurrentChange(val){
            this.currentCardPage = val;
            this.getUserCardList();
        },
        handleCardSizeChange(val){
            this.pageCardSize = val;
            this.getUserCardList();
        },
        //搜索卡
        searchCard(){
            this.currentCardPage = 1;
            this.getUserCardList();
        },
        //显示全部车队
        searchAllCompany(){
            this.currentPage = 1;
            this.searchText = "";
            this.getCompanyList();
        },
        //显示全部卡
        searchAllCard(){
            this.currentCardPage = 1;
            this.searchCardText = "";
            this.getUserCardList();
        },
        //多选操作
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        //展示单人划拨弹窗
        showSingleTransfer(val){
            this.cardInfo = val;
            this.transferType = 1;
            this.giveAmount = '';
            this.PrincipalAmount = '';
            this.orig_amount = "";
            this.singleDialogVisible = true;
        },
        //展示批量划拨弹窗
        showMoreTransfer(){
          console.log("=>(CardTransfer.vue:1316) this.dialogParams.item.CardLockType !== FIXED_PRICE_MODE.No", this.dialogParams.item.CardLockType , FIXED_PRICE_MODE.No);
            if(this.showLockPrice && this.currentFixPriceMode !== FIXED_PRICE_MODE.No){
              this.dialogParams.radioType = 1;
              this.dialogParams.showBatchAllocation = true;
              this.allocationDialogInit()
              return;
            }
            if(this.radioType == 1){
              this.transferType = 2;
            }else{
              this.transferType = 3;
            }
            this.moreDialogVisible = true;
            this.orig_amount = "";
            this.multipleSelection.forEach(element =>{
                // element.giveAmount = 0;
                // element.PrincipalAmount = 0;
            })
            this.PrincipalAmount = '';
            this.giveAmount = '';
        },
        //changeRadioType
        changeRadioType(val){
            if(val==1){
                this.transferType = 2;
            }else if(val==2){
                this.transferType = 3;
            }
        },
        async allocationConfirm() {
          if (!this.dialogParams.companyStationList || this.dialogParams.companyStationList && this.dialogParams.companyStationList.length === 0){
            this.$message.error("当前无可用划拨油品，不可执行划拨处理")
            return;
          }
          if (this.showLockPrice && this.currentFixPriceMode !== FIXED_PRICE_MODE.No) {
            if (this.dialogParams.showBatchAllocation) {
              await this.$refs.allocationBatchForm.validate()
            } else {
              await this.$refs.allocationForm.validate()
            }
          }

          const transfer_info = [];
          if(this.dialogParams.showBatchAllocation){
            let result = BigNumber(0);
            for (let card of this.multipleSelection) {
              const val = (this.dialogParams.radioType === 1 ? this.dialogParams.query.amount : card.TransferUnitLiter) || 0;
              const item = {
                GiveAmount: 0,
                TransferAmount: this.currentFixPriceMode === FIXED_PRICE_MODE.FixedAmount ? val : 0,
                CardID: card.ID,
                TransferUnitLiter: this.currentFixPriceMode === FIXED_PRICE_MODE.FixedRise ? val : 0
              }
              if(item.TransferUnitLiter < 0.1 && this.currentFixPriceMode === FIXED_PRICE_MODE.FixedRise){
                this.$message.error(`划拨余额不能小于0.1升`)
                return;
              }
              result = result.plus(this.currentFixPriceMode === FIXED_PRICE_MODE.FixedRise? item.TransferUnitLiter:item.TransferAmount);
              transfer_info.push(item)
            }
            const currentValue = this.currentCompanyBatchInfo[this.dialogParams.query.oils]||0;
            if(result.eq(0)){
              this.$message.error(`划拨总额不能为0`)
              return;
            }else if(result.lt(0.1) && this.currentFixPriceMode === FIXED_PRICE_MODE.FixedRise){
              this.$message.error(`划拨总额不能小于0.1升`)
              return;
            }else if(result.gt(BigNumber(currentValue))){
              this.$message.error(`划拨总额不能大于当前余额（当前剩余：${currentValue}${this.currentFixPriceMode === FIXED_PRICE_MODE.FixedAmount ? '元' : '升'}）`)
              return;
            }
          }else{
            transfer_info.push({
              CardID:this.cardInfo.ID,
              TransferAmount:this.currentFixPriceMode === FIXED_PRICE_MODE.FixedAmount ?(this.dialogParams.query.amount || 0):0,
              GiveAmount:0,
              TransferUnitLiter:this.currentFixPriceMode === FIXED_PRICE_MODE.FixedRise ?(this.dialogParams.query.amount || 0):0
            });
          }
          console.log("=>(CardTransfer.vue:1565) transfer_info", transfer_info);
          const params = {
            type:this.dialogParams.item.CardLockType,
            id: this.company_id,
            oils:this.dialogParams.query.oils,
            transfer_info
          };
          try {
            this.dialogParams.allocationLoading = true
            const res = await transferCompanyAccountV2(params);
            console.log("=>(CardTransfer.vue:1482) res", res);
            if(res.status !== 200){
              this.$message.error(res.info)
              return;
            }
            this.$message.success('操作成功')
            this.getUserCardList();
            this.getCurrentCompanyInfo()
            this.dialogParams.showBatchAllocation = false;
            this.singleDialogVisible = false;
          } catch (e) {
            console.log("=>(CardTransfer.vue:1484) 划拨金额失败", e);
            this.$message.error("划拨金额失败")
          } finally {
            this.dialogParams.allocationLoading = false
          }
        },
        //划拨
        transferCompanyAccount(){
            let that = this;
            let regex = /^\d+(\.\d{1,2})?$/;
            that.transfer_info = [];
            //根据transferType不同传不同的参数
            if(this.transferType == 1){
                if((Number(that.PrincipalAmount) + Number(that.giveAmount)) > Number(that.companyInfo.amount)+Number(that.companyInfo.give_amount)){
                    that.$message({
                        message: "划拨金额过大",
                        type: 'error'
                    });
                    return false;
                }
                if(!regex.test(that.PrincipalAmount)){
                  return that.$message.error('请输入正确的划拨本金，限两位小数')
                }
                if(!regex.test(that.giveAmount)){
                  return that.$message.error('请输入正确的划拨赠金，限两位小数')
                }
                if(that.PrincipalAmount == 0 && that.giveAmount == 0){
                  return that.$message.error("划拨本金与赠金不能同时为0元")
                }
                that.transfer_info.push({
                    CardID:that.cardInfo.ID,
                    TransferAmount:that.PrincipalAmount,
                    GiveAmount:that.giveAmount
                });
                that.transferAble = false;
            }else if(this.transferType == 2){
                if(that.PrincipalAmount == 0 && that.giveAmount == 0){
                  return that.$message.error("划拨本金与赠金不能同时为0元")
                }
                if(!regex.test(that.PrincipalAmount)){
                  return that.$message.error('请输入正确的划拨本金，限两位小数')
                }
                if(!regex.test(that.giveAmount)){
                  return that.$message.error('请输入正确的划拨赠金，限两位小数')
                }
                that.multipleSelection.forEach((element)=>{
                    that.transfer_info.push({
                        CardID:element.ID,
                        TransferAmount:that.PrincipalAmount,
                        GiveAmount:that.giveAmount
                    })
                });
                that.transferAble = false;
            }else{
                for(let i=0;i<that.multipleSelection.length;i++){
                    if(!that.multipleSelection[i].PrincipalAmount){
                      return that.$message.error('请输入划拨本金')
                    }
                    else if(!regex.test(that.multipleSelection[i].PrincipalAmount)){
                      return that.$message.error('请输入正确的划拨本金，限两位小数')
                    }
                    else if(!that.multipleSelection[i].giveAmount){
                      return that.$message.error('请输入划拨赠金')
                    }
                    else if(!regex.test(that.multipleSelection[i].giveAmount)){
                      return that.$message.error('请输入正确的划拨赠金，限两位小数')
                    }
                    else if(that.multipleSelection[i].PrincipalAmount == 0 && that.multipleSelection[i].giveAmount == 0){
                      return that.$message.error("划拨本金与赠金不能同时为0元")
                    }
                }
                that.multipleSelection.forEach((element)=>{
                    that.transfer_info.push({
                        CardID:element.ID,
                        TransferAmount:element.PrincipalAmount,
                        GiveAmount:element.giveAmount
                    })
                });
            }

            that.$axios.post('/CompanyCard/transferCompanyAccount', {
                id: that.company_id,
                transfer_info: that.transfer_info
            })
            .then(function (res) {
                if(res.data.status == 200){
                    that.$message({
                        message: "划拨成功",
                        type: 'success'
                    });
                    that.singleDialogVisible = false;
                    that.moreDialogVisible = false;
                    that.getCompanyInfo(that.company_id);
                    that.getUserCardList();
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
            });
        },
        //展示充值界面
      async goToRecharge(val) {
        this.dialogParams.item = val;
        this.company_id = val.ID;
        this.amount = "";
        this.companyInfo = {};
        this.companyInfo.amount = 0;
        this.companyInfo.give_amount = 0;
        this.companyInfo.credit_type = 0;
        this.transferAble = false;
        this.giveAmount = 0;
        this.couponInfo = "";
        this.getCompanyInfo(val.ID);
        
        if(this.isGroup){
          this.$message.error('暂不支持集团');
          return;
        }
        console.log("=>(CardTransfer.vue:1515) this.currentFixPriceMode !== FIXED_PRICE_MODE.No", this.currentFixPriceMode,FIXED_PRICE_MODE.No);
        if (this.showLockPrice && this.currentFixPriceMode !== FIXED_PRICE_MODE.No) {
          this.dialogParams.showFixedCharge = true;
          return;
        }
        
        this.rechargeDialogVisible = true;
      },
        //充值
        async chargeCompanyCard(){
            let that = this;
            let userinfo = JSON.parse(localStorage.getItem('__userInfo__'));
            let params = {
                company_id: that.company_id,
                amount: that.amount,
                pay_way: 1,
                CardLockType : this.dialogParams.item.CardLockType,
                oils:'',
                adid:userinfo?userinfo.adid:"",
                "liters": 0,
                "unit_price": 0,
                "remark": ""
            }
            if(this.showLockPrice && this.dialogParams.showFixedCharge){
                await this.$refs.form.validate()
                params = {...params,...this.dialogParams.query}
            }
            this.dialogParams.charging = true;
            that.transferAble = false;
            that.$axios.post('/CompanyCard/chargeCompanyCard', params)
            .then(function (res) {
                that.rechargeDialogVisible = false;
                that.dialogParams.showFixedCharge  = false;
                if(res.data.status == 200){
                    that.$message({
                        message: "充值成功",
                        type: 'success'
                    });
                    that.getCompanyList();
                }else if(res.data.status == 2000){
                  that.$message({
                    message:'充值进行中，请稍后于资金流水中查询充值订单。',
                    duration: 5000
                  })
                  that.getCompanyList();
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
            }).finally(()=>{
                this.dialogParams.charging = false;
            });
        },
        //显示划拨列表
        showCardList(val){
            this.company_id = val.ID;
            this.companyName = val.CompanyName;
            this.searchDate = [this.$moment().subtract(3, 'months').format('YYYY-MM-DD'), this.$moment().format('YYYY-MM-DD')];
            this.getTransferDetailed();
            this.showCompany = true;
            this.companyListShow = false;
            this.transferListShow = false;
            this.multipleSelectionCard = [];
            this.searchTxt = "";
            this.searchType = '1';
            this.dialogParams.item = val;
        },
        //获取划拨列表
        getTransferDetailed(){
            if(this.searchDate && this.searchDate.length === 2) {
              let startTime = this.$moment(this.searchDate[0]);
              let endTime = this.$moment(this.searchDate[1]); 
              let diffMonths = endTime.diff(startTime, 'months', true);
              if(diffMonths > 6) {
                this.$message.error('选择的时间范围不能超过6个月');
                this.searchDate = [this.$moment().subtract(3, 'months').format('YYYY-MM-DD'), this.$moment().format('YYYY-MM-DD')];
                return;
              }
            }
            let that = this;
            that.cardLoading = true;
            that.$axios.post('/CompanyCard/getTransferDetailed', {
                Page: that.currentPageTransfer,
                PageSize: that.pageSizeTransfer,
                CompanyID: that.company_id,
                QueryType: that.searchType,
                QueryInfo: that.searchTxt,
                StartTime: that.searchDate?that.$moment(that.searchDate[0]+' 00:00:00', 'YYYY-MM-DD HH:mm:ss').unix():"",
                EndTime: that.searchDate?that.$moment(that.searchDate[1]+' 23:59:59', 'YYYY-MM-DD HH:mm:ss').unix():""
            })
            .then(function (res) {
                that.cardLoading = false;
                if(res.data.status == 200){
                    that.cardList = res.data.data.dt;
                    that.totalTransfer = res.data.data.TotalQty;
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
            });
        },
        //切换页码
        handleTransferCurrentChange(val){
            this.currentPageTransfer = val;
            this.getTransferDetailed();
        },
        handleTransferSizeChange(val){
            this.pageSizeTransfer = val;
            this.getTransferDetailed();
        },
        //多选处理
        handleSelectionCardChange(val){
            this.multipleSelectionCard = val;
        },
      async getRefundOilList() {
        this.getCompanyStationList(this.dialogParams.item.StationNO).then(stationList => {
          if (!stationList || stationList.length === 0) {
            return;
          }
          this.dialogParams.refundOilList = stationList;
        })
      },
        //显示返款弹窗
        async showRefund(val){
            this.dialogVisible = true;
            this.$refs.multipleTable.clearSelection();
            this.multipleSelectionCard = [];
            this.multipleSelectionCard.push(val);
            this.showLockPrice && this.getRefundOilList()
        },
        //返款
        refund(){
            let that = this;
            let batchCardRefunds = [];
            this.multipleSelectionCard.forEach(element => {
                batchCardRefunds.push({
                    ID:element.ID,
                    CompanyID:element.CompanyID,
                    CardID:element.CardID,
                    RefundsAmount:element.ReceiveAmount,
                    RefundsGiveAmount:element.ReceiveGiveAmount
                })
            })
            that.$axios.post('/CompanyCard/transferGoBack', {
                batchCardRefunds: batchCardRefunds,
            })
            .then(function (res) {
                if(res.data.status == 200){
                    that.$message({
                        message: "返款成功",
                        type: 'success'
                    });
                    that.dialogVisible = false;
                    that.currentPageTransfer = 1;
                    that.getTransferDetailed();
                }else{
                    that.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
            .catch(function (error) {
            });
        },
        //返回
        goBack(){
            this.showCompany = false;
            this.transferListShow = false;
            this.showRechargeApprovalView = false;
            this.companyListShow = true;
            this.getCompanyList();
        },

        //下载导入模板
        downloadTemplate(){
          window.location.href = this.baseURL + "/CustomerGroup/downloadTransMoney";
        },
        //批量添加
        httpRequest (e) {
            let that = this;
            this.$axios.post('Card/commpayCardCache',{company_id:this.company_id}).then((res)=>{
                console.log(res);
                if(res.data.status == 200){
                    let file = e.file // 文件信息
            let that = this;

            if (!file) {
                // 没有文件
                return false
            } else if (!/\.(xls|xlsx)$/.test(file.name.toLowerCase())) {
                // 格式根据自己需求定义
                that.$message.error('上传格式不正确，请上传xls或者xlsx格式')
                return false
            }

            const fileReader = new FileReader()
            fileReader.onload = (ev) => {
            try {
                const data = ev.target.result
                const workbook = XLSX.read(data, {
                type: 'binary' // 以字符编码的方式解析
                })
                const exlname = workbook.SheetNames[0] // 取第一张表
                const exl = XLSX.utils.sheet_to_json(workbook.Sheets[exlname]) // 生成json表格内容

                let params = {
                    id: that.company_id,
                    transfer_info: exl
                }
                console.log('exl',exl)
                for(let i=0;i<exl.length;i++){
                  if(!exl[i]['卡号（必填）']){
                    return that.$message.error('请填入卡号')
                  }
                  else if(!(exl[i]['卡号（必填）'] > 0)){
                    return that.$message.error(`“${exl[i]['卡号（必填）']}”格式有误，请输入正确的卡号`)
                  }
                  else if(!exl[i]['划拨本金（必填）'] && exl[i]['划拨本金（必填）'] !== 0){
                    return that.$message.error('请填入划拨本金')
                  }
                  else if(!(exl[i]['划拨本金（必填）'] >= 0)){
                    return that.$message.error(`”${exl[i]['划拨本金（必填）']}“格式有误，请输入正确的划拨本金`)
                  }
                  else if(!exl[i]['划拨赠金（必填）'] && exl[i]['划拨赠金（必填）'] !== 0){
                    return that.$message.error('请填入划拨赠金')
                  }
                  else if(!(exl[i]['划拨赠金（必填）'] >= 0)){
                    return that.$message.error(`“${exl[i]['划拨赠金（必填）']}”格式有误，请输入正确的划拨赠金`)
                  }
                  else if(exl[i]['划拨本金（必填）'] === 0 && exl[i]['划拨赠金（必填）'] === 0){
                    return that.$message.error("划拨本金与赠金不能同时为0元")
                  }
                }
                that.capitalAllocationContent = '正在导入资金划拨中，请等待...'
                this.cardLoading = true
                let fundAxios = axios.create()
                fundAxios.post('/CompanyCard/batchImportTransferMoney ', params,{timeout:50000})
                .then(function (res) {
                    if(res.data.status == 200){
                        that.$message({
                            message: "导入成功",
                            type: 'success'
                        });
                        that.cardLoading = false
                        that.getCompanyInfo(that.company_id);
                        that.getUserCardList();
                    }else{
                        that.$message({
                            message: res.data.info,
                            type: 'error'
                        });
                    }
                })
                .catch(function (error) {
                }).finally(()=>{
                    this.cardLoading = false
                    this.capitalAllocationContent = ''
                });

                // 将 JSON 数据挂到 data 里

                // this.tableData = exl
                // document.getElementsByName('file')[0].value = '' // 根据自己需求，可重置上传value为空，允许重复上传同一文件
            } catch (e) {
                console.log(e)
                return false
            }
            }
            fileReader.readAsBinaryString(file)
                }
                else {
                    that.$message.error(res.data.info)
                }
            })
        },
        showWorkflowDetail() {
            this.companyListShow = false;
            this.transferListShow = false;
            this.showCompany = false;
            this.showRechargeApprovalView = true;
        }
    }
}
</script>
<style scoped>
.CardTransfer{
    font-size: 14px;
    color: #333;
}
.header{
  display: flex;
  justify-content:space-between;
}
.search{
    display: flex;
    align-items: center;
}
.search-bar{
    width: 270px;
}
.company-info{
    border-bottom: 1px solid #E7E7E7;
    margin-top: 15px;
    margin-bottom: 24px;
}
.company-info .line{
    display: flex;
    margin-bottom: 16px;
}
.company-info .item{
    width: 270px;
}
.company-info .tab{
    color: #777;
    display: inline-block;
    width: 60px;
    text-align: right;
    margin-right: 16px;
}
.tips{
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.tips .search{
    display: flex;
    align-items: center;
}
.tips .txt{
    margin: 0 20px 0 5px;
    font-size: 12px;
}
.tips .search{
    margin: 0;
}
/* 单人划拨 */
.card-info{
    font-size: 16px;
    margin-left: 130px;
}
.card-info .item{
    margin-bottom: 25px;
}
.card-info .tab{
    text-align: right;
    display: inline-block;
    width: 65px;
    margin-right: 24px;
    color: #777;
}
.card-info .item:last-child{
    display: flex;
    margin-bottom: 0;
    align-items: baseline;
}
.given-icon{
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-right: 5px;
}

/* 页码 */
.page_content {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
}

</style>
<style>
.search-bar input{
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: 0;
}
.search .search-btn{
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 0;
}
</style>
