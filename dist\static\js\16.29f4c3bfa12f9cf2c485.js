webpackJsonp([16],{"7aMu":function(e,t){},ePS9:function(e,t){},"iG/M":function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r("mvHQ"),a=r.n(i),n=r("Gu7T"),l=r.n(n),s=r("Dd8w"),c=r.n(s),o=r("Xxa5"),u=r.n(o),_=r("exGp"),p=r.n(_),d=r("NYxO"),h={name:"CardRule",data:function(){var e=this;return{showLockRule:!1,isCreate:!0,cardId:"",state:0,stateList:[{value:0,label:"全部"},{value:100,label:"启用"},{value:101,label:"禁用"}],station_id:0,currentPage:1,showTableData:!0,pageSize:10,totalNumber:1,tableData:[],loading:!0,stepActive:0,showBaseForm:!1,showConditionForm:!1,showtimeRuleForm:!1,showgivenForm:!1,public_config:{rule_name:"",date:[],description:"",stationList:[],priority:"10"},public_config_rules:{rule_name:[{required:!0,message:"请输入规则名称",trigger:"blur"},{max:50,message:"名称不能超过50个字符",trigger:"blur"}],date:[{required:!0,message:"请选择日期",trigger:"blur"}],stationList:[{required:!0,message:"请选择油站",trigger:"change"}],description:[{max:100,message:"规则不能超过100个字符",trigger:"blur"}]},stationList:[],stationListProps:{multiple:!0,label:"stname",value:"stid"},oilList:[],oilListProps:{multiple:!0,label:"name",value:"oil_id",children:"parent_arr"},extend_rule:{card_theme:[],customer_group_id:[],card_type:[],customer_id_type:"L",customer_id:"",pay_way:[],is_birthday:"0",first_recharge:"0",account_type:"1",company_id:[],GradeStr:[],market_type:"2"},extendRules:{customer_id_type:[{required:!0,message:"请选择",trigger:"blur"}]},cardThemeRuleList:[],allCardThemeRuleList:[],customerGroupList:[],companyList:[],classList:[],payWayList:[],options:[],couponsOptions:[],couponsProps:{multiple:!0,label:"coupon_name",value:"coupon_id"},timeRuleForm:{},time_rule:{type:0,selectedTime:[]},isCheckTime:!1,charge_give_rule:{type:"0",guding:{max_price:"",min_price:"",give:""},bili:{max_price:"",min_price:"",give:""},coupons:{max_price:"",min_price:"",give:""},gudingCouponsData:[],addCouponsData:[],couponsData:[]},chargeGiveRules:{guding_max_price:[{validator:function(t,r,i){0==e.charge_give_rule.type&&(""!=String(e.charge_give_rule.guding.max_price)?Number(e.charge_give_rule.guding.max_price)>Number(e.charge_give_rule.guding.min_price)?i():i(new Error("最小值必须小于最大值")):i(new Error("请输入数值")))},trigger:"blur"}],guding_min_price:[{validator:function(t,r,i){0==e.charge_give_rule.type&&(""!=String(e.charge_give_rule.guding.min_price)?i():i(new Error("请输入数值")))},trigger:"blur"}],guding_give_money:[{validator:function(t,r,i){0==e.charge_give_rule.type&&(""!=String(e.charge_give_rule.guding.give)?i():i(new Error("请输入数值")))},trigger:"blur"}],bili_max_price:[{validator:function(t,r,i){1==e.charge_give_rule.type&&(""!=String(e.charge_give_rule.bili.max_price)?Number(e.charge_give_rule.bili.max_price)>Number(e.charge_give_rule.bili.min_price)?i():i(new Error("最小值必须小于最大值")):i(new Error("请输入数值")))},trigger:"blur"}],bili_min_price:[{validator:function(t,r,i){1==e.charge_give_rule.type&&(""!=String(e.charge_give_rule.bili.min_price)?i():i(new Error("请输入数值")))},trigger:"blur"}],bili_give_money:[{validator:function(t,r,i){1==e.charge_give_rule.type&&(""!=String(e.charge_give_rule.bili.give)?i():i(new Error("请输入数值")))},trigger:"blur"}],coupons_max_price:[{validator:function(t,r,i){1==e.charge_give_rule.type&&(""!=String(e.charge_give_rule.coupons.max_price)?Number(e.charge_give_rule.coupons.max_price)>Number(e.charge_give_rule.coupons.min_price)?i():i(new Error("最小值必须小于最大值")):i(new Error("请输入数值")))},trigger:"blur"}],coupons_min_price:[{validator:function(t,r,i){1==e.charge_give_rule.type&&(""!=String(e.charge_give_rule.coupons.min_price)?i():i(new Error("请输入数值")))},trigger:"blur"}],couponsData:[{validator:function(t,r,i){2==e.charge_give_rule.type&&(r.length>0?i():i(new Error("请选择营销券")))},trigger:"change"}]},couponsTableData:[],gudingCouponsTableData:[],addCouponsTableData:[],btnDisabled:!1,isPayWayIndeterminate:!1,checkPayWayAll:!1,isThemeIndeterminate:!1,checkThemeAll:!1,isCompanyIndeterminate:!1,checkCompanyAll:!1,isCustomerGroupIndeterminate:!1,checkCustomerGroupAll:!1,isClassIndeterminate:!1,checkClassAll:!1,showGudingCoupons:!1,searchTxt:"",recharge:{num:"0",type:"month",count:"0",total:"0"},rechargeRules:{num:[{required:!0,message:"输入用户个数",trigger:"blur"},{pattern:/^(0|[1-9]\d?|1000)$/,message:"范围在0-1000",trigger:"blur"}],type:[{required:!0,message:"请选择类型",trigger:"blur"}],count:[{required:!0,message:"请输入充值赠送次数",trigger:"blur"},{pattern:/^(0|[1-9]\d?|1000)$/,message:"范围在0-1000",trigger:"blur"}],total:[{required:!0,message:"请输入最多赠送次数",trigger:"blur"},{pattern:/^(0|[1-9]\d?|1000)$/,message:"范围在0-1000",trigger:"blur"}]},couponDeliveryOptions:[{value:"month",label:"自然月"},{value:"week",label:"自然周"},{value:"day",label:"自然日"}],isLockPrice:!1,sellPrice:0,giveNumber:0,SumPrice:0,UnitPrice:0,flag:0,newThemeList:[]}},mounted:function(){var e=this;return p()(u.a.mark(function t(){return u.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0!=e.getCurrentStation.merchant_type&&0!=e.getCurrentStation.merchant_type){t.next=2;break}return t.abrupt("return",!1);case 2:return e.getGroupBaseInfo(),e.getStationList(),e.getCouponsList(),e.getCardRuleLists(),e.getChargeRuleTem(),t.next=9,e.getCardThemeRuleList();case 9:e.getCustomerGroupList(),e.getCompanyList(),console.log(e.$route.hash),"#goToCreateLockRule"==e.$route.hash&&e.goToCreateLockRule();case 13:case"end":return t.stop()}},t,e)}))()},activated:function(){console.log("hash",this.$route.hash),console.log("query",this.$route.query);var e=this.$route.query.id;this.showBaseForm=!0,this.showConditionForm=!1,this.showtimeRuleForm=!1,this.showgivenForm=!1,this.stepActive=0,"#goToCreateLockRule"!=this.$route.hash||e?"#goToCreateLockRule"==this.$route.hash&&e&&(this.flag=1,this.modifyRule(e)):this.goToCreateLockRule()},computed:c()({},Object(d.c)({getCurrentStation:"getCurrentStation"}),{salePrice:function(){return 3==this.extend_rule.market_type?Number(this.sellPrice)+Number(this.giveNumber)==0?0:(+this.SumPrice/(+this.sellPrice+ +this.giveNumber)).toFixed(2):0==Number(this.UnitPrice)||(Number(this.SumPrice)+Number(this.giveNumber))/Number(this.UnitPrice)==0?0:(Number(this.SumPrice)/((Number(this.SumPrice)+Number(this.giveNumber))/Number(this.UnitPrice))).toFixed(2)},givePrice:function(){return 0==+this.sellPrice||this.SumPrice/this.sellPrice==0?0:(this.giveNumber*(this.SumPrice/this.sellPrice).toFixed(2)).toFixed(2)}}),methods:{getGroupBaseInfo:function(){var e=this;return p()(u.a.mark(function t(){var r;return u.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.$axios.post("/Ostn/getGroupBaseInfo");case 3:if(200==(r=t.sent).data.status){t.next=6;break}return t.abrupt("return",e.$message.error(r.data.info));case 6:e.showLockRule=1==r.data.data.is_show_create_lock_price,t.next=13;break;case 9:t.prev=9,t.t0=t.catch(0),e.showLockRule=!1,e.$message.error("网络错误！");case 13:case"end":return t.stop()}},t,e,[[0,9]])}))()},getCardRuleLists:function(){var e=this;this.loading=!0,this.$axios.post("/CardRule/getCardChargeRuleList",{state:this.state,station_id:this.station_id,page:this.currentPage,page_size:this.pageSize,ruleName:this.searchTxt,RuleType:this.cardType,accurate_name:3}).then(function(t){e.loading=!1,200==t.data.status?(e.tableData=t.data.data.dt,e.totalNumber=t.data.data.TotalQty):e.$message({message:t.data.info,type:"error"})})},handleCurrentChange:function(e){this.currentPage=e,this.getCardRuleLists()},handleSizeChange:function(e){this.pageSize=e,this.getCardRuleLists()},stateChange:function(e){this.currentPage=1,this.getCardRuleLists()},stationChange:function(e){this.currentPage=1,this.getCardRuleLists()},modifyRule:function(e){var t=this;return p()(u.a.mark(function r(){return u.a.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,t.getCardThemeRuleList();case 2:console.log("flag",t.flag),0==t.flag?(t.cardId=e.ID,console.log("cardThemeRuleList111",t.cardThemeRuleList),t.cardThemeRuleList=t.cardThemeRuleList.filter(function(t){return t.Type==e.Type}),console.log("cardThemeRuleList111",t.cardThemeRuleList),t.newThemeList=t.cardThemeRuleList):t.cardId=e,console.log("id",e),console.log(t.cardId),t.isCreate=!1,t.$axios.post("/CardRule/getCardChargeRuleInfo",{id:t.cardId}).then(function(e){if(200==e.data.status){var r=e.data.data;t.public_config.rule_name=r.public_config.rule_name,t.public_config.description=r.public_config.description,t.public_config.priority=r.public_config.priority,t.public_config.date=[r.public_config.start_time,r.public_config.end_time],t.public_config.stationList=[],r.public_config.use_station_list&&r.public_config.use_station_list.length>0?r.public_config.use_station_list.forEach(function(e){t.public_config.stationList.push([e])}):t.public_config.stationList=[],r.extend_rule.card_theme.length==t.cardThemeRuleList.length?(t.checkThemeAll=!0,t.isThemeIndeterminate=!1):(t.isThemeIndeterminate=!0,t.checkThemeAll=!1),t.extend_rule.card_theme=r.extend_rule.card_theme;var i=[];r.extend_rule.customer_group_id&&r.extend_rule.customer_group_id.map(function(e){i.push(Number(e))}),i.length==t.customerGroupList.length?(t.checkCustomerGroupAll=!0,t.isCustomerGroupIndeterminate=!1):(t.isCustomerGroupIndeterminate=!0,t.checkCustomerGroupAll=!1),t.extend_rule.customer_group_id=i;var a=[];r.extend_rule.company_id&&r.extend_rule.company_id.map(function(e){a.push(Number(e))}),a.length==t.companyList.length?(t.checkCompanyAll=!0,t.isCompanyIndeterminate=!1):(t.isCompanyIndeterminate=!0,t.checkCompanyAll=!1),t.extend_rule.company_id=a;var n=[];if(r.extend_rule.gradeStr&&r.extend_rule.gradeStr.map(function(e){n.push(Number(e))}),n.length==t.classList.length?(t.checkClassAll=!0,t.isClassIndeterminate=!1):(t.isClassIndeterminate=!0,t.checkClassAll=!1),t.extend_rule.GradeStr=n,t.extend_rule.card_type=r.extend_rule.card_type,t.extend_rule.customer_id=r.extend_rule.customer_id,t.extend_rule.customer_id_type=r.extend_rule.customer_id_type,t.extend_rule.is_birthday=String(r.extend_rule.is_birthday),void 0!==r.extend_rule.first_recharge&&(t.extend_rule.first_recharge=String(r.extend_rule.first_recharge)),t.extend_rule.account_type=String(r.extend_rule.account_type),r.extend_rule.pay_way?(r.extend_rule.pay_way.length==t.payWayList.length?(t.checkPayWayAll=!0,t.isPayWayIndeterminate=!1):(t.checkPayWayAll=!1,t.isPayWayIndeterminate=!0),t.extend_rule.pay_way=r.extend_rule.pay_way):t.extend_rule.pay_way=[],t.time_rule.selectedTime=[],1==r.time_rule.type&&(t.time_rule.type=0,t.time_rule.rule=[]),2==r.time_rule.type&&(t.time_rule.type=1,r.time_rule.rule.forEach(function(e){t.time_rule.selectedTime.push({time:[e.start_time,e.end_time]})})),3==r.time_rule.type&&(t.time_rule.type=2,r.time_rule.rule.forEach(function(e){t.time_rule.selectedTime.push({date:e.date,time:[e.start_time,e.end_time]})})),4==r.time_rule.type&&(t.time_rule.type=3,r.time_rule.rule.forEach(function(e){t.time_rule.selectedTime.push({date:e.date,time:[e.start_time,e.end_time]})})),t.charge_give_rule.type=String(r.charge_give_rule.type),1!=r.charge_give_rule.data.market_type&&(t.isLockPrice=!0),1==r.charge_give_rule.data.market_type){var l=t.allCardThemeRuleList;t.cardThemeRuleList=l,t.isLockPrice=!1}0==r.charge_give_rule.type&&(t.charge_give_rule.guding.max_price=r.charge_give_rule.data.max_price,t.charge_give_rule.guding.min_price=r.charge_give_rule.data.min_price,t.charge_give_rule.guding.give=r.charge_give_rule.data.give.number,t.isLockPrice&&(t.sellPrice=t.charge_give_rule.guding.max_price,t.SumPrice=r.charge_give_rule.data.SumPrice,t.UnitPrice=(+r.charge_give_rule.data.UnitPrice).toFixed(2),t.extend_rule.market_type=r.charge_give_rule.data.market_type,t.giveNumber=r.charge_give_rule.data.give.number,t.handleCheckedMarketType())),1==r.charge_give_rule.type&&(t.charge_give_rule.bili.max_price=r.charge_give_rule.data.max_price,t.charge_give_rule.bili.min_price=r.charge_give_rule.data.min_price,t.charge_give_rule.bili.give=r.charge_give_rule.data.give.number),2==r.charge_give_rule.type&&(t.charge_give_rule.coupons.max_price=r.charge_give_rule.data.max_price,t.charge_give_rule.coupons.min_price=r.charge_give_rule.data.min_price,t.charge_give_rule.coupons.give=r.charge_give_rule.data.give.number),t.handelCouponlist(r.charge_give_rule.data.give.couponlist,r.charge_give_rule.type),void 0!==r.charge_give_rule.recharge&&(t.recharge.num=r.charge_give_rule.recharge.num,t.recharge.type=r.charge_give_rule.recharge.type,t.recharge.count=r.charge_give_rule.recharge.count,t.recharge.total=r.charge_give_rule.recharge.total),t.showTableData=!1,t.showBaseForm=!0}else t.$message({message:e.data.info,type:"error"})});case 8:case"end":return r.stop()}},r,t)}))()},handelCouponlist:function(e,t){var r=this,i=[],a=[];this.charge_give_rule.gudingCouponsData=[],this.charge_give_rule.addCouponsData=[],this.charge_give_rule.couponsData=[],e.forEach(function(e){i.push(e.coupon_id),a.push([e.coupon_id]),r.couponsOptions.forEach(function(t){t.coupon_id==e.coupon_id&&(t.num=e.coupon_num)})}),0==t?this.charge_give_rule.gudingCouponsData=i:1==t?this.charge_give_rule.addCouponsData=i:this.charge_give_rule.couponsData=i,this.addTableData(a)},disableRule:function(e){var t=this;this.$confirm("是否禁用“"+e.Name+"”？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){t.$axios.post("/CardRule/changeCardChargeRuleState",{state:101,id:e.ID}).then(function(e){200==e.status&&t.getCardRuleLists()})}).catch(function(){})},enableRule:function(e){var t=this;this.$confirm("是否启用“"+e.Name+"”？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){t.$axios.post("/CardRule/changeCardChargeRuleState",{state:100,id:e.ID}).then(function(e){200==e.status&&t.getCardRuleLists()})}).catch(function(){})},goToCreateRule:function(){this.showTableData=!1,this.showBaseForm=!0,this.isCreate=!0,this.isLockPrice=!1,this.clearFormData(),this.extend_rule.card_type=["1","2","3"],this.cardThemeRuleList=[].concat(l()(this.allCardThemeRuleList)),this.cardThemeRuleList=this.cardThemeRuleList.filter(function(e){return 1==e.Type}),this.$forceUpdate()},goToCreateLockRule:function(){this.showTableData=!1,this.showBaseForm=!0,this.isCreate=!0,this.isLockPrice=!0,console.log(this.extend_rule.card_type),this.clearFormData(),this.extend_rule.card_type=["1"],this.extend_rule.market_type="2";var e=this.allCardThemeRuleList;this.cardThemeRuleList=e,this.cardThemeRuleList=this.cardThemeRuleList.filter(function(e){return 2==e.Type}),console.log(this.cardThemeRuleList),this.$forceUpdate()},resetForm:function(){this.showBaseForm=!1,this.showTableData=!0,"#goToCreateLockRule"==this.$route.hash&&this.$router.push("/MarketingRules")},baseInfoNext:function(e){var t=this;console.log("hash",this.$route.hash),this.newThemeList.length>0&&!this.$route.hash&&(this.cardThemeRuleList=this.newThemeList,console.log("cardThemeRuleList222",this.cardThemeRuleList)),this.$refs[e].validate(function(e){e&&(t.stepActive=1,t.showBaseForm=!1,t.showConditionForm=!0)})},getStationList:function(){var e=this;this.$axios.post("/Stations/getStationList",{}).then(function(t){200==t.status&&(e.stationList=t.data.data,e.stationList.forEach(function(t){e.public_config.stationList.push([t.stid])}))})},getStationIdList:function(e){var t=[];e.forEach(function(e){t.push(e[0])})},getCardThemeRuleList:function(){var e=this;return p()(u.a.mark(function t(){var r;return u.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return r=e,t.next=3,r.$axios.post("/CardRule/getCardThemeRuleList",{state:100,station_id:0,page:1,page_size:500}).then(function(e){200==e.data.status?(r.cardThemeRuleList=e.data.data.dt,r.allCardThemeRuleList=e.data.data.dt):r.$message({message:e.data.info,type:"error"})});case 3:case"end":return t.stop()}},t,e)}))()},getCustomerGroupList:function(){var e=this;e.$axios.post("/CustomerGroup/getCustomerGroupList",{page:1,page_size:500}).then(function(t){200==t.data.status?e.customerGroupList=t.data.data.dt:e.$message({message:t.data.info,type:"error"})})},getCompanyList:function(){var e=this;e.$axios.post("/CompanyCard/getSimpleCompanyList",{page:1,page_size:1250,input:"",state:100}).then(function(t){200==t.data.status?e.companyList=t.data.data.dt:e.$message({message:t.data.info,type:"error"})}).catch(function(e){})},getChargeRuleTem:function(){var e=this;this.$axios.post("/CardRule/getChargeRuleTem",{}).then(function(t){200==t.data.status?(e.payWayList=t.data.data.pay_way,e.classList=t.data.data.level):e.$message({message:t.data.info,type:"error"})})},customerIdTypeChange:function(e){this.getCustomerIdList(e)},getCustomerIdList:function(e){},conditionBefore:function(){this.stepActive=0,this.showBaseForm=!0,this.showConditionForm=!1},conditionNext:function(){if(3!=this.extend_rule.account_type||0!=this.extend_rule.GradeStr.length)if(0!=this.extend_rule.account_type||0!=this.extend_rule.company_id.length){if(1==this.extend_rule.account_type){if(0==this.extend_rule.card_theme.length)return void this.$message.error("请勾选规则对应的卡！");if(0==this.extend_rule.card_type.length)return void this.$message.error("请勾选卡类型！")}2!=this.extend_rule.account_type||0!=this.extend_rule.customer_group_id.length?this.payWayList.length>0&&0==this.extend_rule.pay_way.length?this.$message.error("请勾选付款方式！"):(this.stepActive=2,this.showConditionForm=!1,this.showtimeRuleForm=!0):this.$message.error("请勾选规则对应的卡组！")}else this.$message.error("请勾选规则对应的车队！");else this.$message.error("请勾选规则对应的会员等级！")},checkTime:function(e){this.isCheckTime=e},timeRuleBefore:function(){this.stepActive=1,this.showConditionForm=!0,this.showtimeRuleForm=!1},timeRuleNext:function(){this.isCheckTime?(this.stepActive=3,this.showtimeRuleForm=!1,this.showgivenForm=!0):this.$message.error("请选择完整时间和日期！")},givenBefore:function(){this.stepActive=2,this.showtimeRuleForm=!0,this.showgivenForm=!1},getCouponsList:function(){var e=this;this.$axios.post("/GameActivity/getCouponsByStid",{}).then(function(t){200==t.status&&(e.couponsOptions=t.data.data,console.log(e.couponsOptions),e.couponsOptions.forEach(function(e){e.num=1,e.coupon_name=e.coupon_name+" "+e.coupon_id}))})},addTableData:function(e){var t=this,r=[];this.$nextTick(function(){(2==t.charge_give_rule.type?t.$refs.myCascader.getCheckedNodes():0==t.charge_give_rule.type?t.$refs.myGudingCascader.getCheckedNodes():t.$refs.myAddCascader.getCheckedNodes()).forEach(function(e){r.push(JSON.parse(a()(e.data)))});var i=[];e.forEach(function(e){i.push(e[0])}),r.forEach(function(e,t){i.includes(e.coupon_id)||r.splice(t,1)}),r.length>0&&(t.showGudingCoupons=!0),2==t.charge_give_rule.type?t.couponsTableData=r:1==t.charge_give_rule.type?t.addCouponsTableData=r:t.gudingCouponsTableData=r})},changeNumber:function(e){this.couponsOptions.forEach(function(t){t.coupon_id==e.coupon_id&&(t.num=e.num)})},handleClick:function(e,t){var r=this,i=e.coupon_id;2==t?(this.charge_give_rule.couponsData=[],this.couponsTableData.forEach(function(e,t){i==e.coupon_id&&r.couponsTableData.splice(t,1)}),this.couponsTableData.forEach(function(e){r.charge_give_rule.couponsData.push([e.coupon_id])})):1==t?(this.charge_give_rule.addCouponsData=[],this.addCouponsTableData.forEach(function(e,t){i==e.coupon_id&&r.addCouponsTableData.splice(t,1)}),this.addCouponsTableData.forEach(function(e){r.charge_give_rule.addCouponsData.push([e.coupon_id])})):(this.charge_give_rule.gudingCouponsData=[],this.gudingCouponsTableData.forEach(function(e,t){i==e.coupon_id&&r.gudingCouponsTableData.splice(t,1)}),this.gudingCouponsTableData.forEach(function(e){r.charge_give_rule.gudingCouponsData.push([e.coupon_id])}))},create:function(){var e=this,t="";0==this.charge_give_rule.type?t="chargeGive":1==this.charge_give_rule.type?t="chargeGive1":2==this.charge_give_rule.type&&(t="chargeGive2"),this.$refs[t].validate(function(t){t&&e.$refs.recharge.validate(function(t){if(t){e.btnDisabled=!0;var r={public_config:{id:"",rule_name:"",start_time:"",end_time:"",description:"",state:"",use_station_list:[],priority:""},extend_rule:{card_theme:[],customer_group_id:[],card_type:[],customer_id_type:"",customer_id:"",pay_way:[],is_birthday:"",first_recharge:"",account_type:"",company_id:[],GradeStr:[]},time_rule:{rule:[],type:""},charge_give_rule:{type:"",data:""},recharge:{num:"",type:"month",count:"",total:""}};e.isCreate||(r.public_config.id=e.cardId),r.public_config.rule_name=e.public_config.rule_name,r.public_config.start_time=e.public_config.date[0],r.public_config.end_time=e.public_config.date[1],r.public_config.description=e.public_config.description,r.public_config.state=100,r.public_config.priority=Number(e.public_config.priority);var i=[];if(e.public_config.stationList.forEach(function(e){i.push(e[0])}),r.public_config.use_station_list=i,r.extend_rule.card_theme=e.extend_rule.card_theme,r.extend_rule.customer_group_id=e.extend_rule.customer_group_id,r.extend_rule.company_id=e.extend_rule.company_id,r.extend_rule.GradeStr=e.extend_rule.GradeStr,r.extend_rule.card_type=e.extend_rule.card_type,r.extend_rule.customer_id_type=e.extend_rule.customer_id_type,r.extend_rule.customer_id=e.extend_rule.customer_id,r.extend_rule.pay_way=e.extend_rule.pay_way,r.extend_rule.is_birthday=Number(e.extend_rule.is_birthday),r.extend_rule.first_recharge=Number(e.extend_rule.first_recharge),r.extend_rule.account_type=Number(e.extend_rule.account_type),"0"==e.extend_rule.account_type?(r.extend_rule.card_theme=[],r.extend_rule.card_type=[],r.extend_rule.customer_group_id=[],r.extend_rule.GradeStr=[]):"1"==e.extend_rule.account_type?(r.extend_rule.company_id=[],r.extend_rule.customer_group_id=[],r.extend_rule.GradeStr=[]):"2"==e.extend_rule.account_type?(r.extend_rule.card_theme=[],r.extend_rule.card_type=[],r.extend_rule.company_id=[],r.extend_rule.GradeStr=[]):(r.extend_rule.card_theme=[],r.extend_rule.card_type=[],r.extend_rule.company_id=[],r.extend_rule.customer_group_id=[]),0==e.time_rule.type&&(r.time_rule.type=1,r.time_rule.rule=""),1==e.time_rule.type&&(r.time_rule.type=2,e.time_rule.selectedTime.forEach(function(e){r.time_rule.rule.push({start_time:e.time[0],end_time:e.time[1]})})),2==e.time_rule.type&&(r.time_rule.type=3,e.time_rule.selectedTime.forEach(function(e){r.time_rule.rule.push({date:e.date,start_time:e.time[0],end_time:e.time[1]})})),3==e.time_rule.type&&(r.time_rule.type=4,e.time_rule.selectedTime.forEach(function(e){r.time_rule.rule.push({date:e.date,start_time:e.time[0],end_time:e.time[1]})})),r.charge_give_rule.type=e.charge_give_rule.type,0==e.charge_give_rule.type){var a=[];e.gudingCouponsTableData.forEach(function(e){a.push({coupon_id:e.coupon_id,coupon_num:e.num})}),r.charge_give_rule.data={max_price:e.charge_give_rule.guding.max_price,min_price:e.charge_give_rule.guding.min_price,SumPrice:0,UnitPrice:0,market_type:1,give:{number:e.charge_give_rule.guding.give,couponlist:a}},e.isLockPrice&&(r.charge_give_rule.data={max_price:e.sellPrice,min_price:e.sellPrice,SumPrice:e.SumPrice,UnitPrice:e.UnitPrice,market_type:e.extend_rule.market_type,give:{number:e.giveNumber,couponlist:a}})}if(1==e.charge_give_rule.type){var n=[];e.addCouponsTableData.forEach(function(e){n.push({coupon_id:e.coupon_id,coupon_num:e.num})}),r.charge_give_rule.data={max_price:e.charge_give_rule.bili.max_price,min_price:e.charge_give_rule.bili.min_price,SumPrice:0,UnitPrice:0,market_type:1,give:{number:e.charge_give_rule.bili.give,couponlist:n}}}if(2==e.charge_give_rule.type){var l=[];e.couponsTableData.forEach(function(e){l.push({coupon_id:e.coupon_id,coupon_num:e.num})}),r.charge_give_rule.data={max_price:e.charge_give_rule.coupons.max_price,min_price:e.charge_give_rule.coupons.min_price,SumPrice:0,UnitPrice:0,market_type:1,give:{number:"",couponlist:l}}}r.charge_give_rule.recharge={num:e.recharge.num,type:e.recharge.type,count:e.recharge.count,total:e.recharge.total},e.$axios.post("/CardRule/setCardChargeRule",r).then(function(t){200==t.data.status?(e.isCreate?e.$message.success("创建成功"):e.$message.success("修改成功"),e.showTableData=!0,e.showBaseForm=!1,e.showConditionForm=!1,e.showtimeRuleForm=!1,e.showgivenForm=!1,e.btnDisabled=!1,e.currentPage=1,"#goToCreateLockRule"==e.$route.hash?e.$router.push("/MarketingRules"):e.getCardRuleLists()):(e.btnDisabled=!1,e.$message.error(t.data.info))})}})})},save:function(){var e=this,t="";0==this.charge_give_rule.type?t="chargeGive":1==this.charge_give_rule.type?t="chargeGive1":2==this.charge_give_rule.type&&(t="chargeGive2"),this.$refs[t].validate(function(t){t&&e.$refs.recharge.validate(function(t){if(t&&t){e.btnDisabled=!0;var r={public_config:{id:"",rule_name:"",start_time:"",end_time:"",description:"",state:"",use_station_list:[],priority:""},extend_rule:{card_theme:[],customer_group_id:[],card_type:[],customer_id_type:"",customer_id:"",pay_way:[],is_birthday:"",first_recharge:"",account_type:"",company_id:[],GradeStr:[]},time_rule:{rule:[],type:""},charge_give_rule:{type:"",data:""},recharge:{num:"",type:"month",count:"",total:""}};e.isCreate||(r.public_config.id=e.cardId),r.public_config.rule_name=e.public_config.rule_name,r.public_config.start_time=e.public_config.date[0],r.public_config.end_time=e.public_config.date[1],r.public_config.description=e.public_config.description,r.public_config.state=100,r.public_config.priority=Number(e.public_config.priority);var i=[];if(e.public_config.stationList.forEach(function(e){i.push(e[0])}),r.public_config.use_station_list=i,r.extend_rule.card_theme=e.extend_rule.card_theme,r.extend_rule.customer_group_id=e.extend_rule.customer_group_id,r.extend_rule.company_id=e.extend_rule.company_id,r.extend_rule.GradeStr=e.extend_rule.GradeStr,r.extend_rule.card_type=e.extend_rule.card_type,r.extend_rule.customer_id_type=e.extend_rule.customer_id_type,r.extend_rule.customer_id=e.extend_rule.customer_id,r.extend_rule.pay_way=e.extend_rule.pay_way,r.extend_rule.is_birthday=Number(e.extend_rule.is_birthday),r.extend_rule.first_recharge=Number(e.extend_rule.first_recharge),r.extend_rule.account_type=Number(e.extend_rule.account_type),"0"==e.extend_rule.account_type?(r.extend_rule.card_theme=[],r.extend_rule.card_type=[],r.extend_rule.customer_group_id=[],r.extend_rule.GradeStr=[]):"1"==e.extend_rule.account_type?(r.extend_rule.company_id=[],r.extend_rule.customer_group_id=[],r.extend_rule.GradeStr=[]):"2"==e.extend_rule.account_type?(r.extend_rule.card_theme=[],r.extend_rule.card_type=[],r.extend_rule.company_id=[],r.extend_rule.GradeStr=[]):(r.extend_rule.card_theme=[],r.extend_rule.card_type=[],r.extend_rule.company_id=[],r.extend_rule.customer_group_id=[]),0==e.time_rule.type&&(r.time_rule.type=1,r.time_rule.rule=""),1==e.time_rule.type&&(r.time_rule.type=2,e.time_rule.selectedTime.forEach(function(e){r.time_rule.rule.push({start_time:e.time[0],end_time:e.time[1]})})),2==e.time_rule.type&&(r.time_rule.type=3,e.time_rule.selectedTime.forEach(function(e){r.time_rule.rule.push({date:e.date,start_time:e.time[0],end_time:e.time[1]})})),3==e.time_rule.type&&(r.time_rule.type=4,e.time_rule.selectedTime.forEach(function(e){r.time_rule.rule.push({date:e.date,start_time:e.time[0],end_time:e.time[1]})})),r.charge_give_rule.type=e.charge_give_rule.type,0==e.charge_give_rule.type){var a=[];e.gudingCouponsTableData.forEach(function(e){a.push({coupon_id:e.coupon_id,coupon_num:e.num})}),r.charge_give_rule.data={max_price:e.charge_give_rule.guding.max_price,min_price:e.charge_give_rule.guding.min_price,SumPrice:0,UnitPrice:0,market_type:1,give:{number:e.charge_give_rule.guding.give,couponlist:a}},e.isLockPrice&&(r.charge_give_rule.data={max_price:e.sellPrice,min_price:e.sellPrice,SumPrice:e.SumPrice,UnitPrice:e.UnitPrice,market_type:e.extend_rule.market_type,give:{number:e.giveNumber,couponlist:a}})}if(1==e.charge_give_rule.type){var n=[];e.addCouponsTableData.forEach(function(e){n.push({coupon_id:e.coupon_id,coupon_num:e.num})}),r.charge_give_rule.data={max_price:e.charge_give_rule.bili.max_price,min_price:e.charge_give_rule.bili.min_price,SumPrice:0,UnitPrice:0,market_type:1,give:{number:e.charge_give_rule.bili.give,couponlist:n}}}if(2==e.charge_give_rule.type){var l=[];e.couponsTableData.forEach(function(e){l.push({coupon_id:e.coupon_id,coupon_num:e.num})}),r.charge_give_rule.data={max_price:e.charge_give_rule.coupons.max_price,min_price:e.charge_give_rule.coupons.min_price,SumPrice:0,UnitPrice:0,market_type:1,give:{number:"",couponlist:l}}}r.charge_give_rule.recharge={num:e.recharge.num,type:e.recharge.type,count:e.recharge.count,total:e.recharge.total},e.$axios.post("/CardRule/setCardChargeRule",r).then(function(t){200==t.data.status?(e.isCreate?e.$message.success("创建成功"):e.$message.success("修改成功"),e.showTableData=!0,e.showBaseForm=!1,e.showConditionForm=!1,e.showtimeRuleForm=!1,e.showgivenForm=!1,e.btnDisabled=!1,e.currentPage=1,"#goToCreateLockRule"==e.$route.hash?e.$router.push("/MarketingRules"):e.getCardRuleLists()):(e.btnDisabled=!1,e.$message.error(t.data.info))})}})})},clearFormData:function(){var e=this;this.public_config.rule_name="",this.public_config.date=[],this.public_config.description="",this.public_config.stationList=[],this.stationList.forEach(function(t){e.public_config.stationList.push([t.stid])}),this.public_config.priority="10",this.extend_rule.card_theme=[],this.extend_rule.customer_group_id=[],this.extend_rule.company_id=[],this.extend_rule.GradeStr=[],this.extend_rule.card_type=[],this.extend_rule.customer_id_type="L",this.extend_rule.pay_way=[],this.extend_rule.is_birthday="0",this.extend_rule.first_recharge="0",this.extend_rule.account_type="1",this.time_rule.type=0,this.time_rule.selectedTime=[],this.charge_give_rule.type="0",this.charge_give_rule.coupons.max_price="",this.charge_give_rule.coupons.min_price="",this.charge_give_rule.couponsData=[],this.charge_give_rule.guding.max_price="",this.charge_give_rule.guding.min_price="",this.charge_give_rule.guding.give="",this.charge_give_rule.bili.max_price="",this.charge_give_rule.bili.min_price="",this.charge_give_rule.bili.give="",this.recharge.num="0",this.recharge.type="month",this.recharge.count="0",this.recharge.total="0",this.couponsTableData=[],this.addCouponsTableData=[],this.gudingCouponsTableData=[],this.charge_give_rule.gudingCouponsData=[],this.charge_give_rule.addCouponsData=[],this.showGudingCoupons=!1,this.couponsOptions.forEach(function(e){e.num=1})},changeAccountType:function(e){0==e&&2==this.charge_give_rule.type&&(this.charge_give_rule.type="0")},handleCheckThemeAllChange:function(e){var t=[];this.cardThemeRuleList.forEach(function(e){t.push(e.ID)}),this.extend_rule.card_theme=e?t:[],this.isThemeIndeterminate=!1},handleCheckedThemeChange:function(e){var t=e.length;this.checkThemeAll=t===this.cardThemeRuleList.length,this.isThemeIndeterminate=t>0&&t<this.cardThemeRuleList.length},handleCheckClassAllChange:function(e){var t=[];this.classList.forEach(function(e){t.push(e.id)}),this.extend_rule.GradeStr=e?t:[],this.isClassIndeterminate=!1},handleCheckedClassChange:function(e){var t=e.length;this.checkClassAll=t===this.classList.length,this.isClassIndeterminate=t>0&&t<this.classList.length},handleCheckCompanyAllChange:function(e){var t=[];this.companyList.forEach(function(e){t.push(e.ID)}),this.extend_rule.company_id=e?t:[],this.isCompanyIndeterminate=!1},handleCheckedCompanyChange:function(e){var t=e.length;this.checkCompanyAll=t===this.companyList.length,this.isCompanyIndeterminate=t>0&&t<this.companyList.length},handleCheckCustomerGroupAllChange:function(e){var t=[];this.customerGroupList.forEach(function(e){t.push(e.ID)}),this.extend_rule.customer_group_id=e?t:[],this.isCustomerGroupIndeterminate=!1},handleCheckedCustomerGroupChange:function(e){var t=e.length;this.checkCustomerGroupAll=t===this.customerGroupList.length,this.isCustomerGroupIndeterminate=t>0&&t<this.customerGroupList.length},handleCheckPayWayAllChange:function(e){var t=[];this.payWayList.forEach(function(e){t.push(e.BH)}),this.extend_rule.pay_way=e?t:[],this.isPayWayIndeterminate=!1},handleCheckedPayWayChange:function(e){var t=e.length;this.checkPayWayAll=t===this.payWayList.length,this.isPayWayIndeterminate=t>0&&t<this.payWayList.length},search:function(){this.currentPage=1,this.getCardRuleLists()},changeRadioValue:function(e){this.$refs.chargeGive.resetFields(),this.$refs.chargeGive1.resetFields(),this.$refs.chargeGive2.resetFields(),this.couponsOptions.forEach(function(e){e.num=1}),"0"==e?(this.charge_give_rule.bili.max_price="",this.charge_give_rule.bili.min_price="",this.charge_give_rule.bili.give="",this.charge_give_rule.coupons.max_price="",this.charge_give_rule.coupons.min_price="",this.charge_give_rule.couponsData=[],this.charge_give_rule.addCouponsData=[],this.couponsTableData=[],this.addCouponsTableData=[],this.showGudingCoupons=!1):"1"==e?(this.charge_give_rule.coupons.max_price="",this.charge_give_rule.coupons.min_price="",this.charge_give_rule.couponsData=[],this.charge_give_rule.gudingCouponsData=[],this.couponsTableData=[],this.gudingCouponsTableData=[],this.charge_give_rule.guding.max_price="",this.charge_give_rule.guding.min_price="",this.charge_give_rule.guding.give="",this.showGudingCoupons=!1):"2"==e&&(this.charge_give_rule.guding.max_price="",this.charge_give_rule.guding.min_price="",this.charge_give_rule.guding.give="",this.charge_give_rule.bili.max_price="",this.charge_give_rule.bili.min_price="",this.charge_give_rule.bili.give="",this.showGudingCoupons=!1,this.gudingCouponsTableData=[],this.addCouponsTableData=[],this.charge_give_rule.gudingCouponsData=[],this.charge_give_rule.addCouponsData=[])},calculationRules:function(){if(2==this.extend_rule.market_type){var e=(Number(this.SumPrice)+Number(this.giveNumber))/Number(this.sellPrice);console.log(e)}3==this.extend_rule.market_type&&(this.UnitPrice=(Number(this.SumPrice)/(Number(this.sellPrice)+Number(this.giveNumber))).toFixed(2),this.sellPrice=this.regNumber(this.sellPrice)),this.SumPrice=this.regNumber(this.SumPrice),this.giveNumber=this.regNumber(this.giveNumber)},regNumber:function(e){var t=e+"",r=t.substr(0,1),i=t.substr(1,1);return t.length>1&&0==r&&"."!=i&&(t=t.substr(1,t.length-1)),t=t.replace(/[^\d^\.]+/g,"")},handleCheckedMarketType:function(){var e=this,t=this.allCardThemeRuleList;this.cardThemeRuleList=t,this.cardThemeRuleList=this.cardThemeRuleList.filter(function(t){return t.Type==e.extend_rule.market_type}),console.log(this.cardThemeRuleList)}},watch:{getCurrentStation:function(e,t){0!=e.merchant_type&&e.value!=t.value&&(this.showLockRule=!1,this.getGroupBaseInfo(),this.getStationList(),this.getCouponsList(),this.getCardRuleLists(),this.getChargeRuleTem(),this.getCardThemeRuleList(),this.getCustomerGroupList(),this.getCompanyList())}}},g={render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"cardRule",attrs:{id:"cardRule"}},[r("div",{directives:[{name:"show",rawName:"v-show",value:e.showTableData,expression:"showTableData"}],staticClass:"table-box"},[r("div",{staticClass:"tab-box"},[r("div",{staticClass:"item"},[r("span",{staticClass:"tab"},[e._v("状态")]),e._v(" "),r("el-radio-group",{on:{change:e.stateChange},model:{value:e.state,callback:function(t){e.state=t},expression:"state"}},e._l(e.stateList,function(t){return r("el-radio-button",{key:t.key,attrs:{label:t.value}},[e._v(e._s(t.label))])}),1)],1),e._v(" "),r("div",{staticClass:"item"},[r("span",{staticClass:"tab"},[e._v("可用油站")]),e._v(" "),r("el-radio-group",{on:{change:e.stationChange},model:{value:e.station_id,callback:function(t){e.station_id=t},expression:"station_id"}},[r("el-radio-button",{attrs:{label:0}},[e._v("全部")]),e._v(" "),e._l(e.stationList,function(t){return r("el-radio-button",{key:t.key,attrs:{label:t.stid}},[e._v(e._s(t.stname))])})],2)],1)]),e._v(" "),r("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[r("div",[r("el-button",{staticStyle:{border:"none"},attrs:{type:"primary"},on:{click:e.goToCreateRule}},[e._v("创建通用规则")]),e._v(" "),e.showLockRule?r("el-button",{on:{click:e.goToCreateLockRule}},[e._v("创建锁价规则")]):e._e()],1),e._v(" "),r("div",[r("el-input",{staticStyle:{width:"200px",margin:"0"},attrs:{placeholder:"请输入规则名称","prefix-icon":"el-icon-search",clearable:""},model:{value:e.searchTxt,callback:function(t){e.searchTxt=t},expression:"searchTxt"}}),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:e.search}},[e._v("查询")])],1)]),e._v(" "),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"cardRuleListTable",attrs:{data:e.tableData}},[r("el-table-column",{attrs:{prop:"ID",align:"left","min-width":"80",label:"ID"}}),e._v(" "),r("el-table-column",{attrs:{align:"center","header-align":"center","min-width":"120",label:"状态"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",{class:{redfont:100!=t.row.State}},[e._v(e._s(100==t.row.State?"启用":"禁用"))])]}}])}),e._v(" "),r("el-table-column",{attrs:{prop:"Name",align:"left","min-width":"180",label:"名称"}}),e._v(" "),r("el-table-column",{attrs:{prop:"Priority",align:"center","header-align":"center","min-width":"120",label:"优先级"}}),e._v(" "),r("el-table-column",{attrs:{align:"center","header-align":"center","min-width":"180",label:"计算公式"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",{staticStyle:{"white-space":"pre-line"},domProps:{innerHTML:e._s(t.row.CalculationFormulaInfo)}})]}}])}),e._v(" "),r("el-table-column",{attrs:{prop:"TimeRuleInfo",align:"center","header-align":"center","min-width":"360",label:"时间规则"}}),e._v(" "),r("el-table-column",{attrs:{prop:"address",align:"center","header-align":"center","min-width":"360",label:"起止时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n                    "+e._s(t.row.StartingTime+"至"+t.row.CutOffTime)+"\n                ")]}}])}),e._v(" "),r("el-table-column",{attrs:{prop:"address",align:"center","min-width":"120",fixed:"right","header-align":"center",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{attrs:{type:"text",size:"small"},on:{click:function(r){e.flag=0,e.modifyRule(t.row)}}},[e._v("修改")]),e._v(" "),100==t.row.State?r("el-button",{attrs:{type:"text",size:"small"},on:{click:function(r){return e.disableRule(t.row)}}},[e._v("禁用")]):e._e(),e._v(" "),100!=t.row.State?r("el-button",{attrs:{type:"text",size:"small"},on:{click:function(r){return e.enableRule(t.row)}}},[e._v("启用")]):e._e()]}}])})],1),e._v(" "),r("div",{staticClass:"page_content"},[r("el-pagination",{staticClass:"page_left",attrs:{"current-page":e.currentPage,"page-size":e.pageSize,layout:"prev, pager, next",total:e.totalNumber},on:{"current-change":e.handleCurrentChange}}),e._v(" "),r("el-pagination",{staticClass:"page_right",attrs:{"page-sizes":[10,15,20,30],"page-size":e.pageSize,layout:"total, sizes",total:e.totalNumber},on:{"size-change":e.handleSizeChange}})],1)],1),e._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:!e.showTableData,expression:"!showTableData"}]},[r("el-steps",{staticClass:"card-step",staticStyle:{"margin-top":"20px"},attrs:{active:e.stepActive,"finish-status":"success",simple:""}},[r("el-step",{attrs:{title:"基本信息"}}),e._v(" "),r("el-step",{attrs:{title:"使用条件"}}),e._v(" "),r("el-step",{attrs:{title:"时间规则"}}),e._v(" "),r("el-step",{attrs:{title:"充值赠送"}})],1),e._v(" "),r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showBaseForm,expression:"showBaseForm"}],ref:"baseForm",staticClass:"demo-ruleForm card-form base-form",attrs:{model:e.public_config,rules:e.public_config_rules,"label-width":"108px"}},[r("el-form-item",{attrs:{label:"充值规则名称",prop:"rule_name"}},[r("el-input",{staticStyle:{width:"250px"},attrs:{placeholder:"请输入规则名称"},model:{value:e.public_config.rule_name,callback:function(t){e.$set(e.public_config,"rule_name",t)},expression:"public_config.rule_name"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"起止时间",prop:"date"}},[r("el-date-picker",{staticStyle:{width:"360px"},attrs:{"default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd HH:mm:ss",type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.public_config.date,callback:function(t){e.$set(e.public_config,"date",t)},expression:"public_config.date"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"规则说明",prop:"description"}},[r("el-input",{staticStyle:{width:"360px"},attrs:{type:"textarea",placeholder:"请输入规则说明",rows:5,maxlength:"100","show-word-limit":""},model:{value:e.public_config.description,callback:function(t){e.$set(e.public_config,"description",t)},expression:"public_config.description"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"可用油站",prop:"stationList"}},[r("el-cascader",{staticStyle:{width:"250px"},attrs:{options:e.stationList,props:e.stationListProps,"collapse-tags":""},on:{change:e.getStationIdList},model:{value:e.public_config.stationList,callback:function(t){e.$set(e.public_config,"stationList",t)},expression:"public_config.stationList"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"优先级",required:""}},[r("el-select",{staticStyle:{width:"250px"},attrs:{placeholder:"请选择"},model:{value:e.public_config.priority,callback:function(t){e.$set(e.public_config,"priority",t)},expression:"public_config.priority"}},e._l(10,function(e){return r("el-option",{key:e.index,attrs:{label:e,value:e}})}),1),e._v(" "),r("i",{staticStyle:{display:"inline-block","line-height":"20px",position:"relative",top:"10px"}},[e._v("优先级：决定规则执行的顺序。选项为1-10，数值越小优先级越高；同等优先级的情况下，按照ID越小越优先执行。"),r("br"),e._v("建议：设置规则时从10优先级设置，方便以后增加新的高优先级规则。")])],1),e._v(" "),r("el-form-item",[r("el-button",{on:{click:function(t){return e.resetForm()}}},[e._v("取消")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.baseInfoNext("baseForm")}}},[e._v("下一步")])],1)],1),e._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:e.showConditionForm,expression:"showConditionForm"}],staticClass:"card-form recharge-form"},[r("p",{staticClass:"title"},[e._v("使用类型")]),e._v(" "),r("el-form",{staticClass:"card-form rule-form",attrs:{model:e.extend_rule}},[r("el-form-item",{attrs:{label:"客户类型:",required:""}},[r("el-radio-group",{on:{change:e.changeAccountType},model:{value:e.extend_rule.account_type,callback:function(t){e.$set(e.extend_rule,"account_type",t)},expression:"extend_rule.account_type"}},[r("el-radio",{attrs:{label:"3",disabled:e.isLockPrice}},[e._v("按会员等级")]),e._v(" "),r("el-radio",{attrs:{label:"1"}},[e._v("按制卡规则")]),e._v(" "),r("el-radio",{attrs:{label:"0",disabled:e.isLockPrice}},[e._v("按车队客户")]),e._v(" "),r("el-radio",{attrs:{label:"2",disabled:e.isLockPrice}},[e._v("按卡组")])],1)],1)],1),e._v(" "),r("p",{directives:[{name:"show",rawName:"v-show",value:3==e.extend_rule.account_type,expression:"extend_rule.account_type==3"}],staticClass:"title"},[e._v("勾选规则对应的会员等级")]),e._v(" "),r("el-form",{directives:[{name:"show",rawName:"v-show",value:3==e.extend_rule.account_type,expression:"extend_rule.account_type==3"}],staticClass:"card-form rule-form",attrs:{model:e.extend_rule}},[r("el-form-item",{staticClass:"last-item",attrs:{label:"会员等级:",required:""}},[r("el-checkbox",{attrs:{indeterminate:e.isClassIndeterminate},on:{change:e.handleCheckClassAllChange},model:{value:e.checkClassAll,callback:function(t){e.checkClassAll=t},expression:"checkClassAll"}},[e._v("全选")]),e._v(" "),r("el-checkbox-group",{staticStyle:{"margin-left":"81px"},on:{change:e.handleCheckedClassChange},model:{value:e.extend_rule.GradeStr,callback:function(t){e.$set(e.extend_rule,"GradeStr",t)},expression:"extend_rule.GradeStr"}},e._l(e.classList,function(t){return r("el-checkbox",{key:t.id,attrs:{label:t.id}},[e._v(e._s(t.level_name))])}),1)],1)],1),e._v(" "),r("p",{directives:[{name:"show",rawName:"v-show",value:0==e.extend_rule.account_type,expression:"extend_rule.account_type==0"}],staticClass:"title"},[e._v("勾选规则对应的车队")]),e._v(" "),r("el-form",{directives:[{name:"show",rawName:"v-show",value:0==e.extend_rule.account_type,expression:"extend_rule.account_type==0"}],staticClass:"card-form rule-form",attrs:{model:e.extend_rule}},[r("el-form-item",{staticClass:"last-item",attrs:{label:"车队名称:",required:""}},[r("el-checkbox",{attrs:{indeterminate:e.isCompanyIndeterminate},on:{change:e.handleCheckCompanyAllChange},model:{value:e.checkCompanyAll,callback:function(t){e.checkCompanyAll=t},expression:"checkCompanyAll"}},[e._v("全选")]),e._v(" "),r("el-checkbox-group",{staticStyle:{"margin-left":"81px"},on:{change:e.handleCheckedCompanyChange},model:{value:e.extend_rule.company_id,callback:function(t){e.$set(e.extend_rule,"company_id",t)},expression:"extend_rule.company_id"}},e._l(e.companyList,function(t){return r("el-checkbox",{key:t.ID,attrs:{label:t.ID}},[e._v(e._s(t.CompanyName))])}),1)],1)],1),e._v(" "),r("p",{directives:[{name:"show",rawName:"v-show",value:1==e.extend_rule.account_type,expression:"extend_rule.account_type==1"}],staticClass:"title"},[e._v("勾选规则对应的卡")]),e._v(" "),r("el-form",{directives:[{name:"show",rawName:"v-show",value:1==e.extend_rule.account_type,expression:"extend_rule.account_type==1"}],staticClass:"card-form rule-form",attrs:{model:e.extend_rule}},[e.isLockPrice?r("el-form-item",{staticClass:"last-item",attrs:{label:"锁价卡类型:",required:""}},[r("el-radio-group",{on:{change:e.handleCheckedMarketType},model:{value:e.extend_rule.market_type,callback:function(t){e.$set(e.extend_rule,"market_type",t)},expression:"extend_rule.market_type"}},[r("el-radio",{attrs:{label:"2",disabled:!e.isCreate}},[e._v("定额卡")]),e._v(" "),r("el-radio",{attrs:{label:"3",disabled:!e.isCreate}},[e._v("定升卡")])],1),e._v(" "),r("i",{staticClass:"el-icon-warning warning-icon",staticStyle:{"margin-left":"30px"}}),e._v("锁价类型选中提交后不可更改\n                    ")],1):e._e(),e._v(" "),e.isLockPrice?e._e():r("el-form-item",{staticClass:"last-item",attrs:{label:"卡名称:",required:""}},[r("el-checkbox",{attrs:{indeterminate:e.isThemeIndeterminate},on:{change:e.handleCheckThemeAllChange},model:{value:e.checkThemeAll,callback:function(t){e.checkThemeAll=t},expression:"checkThemeAll"}},[e._v("全选")]),e._v(" "),r("el-checkbox-group",{staticStyle:{"margin-left":"110px"},on:{change:e.handleCheckedThemeChange},model:{value:e.extend_rule.card_theme,callback:function(t){e.$set(e.extend_rule,"card_theme",t)},expression:"extend_rule.card_theme"}},e._l(e.cardThemeRuleList,function(t){return r("el-checkbox",{key:t.ID,attrs:{label:t.ID}},[e._v(e._s(t.Name))])}),1)],1),e._v(" "),e.isLockPrice?r("el-form-item",{staticClass:"last-item",attrs:{label:"卡名称:",required:""}},[r("el-checkbox",{attrs:{indeterminate:e.isThemeIndeterminate},on:{change:e.handleCheckThemeAllChange},model:{value:e.checkThemeAll,callback:function(t){e.checkThemeAll=t},expression:"checkThemeAll"}},[e._v("全选")]),e._v(" "),r("el-checkbox-group",{staticStyle:{"margin-left":"110px"},on:{change:e.handleCheckedThemeChange},model:{value:e.extend_rule.card_theme,callback:function(t){e.$set(e.extend_rule,"card_theme",t)},expression:"extend_rule.card_theme"}},e._l(e.cardThemeRuleList,function(t){return r("el-checkbox",{key:t.ID,attrs:{label:t.ID}},[e._v(e._s(t.Name))])}),1)],1):e._e()],1),e._v(" "),r("p",{directives:[{name:"show",rawName:"v-show",value:2==e.extend_rule.account_type,expression:"extend_rule.account_type==2"}],staticClass:"title"},[e._v("勾选规则对应的卡组")]),e._v(" "),r("el-form",{directives:[{name:"show",rawName:"v-show",value:2==e.extend_rule.account_type,expression:"extend_rule.account_type==2"}],staticClass:"card-form rule-form",attrs:{model:e.extend_rule}},[r("el-form-item",{staticClass:"last-item",attrs:{label:"卡组名称:",required:""}},[r("el-checkbox",{attrs:{indeterminate:e.isCustomerGroupIndeterminate},on:{change:e.handleCheckCustomerGroupAllChange},model:{value:e.checkCustomerGroupAll,callback:function(t){e.checkCustomerGroupAll=t},expression:"checkCustomerGroupAll"}},[e._v("全选")]),e._v(" "),r("el-checkbox-group",{staticStyle:{"margin-left":"100px"},on:{change:e.handleCheckedCustomerGroupChange},model:{value:e.extend_rule.customer_group_id,callback:function(t){e.$set(e.extend_rule,"customer_group_id",t)},expression:"extend_rule.customer_group_id"}},e._l(e.customerGroupList,function(t){return r("el-checkbox",{key:t.ID,attrs:{label:t.ID}},[e._v(e._s(t.CustomerGroupName))])}),1)],1)],1),e._v(" "),r("p",{directives:[{name:"show",rawName:"v-show",value:1==e.extend_rule.account_type,expression:"extend_rule.account_type==1"}],staticClass:"title"},[e._v("卡使用范围")]),e._v(" "),r("el-form",{directives:[{name:"show",rawName:"v-show",value:1==e.extend_rule.account_type,expression:"extend_rule.account_type==1"}],staticClass:"card-form rule-form",attrs:{model:e.extend_rule}},[r("el-form-item",{attrs:{label:"卡类型:",prop:"card_type",required:""}},[r("el-checkbox-group",{model:{value:e.extend_rule.card_type,callback:function(t){e.$set(e.extend_rule,"card_type",t)},expression:"extend_rule.card_type"}},[r("el-checkbox",{attrs:{label:"1"}},[e._v("个人卡")]),e._v(" "),r("el-checkbox",{attrs:{label:"2",disabled:e.isLockPrice}},[e._v("车队卡")]),e._v(" "),r("el-checkbox",{attrs:{label:"3",disabled:e.isLockPrice}},[e._v("不记名卡")])],1)],1)],1),e._v(" "),r("p",{staticClass:"title"},[e._v("高级设置")]),e._v(" "),r("el-form",{staticClass:"card-form rule-form",attrs:{model:e.extend_rule}},[r("el-form-item",{attrs:{label:"付款方式:",required:""}},[r("el-checkbox",{attrs:{indeterminate:e.isPayWayIndeterminate},on:{change:e.handleCheckPayWayAllChange},model:{value:e.checkPayWayAll,callback:function(t){e.checkPayWayAll=t},expression:"checkPayWayAll"}},[e._v("全选")]),e._v(" "),r("el-checkbox-group",{staticStyle:{"margin-left":"110px"},on:{change:e.handleCheckedPayWayChange},model:{value:e.extend_rule.pay_way,callback:function(t){e.$set(e.extend_rule,"pay_way",t)},expression:"extend_rule.pay_way"}},e._l(e.payWayList,function(t){return r("el-checkbox",{key:t.key,attrs:{label:t.BH}},[e._v(e._s(t.MC))])}),1)],1),e._v(" "),r("el-form-item",{attrs:{label:"专享设置:"}},[r("el-radio-group",{model:{value:e.extend_rule.is_birthday,callback:function(t){e.$set(e.extend_rule,"is_birthday",t)},expression:"extend_rule.is_birthday"}},[r("el-radio",{attrs:{label:"0"}},[e._v("无")]),e._v(" "),r("el-radio",{attrs:{label:"1"}},[e._v("生日专享")])],1),e._v(" "),r("el-tooltip",{attrs:{placement:"right",effect:"light","popper-class":"card-tooltip"}},[r("div",{attrs:{slot:"content"},slot:"content"},[e._v("生日信息来源于公众号个人中心的个人资料设置。若勾选，则"),r("br"),e._v("用户生日当天享受本条规则。")]),e._v(" "),r("i",{staticClass:"el-icon-question",staticStyle:{"font-size":"18px"}})])],1),e._v(" "),r("el-form-item",{attrs:{label:"充值专享:"}},[r("el-radio-group",{model:{value:e.extend_rule.first_recharge,callback:function(t){e.$set(e.extend_rule,"first_recharge",t)},expression:"extend_rule.first_recharge"}},[r("el-radio",{attrs:{label:"0"}},[e._v("无")]),e._v(" "),r("el-radio",{attrs:{label:"1"}},[e._v("首次充值专享")])],1)],1)],1),e._v(" "),r("div",{staticClass:"btn-box"},[r("el-button",{on:{click:function(t){return e.conditionBefore()}}},[e._v("上一步")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.conditionNext()}}},[e._v("下一步")])],1)],1),e._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:e.showtimeRuleForm,expression:"showtimeRuleForm"}],staticClass:"card-form spend-form"},[r("el-form",{ref:"form",staticClass:"time-form",attrs:{model:e.time_rule,"label-width":"80px"}},[r("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"时间类型"}},[r("el-radio-group",{model:{value:e.time_rule.type,callback:function(t){e.$set(e.time_rule,"type",t)},expression:"time_rule.type"}},[r("el-radio",{staticStyle:{"min-width":"80px"},attrs:{label:0}},[e._v("无限制")]),e._v(" "),r("el-radio",{staticStyle:{"min-width":"80px"},attrs:{label:1}},[e._v("每日")]),e._v(" "),r("el-radio",{staticStyle:{"min-width":"80px"},attrs:{label:2}},[e._v("每周")]),e._v(" "),r("el-radio",{staticStyle:{"min-width":"80px"},attrs:{label:3}},[e._v("每月")])],1)],1)],1),e._v(" "),r("el-repeat-time-picker",{staticStyle:{"margin-left":"128px"},attrs:{xstyle:"width: 280px;","value-format":"HH:mm:ss","max-length":5,type:e.time_rule.type},on:{change:e.checkTime},model:{value:e.time_rule.selectedTime,callback:function(t){e.$set(e.time_rule,"selectedTime",t)},expression:"time_rule.selectedTime"}}),e._v(" "),r("div",{staticClass:"time-btn-box"},[r("el-button",{on:{click:function(t){return e.timeRuleBefore()}}},[e._v("上一步")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.timeRuleNext("timeRuleForm")}}},[e._v("下一步")])],1)],1),e._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:e.showgivenForm,expression:"showgivenForm"}],staticClass:"card-form rule-form given-form"},[e.isLockPrice?e._e():r("el-radio-group",{on:{change:e.changeRadioValue},model:{value:e.charge_give_rule.type,callback:function(t){e.$set(e.charge_give_rule,"type",t)},expression:"charge_give_rule.type"}},[r("el-form",{ref:"chargeGive",staticClass:"card-form",attrs:{model:e.charge_give_rule,rules:e.chargeGiveRules}},[r("el-form-item",{attrs:{label:"充值赠送类型:"}},[r("el-row",{staticClass:"card-radio-group"},[r("el-radio",{attrs:{label:"0"}},[e._v("固定赠送")]),e._v(" "),r("span",[e._v("充值大于等于")]),e._v(" "),r("el-form-item",{attrs:{prop:"guding_min_price"}},[r("el-input",{ref:"guding_min_price",staticStyle:{width:"88px"},attrs:{onkeyup:"value=value.replace(/[^\\d\\.]/g,'')"},model:{value:e.charge_give_rule.guding.min_price,callback:function(t){e.$set(e.charge_give_rule.guding,"min_price",t)},expression:"charge_give_rule.guding.min_price"}})],1),e._v(" "),r("span",[e._v("元，小于")]),e._v(" "),r("el-form-item",{attrs:{prop:"guding_max_price"}},[r("el-input",{ref:"guding_max_price",staticStyle:{width:"88px"},attrs:{onkeyup:"value=value.replace(/[^\\d\\.]/g,'')"},model:{value:e.charge_give_rule.guding.max_price,callback:function(t){e.$set(e.charge_give_rule.guding,"max_price",t)},expression:"charge_give_rule.guding.max_price"}})],1),e._v(" "),r("span",[e._v("元时，赠送")]),e._v(" "),r("el-form-item",{attrs:{prop:"guding_give_money"}},[r("el-input",{ref:"guding_give_money",staticStyle:{width:"88px"},attrs:{onkeyup:"value=value.replace(/[^\\d\\.]/g,'')"},model:{value:e.charge_give_rule.guding.give,callback:function(t){e.$set(e.charge_give_rule.guding,"give",t)},expression:"charge_give_rule.guding.give"}})],1),e._v(" "),r("span",[e._v("元。")]),e._v(" "),0==e.charge_give_rule.type?r("span",{staticClass:"add-coupons active"},[e._v("需要叠加送券？"),r("span",{staticClass:"add",on:{click:function(t){e.showGudingCoupons=!0}}},[e._v("添加券")])]):r("span",{staticClass:"add-coupons"},[e._v("需要叠加送券？"),r("span",{staticClass:"add"},[e._v("添加券")])])],1),e._v(" "),r("el-row",{directives:[{name:"show",rawName:"v-show",value:0==e.charge_give_rule.type&&e.showGudingCoupons,expression:"charge_give_rule.type == 0 && showGudingCoupons"}],staticClass:"card-radio-group"},[r("el-form-item",{staticStyle:{"margin-left":"208px","margin-top":"20px"},attrs:{prop:"gudingCouponsData"}},[r("el-cascader",{ref:"myGudingCascader",attrs:{filterable:"",options:e.couponsOptions,props:e.couponsProps,"collapse-tags":""},on:{change:e.addTableData},model:{value:e.charge_give_rule.gudingCouponsData,callback:function(t){e.$set(e.charge_give_rule,"gudingCouponsData",t)},expression:"charge_give_rule.gudingCouponsData"}})],1)],1)],1),e._v(" "),r("el-form-item",{directives:[{name:"show",rawName:"v-show",value:0==e.charge_give_rule.type&&e.gudingCouponsTableData.length,expression:"charge_give_rule.type == 0 && gudingCouponsTableData.length"}],staticClass:"item item04"},[r("el-table",{staticStyle:{width:"100%"},attrs:{data:e.gudingCouponsTableData}},[r("el-table-column",{attrs:{prop:"coupon_id",align:"center",label:"券ID"}}),e._v(" "),r("el-table-column",{attrs:{prop:"coupon_name",align:"center",label:"券名"}}),e._v(" "),r("el-table-column",{attrs:{align:"center",label:"券额"},scopedSlots:e._u([{key:"default",fn:function(t){return[5!=t.row.price_type?r("span",[e._v(e._s(t.row.price_rule.price?t.row.price_rule.price:t.row.price_rule.discount?t.row.price_rule.discount+"折":t.row.price_rule.min_price+"-"+t.row.price_rule.max_price))]):r("span",[e._v(e._s(t.row.price_rule.price)+"元/升")])]}}],null,!1,1721922336)}),e._v(" "),r("el-table-column",{attrs:{label:"数量",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-input-number",{attrs:{size:"small",step:1,"step-strictly":"",min:1},on:{change:function(r){return e.changeNumber(t.row)}},model:{value:t.row.num,callback:function(r){e.$set(t.row,"num",r)},expression:"scope.row.num"}})]}}],null,!1,928731534)}),e._v(" "),r("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{attrs:{type:"text",size:"small"},on:{click:function(r){return e.handleClick(t.row,0)}}},[e._v("删除")])]}}],null,!1,726385537)})],1)],1)],1),e._v(" "),r("el-form",{ref:"chargeGive1",staticClass:"card-form",attrs:{model:e.charge_give_rule,rules:e.chargeGiveRules}},[r("el-form-item",[r("el-row",{staticClass:"card-radio-group",staticStyle:{"margin-left":"100px"}},[r("el-radio",{attrs:{label:"1"}},[e._v("比例赠送")]),e._v(" "),r("span",[e._v("充值大于等于")]),e._v(" "),r("el-form-item",{attrs:{prop:"bili_min_price"}},[r("el-input",{ref:"bili_min_price",staticStyle:{width:"88px"},attrs:{onkeyup:"value=value.replace(/[^\\d\\.]/g,'')"},model:{value:e.charge_give_rule.bili.min_price,callback:function(t){e.$set(e.charge_give_rule.bili,"min_price",t)},expression:"charge_give_rule.bili.min_price"}})],1),e._v(" "),r("span",[e._v("元，小于")]),e._v(" "),r("el-form-item",{attrs:{prop:"bili_max_price"}},[r("el-input",{ref:"bili_max_price",staticStyle:{width:"88px"},attrs:{onkeyup:"value=value.replace(/[^\\d\\.]/g,'')"},model:{value:e.charge_give_rule.bili.max_price,callback:function(t){e.$set(e.charge_give_rule.bili,"max_price",t)},expression:"charge_give_rule.bili.max_price"}})],1),e._v(" "),r("span",[e._v("元时，赠送")]),e._v(" "),r("el-form-item",{attrs:{prop:"bili_give_money"}},[r("el-input",{ref:"bili_give_money",staticStyle:{width:"88px"},attrs:{onkeyup:"value=value.replace(/[^\\d\\.]/g,'')"},model:{value:e.charge_give_rule.bili.give,callback:function(t){e.$set(e.charge_give_rule.bili,"give",t)},expression:"charge_give_rule.bili.give"}})],1),e._v(" "),r("span",[e._v("%。")]),e._v(" "),1==e.charge_give_rule.type?r("span",{staticClass:"add-coupons active"},[e._v("需要叠加送券？"),r("span",{staticClass:"add",on:{click:function(t){e.showGudingCoupons=!0}}},[e._v("添加券")])]):r("span",{staticClass:"add-coupons"},[e._v("需要叠加送券？"),r("span",{staticClass:"add"},[e._v("添加券")])])],1),e._v(" "),r("el-row",{directives:[{name:"show",rawName:"v-show",value:1==e.charge_give_rule.type&&e.showGudingCoupons,expression:"charge_give_rule.type == 1 && showGudingCoupons"}],staticClass:"card-radio-group"},[r("el-form-item",{staticStyle:{"margin-left":"208px","margin-top":"20px"},attrs:{prop:"addCouponsData"}},[r("el-cascader",{ref:"myAddCascader",attrs:{filterable:"",options:e.couponsOptions,props:e.couponsProps,"collapse-tags":""},on:{change:e.addTableData},model:{value:e.charge_give_rule.addCouponsData,callback:function(t){e.$set(e.charge_give_rule,"addCouponsData",t)},expression:"charge_give_rule.addCouponsData"}})],1)],1)],1),e._v(" "),r("el-form-item",{directives:[{name:"show",rawName:"v-show",value:1==e.charge_give_rule.type&&e.addCouponsTableData.length,expression:"charge_give_rule.type == 1 && addCouponsTableData.length"}],staticClass:"item item04"},[r("el-table",{staticStyle:{width:"100%"},attrs:{data:e.addCouponsTableData}},[r("el-table-column",{attrs:{prop:"coupon_id",align:"center",label:"券ID"}}),e._v(" "),r("el-table-column",{attrs:{prop:"coupon_name",align:"center",label:"券名"}}),e._v(" "),r("el-table-column",{attrs:{align:"center",label:"券额"},scopedSlots:e._u([{key:"default",fn:function(t){return[5!=t.row.price_type?r("span",[e._v(e._s(t.row.price_rule.price?t.row.price_rule.price:t.row.price_rule.discount?t.row.price_rule.discount+"折":t.row.price_rule.min_price+"-"+t.row.price_rule.max_price))]):r("span",[e._v(e._s(t.row.price_rule.price)+"元/升")])]}}],null,!1,1721922336)}),e._v(" "),r("el-table-column",{attrs:{label:"数量",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-input-number",{attrs:{size:"small",step:1,"step-strictly":"",min:1,max:100},on:{change:function(r){return e.changeNumber(t.row)}},model:{value:t.row.num,callback:function(r){e.$set(t.row,"num",r)},expression:"scope.row.num"}})]}}],null,!1,175063997)}),e._v(" "),r("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{attrs:{type:"text",size:"small"},on:{click:function(r){return e.handleClick(t.row,1)}}},[e._v("删除")])]}}],null,!1,2982268320)})],1)],1)],1),e._v(" "),r("el-form",{ref:"chargeGive2",staticClass:"card-form",attrs:{model:e.charge_give_rule,rules:e.chargeGiveRules}},[r("el-form-item",{staticClass:"item item03"},[r("el-radio",{attrs:{label:"2"}},[e._v("充值送券")]),e._v(" "),r("span",[e._v("充值大于等于")]),e._v(" "),r("el-form-item",[r("el-input",{ref:"coupons_min_price",staticStyle:{width:"88px"},model:{value:e.charge_give_rule.coupons.min_price,callback:function(t){e.$set(e.charge_give_rule.coupons,"min_price",t)},expression:"charge_give_rule.coupons.min_price"}})],1),e._v(" "),r("span",[e._v("元，小于")]),e._v(" "),r("el-form-item",[r("el-input",{ref:"coupons_max_price",staticStyle:{width:"88px"},model:{value:e.charge_give_rule.coupons.max_price,callback:function(t){e.$set(e.charge_give_rule.coupons,"max_price",t)},expression:"charge_give_rule.coupons.max_price"}})],1),e._v(" "),r("span",[e._v("元时，")]),e._v(" "),r("el-form-item",{attrs:{prop:"couponsData"}},[r("el-cascader",{ref:"myCascader",attrs:{filterable:"",options:e.couponsOptions,props:e.couponsProps,"collapse-tags":""},on:{change:e.addTableData},model:{value:e.charge_give_rule.couponsData,callback:function(t){e.$set(e.charge_give_rule,"couponsData",t)},expression:"charge_give_rule.couponsData"}})],1)],1),e._v(" "),r("el-form-item",{directives:[{name:"show",rawName:"v-show",value:2==e.charge_give_rule.type&&e.couponsTableData.length,expression:"charge_give_rule.type == 2 && couponsTableData.length"}],staticClass:"item item04"},[r("el-table",{staticStyle:{width:"100%"},attrs:{data:e.couponsTableData}},[r("el-table-column",{attrs:{prop:"coupon_id",align:"center",label:"券ID"}}),e._v(" "),r("el-table-column",{attrs:{prop:"coupon_name",align:"center",label:"券名"}}),e._v(" "),r("el-table-column",{attrs:{align:"center",label:"券额"},scopedSlots:e._u([{key:"default",fn:function(t){return[5!=t.row.price_type?r("span",[e._v(e._s(t.row.price_rule.price?t.row.price_rule.price:t.row.price_rule.discount?t.row.price_rule.discount+"折":t.row.price_rule.min_price+"-"+t.row.price_rule.max_price))]):r("span",[e._v(e._s(t.row.price_rule.price)+"元/升")])]}}],null,!1,1721922336)}),e._v(" "),r("el-table-column",{attrs:{label:"数量",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-input-number",{attrs:{size:"small",step:1,"step-strictly":"",min:1,max:100},on:{change:function(r){return e.changeNumber(t.row)}},model:{value:t.row.num,callback:function(r){e.$set(t.row,"num",r)},expression:"scope.row.num"}})]}}],null,!1,175063997)}),e._v(" "),r("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{attrs:{type:"text",size:"small"},on:{click:function(r){return e.handleClick(t.row,2)}}},[e._v("删除")])]}}],null,!1,2884121155)})],1)],1)],1)],1),e._v(" "),e.isLockPrice?r("el-form",{ref:"chargeGive",staticClass:"card-form",attrs:{model:e.charge_give_rule,rules:e.chargeGiveRules}},[r("el-form-item",{staticClass:"item03",staticStyle:{"margin-left":"0"},attrs:{label:"充值赠送:"}},[2==e.extend_rule.market_type?r("span",[e._v("销售单价")]):e._e(),e._v(" "),3==e.extend_rule.market_type?r("span",[e._v("销售油量")]):e._e(),e._v(" "),r("el-form-item",{staticClass:"item05"},[2==e.extend_rule.market_type?r("el-input",{staticStyle:{width:"88px"},on:{input:e.calculationRules},model:{value:e.UnitPrice,callback:function(t){e.UnitPrice=t},expression:"UnitPrice"}}):e._e(),e._v(" "),3==e.extend_rule.market_type?r("el-input",{staticStyle:{width:"88px"},on:{input:e.calculationRules},model:{value:e.sellPrice,callback:function(t){e.sellPrice=t},expression:"sellPrice"}}):e._e()],1),e._v(" "),2==e.extend_rule.market_type?r("span",[e._v("元/升，")]):e._e(),e._v(" "),3==e.extend_rule.market_type?r("span",[e._v("升，")]):e._e(),e._v(" "),r("span",[e._v("销售价格")]),e._v(" "),r("el-form-item",{staticClass:"item05"},[r("el-input",{staticStyle:{width:"88px"},on:{input:e.calculationRules},model:{value:e.SumPrice,callback:function(t){e.SumPrice=t},expression:"SumPrice"}})],1),e._v(" "),r("span",[e._v("元，")]),e._v(" "),2==e.extend_rule.market_type?r("span",[e._v("赠送金额")]):e._e(),e._v(" "),3==e.extend_rule.market_type?r("span",[e._v("赠送油量")]):e._e(),e._v(" "),r("el-form-item",{staticClass:"item05"},[r("el-input",{staticStyle:{width:"88px"},on:{input:e.calculationRules},model:{value:e.giveNumber,callback:function(t){e.giveNumber=t},expression:"giveNumber"}})],1),e._v(" "),2==e.extend_rule.market_type?r("span",[e._v("元，")]):e._e(),e._v(" "),3==e.extend_rule.market_type?r("span",[e._v("升")]):e._e(),e._v(" "),2==e.extend_rule.market_type?r("span",[e._v("折后单价")]):e._e(),e._v(" "),2==e.extend_rule.market_type?r("el-form-item",{staticClass:"item05"},[r("el-input",{staticStyle:{width:"88px",color:"#32af50"},attrs:{disabled:!0},model:{value:e.salePrice,callback:function(t){e.salePrice=t},expression:"salePrice"}})],1):e._e(),e._v(" "),2==e.extend_rule.market_type?r("span",[e._v("元/升")]):e._e()],1),e._v(" "),3==e.extend_rule.market_type?r("p",{staticClass:"ruleItem"},[e._v("\n                    油量 "),r("span",{staticClass:"dataBase"},[e._v(e._s((+e.sellPrice+ +e.giveNumber).toFixed(2)))]),e._v(" 升，\n                    本金 "),r("span",{staticClass:"dataBase"},[e._v(e._s(e.SumPrice))]),e._v(" 元，\n                    赠金 "),r("span",{staticClass:"dataBase"},[e._v(e._s(e.givePrice))]),e._v(" 元，\n                    销售单价："),r("span",{staticClass:"dataBase"},[e._v(e._s(0==e.sellPrice||0==e.SumPrice?0:(e.SumPrice/e.sellPrice).toFixed(2)))]),e._v(" 元/升，\n                    折后单价 "),r("span",{staticClass:"dataBase"},[e._v(e._s(e.salePrice))]),e._v(" 元/升\n                    ")]):e._e()],1):e._e(),e._v(" "),r("el-form",{ref:"recharge",staticClass:"card-form",attrs:{model:e.recharge,rules:e.rechargeRules}},[r("el-form-item",{directives:[{name:"show",rawName:"v-show",value:!e.isLockPrice,expression:"!isLockPrice"}],staticClass:"item03",staticStyle:{"margin-left":"0"},attrs:{label:"赠送次数限制:"}},[r("span",[e._v("账户/卡")]),e._v(" "),r("el-form-item",{staticClass:"item05",attrs:{prop:"num"}},[r("el-input",{staticStyle:{width:"88px"},model:{value:e.recharge.num,callback:function(t){e.$set(e.recharge,"num",t)},expression:"recharge.num"}})],1),e._v(" "),r("span",[e._v("个")]),e._v(" "),r("el-form-item",{staticClass:"item05",attrs:{prop:"type"}},[r("el-select",{staticStyle:{width:"88px"},model:{value:e.recharge.type,callback:function(t){e.$set(e.recharge,"type",t)},expression:"recharge.type"}},e._l(e.couponDeliveryOptions,function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),r("span",[e._v("内最多可享受")]),e._v(" "),r("el-form-item",{staticClass:"item05",attrs:{prop:"count"}},[r("el-input",{staticStyle:{width:"88px"},model:{value:e.recharge.count,callback:function(t){e.$set(e.recharge,"count",t)},expression:"recharge.count"}})],1),e._v(" "),r("span",[e._v("次充值赠送，")]),e._v(" "),r("span",[e._v("本赠送活动最多可享受")]),e._v(" "),r("el-form-item",{staticClass:"item05",attrs:{prop:"total"}},[r("el-input",{staticStyle:{width:"88px"},model:{value:e.recharge.total,callback:function(t){e.$set(e.recharge,"total",t)},expression:"recharge.total"}})],1),e._v(" "),r("span",[e._v("次。")]),e._v(" "),r("span",[e._v("填0为不限制")]),e._v(" "),r("el-tooltip",{attrs:{placement:"right",effect:"light","popper-class":"card-rule-tooltip"}},[r("div",{attrs:{slot:"content"},slot:"content"},[r("p",[e._v("例：账户/卡1个自然月内最多可享受3次充值赠送，本赠送活动最多可享受6次。")]),e._v(" "),r("p",[e._v("\n                            账户/卡在2月29日充值第一次享受本规则，则2.29-3.30的前3次充值都可享受本充值规则的赠送。\n                            赠送周期为2.29-3.30，3.31-4.29以此类推到规则生效时间的最后一天。赠送次数累计6次后，不再赠送。\n                          ")])]),e._v(" "),r("i",{staticClass:"el-icon-question",staticStyle:{"font-size":"18px"}})])],1),e._v(" "),r("el-form-item",{staticClass:"last-form-item"},[r("el-button",{on:{click:e.givenBefore}},[e._v("上一步")]),e._v(" "),e.isCreate?r("el-button",{attrs:{disabled:e.btnDisabled,type:"primary"},on:{click:e.create}},[e._v("创建")]):r("el-button",{attrs:{disabled:e.btnDisabled,type:"primary"},on:{click:e.save}},[e._v("确认")])],1)],1)],1)],1)])},staticRenderFns:[]};var m=r("VU/8")(h,g,!1,function(e){r("7aMu"),r("ePS9")},"data-v-87d15802",null);t.default=m.exports}});