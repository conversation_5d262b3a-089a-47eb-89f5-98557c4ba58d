<template>
  <div class="p-4">
    <!-- 下载中心提示 -->
    <download-tips :showDownloadTips.sync="downloadConfig.success"></download-tips>
    <!-- 一级标题 -->
    <div v-loading="loadingData">
      <!-- 搜索表单 -->
      <el-form inline :model="query" class="w-full mb-4" label-width="70px"
               v-loading="downloadConfig.loading || printConfig.loading ">
        <el-form-item label="开户油站" v-if="String(getCurrentStation.merchant_type) === '2'">
          <el-select v-model="query.stid" :multiple="true"
                     class="w-250px mr-20px mb-5px"
                     filterable
                     clearable collapse-tags placeholder="请选择油站">
            <el-option
              v-for="(item,index) in stationOptions"
              :key="index"
              :label="item.stname"
              :value="item.stid">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="车队名称">
          <el-select
            :collapse-tags="true"
            v-model="query.company_id"
            multiple
            clearable
            filterable
            placeholder="请选择车队"
          >
            <el-option
              v-for="company in companyList"
              :key="company.ID"
              :label="company.CompanyName"
              :value="company.ID"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            :clearable="false"
            v-model="query.dateRange"
            type="daterange"
            :picker-options="pickerOptions"
            format="yyyy-MM-dd"
            :default-time="['00:00:00','23:59:59']"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="fetchData">查询</el-button>
          <el-button type="primary" :disabled="!(tableData && tableData.length > 0)" @click="showPrint">打印</el-button>
          <el-button type="primary" :disabled="!(tableData && tableData.length > 0)" @click="download">下载数据
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <div id="dataTable" text="#303133" class="p-0 m-0">
        <h1 class="text-2xl font-bold mb-4 text-center">定升车队进销存汇总表</h1>

        <ul class="list-none p-0 flex space-x-40px items-center text-14px">
          <li><span v-if="String(getCurrentStation.merchant_type) === '2'">集团名称</span><span
            v-else>油站名称</span>：{{ getCurrentStation.label }}
          </li>
          <li>开始时间：{{ $moment(query.dateRange[0]).format('YYYY-MM-DD') }}</li>
          <li>结束时间：{{ $moment(query.dateRange[1]).format('YYYY-MM-DD') }}</li>
        </ul>
        <el-table border :data="tableData" :style="`min-width: ${tableMinWidth}px;`" class="w-full !border-#999999 !after:bg-#999999 !before:bg-#999999" header-cell-class-name="text-#303133 !border-#999999" cell-class-name="text-#303133 !border-#999999" >
          <el-table-column prop="company_name" label="车队名称"/>
          <el-table-column prop="qichu_litre" label="期初升数"/>
          <el-table-column prop="qichu_amt" label="期初金额"/>
          <el-table-column prop="recharge_litre" label="充值升数"/>
          <el-table-column prop="recharge_amt" label="充值金额"/>
          <el-table-column prop="recharge_refund_litre" label="充值退款升数"/>
          <el-table-column prop="recharge_refund_amt" label="充值退款金额"/>
          <el-table-column prop="consume_litre" label="消费升数"/>
          <el-table-column prop="consume_amt" label="消费金额"/>
          <el-table-column prop="consume_refund_litre" label="消费退款升数"/>
          <el-table-column prop="consume_refund_amt" label="消费退款金额"/>
          <el-table-column prop="clear_litre" label="清零升数"/>
          <el-table-column prop="clear_amt" label="清零金额"/>
          <el-table-column prop="qimo_litre" label="期末升数"/>
          <el-table-column prop="qimo_amt" label="期末金额"/>
        </el-table>
        <ul class="list-none p-0 flex justify-between items-center text-14px">
          <li>制表人：{{ printConfig.maker }}</li>
          <li>制表时间：{{ printConfig.queryTime }}</li>
          <li class="mr-100px">签字：</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>

import {
  cardCompanyInvoicingLitreDownload,
  fetchCardCompanyInvoicingLitre,
  fetchSimpleCompanyList
} from "../../api/InventorySummary";
import {mapGetters} from "vuex";
import {isEmpty} from "lodash";
import moment from "moment";
import DownloadTips from "../../components/DownloadTips.vue";
import printJS from "print-js";

export default {
  name:'InventorySummary',
  components: {DownloadTips},
  data() {
    return {
      tableMinWidth:1205,
      printConfig: {
        queryTime: '',
        maker: '',
      },
      query: {
        page: '1',
        page_size: '10',
        type: '1', // 默认类型
        company_id: [],
        stid: [],
        dateRange: [moment().startOf('day').subtract(1, 'day').toDate(), moment().endOf('day').subtract(1, 'day').toDate()]
      },
      stationOptions: [],
      stationLoading: false,
      companyList: [],
      tableData: [],
      loadingCompanies: false,
      loadingData: false,
      pickerOptions: {
        disabledDate(time) {
          const tomorrow = moment().startOf('day').add(1, 'day');
          return time.getTime() >= tomorrow.startOf('day').valueOf(); // 禁止选择明天00:00:00以后的日期
        }
      },
      downloadConfig: {
        loading: false,
        success: false
      }
    };
  },
  computed: {
    ...mapGetters({
      "getCurrentStation": "getCurrentStation"
    })
  },
  watch: {
    getCurrentStation: {
      async handler(val={},old={}){
        console.log("=>(InventorySummary.vue:154) old", val,old);
        if(val.merchant_id === old.merchant_id && 'merchant_id' in old){
          return;
        }
        if (this.getCurrentStation && String(this.getCurrentStation.merchant_type) === '1') {
          this.query.stid = [this.getCurrentStation.merchant_id];
        } else {
          await this.getStationList();
        }
        await this.fetchCompanies()
        this.fetchData();
      },
      immediate:true,
      deep: true
    }
  },
  methods: {
    showPrint() {
      let html = `<div>
        <h1 style="margin:0; padding:0; font-size: 1.5rem; font-weight: bold; text-align: center;margin-top:-1mm;">定升车队进销存汇总表</h1>
        <ul style="list-style-type: none; display: flex; gap: 40px; align-items: center; font-size: 12px; padding:1mm 0;">
          <li><span>${String(this.getCurrentStation.merchant_type) === '2'?'集团名称':'油站名称'}</span>：${ this.getCurrentStation.label }</li>
          <li>开始时间：${ this.$moment(this.query.dateRange[0]).format('YYYY-MM-DD') }</li>
          <li>结束时间：${ this.$moment(this.query.dateRange[1]).format('YYYY-MM-DD') }</li>
        </ul>
        <table style="width:100%; border-collapse: collapse;">
          <thead>
            <tr>
              <th style="border: 1px solid #999; padding: 5px;">车队名称</th>
              <th style="border: 1px solid #999; padding: 5px;">期初升数</th>
              <th style="border: 1px solid #999; padding: 5px;">期初金额</th>
              <th style="border: 1px solid #999; padding: 5px;">充值升数</th>
              <th style="border: 1px solid #999; padding: 5px;">充值金额</th>
              <th style="border: 1px solid #999; padding: 5px;">充值退款升数</th>
              <th style="border: 1px solid #999; padding: 5px;">充值退款金额</th>
              <th style="border: 1px solid #999; padding: 5px;">消费升数</th>
              <th style="border: 1px solid #999; padding: 5px;">消费金额</th>
              <th style="border: 1px solid #999; padding: 5px;">消费退款升数</th>
              <th style="border: 1px solid #999; padding: 5px;">消费退款金额</th>
              <th style="border: 1px solid #999; padding: 5px;">清零升数</th>
              <th style="border: 1px solid #999; padding: 5px;">清零金额</th>
              <th style="border: 1px solid #999; padding: 5px;">期末升数</th>
              <th style="border: 1px solid #999; padding: 5px;">期末金额</th>
            </tr>
          </thead>
          <tbody>
            ${this.tableData.map(item => `
              <tr>
                <td style="border: 1px solid #999; padding: 5px;">${item.company_name}</td>
                <td style="border: 1px solid #999; padding: 5px;">${item.qichu_litre}</td>
                <td style="border: 1px solid #999; padding: 5px;">${item.qichu_amt}</td>
                <td style="border: 1px solid #999; padding: 5px;">${item.recharge_litre}</td>
                <td style="border: 1px solid #999; padding: 5px;">${item.recharge_amt}</td>
                <td style="border: 1px solid #999; padding: 5px;">${item.recharge_refund_litre}</td>
                <td style="border: 1px solid #999; padding: 5px;">${item.recharge_refund_amt}</td>
                <td style="border: 1px solid #999; padding: 5px;">${item.consume_litre}</td>
                <td style="border: 1px solid #999; padding: 5px;">${item.consume_amt}</td>
                <td style="border: 1px solid #999; padding: 5px;">${item.consume_refund_litre}</td>
                <td style="border: 1px solid #999; padding: 5px;">${item.consume_refund_amt}</td>
                <td style="border: 1px solid #999; padding: 5px;">${item.clear_litre}</td>
                <td style="border: 1px solid #999; padding: 5px;">${item.clear_amt}</td>
                <td style="border: 1px solid #999; padding: 5px;">${item.qimo_litre}</td>
                <td style="border: 1px solid #999; padding: 5px;">${item.qimo_amt}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
        <ul class="footer" style="list-style-type: none; padding: 0; display: flex; justify-content: space-between; align-items: center; font-size: 10pt;">
          <li>制表人：${ this.printConfig.maker }</li>
          <li>制表时间：${ this.printConfig.queryTime }</li>
          <li style="margin-right:100px">签字：</li>
        </ul>
      </div>`;

      printJS({
        printable: html,
        type: 'raw-html',
        style: `
          @media print { 
            @page {
              size: auto; 
              margin:20pt; 
              margin-bottom:5pt; 
              padding:2pt;
            } 
            body {
              margin:2pt; 
              margin-bottom:14pt; 
              padding:2pt;
            } 
            ul, li {
              padding:0;
              margin:0;
            } 
            table {
              width: 100%;
              border-collapse: collapse;
              font-size: 9pt;
            }
            th, td {
              border: 1px solid #999;
              padding: 5px;
              text-align: center;
            }
            th {
              font-weight: bold;
            }
            .footer {
              margin-top: 10pt;
            }
          }
        `
      });
    },
    download() {
      const params = this.getQueryParams();
      if (!params) {
        return
      }
      this.downloadConfig.loading = true;
      cardCompanyInvoicingLitreDownload(params).then(res => {
        if (res.status !== 200) {
          this.$message.error(res.info || '下载失败')
          return;
        }
        this.downloadConfig.success = true;
      }).finally(() => {
        this.downloadConfig.loading = false;
      })
    },
    getQueryParams() {
      const [startDate, endDate] = this.query.dateRange;
      const dateDuration = Math.abs(new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24);
      if (dateDuration > 180) {
        this.$message.error("选择日期超过180天，请重新选择");
        return null;
      }
      const start_time = startDate ? Math.floor(startDate.getTime() / 1000) : 0;
      const end_time = endDate ? Math.floor(endDate.getTime() / 1000) : 0;
      return {
        start_time, end_time, type: 1, company_id: this.query.company_id || [],
        stid: this.query.stid || []
      }
    },
    //获取可用油站
    async getStationList() {
      this.stationLoading = true;
      this.query.stid = [];
      await this.$axios.post('/Stations/getStationList', {}).then((res) => {
        if (res.status !== 200) {
          this.$message.error(res.info || res.msg || '获取油站信息失败');
          return;
        }
        this.stationOptions = res.data.data;
        this.query.stid = res.data.data.map(item => item.stid)
      }).finally(() => this.stationLoading = false)
    },
    async fetchCompanies() {
      this.loadingCompanies = true;
      try {
        const response = await fetchSimpleCompanyList({
          page: 1,
          page_size: 1250,
          type: 3
        });
        console.log("=>(InventorySummary.vue:92) response", response);
        if (response.status !== 200) {
          this.$message.error(response.info || response.msg || '获取车队信息失败');
        }
        if (isEmpty(response.data)) {
          return;
        }
        this.companyList = response.data.dt;
      } finally {
        this.loadingCompanies = false;
      }
    },
    async fetchData() {
      const params = this.getQueryParams();
      if (!params) {
        return
      }
      try {
        this.loadingData = true;
        const response = await fetchCardCompanyInvoicingLitre(params);
        if (response.status === 200) {
          this.tableData = []
          // 处理数据，添加汇总行
          if (response.data.card_invoicing && response.data.card_invoicing.length > 0) {
            const total = {...response.data.total, company_name: '汇总'};
            this.tableData = [...response.data.card_invoicing, total];

            let userInfo = localStorage.getItem('__userInfo__');
            if (userInfo && (userInfo !== "" || userInfo !== "undefined")) {
              this.printConfig.maker = JSON.parse(userInfo).name;
              this.printConfig.queryTime = this.$moment().format('YYYY-MM-DD HH:mm:ss');
            }
          }
        }else{
          this.$message.error(response.info || response.msg || '获取数据失败');
        }
      } finally {
        this.loadingData = false;
      }
    }
  }
};
</script>

<style scoped>
/* 这里可以添加一些额外的样式 */
</style>
