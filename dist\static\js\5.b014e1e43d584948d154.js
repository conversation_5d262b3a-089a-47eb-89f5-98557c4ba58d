webpackJsonp([5,20],{"47P+":function(e,t){},Rpzo:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a("Dd8w"),n=a.n(r),o=a("Xxa5"),l=a.n(o),i=a("exGp"),s=a.n(i),u=a("FZmr"),d=a("qiX8"),c=a("NYxO"),p={name:"Report",components:{DownloadTips:u.a,oldReport:d.default},data:function(){return{isTotalReportForm:!0,typeOptions:[{value:1,label:"按自然日期"},{value:4,label:"按开班日期"}],typeValue:1,dateValue:"",DailyTableData1:[],loading:!1,start_time:"",end_time:"",orderMaker:"",orderMakingTime:"",payWayList:[],refPayWayList:[],arr:[],isGroup:!0,showDownloadTips:!1,isGroupSettle:Boolean,flag:0,options:[],values:[],btn_disable:!1,pickerOptions:{disabledDate:function(e){return e.getTime()>Date.now()-864e5}},show_new_report:1,loadReport:!1}},created:function(){this.seeFee()},mounted:function(){var e=this;return s()(l.a.mark(function t(){var a,r,n;return l.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(a=localStorage.getItem("__userInfo__")||"",e.isTotalReportForm=426!=JSON.parse(a).group_id,r=e.$moment(),n=r.subtract(1,"days").format("YYYY-MM-DD"),e.$moment().format("YYYY-MM-DD"),e.dateValue=[n,n],void 0!=e.getCurrentStation.merchant_type&&0!=e.getCurrentStation.merchant_type){t.next=8;break}return t.abrupt("return",!1);case 8:return e.getCurrentStation&&2==e.getCurrentStation.merchant_type?e.isGroup=!0:e.isGroup=!1,t.next=11,e.getCompany();case 11:return t.next=13,e.getOldReport();case 13:return t.next=15,e.getDate();case 15:case"end":return t.stop()}},t,e)}))()},computed:n()({},Object(c.c)({getCurrentStation:"getCurrentStation"})),methods:{changeTypeValue:function(){this.dateValue="",this.orderMakingTime="",this.DailyTableData1=[]},getOldReport:function(){var e=this;return s()(l.a.mark(function t(){var a;return l.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e.loadReport=!0,t.next=3,e.$axios.post("/Ostn/getGroupBaseInfo");case 3:if(a=t.sent,console.log("res111",a.data),200==a.data.status){t.next=7;break}return t.abrupt("return",e.$message.error(e.data.info));case 7:e.show_new_report=a.data.data.show_new_report,window.sessionStorage.setItem("report",e.show_new_report),e.loadReport=!1;case 10:case"end":return t.stop()}},t,e)}))()},getCompany:function(){var e=this;return s()(l.a.mark(function t(){var a;return l.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.$axios.post("/Stations/getStations",{});case 2:a=t.sent,console.log("res",a.data.data),e.options=a.data.data.station_info,e.values=[],a.data.data.station_info.forEach(function(t,a){e.values[a]=t.stid}),console.log("options",e.options),console.log("values",e.values);case 9:case"end":return t.stop()}},t,e)}))()},getDate:function(){var e=this;return s()(l.a.mark(function t(){var a,r,n,o;return l.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!(a=e).dateValue){t.next=22;break}return a.loading=!0,r="",n="",1==e.typeValue?(r=a.$moment(a.dateValue[0]+" 00:00:00","YYYY-MM-DD HH:mm:ss").unix(),n=a.$moment(a.dateValue[1]+" 23:59:59","YYYY-MM-DD HH:mm:ss").unix()):(r=a.$moment(a.dateValue+" 00:00:00","YYYY-MM-DD HH:mm:ss").unix(),n=a.$moment(a.dateValue+" 23:59:59","YYYY-MM-DD HH:mm:ss").unix()),t.next=8,a.$axios.post("/CardReport/getNewCardChargeReport",{type:a.typeValue,start_time:r,end_time:n,stids:a.values});case 8:if(200==(o=t.sent).data.status){t.next=15;break}return e.payWayList=[{title:"-"}],e.refPayWayList=[{title:"-"}],e.DailyTableData1=[],e.loading=!1,t.abrupt("return",e.$message.error(o.data.info));case 15:if(200!=o.data.status){t.next=20;break}return a.payWayList=o.data.data.pay_way,a.refPayWayList=o.data.data.refund_pay_way,t.next=20,e.changeDate();case 20:console.log("payWayList",a.payWayList),console.log("refPayWayList",a.refPayWayList);case 22:case"end":return t.stop()}},t,e)}))()},changeDate:function(){var e=this;if(e.dateValue){e.loading=!0;var t="",a="";1==this.typeValue?(t=e.$moment(e.dateValue[0]+" 00:00:00","YYYY-MM-DD HH:mm:ss").unix(),a=e.$moment(e.dateValue[1]+" 23:59:59","YYYY-MM-DD HH:mm:ss").unix()):(t=e.$moment(e.dateValue+" 00:00:00","YYYY-MM-DD HH:mm:ss").unix(),a=e.$moment(e.dateValue+" 23:59:59","YYYY-MM-DD HH:mm:ss").unix()),e.$axios.post("/CardReport/getNewCardChargeReport",{type:e.typeValue,start_time:t,end_time:a,stids:e.values}).then(function(t){e.DailyTableData1=[],e.loading=!1,e.loadReport=!1,200==t.data.status?(e.$nextTick(function(){e.isGroupSettle=1==t.data.data.group_settle,e.DailyTableData1=t.data.data.charge,console.log("DailyTableData1",e.DailyTableData1),4==e.typeValue&&2==e.getCurrentStation.merchant_type?(e.setData1(e.DailyTableData1,"stname"),e.setData(e.DailyTableData1,"stname","banci"),e.setTable(e.DailyTableData1,"stname","banci")):4==e.typeValue&&(e.setData1(e.DailyTableData1,"banci"),e.setTable(e.DailyTableData1,"banci")),e.start_time=t.data.data.start_time,e.end_time=t.data.data.end_time,e.orderMakingTime=e.$moment().format("YYYY-MM-DD");var a=localStorage.getItem("__userInfo__");!a||""===a&&"undefined"===a||(e.orderMaker=JSON.parse(a).name),e.isGroupSettle&&2==e.getCurrentStation.merchant_type?(e.setData1(e.DailyTableData1,"organize"),e.setTable(e.DailyTableData1,"organize")):e.isGroupSettle&&3==e.getCurrentStation.merchant_type&&(e.setData1(e.DailyTableData1,"stname"),e.setTable(e.DailyTableData1,"stname"))}),e.$forceUpdate()):(e.loadReport=!1,e.$message({message:t.data.info,type:"error"}))}).catch(function(e){})}else e.DailyTableData1=[],e.orderMakingTime="",e.orderMaker=""},clearData:function(){this.dateValue||(this.payWayList=[{title:"-"}],this.refPayWayList=[{title:"-"}],this.DailyTableData1=[],this.orderMakingTime="",this.orderMaker="")},printContent:function(){var e=document.querySelector("#myTable").innerHTML,t=document.body.innerHTML;document.body.innerHTML=e,document.getElementsByClassName("el-table__header")[0].style.width="100%",document.getElementsByClassName("el-table__header")[0].style["table-layout"]="auto",document.getElementsByClassName("el-table__body")[0].style.width="100%",document.getElementsByClassName("el-table__body")[0].style["table-layout"]="auto",window.print(),history.go(0),document.body.innerHTML=t},cardChargeDownload:function(){var e=this;this.$axios.get("/CardReport/newCardChargeDownload",{params:{start_time:this.start_time,end_time:this.end_time,stids:this.values,type:this.typeValue}}).then(function(t){200==t.data.status&&(e.showDownloadTips=!0)})},detailDownload:function(e,t){var a=this;console.log(t.date);var r="",n="";"汇总"==t.date?(r=this.$moment(1e3*this.start_time).format("YYYY-MM-DD"),n=this.$moment(1e3*this.end_time).format("YYYY-MM-DD")):(r=t.date,n=t.date);var o=this.values.join(",");this.$axios.post("/CardReport/downloadChargeDetail",{ostn_id_list:o,start_date:r,end_date:n,request_type:1,class_id:0}).then(function(e){200==e.data.status&&(a.showDownloadTips=!0)})},setTable:function(e,t,a){var r=[],n=0,o=[],l=0;e.forEach(function(i,s){0===s?(r.push(1),o.push(1)):String(i[t])&&String(i[t])==String(e[s-1][t])?(r[n]+=1,r.push(0),String(i[a])&&String(i[a])==String(e[s-1][a])?(o[l]+=1,o.push(0)):(o.push(1),l=s)):(r.push(1),n=s,o.push(1),l=s)});var i={};i[t]=r,i[a]=o,this.arr=[],this.arr.push(i),console.log("arr",this.arr)},setData:function(e,t,a){for(var r,n=e.length,o=0;o<n-1;o++)for(var l=o+1;l<n;l++)e[l][t]==e[o][t]&&e[l][a]==e[o][a]&&(r=e[o+1],e[o+1]=e[l],e[l]=r);return e},setData1:function(e,t){for(var a,r=e.length,n=0;n<r-1;n++)for(var o=n+1;o<r;o++)e[o][t]==e[n][t]&&(a=e[n+1],e[n+1]=e[o],e[o]=a);return e},objectSpanMethod:function(e){e.row,e.column;var t=e.rowIndex,a=e.columnIndex;if(this.tableData1){if(this.isGroupSettle&&2==this.getCurrentStation.merchant_type&&0===a){var r=this.arr[0].organize[t];return{rowspan:r,colspan:r>0?1:0}}if(this.isGroupSettle&&3==this.getCurrentStation.merchant_type&&0===a){var n=this.arr[0].stname[t];return{rowspan:n,colspan:n>0?1:0}}if(4==this.typeValue&&2!=this.getCurrentStation.merchant_type&&0===a){var o=this.arr[0].banci[t];return{rowspan:o,colspan:o>0?1:0}}if(4==this.typeValue&&2==this.getCurrentStation.merchant_type){if(0===a){var l=this.arr[0].stname[t];return{rowspan:l,colspan:l>0?1:0}}if(1===a){var i=this.arr[0].banci[t];return{rowspan:i,colspan:i>0?1:0}}}}},seeFee:function(){var e=this;return s()(l.a.mark(function t(){return l.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.$axios.post("CardReport/chargeFeeSettleAmt");case 3:200==t.sent.data.status&&(e.flag=1),t.next=10;break;case 7:t.prev=7,t.t0=t.catch(0),console.log(t.t0);case 10:case"end":return t.stop()}},t,e,[[0,7]])}))()}},watch:{getCurrentStation:function(e,t){var a=this;return s()(l.a.mark(function r(){return l.a.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(0==e.merchant_type||e.value==t.value){r.next=9;break}return a.getCurrentStation&&2==a.getCurrentStation.merchant_type?a.isGroup=!0:a.isGroup=!1,r.next=4,a.getCompany();case 4:return r.next=6,a.getOldReport();case 6:if(1!=a.show_new_report){r.next=9;break}return r.next=9,a.getDate();case 9:case"end":return r.stop()}},r,a)}))()},typeValue:function(){console.log("typeValue",this.typeValue),1==this.typeValue?this.dateValue=[]:this.dateValue="",this.payWayList=[{title:"-"}],this.refPayWayList=[{title:"-"}]},values:function(){},dateValue:function(){}}},_={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loadReport,expression:"loadReport"}]},[0==e.show_new_report?a("div",[a("old-report",{attrs:{report:e.show_new_report}})],1):e._e(),e._v(" "),1==e.show_new_report?a("div",{directives:[{name:"loading",rawName:"v-loading"}],staticClass:"report"},[a("div",{staticClass:"report-content"},[e.isGroupSettle?e._e():a("div",[a("el-radio-group",{on:{change:e.changeTypeValue},model:{value:e.typeValue,callback:function(t){e.typeValue=t},expression:"typeValue"}},e._l(e.typeOptions,function(t){return a("el-radio-button",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])}),1)],1),e._v(" "),a("div",{staticClass:"content_header"},[a("div",{staticClass:"left"},[a("el-select",{staticStyle:{"margin-right":"20px",width:"250px"},attrs:{multiple:"","collapse-tags":"",placeholder:"请选择"},model:{value:e.values,callback:function(t){e.values=t},expression:"values"}},e._l(e.options,function(e){return a("el-option",{key:e.stid,attrs:{label:e.stname,value:e.stid}})}),1),e._v(" "),1==e.typeValue?a("el-date-picker",{key:"picker1",attrs:{type:"daterange",clearable:!1,"range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy 年 MM 月 dd 日","value-format":"yyyy-MM-dd","picker-options":e.pickerOptions},on:{change:e.clearData},model:{value:e.dateValue,callback:function(t){e.dateValue=t},expression:"dateValue"}}):e._e(),e._v(" "),4==e.typeValue?a("el-date-picker",{key:"picker2",attrs:{type:"date",format:"yyyy 年 MM 月 dd 日","value-format":"yyyy-MM-dd"},on:{change:e.clearData},model:{value:e.dateValue,callback:function(t){e.dateValue=t},expression:"dateValue"}}):e._e(),e._v(" "),a("el-button",{attrs:{type:"primary",disabled:0==e.values.length||!e.dateValue},on:{click:e.getDate}},[e._v("生成")])],1),e._v(" "),a("div",{staticClass:"right"},[a("el-button",{attrs:{type:"primary",disabled:0==e.DailyTableData1.length},on:{click:e.printContent}},[e._v("打印")]),e._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:e.isTotalReportForm,expression:"isTotalReportForm"}],attrs:{type:"primary",disabled:0==e.DailyTableData1.length},on:{click:e.cardChargeDownload}},[e._v("下载报表")])],1)]),e._v(" "),a("div",{attrs:{id:"myTable"}},[a("div",{staticClass:"tableData reportData"},[a("div",{staticClass:"report_title"},[e._v("储值卡充值资金日报表")]),e._v(" "),a("div",{staticClass:"report_header"},[e.isGroup?a("div",[e._v("集团名称："+e._s(e.getCurrentStation.label))]):a("div",[e._v("油站名称："+e._s(e.getCurrentStation.label))]),e._v(" "),1==e.typeValue?a("div",[e._v("开始时间："+e._s(e.dateValue[0]))]):e._e(),e._v(" "),1==e.typeValue?a("div",[e._v("结束时间："+e._s(e.dateValue[1]))]):e._e(),e._v(" "),4==e.typeValue?a("div",[e._v("日期："+e._s(e.dateValue))]):e._e(),e._v(" "),a("div",[e._v("单位：元")])]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.DailyTableData1,"span-method":e.objectSpanMethod,border:"",size:"small",align:"center"}},[2!=e.getCurrentStation.merchant_type&&3!=e.getCurrentStation.merchant_type||4!=e.typeValue?e._e():a("el-table-column",{key:Math.random(),attrs:{align:"center",prop:"stname",label:"油站"}}),e._v(" "),4==e.typeValue?a("el-table-column",{key:Math.random(),attrs:{align:"center",label:"班次",prop:"banci","min-width":"72px"}}):e._e(),e._v(" "),1==e.typeValue?a("el-table-column",{key:Math.random(),attrs:{align:"center",prop:"date",label:"日期",width:"100"}}):e._e(),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值"}},[a("el-table-column",{key:Math.random(),attrs:{align:"center",prop:"recharge_order_cnt",label:"充值笔数"}}),e._v(" "),a("el-table-column",{key:Math.random(),attrs:{align:"center",prop:"recharge_amt",label:"充值金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.recharge_amt).toFixed(2)))]}}],null,!1,2679970693)}),e._v(" "),a("el-table-column",{key:Math.random(),attrs:{align:"center",prop:"order_amt",label:"本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.order_amt).toFixed(2)))]}}],null,!1,1075651622)}),e._v(" "),a("el-table-column",{key:Math.random(),attrs:{align:"center",prop:"gift_amt",label:"赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.gift_amt).toFixed(2)))]}}],null,!1,3815531092)}),e._v(" "),a("el-table-column",{key:Math.random(),attrs:{align:"center",prop:"discount_amt",label:"优惠金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.discount_amt).toFixed(2)))]}}],null,!1,3650663349)}),e._v(" "),a("el-table-column",{key:Math.random(),attrs:{align:"center",prop:"pay_amt",label:"实付金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.pay_amt).toFixed(2)))]}}],null,!1,3967880416)}),e._v(" "),a("el-table-column",{key:Math.random(),attrs:{align:"center",label:"支付方式"}},[e.payWayList.length>0?e._l(e.payWayList,function(t){return a("el-table-column",{key:t.index,attrs:{align:"center",label:t.title,prop:t.prop},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v(e._s(Number(a.row[t.prop]).toFixed(2)))]}}],null,!0)})}):[e._v("\n                  0.00\n                ")]],2)],1),e._v(" "),a("el-table-column",{key:Math.random(),attrs:{align:"center",label:"退款"}},[a("el-table-column",{key:Math.random(),attrs:{align:"center",prop:"recharge_refund_order_cnt",width:"50",label:"退款笔数"}}),e._v(" "),a("el-table-column",{key:Math.random(),attrs:{align:"center",label:"退款本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.recharge_refund_order_amt).toFixed(2)))]}}],null,!1,1540454309)}),e._v(" "),a("el-table-column",{key:Math.random(),attrs:{align:"center",label:"退款赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.recharge_refund_gift_amt).toFixed(2)))]}}],null,!1,3246611799)}),e._v(" "),a("el-table-column",{key:Math.random(),attrs:{align:"center",label:"实退金额"}},[e.refPayWayList.length>0?e._l(e.refPayWayList,function(t){return a("el-table-column",{key:t.index,attrs:{align:"center",label:t.title,prop:t.prop},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v(e._s(Number(a.row[t.prop]).toFixed(2)))]}}],null,!0)})}):[e._v("\n                  0.00\n                ")]],2)],1),e._v(" "),a("el-table-column",{key:Math.random(),attrs:{align:"center",label:"到账金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.received_amt).toFixed(2)))]}}],null,!1,3692183239)}),e._v(" "),e.flag?[1==e.typeValue?a("el-table-column",{key:Math.random(),attrs:{align:"center",prop:"real_settle_amt",label:"应结金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.fact_settle_amt).toFixed(2)))]}}],null,!1,2184650168)}):e._e(),e._v(" "),1==e.typeValue?a("el-table-column",{key:Math.random(),attrs:{align:"center",prop:"real_fee",label:"手续费"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.real_fee).toFixed(2)))]}}],null,!1,2898972300)}):e._e(),e._v(" "),1==e.typeValue?a("el-table-column",{key:Math.random(),attrs:{align:"center",prop:"fact_settle_amt",label:"实结金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.real_settle_amt).toFixed(2)))]}}],null,!1,1744766962)}):e._e()]:e._e(),e._v(" "),1==e.typeValue?a("el-table-column",{key:Math.random(),attrs:{align:"center",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"detail",on:{click:function(a){return e.detailDownload(t.$index,t.row)}}},[e._v("\n                  导出明细\n                ")])]}}],null,!1,587827391)}):e._e()],2)],1),e._v(" "),e._m(0),e._v(" "),a("div",{staticClass:"des_bottom"},[a("div",[e._v("制表人："+e._s(e.orderMaker))]),e._v(" "),a("div",[e._v("制表时间："+e._s(e.orderMakingTime))]),e._v(" "),a("div",[e._v("签字：")])])])]),e._v(" "),a("download-tips",{attrs:{showDownloadTips:e.showDownloadTips},on:{"update:showDownloadTips":function(t){e.showDownloadTips=t},"update:show-download-tips":function(t){e.showDownloadTips=t}}})],1):e._e()])},staticRenderFns:[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"table_des"},[a("div",{staticClass:"table_des_text"},[a("p",[e._v("注：")]),e._v(" "),a("div",[a("p",[e._v("\n                1.实付金额：车主充值实际支付的金额，实付金额+优惠金额=本金。\n              ")]),e._v(" "),a("p",[e._v("\n                2.充值金额：实际计算到储值卡内的金额，本金+赠金=充值金额。\n              ")]),e._v(" "),a("p",[e._v("\n                3.到账金额：油站实际到账的金额，充值实付金额-退款实退金额=到账金额。\n              ")]),e._v(" "),a("p",[e._v("4.应结金额：车主使用微信和支付宝充值实付金额合计。")]),e._v(" "),a("p",[e._v("5.实结金额：结算后金额，应结金额-手续费=实结金额。")])])])])}]};var m=a("VU/8")(p,_,!1,function(e){a("vd3T"),a("Ynmy")},"data-v-6a2ea73c",null);t.default=m.exports},Ynmy:function(e,t){},h1vq:function(e,t){},qiX8:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a("Xxa5"),n=a.n(r),o=a("exGp"),l=a.n(o),i=a("Dd8w"),s=a.n(i),u=a("FZmr"),d=a("NYxO"),c={name:"Report",components:{DownloadTips:u.a},props:{report:""},data:function(){return{isTotalReportForm:!0,typeOptions:[{value:1,label:"按自然日期"},{value:4,label:"按开班日期"}],typeValue:1,dateValue:"",options:[],DailyTableData1:[],loading:!1,start_time:"",end_time:"",orderMaker:"",orderMakingTime:"",payWayList:[],arr:[],isGroup:!0,showDownloadTips:!1,isGroupSettle:Boolean}},mounted:function(){var e=localStorage.getItem("__userInfo__")||"";this.isTotalReportForm=426!=JSON.parse(e).group_id;var t=this.$moment().subtract(1,"days").format("YYYY-MM-DD");if(this.dateValue=t,void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.getCurrentStation&&2==this.getCurrentStation.merchant_type?this.isGroup=!0:this.isGroup=!1,this.changeDate()},computed:s()({},Object(d.c)({getCurrentStation:"getCurrentStation"})),methods:{changeTypeValue:function(){this.dateValue="",this.orderMakingTime="",this.DailyTableData1=[]},changeDate:function(){var e=this;e.dateValue?(e.loading=!0,e.$axios.post("/CardReport/getCardChargeReport",{type:e.typeValue,start_time:e.$moment(e.dateValue+" 00:00:00","YYYY-MM-DD HH:mm:ss").unix(),end_time:e.$moment(e.dateValue+" 23:59:59","YYYY-MM-DD HH:mm:ss").unix()}).then(function(t){e.DailyTableData1=[],e.loading=!1,200==t.data.status?e.$nextTick(function(){e.isGroupSettle=1==t.data.data.group_settle,e.payWayList=t.data.data.pay_way,e.DailyTableData1=t.data.data.charge,4==e.typeValue&&2==e.getCurrentStation.merchant_type?(e.setData1(e.DailyTableData1,"stname"),e.setData(e.DailyTableData1,"stname","banci"),e.setTable(e.DailyTableData1,"stname","banci")):4==e.typeValue&&(e.setData1(e.DailyTableData1,"banci"),e.setTable(e.DailyTableData1,"banci")),e.start_time=t.data.data.start_time,e.end_time=t.data.data.end_time,e.orderMakingTime=e.$moment().format("YYYY-MM-DD");var a=localStorage.getItem("__userInfo__");!a||""===a&&"undefined"===a||(e.orderMaker=JSON.parse(a).name),e.isGroupSettle&&2==e.getCurrentStation.merchant_type?(e.setData1(e.DailyTableData1,"organize"),e.setTable(e.DailyTableData1,"organize")):e.isGroupSettle&&3==e.getCurrentStation.merchant_type&&(e.setData1(e.DailyTableData1,"stname"),e.setTable(e.DailyTableData1,"stname"))}):e.$message({message:t.data.info,type:"error"})}).catch(function(e){})):(e.DailyTableData1=[],e.orderMakingTime="",e.orderMaker="")},clearData:function(){this.dateValue||(this.DailyTableData1=[],this.orderMakingTime="",this.orderMaker="")},printContent:function(){var e=document.querySelector("#myTable").innerHTML,t=document.body.innerHTML;document.body.innerHTML=e,document.getElementsByClassName("el-table__header")[0].style.width="100%",document.getElementsByClassName("el-table__header")[0].style["table-layout"]="auto",document.getElementsByClassName("el-table__body")[0].style.width="100%",document.getElementsByClassName("el-table__body")[0].style["table-layout"]="auto",window.print(),history.go(0),document.body.innerHTML=t},cardChargeDownload:function(){var e=this;this.$axios.get("/CardReport/cardChargeDownload",{params:{start_time:this.start_time,end_time:this.end_time,type:this.typeValue}}).then(function(t){200==t.data.status&&(e.showDownloadTips=!0)})},setTable:function(e,t,a){var r=[],n=0,o=[],l=0;e.forEach(function(i,s){0===s?(r.push(1),o.push(1)):String(i[t])&&String(i[t])==String(e[s-1][t])?(r[n]+=1,r.push(0),String(i[a])&&String(i[a])==String(e[s-1][a])?(o[l]+=1,o.push(0)):(o.push(1),l=s)):(r.push(1),n=s,o.push(1),l=s)});var i={};i[t]=r,i[a]=o,this.arr=[],this.arr.push(i)},setData:function(e,t,a){for(var r,n=e.length,o=0;o<n-1;o++)for(var l=o+1;l<n;l++)e[l][t]==e[o][t]&&e[l][a]==e[o][a]&&(r=e[o+1],e[o+1]=e[l],e[l]=r);return e},setData1:function(e,t){for(var a,r=e.length,n=0;n<r-1;n++)for(var o=n+1;o<r;o++)e[o][t]==e[n][t]&&(a=e[n+1],e[n+1]=e[o],e[o]=a);return e},objectSpanMethod:function(e){e.row,e.column;var t=e.rowIndex,a=e.columnIndex;if(this.isGroupSettle&&2==this.getCurrentStation.merchant_type&&0===a){var r=this.arr[0].organize[t];return{rowspan:r,colspan:r>0?1:0}}if(this.isGroupSettle&&3==this.getCurrentStation.merchant_type&&0===a){var n=this.arr[0].stname[t];return{rowspan:n,colspan:n>0?1:0}}if(4==this.typeValue&&2!=this.getCurrentStation.merchant_type&&0===a){var o=this.arr[0].banci[t];return{rowspan:o,colspan:o>0?1:0}}if(4==this.typeValue&&2==this.getCurrentStation.merchant_type){if(0===a){var l=this.arr[0].stname[t];return{rowspan:l,colspan:l>0?1:0}}if(1===a){var i=this.arr[0].banci[t];return{rowspan:i,colspan:i>0?1:0}}}}},watch:{getCurrentStation:function(e,t){var a=this;return l()(n.a.mark(function r(){var o;return n.a.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(0==e.merchant_type||e.value==t.value){r.next=9;break}return a.getCurrentStation&&2==a.getCurrentStation.merchant_type?a.isGroup=!0:a.isGroup=!1,r.next=4,a.$parent.getOldReport();case 4:if(o=window.sessionStorage.getItem("report"),console.log("report",o),0!=o){r.next=9;break}return r.next=9,a.changeDate();case 9:case"end":return r.stop()}},r,a)}))()}}},p={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading"}],staticClass:"report"},[a("div",{staticClass:"report-content"},[e.isGroupSettle?e._e():a("div",[a("el-radio-group",{on:{change:e.changeTypeValue},model:{value:e.typeValue,callback:function(t){e.typeValue=t},expression:"typeValue"}},e._l(e.typeOptions,function(t){return a("el-radio-button",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])}),1)],1),e._v(" "),a("div",{staticClass:"content_header"},[a("div",{staticClass:"left"},[a("el-date-picker",{attrs:{type:"date",placeholder:"选择日期",format:"yyyy 年 MM 月 dd 日","value-format":"yyyy-MM-dd"},on:{change:e.clearData},model:{value:e.dateValue,callback:function(t){e.dateValue=t},expression:"dateValue"}}),e._v(" "),a("el-button",{attrs:{type:"primary",disabled:!e.dateValue},on:{click:e.changeDate}},[e._v("生成")])],1),e._v(" "),a("div",{staticClass:"right"},[a("el-button",{attrs:{type:"primary",disabled:0==e.DailyTableData1.length},on:{click:e.printContent}},[e._v("打印")]),e._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:e.isTotalReportForm,expression:"isTotalReportForm"}],attrs:{type:"primary",disabled:0==e.DailyTableData1.length},on:{click:e.cardChargeDownload}},[e._v("下载数据")])],1)]),e._v(" "),a("div",{attrs:{id:"myTable"}},[a("div",{staticClass:"tableData reportData"},[a("div",{staticClass:"report_title"},[e._v("储值卡充值资金日报表")]),e._v(" "),a("div",{staticClass:"report_header"},[e.isGroup?a("div",[e._v("集团名称："+e._s(e.getCurrentStation.label))]):a("div",[e._v("油站名称："+e._s(e.getCurrentStation.label))]),e._v(" "),a("div",[e._v("日期："+e._s(e.dateValue))]),e._v(" "),a("div",[e._v("单位：元")])]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.DailyTableData1,"span-method":e.objectSpanMethod,border:"",size:"small",align:"center"}},[2==e.getCurrentStation.merchant_type||3==e.getCurrentStation.merchant_type?a("el-table-column",{attrs:{align:"center",prop:"stname",label:"油站"}}):e._e(),e._v(" "),4==e.typeValue?a("el-table-column",{attrs:{align:"center",label:"班次",prop:"banci","min-width":"72px"}}):e._e(),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"source_name",label:"终端来源"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值"}},[a("el-table-column",{attrs:{align:"center",prop:"recharge_order_cnt",label:"充值笔数"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"pay_amt",label:"实付金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.pay_amt).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"discount_amt",label:"优惠金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.discount_amt).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"支付方式"}},[e.payWayList.length>0?e._l(e.payWayList,function(t){return a("el-table-column",{key:t.index,attrs:{align:"center",label:t.title,prop:t.prop},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v(e._s(Number(a.row[t.prop]).toFixed(2)))]}}],null,!0)})}):[e._v("\n                无\n              ")]],2),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"recharge_amt",label:"充值金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.recharge_amt).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"order_amt",label:"本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.order_amt).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"gift_amt",label:"赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.gift_amt).toFixed(2)))]}}])})],1),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"退款"}},[a("el-table-column",{attrs:{align:"center",prop:"recharge_refund_order_cnt",label:"退款笔数"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"退款本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.recharge_refund_order_amt).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"退款赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.recharge_refund_gift_amt).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"recharge_refund_real_amt",label:"实退金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.recharge_refund_real_amt).toFixed(2)))]}}])})],1),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"到账金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.received_amt).toFixed(2)))]}}])})],1)],1),e._v(" "),e._m(0),e._v(" "),a("div",{staticClass:"des_bottom"},[a("div",[e._v("制表人："+e._s(e.orderMaker))]),e._v(" "),a("div",[e._v("制表时间："+e._s(e.orderMakingTime))]),e._v(" "),a("div",[e._v("签字：")])])])]),e._v(" "),a("download-tips",{attrs:{showDownloadTips:e.showDownloadTips},on:{"update:showDownloadTips":function(t){e.showDownloadTips=t},"update:show-download-tips":function(t){e.showDownloadTips=t}}})],1)},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"table_des"},[t("div",{staticClass:"table_des_text"},[t("p",[this._v("注:")]),this._v(" "),t("div",[t("p",[this._v("1.实付金额：车主充值实际支付的金额，实付金额+优惠金额=本金。")]),this._v(" "),t("p",[this._v("2.充值金额：实际计算到储值卡内的金额，本金+赠金=充值金额。")]),this._v(" "),t("p",[this._v("3.到账金额：油站实际到账的金额，充值实付金额-退款实退金额=到账金额。")])])])])}]};var _=a("VU/8")(c,p,!1,function(e){a("h1vq"),a("47P+")},"data-v-789ef093",null);t.default=_.exports},vd3T:function(e,t){}});