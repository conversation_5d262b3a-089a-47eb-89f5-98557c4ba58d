import { defineConfig,presetUno,presetAttributify   } from 'unocss';

export default defineConfig({
  presets: [
    presetUno({preflight:false}),
    presetAttributify()
  ],
  theme:{
    colors: {
      primary: '#5bbf73',
    }
  },
  cli: {
    entry: {
      /**
       * 😒为了避免不必要的class，请不要把使用通配符去映射所有文件的生成。
       * 😘把你需要增加的文件放入到 patterns 数组即可。
       */
      patterns: [
        './src/views/InventorySummary/InventorySummary.vue',
        './src/components/Report/fleetInvoiceReport.vue',
        './src/components/Report/CustomerSpendReport.vue',// 客户消费变动明细表
        './src/components/Report/fleetJXC.vue',// 车队进销存报表
        './src/views/CustomerType/CustomerType.vue',// 客户类型
        './src/components/CardTransfer.vue',// 车队管理 车队资金管理
        './src/components/Report/CustomerSpendReport.vue',// 客户消费变动明细表
        './src/components/PasswordExpireCheck.vue',// 密码过期检查组件
        './src/views/RechargeApproval/RechargeApproval.vue' // 车队充值审核
        ,'./src/components/Report/FleetCapitalChangeReport.vue' // 法体资金对账报表
      ],
      outFile: './src/assets/css/unify.css'
    }
  }
});
