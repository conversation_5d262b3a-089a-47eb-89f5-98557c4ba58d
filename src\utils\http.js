import axios from "axios";
import {Message} from "new-wcc-ui";

export const DataCode = {
  SUCCESS: 200
}


export async function http(url, data, method = 'get',headers = {}) {
  const config = {
    url,
    method: method.toUpperCase(),
  }
  if (config.method === 'GET' && data) {
    config.params = data
  } else {
    config.data = data || {}
  }
  config.headers = {
    ...config.headers,
    ...headers
  }
  const res = await axios(config);
  if (res.status === DataCode.SUCCESS) {
    return res.data;
  }
  Message({type: 'error', message: `网络请求异常 ${res.status}`})
  throw new Error(res)
}


export function get(url, data,headers) {
  return http(url, data,'get',headers)
}

export function post(url, data,headers) {
  return http(url, data,'post',headers)
}
