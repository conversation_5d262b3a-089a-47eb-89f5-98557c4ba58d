import axiosReport from 'axios'

/**
 * 批量下载客户消费报表
 * @function batchDownLoadCustomerOrder
 * @param {Object} data - 请求参数对象
 * @param {Array<number>} data.company_id - 必填，公司ID数组
 * @param {string} data.start_time - 必填，开始时间，格式:YYYY-MM-DD HH:mm:ss
 * @param {string} data.end_time - 必填，结束时间，格式:YYYY-MM-DD HH:mm:ss
 * @param {Object} data.company_names - 必填，公司ID与名称的映射对象
 * @returns {Promise<AxiosResponse>} 返回下载文件的axios响应Promise对象
 */
export function batchDownLoadCustomerOrder(data) {
    return axiosReport.create({
        timeout: 0  // 设置为 0 表示永不超时
    }).post('/CardReport/batchDownLoadCustomerOrder', data)
}
