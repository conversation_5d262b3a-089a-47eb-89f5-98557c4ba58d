<template>
  <div class="recharge-approval py-5">
    <div class="flex items-center mb-5">
      <!-- 油站名称搜索 -->
      <div class="flex items-center mr-5" v-if="getCurrentStation.merchant_type == 2">
        <span class="mr-2.5">油站名称</span>
        <el-select class="w-250px" v-model="stationId" placeholder="请选择" multiple clearable collapse-tags
          v-loading="stationLoading">
          <el-option v-for="(item, index) in stationOptions" :key="index" :label="item.stname" :value="item.stid" />
        </el-select>
      </div>

      <!-- 日期范围选择 -->
      <div class="flex items-center mr-5">
        <span class="mr-2.5">提交日期范围</span>
        <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" class="w-280px" clearable />
      </div>

      <!-- 车队名称搜索 -->
      <div class="flex items-center mr-5">
        <span class="mr-2.5">车队名称</span>
        <el-select v-model="companyId" clearable filterable remote reserve-keyword placeholder="请输入车队名称"
          :remote-method="debounceRemoteMethod" :loading="companyLoading" multiple collapse-tags class="w-[200px]">
          <el-option v-for="item in companyOptions" :key="item.CompanyID" :label="item.CompanyName"
            :value="item.CompanyID" />
        </el-select>
      </div>
      <el-button type="primary" @click="handleSearch" :disabled="loading">查询</el-button>
    </div>
    <div>
      <el-radio-group v-if="!hideStatusOptions" v-model="status" @change="handleStatusChange">
        <el-radio-button v-for="item in STATUS_OPTIONS" :key="item.value" :label="item.value">
          {{ item.optionLabel }}
        </el-radio-button>
      </el-radio-group>

      <div class="mt-4">
        <el-table v-if="status === STATUS.PENDING.value" :data="dataList" v-loading="loading">
          <el-table-column prop="CompanyName" label="车队名称" />
          <el-table-column prop="CompanyContacts" label="车队管理员" />
          <el-table-column prop="ContactsPhone" label="管理员手机号码" />
          <el-table-column prop="BJ_MZ" label="母账余额（元）" />
          <el-table-column label="母账升数（升）">
            <template #default="scope">
              {{ (scope.row.MZ_Liters === 0 || scope.row.MZ_Liters == null) ? '--' : Number(scope.row.MZ_Liters).toFixed(4) }}
            </template>
          </el-table-column>
          <el-table-column label="充值油品">
            <template #default="scope">
              {{ formatterCellval(scope.row.ChargeOilName) }}
            </template>
          </el-table-column>
          <el-table-column label="本次充值（元）">
            <template #default="scope">
              {{ formatterCellval(scope.row.ChargeAmount) }}
            </template>
          </el-table-column>
          <el-table-column label="本次充值（升）">
            <template #default="scope">
              {{ (scope.row.ChargeLiter === 0 || scope.row.ChargeLiter == null) ? '--' : Number(scope.row.ChargeLiter).toFixed(4) }}
            </template>
          </el-table-column>
          <el-table-column prop="Submitter" label="提交人员" />
          <el-table-column prop="StationName" label="操作油站" />
          <el-table-column prop="SubmitTime" label="提交审核时间" />
          <el-table-column label="操作">
            <template #default="scope">
              <el-link type="primary" :underline="false" 
                @click="showApprovalDialog(scope.row, STATUS.APPROVED.value)">通过</el-link>
              <el-link type="danger" :underline="false" 
                @click="showApprovalDialog(scope.row, STATUS.REJECTED.value)">驳回</el-link>
            </template>
          </el-table-column>
        </el-table>

        <el-table v-if="status === STATUS.APPROVED.value || status === STATUS.REJECTED.value" :data="dataList" style="width: 100%"
          v-loading="loading">
          <el-table-column prop="CompanyName" label="车队名称" />
          <el-table-column prop="CompanyContacts" label="车队管理员" />
          <el-table-column prop="ContactsPhone" label="管理员手机号码" />
          <el-table-column prop="BJ_MZ" label="母账余额（元）" />
          <el-table-column label="母账升数（升）">
            <template #default="scope">
              {{ (scope.row.MZ_Liters === 0 || scope.row.MZ_Liters == null) ? '--' : Number(scope.row.MZ_Liters).toFixed(4) }}
            </template>
          </el-table-column>
          <el-table-column label="充值油品">
            <template #default="scope">
              {{ formatterCellval(scope.row.ChargeOilName) }}
            </template>
          </el-table-column>
          <el-table-column label="本次充值（元）">
            <template #default="scope">
              {{ formatterCellval(scope.row.ChargeAmount) }}
            </template>
          </el-table-column>
          <el-table-column label="本次充值（升）">
            <template #default="scope">
              {{ (scope.row.ChargeLiter === 0 || scope.row.ChargeLiter == null) ? '--' : Number(scope.row.ChargeLiter).toFixed(4) }}
            </template>
          </el-table-column>
          <el-table-column label="本次充值赠送">
            <template #default="scope">
              {{ formatterCellval(scope.row.Bonus) }}
            </template>
          </el-table-column>
          <el-table-column prop="Submitter" label="提交人员" />
          <el-table-column prop="StationName" label="操作油站" />
          <el-table-column prop="SubmitTime" label="提交审核时间" />
          <el-table-column label="审核结果">
            <template #default="scope">
              <span
                v-if="scope.row.status || scope.row.Status === STATUS.REJECTED.value"
                style="color: #F56C6C;"
              >
                {{ getStatusLabel(scope.row.status || scope.row.Status) }}
              </span>
              <span v-else>
                {{ getStatusLabel(scope.row.status || scope.row.Status) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="Auditor" label="审核人员" />
          <el-table-column prop="AuditTime" label="审核时间" />
          <el-table-column label="操作">
            <template #default="scope">
              <el-link type="primary" :underline="false" @click="showWorkflowDetail(scope.row)">工作流明细</el-link>
            </template>
          </el-table-column>
        </el-table>

        <div class="mt-4 w-full flex justify-between items-center">
          <el-pagination
            :current-page="query.page"
            :page-size="query.size"
            layout="prev, pager, next"
            :total="query.total"
            @current-change="handleCurrentChange"
          />
          <el-pagination
            :page-sizes="[10, 20, 50, 100]"
            :page-size="query.size"
            layout="total, sizes"
            :total="query.total"
            @size-change="handleSizeChange"
          />
        </div>
      </div>
    </div>
    <el-dialog title="充值审核" :visible.sync="dialogState.visible" width="600px">
      <div v-loading="dialogState.bonusLoading">
        <el-form label-width="120px">
          <el-row :gutter="15" type="flex" class="flex-wrap">
            <el-col :span="12">
              <el-form-item label="车队名称：">
                <span>{{ dialogState.data.CompanyName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="车队管理员：">
                <span>{{ dialogState.data.CompanyContacts }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="车队余额：">
                <span>{{ dialogState.data.BJ_MZ }}</span>元
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="手机号：">
                <span>{{ dialogState.data.ContactsPhone }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="充值油品：">
                <span>{{ formatterCellval(dialogState.data.ChargeOilName) }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="本次充值(元)：">
                <span>{{ formatterCellval(dialogState.data.ChargeAmount) }} 元</span>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="currentFixPriceMode !== FIXED_PRICE_MODE.No">
              <el-form-item label="本次充值(升)：">
                <span>
                  {{ (dialogState.data.ChargeLiter === 0 || dialogState.data.ChargeLiter == null) ? '--' : Number(dialogState.data.ChargeLiter).toFixed(4) }} 
                  <template v-if="dialogState.data.ChargeLiter !== 0 && dialogState.data.ChargeLiter != null">升</template>
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="currentFixPriceMode !== FIXED_PRICE_MODE.No">
              <el-form-item label="油品单价：">
                <span>{{ dialogState.data.ChargePrice }}元/升</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="提交人员：">
                <span>{{ dialogState.data.Submitter }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="操作油站：">
                <span>{{ dialogState.data.StationName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="操作时间：">
                <span>{{ dialogState.data.SubmitTime }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="充值赠送：" v-if="dialogState.data.gift">
                <span>{{ dialogState.data.gift }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注：">
                <el-input type="textarea" maxlength="120" :rows="3" v-model="dialogState.data.remark" placeholder="请输入备注" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :disabled="dialogState.bonusLoading" @click="dialogState.visible = false">取 消</el-button>
        <el-button v-if="dialogState.actionType === STATUS.APPROVED.value" type="primary" :disabled="dialogState.bonusLoading"
          @click="handleApproval">审核通过</el-button>
        <el-button v-else type="danger" :disabled="dialogState.bonusLoading"
          @click="handleApproval">审核驳回</el-button>
      </span>
    </el-dialog>
    <el-dialog title="安全验证" class="safe-dialog" append-to-body :visible.sync="security.verifyDialogVisible"
      width="400px">
      <div  v-loading="security.loading">
        <p v-if="getCurrentStation.merchant_type == 2">请输入集团管理员操作密码</p>
        <p v-else>请输入油站管理员操作密码</p>
        <el-input v-model="security.password" type="password" placeholder="请输入操作密码" maxlength="64"
          show-word-limit></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" :disabled="loading" @click="() => {
          security.verifyDialogVisible = false;
          security.password = '';
        }">取 消</el-button>
        <el-button size="mini" type="primary"
          :disabled="!security.password || security.loading || loading"
          @click="verifyPassword">确认</el-button>
      </span>
    </el-dialog>

    <el-dialog title="工作流明细" :visible.sync="workflowDetailDialog.visible" width="800px">
      <el-table
        :data="workflowDetailDialog.data"
        border
        v-loading="workflowDetailDialog.loading"
        height="400px"
      >
        <el-table-column
          v-for="col in workflowDetailDialog.columns"
          :key="col.prop"
          :prop="col.prop"
          :label="col.label"
        />
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="workflowDetailDialog.visible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { debounce } from 'lodash'
import { mapGetters, mapState } from 'vuex'
import { get } from '../../utils/http';
import qs from 'qs';
import { FIXED_PRICE_MODE } from '../../utils/contants/fixedPrice';

const STATUS = {
  PENDING: {
    value: 0,
    label: '审核中',
    optionLabel:'待审核'
  },
  APPROVED: {
    value: 1,
    label: '审核通过',
    optionLabel:'已审核'
  },
  REJECTED: {
    value: 2,
    label: '审核驳回'
  },
  SUCCESS: {
    value: 3,
    label: '审核通过'
  },
  FAILED: {
    value: 4,
    label: '审核通过-充值失败'
  }
}

const STATUS_OPTIONS = Object.values(STATUS).filter(status => status.optionLabel)

export default {
  name: 'RechargeApproval',
  props: {
    hideStatusOptions: {
      type: Boolean,
      default: false
    },
    includeSubmitterID: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      STATUS,
      STATUS_OPTIONS,
      status: STATUS.PENDING.value,
      dataList: [],
      stationId: [],
      dateRange: [],
      companyId: [],
      stationLoading: false,
      companyLoading: false,
      stationOptions: [],
      companyOptions: [],
      pickerOptions: {
        disabledDate: (date) => {
          // 禁用明日及以后的日期
          return date > this.$moment().endOf('day').toDate()
        },
        onPick: ({ maxDate, minDate }) => {
          this.pickerMinDate = minDate
          this.pickerMaxDate = maxDate
        },
        shortcuts: [
          {
            text: '最近一周',
            onClick: (picker) => {
              const end = this.$moment().toDate()
              const start = this.$moment().subtract(7, 'days').toDate()
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick: (picker) => {
              const end = this.$moment().toDate();
              const start = this.$moment().subtract(1, 'months').toDate();
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近三个月',
            onClick: (picker) => {
              const end = this.$moment().toDate();
              const start = this.$moment().subtract(3, 'months').toDate();
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      pickerMinDate: null,
      pickerMaxDate: null,
      query: {
        page: 1,
        size: 10,
        total: 0
      },
      loading: false,
      dialogState: {
        visible: false,
        actionType: null,
        bonusLoading: false,
        data: {
          id: '',
          CompanyName: '',
          CompanyContacts: '',
          ContactsPhone: '',
          BJ_MZ: '',
          ChargeOilName: '',
          ChargeAmount: '',
          ChargeLiter: '',
          BonusAmount: '',
          Submitter: '',
          SubmitTime: '',
          StationName: '',
          remark: '',
          CardLockType: 0
        }
      },
      security: {
        verifyDialogVisible: false,
        password: '',
        loading: false // 新增局部loading状态
      },
      workflowDetailDialog: {
        visible: false,
        data: [],
        loading: false,
        columns: [
          { prop: 'OperateTime', label: '操作时间' },
          { prop: 'OperatorName', label: '操作人员' },
          { prop: 'StatusChange', label: '状态变化' },
          { prop: 'Remark', label: '备注' }
        ]
      },
      FIXED_PRICE_MODE
    }
  },
  computed: {
    ...mapState(['isGroup']),
    ...mapGetters(['getCurrentStation']),
    currentFixPriceMode() {
      // 从 dialogState.data 获取价格模式，默认无固定价
      return String((this.dialogState.data ? this.dialogState.data.CardLockType : '') || this.FIXED_PRICE_MODE.No);
    }

  },
  created() {
    this.debounceRemoteMethod = debounce(this.remoteMethod, 300)
    if (this.hideStatusOptions) {
      this.status = this.STATUS.REJECTED.value;
    }
  },
  mounted() {
    this.getStationOptions();
    this.handleSearch();
  },
  methods: {
    async getStationOptions() {
      if (this.stationLoading) return

      this.stationLoading = true
      try {
        const res = await this.$axios.post('/Stations/getStationList', {})
        if (res.status === 200) {
          this.stationOptions = res.data.data.map(item => ({
            stid: item.stid,
            stname: item.stname
          }))
        } else {
          this.$message.error(res.data.info || '获取油站列表失败')
        }
      } catch (error) {
        console.error('获取油站列表失败:', error)
        this.$message.error('获取油站列表失败,请重试')
      } finally {
        this.stationLoading = false
      }
    },
    handleStatusChange() {
      this.query.page = 1;
      this.handleSearch();
    },
    async handleSearch() {
      this.loading = true;
      try {
        const params = {
          Page: this.query.page,
          PageSize: this.query.size,
          StationNoList: this.getCurrentStation.merchant_type == 2
            ? (this.stationId && this.stationId.length ? this.stationId.join(',') : undefined)
            : this.getCurrentStation.merchant_id,
          CompanyIdList: this.companyId.join(','),
          QueryType: this.status
        };
        if (this.includeSubmitterID) {
          const userInfo = JSON.parse(localStorage.getItem('__userInfo__'));
          params.SubmitterID = userInfo && userInfo.adid ? userInfo.adid : '';
        }

        // 处理时间范围
        if (this.dateRange && this.dateRange.length === 2) {
          params.StartTime = this.$moment(this.dateRange[0]).format('YYYY-MM-DD 00:00:00');
          params.EndTime = this.$moment(this.dateRange[1]).format('YYYY-MM-DD 23:59:59');
        }

        const queryString = qs.stringify(params, { arrayFormat: 'repeat' });
        const response = await get(`/CompanyCard/getApplyRechargeList?${queryString}`);
        const res = response.data;
        console.log("🚀 ~ file: RechargeApproval.vue:400 ~ handleSearch ~ res:", res, response)

        if (response.status === 200) {
          this.dataList = res.list;
          this.query.total = res.page_info.TotalQty;
        } else {
          this.$message.error(response.info || '获取数据失败');
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        this.$message.error('获取数据失败，请重试');
      } finally {
        this.loading = false;
      }
    },
    async remoteMethod(query) {
      if (!query) {
        this.companyOptions = []
        return
      }

      this.companyLoading = true
      try {
        const res = await this.$axios.post('/CompanyCard/getCompanyNameList', {
          input: query
        })
        if (res.data.status === 200) {
          // 解析data字段中的JSON字符串
          const companyList = JSON.parse(res.data.data)
          this.companyOptions = companyList.map(item => ({
            CompanyID: item.CompanyID,
            CompanyName: item.CompanyName
          }))
        } else {
          this.$message.error(res.data.info || '搜索车队失败')
        }
      } catch (error) {
        console.error('搜索车队失败:', error)
        this.$message.error('搜索车队失败,请重试')
      } finally {
        this.companyLoading = false
      }
    },
    handleSizeChange(size) {
      this.query.size = size
      this.query.page = 1
      this.handleSearch()
    },
    handleCurrentChange(page) {
      this.query.page = page
      this.handleSearch()
    },

    async showWorkflowDetail(row) {
      console.log("🚀 ~ showWorkflowDetail ~ row:", row)
      this.workflowDetailDialog.visible = true;
      this.workflowDetailDialog.loading = true;      
      try {
        const res = await this.$axios.get('/CompanyCard/getApplyRechargeInfo', {
          params: { id: row.ID }
        });
        
        if (res.data.status === 200) {
          this.workflowDetailDialog.data = res.data.data.map(item => ({
            OperateTime: item.OperateTime,
            OperatorName: item.OperatorName,
            StatusChange: item.StatusChange,
            Remark: item.Remark
          }));
        } else {
          this.$message.error(res.data.info || '获取工作流明细失败');
        }
      } catch (error) {
        console.error('获取工作流明细失败:', error);
        this.$message.error('获取工作流明细失败，请重试');
      } finally {
        this.workflowDetailDialog.loading = false;
      }
    },
    showApprovalDialog(data, actionType) {
      console.log("🚀 ~ showApprovalDialog ~ this.isGroup:", this.isGroup)
      if(this.isGroup){
        this.$message.error('暂不支持集团');
        return;
      }
      this.dialogState.visible = true;
      this.dialogState.actionType = actionType;
      
      // 创建基础对象包含默认值
      const baseData = {
        id: '',
        CompanyName: '',
        CompanyContacts: '',
        ContactsPhone: '',
        BJ_MZ: '',
        ChargeOilName: '',
        ChargeAmount: '',
        ChargeLiter: '',
        BonusAmount: '',
        Submitter: '',
        SubmitTime: '',
        StationName: '',
        remark: '',
        gift: '',
        CardLockType: 0,
        ChargePrice: 0
      };

      // 合并数据
      this.dialogState.data = Object.assign(
        {},
        baseData,
        data ? data : {}, // 处理data为空的情况
        {
          id: (data && data.ID) || '', // 特殊处理不同名字段
          CardLockType: (data && data.CardLockType) || 0,
          ChargePrice: (data && data.ChargePrice) || 0
        }
      );

      this.dialogState.bonusLoading = true;
      this.getChargeBonus();
    },
    async handleApproval() {
      this.security.verifyDialogVisible = true
    },

    getStatusLabel(statusValue) {
      const statusKey = Object.keys(STATUS).find(key => STATUS[key].value === statusValue);
      return statusKey && STATUS[statusKey] ? STATUS[statusKey].label : statusValue;
    },

    async verifyPassword() {
      if (!this.security.password) {
        this.$message.warning('请输入操作密码');
        return;
      }
      if (this.security.password.length > 64) {
        this.$message.warning('操作密码不能超过64位');
        return;
      }
      try {
        this.security.loading = true;
        const status = this.dialogState.actionType;
        const params = {
          id: this.dialogState.data.id,
          status,
          remark: this.dialogState.data.remark,
          password: this.security.password,
          company_id: this.dialogState.data.CompanyID,
          amount: this.dialogState.data.ChargeAmount,
          pay_way: 1,
          CardLockType: this.dialogState.data.CardLockType || 0,
          Bonus: this.dialogState.data.gift,
          oils:  this.dialogState.data.ChargeOil,
          adid: (() => {
            const userInfoStr = localStorage.getItem('__userInfo__');
            if (userInfoStr) {
              try {
                const userInfo = JSON.parse(userInfoStr);
                return userInfo.adid || ''; // 确保userInfo.adid存在，否则返回空字符串
              } catch (e) {
                console.error('解析userInfo失败', e);
                return ''; // 解析失败返回空字符串
              }
            }
            return ''; // userInfoStr 为 null 返回空字符串
          })(),
          liters:  this.dialogState.data.ChargeLiter,
          unit_price:  this.dialogState.data.ChargePrice,
          other: this.dialogState.data
        };

        const res = await this.$axios.post('/CompanyCard/auditApplyRecharge', params);
        if (res.data.status === 200) {
          this.$message.success('操作成功');
          // 关闭两个弹窗
          this.dialogState.visible = false;
          this.security.verifyDialogVisible = false;
          // 清理密码字段
          this.security.password = '';
          // 仅在成功时刷新表格数据
          await this.handleSearch();
        } else {
          this.$message.error(res.data.info || '操作失败');
        }
      } catch (error) {
        console.error('操作失败:', error);
        this.$message.error('操作失败，请重试');
      } finally {
        this.security.loading = false;
      }
    },
    getChargeBonus() {
      this.dialogState.bonusLoading = true;
      // 如果车队类型不是普通车队则不获取赠送信息
      if (this.currentFixPriceMode !== FIXED_PRICE_MODE.No) {
          this.dialogState.data.gift = '';
          this.dialogState.bonusLoading = false;
          return;
      }
      if (!this.dialogState.data.ChargeAmount) {
        this.dialogState.data.gift = '';
        this.dialogState.bonusLoading = false;
        return;
      }
      const company_id = this.dialogState.data.CompanyID || '';
      this.$axios.post('/CompanyCard/getChargeAmount', {
        company_id: company_id,
        amount: this.dialogState.data.ChargeAmount,
        pay_way: 1
      }).then(res => {
        if (res && res.data && res.data.status === 200) {
          const donate_money = res.data.data.donate_money ? (res.data.data.donate_money + '元') : '';
          let couponinfo = res.data.data.couponinfo;
          let info = '';
          if (couponinfo && couponinfo.coupon_type == 1) {
            if (couponinfo.retail_type == 2) {
              info = couponinfo.price + '元油品券*' + couponinfo.count;
            } else if (couponinfo.retail_type == 3) {
              info = couponinfo.price + '元非油券*' + couponinfo.count;
            } else if (couponinfo.retail_type == 4) {
              info = couponinfo.price + '元服务券*' + couponinfo.count;
            } else {
              info = couponinfo.price + '元赠金券*' + couponinfo.count;
            }
          } else if (couponinfo && couponinfo.coupon_type == 3) {
            if (couponinfo.retail_type == 2) {
              info = couponinfo.price + '折油品券*' + couponinfo.count;
            } else if (couponinfo.retail_type == 3) {
              info = couponinfo.price + '折非油券*' + couponinfo.count;
            } else if (couponinfo.retail_type == 4) {
              info = couponinfo.price + '折服务券*' + couponinfo.count;
            } else {
              info = couponinfo.price + '折赠金券*' + couponinfo.count;
            }
          } else if (couponinfo && couponinfo.coupon_type == 2) {
            if (couponinfo.retail_type == 2) {
              info = '油品券*' + couponinfo.count;
            } else if (couponinfo.retail_type == 3) {
              info = '非油券*' + couponinfo.count;
            } else if (couponinfo.retail_type == 4) {
              info = '服务券*' + couponinfo.count;
            } else {
              info = '赠金券*' + couponinfo.count;
            }
          } else if (couponinfo && couponinfo.coupon_type == 0) {
            info = '券包';
          } else {
            info = '';
          }
          this.dialogState.data.gift = donate_money + (info ? ' + ' + info : '');
        } else {
          this.dialogState.data.gift = '';
          const errorMsg = (res && res.data && res.data.info) ? `获取赠送金额失败：${res.data.info}` : '获取赠送金额失败';
          this.$message.error(errorMsg);
        }
      }).catch(err => {
        console.log("请求异常:", err);
        this.dialogState.data.gift = '';
        this.$message.error('获取赠送金额失败，请重试');
      }).finally(() => {
        this.$nextTick(() => {
          this.dialogState.bonusLoading = false;
          console.log('Loading状态已设置为:', this.dialogState.bonusLoading); // 调试用
        });
      });
    }
  },
  watch: {
    dateRange(newVal) {
      if (newVal && newVal.length === 2) {
        const start = this.$moment(newVal[0])
        const end = this.$moment(newVal[1])

        // 计算月份差
        const monthDiff = end.diff(start, 'months', true)
        console.log("🚀 ~ dateRange ~ monthDiff:", monthDiff)

        if (monthDiff > 3) {
          // 超过3个月，清空选择
          this.dateRange = []
          this.$message.error('选择的时间范围不能超过3个月')
        }
      }
    },
    getCurrentStation(newVal, oldVal) {
      if (!oldVal || newVal.value !== oldVal.value) {
        // 切换油站后清空车队名称数据
        this.companyId = [];
        this.companyOptions = [];
        this.query.page = 1;
        this.handleSearch();
      }
    }
  }
}
</script>

<style scoped>
/* 样式已迁移到unocss */
</style>