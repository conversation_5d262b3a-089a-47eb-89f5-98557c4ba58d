webpackJsonp([4,24],{"6/nJ":function(e,t){},"BNa/":function(e,t){},EaG9:function(e,t){},T3zP:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a("//Fk"),l=a.n(n),r=a("Xxa5"),s=a.n(r),o=a("exGp"),i=a.n(o),c=a("Dd8w"),u=a.n(c),d=a("FZmr"),_=a("NYxO"),p=a("mtWM"),m=a.n(p),b={name:"oldCardCustomerReport",components:{DownloadTips:d.a},data:function(){return{isTotalReportForm:!0,checkAll:!1,checkedReport:[],reports:[{name:"客户充值明细表",value:1},{name:"客户消费明细表",value:2},{name:"客户充值退款明细表",value:3},{name:"客户消费退款明细表",value:4},{name:"客户资金分配明细表",value:5}],isIndeterminate:!1,stationOptions:[],stationValue:[],dateValue:[],companyOptions:[],companyValue:"",searchTypeVlaue:"1",inputTxt:"",companyName:"",customerName:"",start_time:"",end_time:"",stationName:"",tableData:[],tableData1:[],tableData2:[],tableData3:[],tableData4:[],tableData5:[],isGroup:!0,showTable01:!1,showTable02:!1,showTable03:!1,showTable04:!1,showTable05:!1,loading:!1,orderMaker:"",orderMakingTime:"",params:{type:"",start_time:"",end_time:"",station_ids:[],company_id:"",query_type:"",content:""},showDownloadTips:!1}},mounted:function(){var e=localStorage.getItem("__userInfo__")||"";if(this.isTotalReportForm=426!=JSON.parse(e).group_id,void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.getCurrentStation&&2==this.getCurrentStation.merchant_type?this.isGroup=!0:this.isGroup=!1,this.getStations(),this.getCompanyList()},computed:u()({},Object(_.c)({getCurrentStation:"getCurrentStation"})),methods:{handleCheckAllChange:function(e){var t=[];this.reports.forEach(function(e){t.push(e.value)}),this.checkedReport=e?t:[],this.isIndeterminate=!1},handleCheckedReportChange:function(e){var t=e.length;this.checkAll=t===this.reports.length,this.isIndeterminate=t>0&&t<this.reports.length},getStations:function(){var e=this;e.stationValue=[],e.$axios.post("/Stations/getStations",{}).then(function(t){200==t.data.status?(e.stationOptions=t.data.data.station_info,e.stationOptions.forEach(function(t){e.stationValue.push(t.stid)})):e.$message({message:t.data.info,type:"error"})})},getCompanyName:function(e){var t=this;this.companyOptions.forEach(function(a){e==a.ID&&(t.companyName=a.CompanyName)})},getCompanyList:function(){var e=this;e.$axios.post("/CompanyCard/getSimpleCompanyList",{page:1,page_size:800,input:""}).then(function(t){200==t.data.status?e.companyOptions=t.data.data.dt:e.$message({message:t.data.info,type:"error"})})},getCheckedStation:function(){var e=this;this.stationName="";var t=this.stationValue.length;this.stationValue.forEach(function(a,n){e.stationOptions.forEach(function(l){l.stid==a&&(e.stationName+=n==t-1?l.stname:l.stname+"，")})})},changeDateValue:function(e){if(this.dateValue){var t=this.$moment(this.dateValue[0],"YYYY-MM-DD HH:mm:ss").unix();(this.$moment(this.dateValue[1],"YYYY-MM-DD HH:mm:ss").unix()-t)/86400>31&&(this.$message({message:"只能获取一个月内的数据",type:"warning"}),this.dateValue[1]=this.$moment(1e3*(t+2678400)).format("YYYY-MM-DD HH:mm:ss"))}},changeCompanyValue:function(){this.companyName=""},createReport:function(){var e=this;return i()(s.a.mark(function t(){var a,n,l,r,o,i,c,u,d,_,p,m,b;return s.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(0!=e.stationValue.length){t.next=3;break}return e.$message({message:"请选择油站",type:"error"}),t.abrupt("return");case 3:if(e.dateValue&&0!=e.dateValue.length){t.next=6;break}return e.$message({message:"请选择时间",type:"error"}),t.abrupt("return");case 6:if(e.companyValue||e.inputTxt){t.next=9;break}return e.$message({message:"请选择车队或输入查询类型",type:"error"}),t.abrupt("return");case 9:return e.loading=!0,e.params.type=6,e.params.start_time=e.dateValue[0],e.params.end_time=e.dateValue[1],e.params.station_ids=e.stationValue,e.params.company_id=e.companyValue?e.companyValue:0,e.params.query_type=Number(e.searchTypeVlaue),e.params.content=e.inputTxt,t.next=19,e.getCapitalDetails();case 19:if(a=t.sent,n=[],200==a.data.status?(n.push(a.data.data),e.tableData=n):e.$message({message:a.data.info,type:"error"}),!e.checkedReport.includes(1)){t.next=28;break}return e.params.type=1,t.next=26,e.getCapitalDetails();case 26:200==(l=t.sent).data.status?(r=l.data.data.list.sort(function(e,t){return e.pay_date>t.pay_date?1:-1}),e.tableData1=r):e.$message({message:l.data.info,type:"error"});case 28:if(!e.checkedReport.includes(2)){t.next=34;break}return e.params.type=2,t.next=32,e.getCapitalDetails();case 32:200==(o=t.sent).data.status?(i=o.data.data.list.sort(function(e,t){return e.pay_date>t.pay_date?1:-1}),e.tableData2=i):e.$message({message:o.data.info,type:"error"});case 34:if(!e.checkedReport.includes(3)){t.next=40;break}return e.params.type=3,t.next=38,e.getCapitalDetails();case 38:200==(c=t.sent).data.status?(u=c.data.data.list.sort(function(e,t){return e.trade_time>t.trade_time?1:-1}),e.tableData3=u):e.$message({message:c.data.info,type:"error"});case 40:if(!e.checkedReport.includes(4)){t.next=46;break}return e.params.type=4,t.next=44,e.getCapitalDetails();case 44:200==(d=t.sent).data.status?(_=d.data.data.list.sort(function(e,t){return e.trade_time>t.trade_time?1:-1}),e.tableData4=_):e.$message({message:d.data.info,type:"error"});case 46:if(!e.checkedReport.includes(5)){t.next=52;break}return e.params.type=5,t.next=50,e.getCapitalDetails();case 50:200==(p=t.sent).data.status?(m=p.data.data.list.sort(function(e,t){return e.trade_time>t.trade_time?1:-1}),e.tableData5=m):e.$message({message:p.data.info,type:"error"});case 52:e.loading=!1,e.getCompanyName(e.companyValue),e.customerName=e.inputTxt,e.start_time=e.dateValue[0],e.end_time=e.dateValue[1],!(b=localStorage.getItem("__userInfo__"))||""===b&&"undefined"===b||(e.orderMaker=JSON.parse(b).name),e.orderMakingTime=e.$moment().format("YYYY-MM-DD");case 60:case"end":return t.stop()}},t,e)}))()},getCapitalDetails:function(){var e=this,t=m.a.create();return new l.a(function(a,n){t.post("/CardReportForm/getCapitalDetails",e.params,{timeout:3e4}).then(function(e){a(e)}).catch(function(e){a(e)})})},printContent:function(){var e=document.querySelector("#myTable").innerHTML,t=document.body.innerHTML;document.body.innerHTML=e;for(var a=document.getElementsByClassName("el-table__header"),n=document.getElementsByClassName("el-table__body"),l=document.getElementsByClassName("el-table__body-wrapper"),r=document.getElementsByClassName("el-table__empty-block"),s=0;s<a.length;s++)a[s].style.width="100%",a[s].style["table-layout"]="auto";for(var o=0;o<n.length;o++)n[o].style.width="100%",n[o].style["table-layout"]="auto";for(var i=0;i<l.length;i++)l[i].style["overflow-x"]="hidden";for(var c=0;c<r.length;c++)r[c]&&(r[c].style.width="100%");window.print(),history.go(0),document.body.innerHTML=t},cardChargeDownload:function(){var e=this;return i()(s.a.mark(function t(){return s.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e.params.type=e.checkedReport,t.next=3,e.exportCapitalDetails();case 3:e.showDownloadTips=!0;case 4:case"end":return t.stop()}},t,e)}))()},exportCapitalDetails:function(){var e=this,t=this;return new l.a(function(a,n){e.$axios.post("/CardReport/newCapitalDetailsDownload",e.params).then(function(e){if(200!=e.data.status)return t.$message({message:e.data.info,type:"error"}),void n(e);a(e)}).catch(function(e){a(e)})})}},watch:{getCurrentStation:function(e,t){0!=e.merchant_type&&e.value!=t.value&&(this.getCurrentStation&&2==this.getCurrentStation.merchant_type?this.isGroup=!0:this.isGroup=!1,this.getStations(),this.getCompanyList(),this.$parent.getOldReport())},checkedReport:function(e){e.includes(1)?this.showTable01=!0:this.showTable01=!1,e.includes(2)?this.showTable02=!0:this.showTable02=!1,e.includes(3)?this.showTable03=!0:this.showTable03=!1,e.includes(4)?this.showTable04=!0:this.showTable04=!1,e.includes(5)?this.showTable05=!0:this.showTable05=!1}}},v={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"report"},[a("div",{staticClass:"report-content"},[a("div",{staticClass:"checkbox-box"},[a("span",[e._v("勾选后生成报表")]),e._v(" "),a("el-checkbox",{staticClass:"checkAll",attrs:{indeterminate:e.isIndeterminate},on:{change:e.handleCheckAllChange},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[e._v("全选")]),e._v(" "),a("el-checkbox-group",{on:{change:e.handleCheckedReportChange},model:{value:e.checkedReport,callback:function(t){e.checkedReport=t},expression:"checkedReport"}},e._l(e.reports,function(t,n){return a("el-checkbox",{key:n,attrs:{label:t.value}},[e._v(e._s(t.name))])}),1)],1),e._v(" "),a("el-select",{staticStyle:{width:"220px"},attrs:{multiple:"","collapse-tags":"",placeholder:"请选择油站"},on:{change:e.getCheckedStation},model:{value:e.stationValue,callback:function(t){e.stationValue=t},expression:"stationValue"}},e._l(e.stationOptions,function(e,t){return a("el-option",{key:t,attrs:{label:e.stname,value:e.stid}})}),1),e._v(" "),a("el-date-picker",{attrs:{clearable:!1,"value-format":"yyyy-MM-dd HH:mm:ss",type:"datetimerange","range-separator":"至","default-time":["00:00:00","23:59:59"],"start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:e.changeDateValue},model:{value:e.dateValue,callback:function(t){e.dateValue=t},expression:"dateValue"}}),e._v(" "),a("el-select",{staticStyle:{width:"220px"},attrs:{filterable:"",placeholder:"请选择车队",clearable:""},on:{change:e.changeCompanyValue},model:{value:e.companyValue,callback:function(t){e.companyValue=t},expression:"companyValue"}},e._l(e.companyOptions,function(e,t){return a("el-option",{key:t,attrs:{label:e.CompanyName,value:e.ID}})}),1),e._v(" "),a("div",{staticClass:"search-box"},[a("span",{staticClass:"txt"},[e._v("查询类型")]),e._v(" "),a("el-radio-group",{model:{value:e.searchTypeVlaue,callback:function(t){e.searchTypeVlaue=t},expression:"searchTypeVlaue"}},[a("el-radio",{attrs:{label:"1"}},[e._v("手机号")]),e._v(" "),a("el-radio",{attrs:{label:"2"}},[e._v("卡号")]),e._v(" "),a("el-radio",{attrs:{label:"3"}},[e._v("卡面卡号")])],1),e._v(" "),a("el-input",{staticStyle:{width:"210px"},attrs:{placeholder:"0"==e.searchTypeVlaue?"请输入卡号":"1"==e.searchTypeVlaue?"请输入手机号":"请输入卡面卡号",clearable:""},model:{value:e.inputTxt,callback:function(t){e.inputTxt=t},expression:"inputTxt"}})],1),e._v(" "),a("div",{staticClass:"content_header"},[a("el-button",{attrs:{type:"primary"},on:{click:e.createReport}},[e._v("生成")]),e._v(" "),a("el-button",{attrs:{type:"primary",disabled:0==e.tableData.length},on:{click:e.printContent}},[e._v("打印")]),e._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:e.isTotalReportForm,expression:"isTotalReportForm"}],attrs:{type:"primary",disabled:0==e.tableData.length},on:{click:e.cardChargeDownload}},[e._v("下载数据")])],1),e._v(" "),a("div",{attrs:{id:"myTable"}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"tableData reportData"},[a("div",{staticClass:"report_title"},[e._v("储值卡客户资金对账汇总表")]),e._v(" "),a("div",{staticClass:"report_header"},[e.isGroup?a("div",[e._v("集团名称："+e._s(e.getCurrentStation.label))]):a("div",[e._v("油站名称："+e._s(e.getCurrentStation.label))]),e._v(" "),e.companyValue?a("div",[e._v("车队："+e._s(e.companyName))]):e._e(),e._v(" "),e.inputTxt?a("div",[e._v("客户："+e._s(e.customerName))]):e._e(),e._v(" "),a("div",[e._v("开始日期："+e._s(e.start_time))]),e._v(" "),a("div",[e._v("结束日期："+e._s(e.end_time))]),e._v(" "),a("div",[e._v("单位：元")])]),e._v(" "),a("el-table",{attrs:{data:e.tableData,border:"",size:"small",align:"center"}},[a("el-table-column",{attrs:{align:"center",label:"期初余额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.StartBalance).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"期间充值"}},[a("el-table-column",{attrs:{align:"center",prop:"RechargeNumber",label:"充值笔数"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.RechargeAmount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.RechargeCapital).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.RechargeBouns).toFixed(2)))]}}])})],1),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"期间消费"}},[a("el-table-column",{attrs:{align:"center",prop:"ConsumeNumber",label:"消费笔数"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ConsumeAmount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ConsumeCapital).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ConsumeBouns).toFixed(2)))]}}])})],1),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"期间退款"}},[a("el-table-column",{attrs:{align:"center",label:"充值退款"}},[a("el-table-column",{attrs:{align:"center",prop:"RechargeRefundNumber",label:"充值退款笔数"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值退款金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.RechargeRefundAmount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值退款赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.RechargeRefundBouns).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"实退金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.RechargeRefundCapital).toFixed(2)))]}}])})],1),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"recharge_refund_order_cnt",label:"消费退款"}},[a("el-table-column",{attrs:{align:"center",prop:"ConsumeRefundNumber",label:"消费退款笔数"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费退款金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ConsumeRefundAmount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费退款本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ConsumeRefundCapital).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费退款赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ConsumeRefundBouns).toFixed(2)))]}}])})],1)],1),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"期间资金分配"}},[a("el-table-column",{attrs:{align:"center",label:"母账划拨"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.DivedeAmount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"子卡卡账返款"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.RebateAmount).toFixed(2)))]}}])})],1),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"余额清零"}},[a("el-table-column",{attrs:{align:"center",prop:"ClearNumber",label:"清零笔数"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"清零金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ClearAmount).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"清零本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ClearCapital).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"清零赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ClearBouns).toFixed(2)))]}}])})],1),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"期末余额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.EndBalance).toFixed(2)))]}}])})],1),e._v(" "),e.stationValue.length!=e.stationOptions.length&&e.isGroup?a("p",{staticClass:"stations"},[a("span",[e._v("*")]),e._v("取数油站："+e._s(e.stationName))]):e._e(),e._v(" "),a("p",{staticClass:"stations"},[e._v("注：客户资金变动计入储值卡开户油站")]),e._v(" "),a("div",{staticClass:"des_bottom"},[a("div",[e._v("制表人："+e._s(e.orderMaker))]),e._v(" "),a("div",[e._v("制表时间:"+e._s(e.orderMakingTime))]),e._v(" "),a("div",[e._v("签字:")])]),e._v(" "),e.showTable01?a("div",{staticClass:"report_title"},[e._v("客户充值明细表")]):e._e(),e._v(" "),e.showTable01?a("el-table",{attrs:{data:e.tableData1,border:"",size:"small",align:"center"}},[a("el-table-column",{attrs:{align:"center",prop:"stname",label:"充值油站"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_no?t.row.card_no:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡面卡号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_number?t.row.card_number:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_name?t.row.card_name:"-"))]}}],null,!1,1916413525)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"手机号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.phone?t.row.phone:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"车队名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.company_name?t.row.company_name:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"card_type",label:"卡类型"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"account_type",label:"到账类型"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"payment_name",label:"付款方式"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"付款金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.pay_money).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s((Number(t.row.recharge_money)+Number(t.row.give_money)).toFixed(2)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.recharge_money).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"赠送金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.give_money).toFixed(2)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"platform",label:"充值方式"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"pay_date",label:"交易时间"}})],1):e._e(),e._v(" "),e.showTable02?a("div",{staticClass:"report_title"},[e._v("客户消费明细表")]):e._e(),e._v(" "),e.showTable02?a("el-table",{attrs:{data:e.tableData2,border:"",size:"small",align:"center"}},[a("el-table-column",{attrs:{align:"center",prop:"stname",label:"付款油站"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_no?t.row.card_no:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡面卡号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_number?t.row.card_number:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_name?t.row.card_name:"-"))]}}],null,!1,1916413525)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"车牌号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.car_number?t.row.car_number:"-"))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"手机号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.phone?t.row.phone:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"车队名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.company_name?t.row.company_name:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"goods_name",label:"商品名称"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"数量(升)"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.goods_number).toFixed(2)))]}}],null,!1,1776219139)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"订单原价"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.goods_amount).toFixed(2)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"支付金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.pay_money).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"支付本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.account_money).toFixed(2)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"支付赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.give_money).toFixed(2)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"pay_date",label:"交易时间"}})],1):e._e(),e._v(" "),e.showTable03?a("div",{staticClass:"report_title"},[e._v("客户充值退款明细表")]):e._e(),e._v(" "),e.showTable03?a("el-table",{attrs:{data:e.tableData3,border:"",size:"small",align:"center"}},[a("el-table-column",{attrs:{align:"center",label:"卡号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_no?t.row.card_no:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡面卡号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_number?t.row.card_number:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_name?t.row.card_name:"-"))]}}],null,!1,1916413525)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"手机号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.phone?t.row.phone:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"车队名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.company_name?t.row.company_name:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"order_code",label:"交易单号"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"付款金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.pay_money).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"account_type",label:"退款账户"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"payment_name",label:"退款方式"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"实退金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.pay_money).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"退款金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.recharge_money).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"退款赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.give_money).toFixed(2)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"trade_time",label:"退款时间"}})],1):e._e(),e._v(" "),e.showTable04?a("div",{staticClass:"report_title"},[e._v("客户消费退款明细表")]):e._e(),e._v(" "),e.showTable04?a("el-table",{attrs:{data:e.tableData4,border:"",size:"small",align:"center"}},[a("el-table-column",{attrs:{align:"center",label:"卡号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_no?t.row.card_no:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡面卡号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_number?t.row.card_number:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_name?t.row.card_name:"-"))]}}],null,!1,1916413525)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"车牌号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.car_number?t.row.car_number:"-"))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"手机号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.phone?t.row.phone:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"车队名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.company_name?t.row.company_name:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"order_code",label:"交易单号"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"goods_name",label:"商品名称"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"数量(升)"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.goods_number).toFixed(2)))]}}],null,!1,1776219139)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"订单原价"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.goods_amount).toFixed(2)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"付款金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.pay_money).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"退回金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.pay_money).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"退回本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.account_money).toFixed(2)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"退回赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.give_money).toFixed(2)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"trade_time",label:"退款时间"}})],1):e._e(),e._v(" "),e.showTable05?a("div",{staticClass:"report_title"},[e._v("客户资金分配明细表")]):e._e(),e._v(" "),e.showTable05?a("el-table",{attrs:{data:e.tableData5,border:"",size:"small",align:"center"}},[a("el-table-column",{attrs:{align:"center",prop:"transfer_type",label:"分配类型"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_no?t.row.card_no:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡面卡号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_number?t.row.card_number:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"卡名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.card_name?t.row.card_name:"-"))]}}],null,!1,1916413525)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"手机号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.phone?t.row.phone:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"车队名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.company_name?t.row.company_name:"-"))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"stname",label:"操作油站"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"account_type",label:"操作对象"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"变动前金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.change_front_balance).toFixed(2)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"操作金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.change_money).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"变动后金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.change_after_balance).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"trade_time",label:"操作时间"}})],1):e._e()],1)])],1),e._v(" "),a("download-tips",{attrs:{showDownloadTips:e.showDownloadTips},on:{"update:showDownloadTips":function(t){e.showDownloadTips=t},"update:show-download-tips":function(t){e.showDownloadTips=t}}})],1)},staticRenderFns:[]};var f=a("VU/8")(b,v,!1,function(e){a("6/nJ"),a("tyvY")},"data-v-6efaaf69",null);t.default=f.exports},WVvt:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a("//Fk"),l=a.n(n),r=a("Xxa5"),s=a.n(r),o=a("mvHQ"),i=a.n(o),c=a("exGp"),u=a.n(c),d=a("Dd8w"),_=a.n(d),p=a("FZmr"),m={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",[a("div",{staticClass:"table3_head"},[a("span",[e._v("卡号："+e._s(e.tabledata.cardNo)+"   卡面卡号："+e._s(e.tabledata.card_no)+"   车牌："+e._s(e.tabledata.car)+"  ")]),e._v(" "),a("span",[e._v("车队："+e._s(e.tabledata.carList))])]),e._v(" "),a("el-table",{attrs:{data:e.tabledata.child,"span-method":e.objectSpanMethod,border:"",size:"small",align:"center"}},[a("el-table-column",{attrs:{align:"center",prop:"type",label:"变动类型"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",prop:"name",label:"商品名称"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"挂牌价"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.p1).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"实付单价"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.p2).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"油品升数"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.p3).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"油品应付"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.p4).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"非油应付"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.p5).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"优惠金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.p6).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"实付金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.p7).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.p8).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"变动账户",prop:"account"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"余额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.yue).toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"交易时间",prop:"time",width:"150"}})],1)],1)])},staticRenderFns:[]};var b=a("VU/8")({name:"CardCustomerTable",props:["tabledata","spanlist"],data:function(){return{}},mounted:function(){},methods:{objectSpanMethod:function(e){e.row,e.column;var t=e.rowIndex,a=e.columnIndex;if(0===a){var n=this.spanlist.type[t];return{rowspan:n,colspan:n>0?1:0}}if(1===a){var l=this.spanlist.name[t];return{rowspan:l,colspan:l>0?1:0}}if(2===a){var r=this.spanlist.p1[t];return{rowspan:r,colspan:r>0?1:0}}if(3===a){var s=this.spanlist.p2[t];return{rowspan:s,colspan:s>0?1:0}}if(4===a){var o=this.spanlist.p3[t];return{rowspan:o,colspan:o>0?1:0}}if(5===a){var i=this.spanlist.p4[t];return{rowspan:i,colspan:i>0?1:0}}if(6===a){var c=this.spanlist.p5[t];return{rowspan:c,colspan:c>0?1:0}}if(7===a){var u=this.spanlist.p6[t];return{rowspan:u,colspan:u>0?1:0}}}}},m,!1,function(e){a("BNa/")},"data-v-192e7658",null).exports,v=a("T3zP"),f=a("NYxO"),h=a("mtWM"),g=a.n(h),y={name:"CardCustomerReport",components:{DownloadTips:p.a,CardCustomerTable:b,oldCardCustomerReport:v.default},data:function(){return{titleVisible:!1,showTitles:!0,is_select:!0,momList:[],copyMom:[],checkedMom:[],sonList:[],copySon:[],checkedSon:[],title_submit:!1,isTotalReportForm:!0,new_customer_report:1,bigLoading:!1,typeOptions:[{value:1,label:"按车队查询"},{value:4,label:"按卡查询"}],typeValue:1,checkAll:!1,checkedReport:[],isIndeterminate:!1,stationOptions:[],stationValue:[],dateValue:[],companyOptions:[],companyValue:"",searchTypeVlaue:"1",inputTxt:"",companyName:"",customerName:"",start_time:"",end_time:"",stationName:"",tableData:[],tableData2:[],tableData3:[],tableList:[],showTable:!1,showGet:!0,showPrint:!1,showDownload:!1,spanList:[],spanAllList:[],isGroup:!0,loading:!1,orderMaker:"",orderMakingTime:"",params:{},params2:{},params3:{},showDownloadTips:!1,flag:1,data1:[],data2:[],data3:[],data4:[],is_stid:0,single_stid:0,showT3:1,dataShow:1,new_customer_report_download:0,new_customer_report_download_count:0,showThirdDownLoad:!1}},created:function(){this.getOldReport()},mounted:function(){var e=localStorage.getItem("__userInfo__")||"";if(this.isTotalReportForm=426!=JSON.parse(e).group_id,void 0==this.getCurrentStation.merchant_type||0==this.getCurrentStation.merchant_type)return!1;this.getCurrentStation&&2==this.getCurrentStation.merchant_type?this.isGroup=!0:this.isGroup=!1,this.getStations(),this.getCompanyList(),this.getCardReportColumn()},computed:_()({},Object(f.c)({getCurrentStation:"getCurrentStation"})),methods:{getCardReportColumn:function(){var e=this;return u()(s.a.mark(function t(){var a;return s.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.$axios.post("/CardReportColumn/getCardReportColumn",{type:[10,11]});case 3:if(200==(a=t.sent).data.status){t.next=6;break}return t.abrupt("return",e.$message.error(a.data.info));case 6:e.momList=a.data.data[10],e.copyMom=JSON.parse(i()(a.data.data[10])),e.sonList=a.data.data[11],e.copySon=JSON.parse(i()(a.data.data[11])),e.showTitles=!1,t.next=16;break;case 13:t.prev=13,t.t0=t.catch(0),e.$message.error("网络错误");case 16:case"end":return t.stop()}},t,e,[[0,13]])}))()},showTitle:function(){this.momList=JSON.parse(i()(this.copyMom)),this.sonList=JSON.parse(i()(this.copySon)),this.setTitle(),this.titleVisible=!0},setTitle:function(){var e=this;this.checkedMom=[],this.momList.forEach(function(t){t.is_checked&&e.checkedMom.push(t.field)}),this.checkedSon=[],this.sonList.forEach(function(t){t.is_checked&&e.checkedSon.push(t.field)})},changeMom:function(){var e=this;this.momList.forEach(function(t){e.checkedMom.includes(t.field)?e.$set(t,"is_checked",!0):e.$set(t,"is_checked",!1)})},changeSon:function(){var e=this;this.sonList.forEach(function(t){e.checkedSon.includes(t.field)?e.$set(t,"is_checked",!0):e.$set(t,"is_checked",!1)})},resetTitle:function(){var e=this;this.checkedMom=[],this.momList.forEach(function(t){e.checkedMom.push(t.field),e.$set(t,"is_checked",!0)}),this.checkedSon=[],this.sonList.forEach(function(t){e.checkedSon.push(t.field),e.$set(t,"is_checked",!0)})},sureTitle:function(){this.is_select?this.submitTitle():(this.copyMom=JSON.parse(i()(this.momList)),this.copySon=JSON.parse(i()(this.sonList)),this.titleVisible=!1)},submitTitle:function(){var e=this;return u()(s.a.mark(function t(){var a,n,l;return s.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,e.title_submit=!0,a=1,n=[{type:10,cid_list:[],is_keep_tiered:!0},{type:11,cid_list:[],is_keep_tiered:!0}],e.momList.forEach(function(e){e.is_checked&&n[0].cid_list.push(e.cid),0==e.crid?a=1:(a=2,n[0].crid=e.crid)}),e.sonList.forEach(function(e){e.is_checked&&n[1].cid_list.push(e.cid),0==e.crid?a=1:(a=2,n[1].crid=e.crid)}),t.next=8,e.$axios.post("/CardReportColumn/changeUserReportColumn",{changeType:a,userReportColumn:n});case 8:if(200==(l=t.sent).data.status){t.next=11;break}return t.abrupt("return",e.$message.error(l.data.info));case 11:e.$message.success("自定义显示字段成功"),e.getCardReportColumn(),e.titleVisible=!1,t.next=19;break;case 16:t.prev=16,t.t0=t.catch(0),e.$message.error("网络错误");case 19:return t.prev=19,e.title_submit=!1,t.finish(19);case 22:case"end":return t.stop()}},t,e,[[0,16,19,22]])}))()},getOldReport:function(){var e=this;return u()(s.a.mark(function t(){var a;return s.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,e.bigLoading=!0,t.next=4,e.$axios.post("/Ostn/getGroupBaseInfo");case 4:if(a=t.sent,console.log("灰度res",a.data.data),200==a.data.status){t.next=8;break}return t.abrupt("return",e.$message.error(e.data.info));case 8:e.new_customer_report=a.data.data.new_customer_report,e.stationValue=a.data.data.group_stids,e.new_customer_report_download=a.data.data.new_customer_report_download,e.new_customer_report_download_count=a.data.data.new_customer_report_download_count,e.changeTypeValue(),e.bigLoading=!1,console.log(e.dateValue),t.next=20;break;case 17:t.prev=17,t.t0=t.catch(0),e.bigLoading=!1;case 20:case"end":return t.stop()}},t,e,[[0,17]])}))()},setTime:function(){this.dateValue=[];var e=this.$moment().format("YYYY-MM-DD"),t=this.$moment().subtract(1,"months").format("YYYY-MM-DD");this.dateValue.push(t+" 00:00:00"),this.dateValue.push(e+" 23:59:59")},changeTypeValue:function(){console.log(this.typeValue),this.setTime(),this.start_time=this.dateValue[0],this.end_time=this.dateValue[1],this.typeValue!=this.flag&&(this.tableData=[],this.tableData2=[],this.tableData3=[],this.tableList=[]),this.showPrint=!1,this.showDownload=!1,1==this.typeValue&&(this.companyValue=""),4==this.typeValue&&(this.searchTypeVlaue="1",this.inputTxt="")},getStations:function(){var e=this;e.$axios.post("/Stations/getStations",{}).then(function(t){200==t.data.status?(1==t.data.data.is_group?(e.is_stid=0,e.single_stid=0):(e.is_stid=1,e.single_stid=t.data.data.station_info[0].stid),console.log("is_stid",e.is_stid,e.single_stid)):e.$message({message:t.data.info,type:"error"})})},getCompanyName:function(e){var t=this;this.companyOptions.forEach(function(a){e==a.ID&&(t.companyName=a.CompanyName)})},getCompanyList:function(){var e=this;return u()(s.a.mark(function t(){var a;return s.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return a=e,t.next=3,g.a.create().post("/CompanyCard/getSimpleCompanyList",{page:1,page_size:1250,input:""},{timeout:3e4}).then(function(e){200==e.data.status?a.companyOptions=e.data.data.dt:a.$message({message:e.data.info,type:"error"})});case 3:case"end":return t.stop()}},t,e)}))()},getCheckedStation:function(){var e=this;this.stationName="";var t=this.stationValue.length;this.stationValue.forEach(function(a,n){e.stationOptions.forEach(function(l){l.stid==a&&(e.stationName+=n==t-1?l.stname:l.stname+"，")})})},changeDateValue:function(e){if(this.dateValue){var t=this.$moment(this.dateValue[0],"YYYY-MM-DD HH:mm:ss").unix();(this.$moment(this.dateValue[1],"YYYY-MM-DD HH:mm:ss").unix()-t)/86400>366&&(this.$message({message:"只能获取一年(366天)的数据",type:"warning"}),this.dateValue[1]=this.$moment(1e3*(t+31622400)).format("YYYY-MM-DD HH:mm:ss"))}},changeCompanyValue:function(){this.companyName=""},createReport:function(){var e=this;return u()(s.a.mark(function t(){var a,n,l,r,o,i,c,u;return s.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(e.dateValue&&0!=e.dateValue.length){t.next=3;break}return e.$message({message:"请选择时间",type:"error"}),t.abrupt("return");case 3:if(e.companyValue||1!=e.typeValue){t.next=6;break}return e.$message({message:"请选择车队",type:"error"}),t.abrupt("return");case 6:if(4!=e.typeValue||e.inputTxt){t.next=8;break}return t.abrupt("return",e.$message.error("请输入查询类型"));case 8:return e.loading=!0,e.showGet=!1,e.showPrint=!1,e.showDownload=!1,e.params.type=6,e.params.start_time=e.dateValue[0],e.params2.start_time=e.dateValue[0],e.params3.start_time=e.dateValue[0],e.params.end_time=e.dateValue[1],e.params2.end_time=e.dateValue[1],e.params3.end_time=e.dateValue[1],e.params.station_ids=e.stationValue,e.params2.stids=e.stationValue,e.params3.stids=e.stationValue,e.params.is_stid=e.is_stid,e.params2.is_stid=e.is_stid,e.params3.is_stid=e.is_stid,e.params.single_stid=e.single_stid,e.params2.single_stid=e.single_stid,e.params3.single_stid=e.single_stid,1==e.typeValue?(delete e.params.query_type,delete e.params.content,e.params.company_id=e.companyValue?e.companyValue:0,e.params2.company_id=e.companyValue?e.companyValue:0,e.params3.company_id=e.companyValue?e.companyValue:0,e.params2.company_type=1,e.params3.company_type=1,delete e.params2.phone,delete e.params2.card_no,delete e.params2.card_number,delete e.params2.car_no,delete e.params3.phone,delete e.params3.card_no,delete e.params3.card_number,delete e.params3.car_no):(delete e.params.company_id,delete e.params3.company_id,e.params2.company_id=0,e.params.query_type=Number(e.searchTypeVlaue),e.params.content=e.inputTxt,1==e.params.query_type&&(delete e.params2.card_no,delete e.params2.card_number,delete e.params2.car_no,e.params2.phone=e.params.content,delete e.params3.card_no,delete e.params3.card_number,delete e.params3.car_no,e.params3.phone=e.params.content),2==e.params.query_type&&(delete e.params2.phone,delete e.params2.card_number,delete e.params2.car_no,e.params2.card_no=e.params.content,delete e.params3.phone,delete e.params3.card_number,delete e.params3.car_no,e.params3.card_no=e.params.content),3==e.params.query_type&&(delete e.params2.phone,delete e.params2.card_no,delete e.params2.car_no,e.params2.card_number=e.params.content,delete e.params3.phone,delete e.params3.card_no,delete e.params3.car_no,e.params3.card_number=e.params.content),4==e.params.query_type&&(delete e.params2.phone,delete e.params2.card_no,delete e.params2.card_number,e.params2.car_no=e.params.content,delete e.params3.phone,delete e.params3.card_no,delete e.params3.card_number,e.params3.car_no=e.params.content),e.params2.company_type=2,e.params3.company_type=2),t.prev=29,t.next=32,e.$axios.post("/CardReportForm/checkOrderListsCount",e.params);case 32:if(200==(a=t.sent).data.status){t.next=38;break}return e.$message.error(a.data.info),e.showGet=!0,e.loading=!1,t.abrupt("return");case 38:if(n=1,1!=e.new_customer_report_download){t.next=43;break}a.data.data.total>Number(e.new_customer_report_download_count)&&(n=0),t.next=48;break;case 43:if(n=1,!(a.data.data.total>3e3)){t.next=48;break}return e.showGet=!0,e.loading=!1,t.abrupt("return",e.$message.warning("当前订单超过3000条,请缩小查询时间!"));case 48:return(l=g.a.create({timeout:3e4,headers:{"Content-Type":"application/json;charset=UTF-8"},withCredentials:!0})).defaults.headers.common["X-Requested-With"]="XMLHttpRequest",t.next=52,e.getCapitalDetails(l);case 52:if(200==(r=t.sent).data.status){t.next=58;break}return e.$message.error(r.data.info),e.showGet=!0,e.loading=!1,t.abrupt("return");case 58:return(o=[]).push(r.data.data),e.tableData=o,t.next=63,e.getOilTotalSum(l);case 63:if(200==(i=t.sent).data.status){t.next=69;break}return e.$message.error(i.data.info),e.showGet=!0,e.loading=!1,t.abrupt("return");case 69:if(e.tableData2=i.data.data,!n){t.next=85;break}return t.next=73,e.getCustomerOrderLists(l);case 73:if(200==(c=t.sent).data.status){t.next=79;break}return e.$message.error(c.data.info),e.showGet=!0,e.loading=!1,t.abrupt("return");case 79:c.data.data.result?e.tableData3=c.data.data.result:e.tableData3=[],c.data.data.card_result?e.tableList=c.data.data.card_result:e.tableList=[],e.showT3=1,0==e.tableData3.length&&e.tableList.length>0&&(e.showT3=0),t.next=86;break;case 85:e.showThirdDownLoad=!0;case 86:console.log("this.tableList",e.tableList),e.flag=e.typeValue,e.data1=e.tableData,e.data2=e.tableData2,e.data3=e.tableData3,e.data4=e.tableList,e.showPrint=!0,e.showDownload=!0,e.showGet=!0,e.loading=!1,t.next=104;break;case 98:t.prev=98,t.t0=t.catch(29),e.showPrint=!1,e.showDownload=!1,e.showGet=!0,e.loading=!1;case 104:e.getCompanyName(e.companyValue),e.customerName=e.inputTxt,e.start_time=e.dateValue[0],e.end_time=e.dateValue[1],!(u=localStorage.getItem("__userInfo__"))||""===u&&"undefined"===u||(e.orderMaker=JSON.parse(u).name),e.orderMakingTime=e.$moment().format("YYYY-MM-DD");case 111:case"end":return t.stop()}},t,e,[[29,98]])}))()},setTable:function(){var e=this;this.spanList=[],this.tableList.forEach(function(t,a){e.spanList[a]=0,t.type||(e.spanList[a]=1)}),console.log("spanList",this.spanList)},setList:function(){console.log("tableList2222",this.tableList);var e=[0];this.tableList.forEach(function(t,a){t.type||(console.log("i",a),e.push(a))}),e.push(this.tableList.length),console.log("list",e);for(var t=[],a=0;a<e.length-1;a++)console.log(e[a],e[a+1]),t.push(this.tableList.slice(e[a],e[a+1])),e[a+1]=e[a+1]+1;console.log("newtable",t),this.tableList=t},objectSpanMethod:function(e){e.row,e.column;var t=e.rowIndex,a=e.columnIndex;if(this.spanList[t]>0)return 0===a?[1,15]:[0,0]},getCapitalDetails:function(e){var t=this;return new l.a(function(a,n){e.post("/CardReportForm/getNewCapitalDetails",t.params).then(function(e){a(e)}).catch(function(e){n(e)})})},getOilTotalSum:function(e){var t=this;return new l.a(function(a,n){e.post("/CardReport/getOilTotalSum",t.params2).then(function(e){a(e)}).catch(function(e){n(e)})})},getCustomerOrderLists:function(e){var t=this;return new l.a(function(a,n){e.post("/CardReport/getCustomerOrderLists",t.params3).then(function(e){a(e)}).catch(function(e){n(e)})})},printContent:function(){var e=document.querySelector("#myTable").innerHTML,t=document.body.innerHTML;document.body.innerHTML=e;for(var a=document.getElementsByClassName("el-table__header"),n=document.getElementsByClassName("el-table__body"),l=document.getElementsByClassName("el-table__body-wrapper"),r=document.getElementsByClassName("el-table__empty-block"),s=0;s<a.length;s++)a[s].style.width="100%",a[s].style["table-layout"]="auto";for(var o=0;o<n.length;o++)n[o].style.width="100%",n[o].style["table-layout"]="auto";for(var i=0;i<l.length;i++)l[i].style["overflow-x"]="hidden";for(var c=0;c<r.length;c++)r[c]&&(r[c].style.width="100%");window.print(),history.go(0),document.body.innerHTML=t},cardChargeDownload:function(){var e=this;return u()(s.a.mark(function t(){return s.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.exportCapitalDetails();case 3:e.showDownloadTips=!0,t.next=8;break;case 6:t.prev=6,t.t0=t.catch(0);case 8:case"end":return t.stop()}},t,e,[[0,6]])}))()},exportCapitalDetails:function(){var e=this,t={};t.new_capital_detail=this.params,t.oil_total_sum=this.params2,t.customer_order_lists=JSON.parse(i()(this.params3));var a=[],n=[];this.copyMom.forEach(function(e,t){e.is_checked&&a.push({cid:e.cid,name:e.name,pid:e.pid,field:e.field})}),this.copySon.forEach(function(e,t){e.is_checked&&n.push({cid:e.cid,name:e.name,pid:e.pid,field:e.field})}),t.customer_order_lists.mz_report_column=a,t.customer_order_lists.sub_report_column=n,console.log("下载params",t);var r=this;return new l.a(function(a,n){e.$axios.post("/CardReport/downLoadNewCustomerOrder",t).then(function(e){if(200!=e.data.status)return r.$message({message:e.data.info,type:"error"}),void n(e);a(e)}).catch(function(e){a(e)})})}},watch:{getCurrentStation:function(e,t){var a=this;return u()(s.a.mark(function n(){return s.a.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(0==e.merchant_type||e.value==t.value){n.next=18;break}return a.getCurrentStation&&2==a.getCurrentStation.merchant_type?a.isGroup=!0:a.isGroup=!1,a.tableData=[],a.tableData2=[],a.tableData3=[],a.tableList=[],a.data1=[],a.data2=[],a.data3=[],a.data4=[],a.bigLoading=!0,a.getStations(),a.dataShow=0,a.getCardReportColumn(),n.next=16,a.getCompanyList();case 16:a.dataShow=1,a.getOldReport();case 18:case"end":return n.stop()}},n,a)}))()},typeValue:function(){this.typeValue==this.flag&&(this.tableData=this.data1,this.tableData2=this.data2,this.tableData3=this.data3,this.tableList=this.data4,this.$forceUpdate())}}},w={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.bigLoading,expression:"bigLoading"}]},[e.new_customer_report?a("div",{staticClass:"report"},[a("div",{staticClass:"report-content"},[a("div",{staticStyle:{"margin-bottom":"20px"}},[a("el-radio-group",{on:{change:e.changeTypeValue},model:{value:e.typeValue,callback:function(t){e.typeValue=t},expression:"typeValue"}},e._l(e.typeOptions,function(t){return a("el-radio-button",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])}),1)],1),e._v(" "),a("div",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"end","flex-wrap":"wrap"}},[a("div",{staticStyle:{display:"flex","flex-wrap":"wrap"}},[a("el-date-picker",{staticStyle:{margin:"0 10px"},attrs:{clearable:!1,"value-format":"yyyy-MM-dd HH:mm:ss",type:"datetimerange","range-separator":"至","default-time":["00:00:00","23:59:59"],"start-placeholder":"开始时间","end-placeholder":"结束时间"},on:{change:e.changeDateValue},model:{value:e.dateValue,callback:function(t){e.dateValue=t},expression:"dateValue"}}),e._v(" "),1==e.typeValue&&e.dataShow?a("el-select",{staticStyle:{width:"220px","margin-right":"10px"},attrs:{filterable:"",placeholder:"请选择车队",clearable:""},on:{change:e.changeCompanyValue},model:{value:e.companyValue,callback:function(t){e.companyValue=t},expression:"companyValue"}},e._l(e.companyOptions,function(e,t){return a("el-option",{key:t,attrs:{label:e.CompanyName,value:e.ID}})}),1):e._e(),e._v(" "),4==e.typeValue?a("div",{staticClass:"search-box"},[a("span",{staticClass:"txt"},[e._v("查询客户")]),e._v(" "),a("el-radio-group",{model:{value:e.searchTypeVlaue,callback:function(t){e.searchTypeVlaue=t},expression:"searchTypeVlaue"}},[a("el-radio",{attrs:{label:"1"}},[e._v("手机号")]),e._v(" "),a("el-radio",{attrs:{label:"2"}},[e._v("卡号")]),e._v(" "),a("el-radio",{attrs:{label:"3"}},[e._v("卡面卡号")]),e._v(" "),a("el-radio",{attrs:{label:"4"}},[e._v("车牌号")])],1),e._v(" "),a("el-input",{staticStyle:{width:"210px"},attrs:{clearable:"",placeholder:"2"==e.searchTypeVlaue?"请输入卡号":"1"==e.searchTypeVlaue?"请输入手机号":"3"==e.searchTypeVlaue?"请输入卡面卡号":"请输入车牌号"},model:{value:e.inputTxt,callback:function(t){e.inputTxt=t},expression:"inputTxt"}})],1):e._e(),e._v(" "),a("div",{staticStyle:{"margin-bottom":"5px"}},[a("el-button",{attrs:{type:"primary",disabled:!e.showGet||e.showTitles},on:{click:e.createReport}},[e._v("生成")])],1)],1),e._v(" "),a("div",{staticClass:"content_header"},[a("el-button",{staticClass:"my_btn",attrs:{disabled:e.showTitles},on:{click:e.showTitle}},[e._v("自定义显示字段")]),e._v(" "),a("el-button",{attrs:{type:"primary",disabled:!e.showPrint},on:{click:e.printContent}},[e._v("打印")]),e._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:e.isTotalReportForm,expression:"isTotalReportForm"}],attrs:{type:"primary",disabled:!e.showDownload},on:{click:e.cardChargeDownload}},[e._v("下载数据")])],1)]),e._v(" "),a("div",{attrs:{id:"myTable"}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"tableData reportData"},[a("div",{staticClass:"report_title"},[e._v("客户资金对账报表")]),e._v(" "),a("div",{staticClass:"report_header"},[e.isGroup?a("div",[e._v("集团名称："+e._s(e.getCurrentStation.label))]):a("div",[e._v("油站名称："+e._s(e.getCurrentStation.label))]),e._v(" "),e.companyValue&&1==e.typeValue?a("div",[e._v("车队："+e._s(e.companyName))]):e._e(),e._v(" "),e.inputTxt&&4==e.typeValue?a("div",[e._v("客户："+e._s(e.customerName))]):e._e(),e._v(" "),a("div",[e._v("开始时间："+e._s(e.start_time))]),e._v(" "),a("div",[e._v("结束时间："+e._s(e.end_time))]),e._v(" "),a("div",[e._v("单位：元")])]),e._v(" "),a("div",{staticClass:"report_title"},[e._v("客户资金对账汇总表")]),e._v(" "),a("el-table",{attrs:{data:e.tableData,border:"",size:"small",align:"center"}},[a("el-table-column",{attrs:{align:"center",label:"期初余额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.StartBalance).toFixed(2)))]}}],null,!1,2673320617)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"期间充值"}},[a("el-table-column",{attrs:{align:"center",label:"充值金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.RechargeAmount).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"充值退款"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.RechargeRefundMoney).toFixed(2)))]}}],null,!1,2657950812)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"余额清零"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ClearBalance).toFixed(2)))]}}],null,!1,53922608)})],1),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"期间消费"}},[a("el-table-column",{attrs:{align:"center",label:"消费金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ConsumeAmount).toFixed(2)))]}}],null,!1,2399306543)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"消费退款"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.ConsumeRefundMoney).toFixed(2)))]}}],null,!1,3990016413)})],1),e._v(" "),4==e.typeValue?a("el-table-column",{attrs:{align:"center",label:"期间分配"}},[a("el-table-column",{attrs:{align:"center",label:"资金划拨"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.DivedeAmount).toFixed(2)))]}}],null,!1,4275048412)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"资金返款"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.RebateAmount).toFixed(2)))]}}],null,!1,923830918)})],1):e._e(),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"期末余额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.EndBalance).toFixed(2)))]}}],null,!1,2909866502)})],1),e._v(" "),a("div",{staticClass:"report_title"},[e._v("客户消费商品汇总表")]),e._v(" "),a("el-table",{attrs:{data:e.tableData2,border:"",size:"small",align:"center","header-cell-style":{background:"#F5F7FA"}}},[a("el-table-column",{attrs:{align:"center",prop:"oil_name",label:"油品"}}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"数量(升)"},scopedSlots:e._u([{key:"default",fn:function(t){return a("div",{},["-"==t.row.sale_liter?[e._v(e._s(t.row.sale_liter))]:[e._v(e._s(Number(t.row.sale_liter).toFixed(2)))]],2)}}],null,!1,753698719)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"应付金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.origin_price).toFixed(2)))]}}],null,!1,3231352649)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"优惠金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.dis_price).toFixed(2)))]}}],null,!1,2042447235)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"实付金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.pay_money).toFixed(2)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"实付本金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.pay_bj).toFixed(2)))]}}],null,!1,3576105040)}),e._v(" "),a("el-table-column",{attrs:{align:"center",label:"实付赠金"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(Number(t.row.pay_gift).toFixed(2)))]}}],null,!1,884826788)})],1),e._v(" "),a("div",{staticClass:"report_title"},[e._v("客户资金变动明细表")]),e._v(" "),1==e.typeValue&&1==e.showT3?a("div",[e._m(0),e._v(" "),a("el-table",{attrs:{data:e.tableData3,border:"",size:"small","header-cell-style":{background:"#F5F7FA"}}},[e._l(e.copyMom,function(t,n){return[t.is_checked?a("el-table-column",{attrs:{align:"center",prop:t.field,label:t.name,width:"card_no"==t.field?"180px":"pay_time"==t.field?"150px":""},scopedSlots:e._u([{key:"default",fn:function(n){return["card_no"==t.field||"card_number"==t.field?a("span",[e._v(e._s(n.row[t.field]))]):"type"==t.field?[e.copyMom[0].is_checked||n.$index!=e.tableData3.length-1?a("span",[e._v(e._s(n.row[t.field]))]):a("span",[e._v("合计")])]:Math.abs(Number(n.row[t.field]))>-1?a("span",[e._v(e._s(Number(n.row[t.field]).toFixed(2)))]):a("span",[e._v(e._s(n.row[t.field]))])]}}],null,!0)}):e._e()]})],2)],1):e._e(),e._v(" "),1!=e.typeValue||0!=e.tableList.length?a("div",{staticStyle:{"margin-top":"20px"}},[0==e.tableList.length?a("div",[e._m(1),e._v(" "),a("el-table",{attrs:{data:e.tableList,border:"",size:"small",align:"center","header-cell-style":{background:"#F5F7FA"}}},[e._l(e.copySon,function(t,n){return[t.is_checked?a("el-table-column",{attrs:{align:"center",prop:t.field,label:t.name,width:"pay_time"==t.field?"150px":""},scopedSlots:e._u([{key:"default",fn:function(n){return[Math.abs(Number(n.row[t.field]))>-1?a("span",[e._v(e._s(Number(n.row[t.field]).toFixed(2)))]):"type"==t.field?[e.copySon[0].is_checked||n.$index!=e.tableList.length-1?a("span",[e._v(e._s(n.row[t.field]))]):a("span",[e._v("合计")])]:a("span",[e._v(e._s(n.row[t.field]))])]}}],null,!0)}):e._e()]})],2)],1):a("div",e._l(e.tableList,function(t){return a("div",[a("div",{staticClass:"table3_head"},[t.card_info.CardNo?a("span",[e._v("卡号："+e._s(t.card_info.CardNo))]):a("span",[e._v("卡号：-")]),e._v("  \n                    "),t.card_info.CardNumber?a("span",[e._v("卡面卡号："+e._s(t.card_info.CardNumber))]):a("span",[e._v("卡面卡号：- ")]),e._v("  \n                    "),t.card_info.CarNumber?a("span",[e._v("车牌："+e._s(t.card_info.CarNumber))]):a("span",[e._v(" 车牌：- ")]),e._v("  \n                    "),t.card_info.CardName?a("span",[e._v("卡名称："+e._s(t.card_info.CardName))]):a("span",[e._v("卡名称：- ")])]),e._v(" "),a("el-table",{attrs:{data:t.card_list,border:"",size:"small",align:"center","header-cell-style":{background:"#F5F7FA"}}},[e._l(e.copySon,function(n,l){return[n.is_checked?a("el-table-column",{attrs:{align:"center",prop:n.field,label:n.name,width:"pay_time"==n.field?"150px":""},scopedSlots:e._u([{key:"default",fn:function(l){return[Math.abs(Number(l.row[n.field]))>-1?a("span",[e._v(e._s(Number(l.row[n.field]).toFixed(2)))]):"type"==n.field?[e.copySon[0].is_checked||l.$index!=t.card_list.length-1?a("span",[e._v(e._s(l.row[n.field]))]):a("span",[e._v("合计")])]:a("span",[e._v(e._s(l.row[n.field]))])]}}],null,!0)}):e._e()]})],2)],1)}),0)]):e._e(),e._v(" "),a("div",{staticClass:"des_bottom"},[a("div",[e._v("制表人："+e._s(e.orderMaker))]),e._v(" "),a("div",[e._v("制表时间："+e._s(e.orderMakingTime))]),e._v(" "),a("div",[e._v("签字：")])])],1)])]),e._v(" "),a("el-dialog",{attrs:{title:"自定义显示字段",visible:e.titleVisible,width:"710px","append-to-body":"","close-on-click-modal":!1},on:{"update:visible":function(t){e.titleVisible=t}}},[a("div",{staticClass:"titleBody"},[a("div",{staticClass:"titleLeft"},[a("div",{staticClass:"titleTop"},[e._v("可选字段")]),e._v(" "),a("div",{staticClass:"leftBody"},[a("div",{staticClass:"left-body-title"},[e._v("客户资金变动明细表")]),e._v(" "),a("div",{staticClass:"left-body-inner"},[a("div",{staticClass:"left-inner-title"},[e._v("车队卡母账")]),e._v(" "),a("div",[a("el-checkbox-group",{on:{change:e.changeMom},model:{value:e.checkedMom,callback:function(t){e.checkedMom=t},expression:"checkedMom"}},e._l(e.momList,function(t){return a("el-checkbox",{key:t.id,attrs:{label:t.field,disabled:t.disabled}},[e._v(e._s(t.name))])}),1)],1)]),e._v(" "),a("div",{staticClass:"left-body-inner"},[a("div",{staticClass:"left-inner-title"},[e._v("车队子卡")]),e._v(" "),a("div",[a("el-checkbox-group",{on:{change:e.changeSon},model:{value:e.checkedSon,callback:function(t){e.checkedSon=t},expression:"checkedSon"}},e._l(e.sonList,function(t){return a("el-checkbox",{key:t.id,attrs:{label:t.field,disabled:t.disabled}},[e._v(e._s(t.name))])}),1)],1)])])]),e._v(" "),a("div",{staticClass:"titleRight"},[a("div",{staticClass:"titleTop"},[e._v("已选字段")]),e._v(" "),a("div",{staticClass:"rightBody"},[a("div",{staticClass:"right-item"},[a("div",{staticClass:"left-inner-title"},[e._v("车队卡母账")]),e._v(" "),e._l(e.momList,function(t){return a("div",{staticClass:"right-inner"},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.is_checked,expression:"v.is_checked"}],class:[t.disabled?"greyColor":""]},[e._v(e._s(t.name))])])})],2),e._v(" "),a("div",{staticClass:"right-item"},[a("div",{staticClass:"left-inner-title"},[e._v("车队子卡")]),e._v(" "),e._l(e.sonList,function(t){return a("div",{staticClass:"right-inner"},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.is_checked,expression:"v.is_checked"}],class:[t.disabled?"greyColor":""]},[e._v(e._s(t.name))])])})],2)])])]),e._v(" "),a("div",{staticClass:"titleBottom"},[a("div",[a("el-button",{on:{click:e.resetTitle}},[e._v("重 置")]),e._v(" "),a("el-checkbox",{staticStyle:{"margin-left":"15px"},model:{value:e.is_select,callback:function(t){e.is_select=t},expression:"is_select"}},[e._v("记住选择")])],1),e._v(" "),a("div",[a("el-button",{on:{click:function(t){e.titleVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary",disabled:e.title_submit},on:{click:e.sureTitle}},[e._v("确 定")])],1)])]),e._v(" "),a("el-dialog",{attrs:{title:"提示",center:"",visible:e.showThirdDownLoad,"close-on-click-modal":!1,width:"400px"},on:{"update:visible":function(t){e.showThirdDownLoad=t},close:function(t){e.showThirdDownLoad=!1}}},[a("div",{staticClass:"thirdText"},[a("div",[e._v("客户资金变动明细超过"+e._s(e.new_customer_report_download_count)+"条，")]),e._v(" "),a("div",[e._v("如需查看，请手动下载明细数据")])]),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.showThirdDownLoad=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:e.isTotalReportForm,expression:"isTotalReportForm"}],attrs:{type:"primary"},on:{click:function(t){e.showThirdDownLoad=!1,e.cardChargeDownload()}}},[e._v("下载数据")])],1)]),e._v(" "),a("download-tips",{attrs:{showDownloadTips:e.showDownloadTips},on:{"update:showDownloadTips":function(t){e.showDownloadTips=t},"update:show-download-tips":function(t){e.showDownloadTips=t}}})],1):a("div",[a("old-card-customer-report")],1)])},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"table3_head"},[t("span",[this._v("车队卡母账")])])},function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"table3_head"},[t("span",[this._v("卡号：-   卡面卡号：-   车牌：-  ")]),this._v(" "),t("span",[this._v("卡名称：- ")])])}]};var k=a("VU/8")(y,w,!1,function(e){a("cH7s"),a("EaG9")},"data-v-7527e90c",null);t.default=k.exports},cH7s:function(e,t){},tyvY:function(e,t){}});