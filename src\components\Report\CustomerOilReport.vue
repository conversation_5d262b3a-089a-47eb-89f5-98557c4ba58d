<template>
    <div class="report">
        <div class="report-content">
            <div class="content-header">
                <!-- 切换选项 -->
                <div style="margin-bottom:10px">
                    <el-radio-group v-model="typeValue" @change="changeTypeValue">
                        <el-radio-button
                            v-for="item in typeOptions"
                            :key="item.value"
                            :label="item.value">
                            {{ item.label }}
                        </el-radio-button>
                    </el-radio-group>
                </div>
                <span class="txt" v-if="getCurrentStation.merchant_type == 2">油站名称</span>
                <el-select v-model="stationIdArr" multiple clearable collapse-tags style="width:250px;margin-right:20px;" placeholder="请选择油站" v-if="getCurrentStation.merchant_type == 2 && typeValue == 1">
                    <el-option
                    v-for="(item,index) in stationOptions"
                    :key="index"
                    :label="item.stname"
                    :value="item.stid">
                    </el-option>
                </el-select>
                <el-select v-model="stationId" clearable collapse-tags style="width:220px;margin-right:20px;" placeholder="请选择油站" v-if="getCurrentStation.merchant_type == 2 && typeValue == 4">
                    <el-option
                    v-for="(item,index) in stationOptions"
                    :key="index"
                    :label="item.stname"
                    :value="item.stid">
                    </el-option>
                </el-select>
                <!-- 时间 -->
                <el-date-picker
                    v-show="typeValue == 1"
                    style="margin-right: 15px"
                    v-model="dateValue"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :default-time="['00:00:00', '23:59:59']"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
                <!-- 班次时间 -->
                <banci-date-time
                    ref="banciRef"
                    :stationValue="typeValue == 4?stationId:stationIdArr"
                    :dateValue="dateBanciValue"
                    @searchDate="searchBanciDate"
                    v-show="typeValue == 4">
                </banci-date-time>
                <span class="txt">车队名称</span>
                <el-select
                    v-model="company_id"
                    filterable
                    style="width: 220px; margin-right: 20px"
                    clearable
                    placeholder="请选择车队">
                    <el-option
                        v-for="(item, index) in companyOptions"
                        :key="index"
                        :label="item.CompanyName"
                        :value="item.ID">
                    </el-option>
                </el-select>
                <el-button type="primary" @click="changeDate()">生成</el-button>
            </div>
            <div div class="search-box">
                <el-button type="primary" :disabled="tableData.length == 0 && tableDataDetail.length == 0" @click="printContent">打印</el-button>
                <div class="download-tips" v-show="isTotalReportForm">
                    <el-button type="primary" :disabled="tableData.length == 0 && tableDataDetail.length == 0" @click="cardChargeDownload">下载数据</el-button>
                    <el-checkbox style="margin-top:7px;font-size: 13px;" v-model="checked">同时下载销售明细</el-checkbox>
                </div>
            </div>
            <div id="myTable">
                <div class="tableData reportData">
                    <div class="report_title">车队油品销售汇总表</div>
                    <div class="report_header">
                        <div v-if="isGroup">
                            集团名称：{{ getCurrentStation.label }}
                        </div>
                        <div v-else>
                            油站名称：{{ getCurrentStation.label }}
                        </div>
                        <div>车队：{{ companyName }}</div>
                        <div>开始日期：{{ dateValue ? dateValue[0] : "" }}</div>
                        <div>结束日期：{{ dateValue ? dateValue[1] : "" }}</div>
                        <div>单位：元</div>
                    </div>
                    <el-table
                        :data="tableData"
                        border
                        v-loading="loading"
                        size="small"
                        align="center"
                        ref="table">
                        <el-table-column
                            align="center"
                            prop="oil_name"
                            label="油品"
                            :formatter="formatterCellval">
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="sale_liter"
                            label="销售量（升）"
                            :formatter="formatterCellval">
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="origin_price"
                            label="油品原价"
                            :formatter="formatterCellval">
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="dis_price"
                            label="优惠"
                            :formatter="formatterCellval">
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="pay_money"
                            label="支付金额"
                            :formatter="formatterCellval">
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="pay_bj"
                            label="支付本金"
                            :formatter="formatterCellval">
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="pay_gift"
                            label="支付赠金"
                            :formatter="formatterCellval">
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="pay_xyed"
                            label="信用额度"
                            :formatter="formatterCellval">
                        </el-table-column>
                    </el-table>
                </div>
                <div class="tableData reportData">
                    <div class="report_title">车队油品销售明细汇总表</div>
                    <el-table
                        :data="tableDataDetail"
                        border
                        v-loading="loadingDetail"
                        size="small"
                        align="center"
                        ref="tableDetail">
                        <el-table-column
                            align="center"
                            prop="card_no"
                            label="卡号"
                            width="160"
                            :formatter="formatterCellval">
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="card_number"
                            label="卡面卡号"
                            :formatter="formatterCellval">
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="card_name"
                            label="卡名称"
                            :formatter="formatterCellval">
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="phone"
                            label="手机号"
                            width="100"
                            :formatter="formatterCellval">
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="name"
                            label="持卡人"
                            :formatter="formatterCellval">
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="car_no"
                            label="车牌号"
                            :formatter="formatterCellval">
                        </el-table-column>
                        <template v-for="item in card_oil">
                            <el-table-column
                                :key="item.index"
                                align="center"
                                :prop="String(item.prop)"
                                :label="item.oil_name+'（升）'"
                                :formatter="formatterCellval">
                            </el-table-column>
                            <el-table-column
                                :key="item.index"
                                align="center"
                                :prop="String(item.prop_money)"
                                label="支付金额"
                                :formatter="formatterCellval">
                            </el-table-column>
                        </template>
                        <el-table-column
                            align="center"
                            prop="oil_total"
                            label="销量合计（升）"
                            :formatter="formatterCellval">
                        </el-table-column>
                        <el-table-column
                            align="center"
                            prop="pay_money_total"
                            label="金额合计"
                            :formatter="formatterCellval">
                        </el-table-column>
                    </el-table>
                    <!-- 页码 -->
                    <div class="page_content">
                        <el-pagination class="page_left"
                            @current-change="handleCurrentChange"
                            :current-page="page"
                            :page-size="pageSize"
                            layout="prev, pager, next"
                            :total="total">
                        </el-pagination>
                        <el-pagination class="page_right"
                            @size-change="handleSizeChange"
                            :page-sizes="[20, 30, 40, 50]"
                            :page-size="pageSize"
                            layout="total, sizes"
                            :total="total">
                        </el-pagination>
                    </div>
                </div>
                <div class="des_bottom">
                    <div>制表人：{{ orderMaker }}</div>
                    <div>制表时间：{{ orderMakingTime }}</div>
                    <div>签字：</div>
                </div>
            </div>
        </div>
        <!-- 下载中心提示 -->
        <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>
    </div>
</template>
<script>
import { getStationList, getCompanySaleOilSum , getCompanySaleOilSumDetail,companySaleOilSumDetailDownLoad } from "../api/index"
import DownloadTips from "../DownloadTips.vue";
import BanciDateTime from "../Banci/banciDateTime.vue";
import { mapGetters } from "vuex";
export default {
    name: "customerOilReport",
    components: {
        DownloadTips,
        BanciDateTime,
    },
    data() {
        return {
            isTotalReportForm: true,
            typeOptions: [
                {
                    value: 1,
                    label: "按自然日期",
                },
                {
                    value: 4,
                    label: "按班结日期",
                },
            ],
            typeValue: 1, //按自然日期1 按班结4
            dateValue: [], //按自然日期时间
            dateBanciValue: "",
            companyOptions: [],
            company_id: "", //选中车队
            isGroup: true, //是否是集团账号
            loading: false,
            loadingDetail: false,
            tableData: [],
            card_oil : [],//油品列表
            tableDataDetail: [], //明细
            noMore: true,
            page: 1,
            pageSize: 20,
            total: 0,
            showDownloadTips: false,
            stationIdArr: [],//自然日油站id
            stationId: "",//班结油站id
            stationName:"",//所选油站名称
            stationOptions: [],//油站列表
            orderMaker: "", //制表人
            orderMakingTime: "", //制表时间
            checked:true
        };
    },

    computed: {
        ...mapGetters({
            getCurrentStation: "getCurrentStation",
        }),
        companyName() {
            let companyName = "";
            this.companyOptions.forEach((item) => {
                if (item.ID == this.company_id) {
                    companyName = item.CompanyName;
                }
            });
            return companyName;
        },
    },
    watch: {
        getCurrentStation(newValue, oldValue) {
            if (
                newValue.merchant_type != 0 &&
                newValue.value != oldValue.value
            ) {
              this.stationId = ''
                if (
                    this.getCurrentStation &&
                    this.getCurrentStation.merchant_type == 2
                ) {
                    this.isGroup = true;
                    this.getStationList();
                } else {
                    this.isGroup = false;
                    this.stationId = this.getCurrentStation.merchant_id
                }
                this.getCompanyList();
            }
        },
    },
    mounted() {
        let userInfo = localStorage.getItem("__userInfo__") || "";
        this.isTotalReportForm = JSON.parse(userInfo).group_id != 426;
        //自然日默认为前一天的数据
        let currentDateStart = this.$moment(new Date())
            .subtract(1, "days")
            .format("YYYY-MM-DD HH:mm:ss");
        let currentDateEnd = this.$moment().format("YYYY-MM-DD HH:mm:ss");
        this.dateValue = [currentDateStart, currentDateEnd];
        //班结默认为前一天的数据
        let _today = this.$moment();
        let yesterday = _today.subtract(1, "days").format("YYYY-MM-DD");
        this.dateBanciValue = yesterday;

        if (
            this.getCurrentStation.merchant_type == undefined ||
            this.getCurrentStation.merchant_type == 0
        ) {
            return false;
        }
        if (this.getCurrentStation && this.getCurrentStation.merchant_type == 2) {
            this.isGroup = true;
            this.getStationList();
        } else {
            this.isGroup = false;
            this.stationId = this.getCurrentStation.merchant_id
        }
        this.getCompanyList();
    },

    methods: {
        //获取可用油站
        getStationList(){
            this.stationIdArr = [];
            let that = this;
            getStationList().then((res)=>{
                if(res.status == 200){
                    that.stationOptions = [];
                    res.data.data.forEach((item)=>{
                        that.stationIdArr.push(item.stid);
                    })
                    that.stationOptions = res.data.data;
                }
            })
        },
        //获取车队信息列表
        getCompanyList() {
            let that = this;
            that.$axios
                .post("/CompanyCard/getSimpleCompanyList", {
                    page: 1,
                    page_size: 1250,
                    input: "",
                })
                .then(function (res) {
                    if (res.data.status == 200) {
                        that.companyOptions = [];
                        that.companyOptions = that.companyOptions.concat(
                            res.data.data.dt
                        );
                    } else {
                        that.$message({
                            message: res.data.info,
                            type: "error",
                        });
                    }
                })
                .catch(function (error) {});
        },
        //切换日期类型
        changeTypeValue(e){
            //自然日默认为前一天的数据
            let currentDateStart = this.$moment(new Date()).subtract(1,'days').format('YYYY-MM-DD HH:mm:ss');
            let currentDateEnd = this.$moment().format('YYYY-MM-DD HH:mm:ss');
            this.dateValue = [currentDateStart,currentDateEnd];
            //班结默认为前一天的数据
            let _today = this.$moment();
            let yesterday = _today.subtract(1, 'days').format('YYYY-MM-DD');
            this.dateBanciValue = yesterday;

            this.orderMakingTime = "";
            this.tableData = [];
            this.tableDataDetail = [];
            this.page = 1;
        },
        //查询班次,子组件将班次获取到的时间传给父组件的dateValue
        searchBanciDate(value) {
            this.dateValue = value;
        },
        //选中时间生成报表
        async changeDate() {
            let that = this;
            if(this.isGroup){
                if(!that.stationId && that.typeValue == 4){
                    this.$message.error("请选择油站");
                    return;
                }
            }
            if(!that.company_id){
                this.$message.error("请选择车队");
                return;
            }
            that.loading = true;
            that.loadingDetail = true;
            let stid;
            if(this.typeValue == 4){
                //按班结，油站是单选，类型要转换一下
                stid = [that.stationId]
            }else{
                stid = that.stationIdArr;
            }
            let params = {
                start_time: that.dateValue?that.dateValue[0]:"",
                end_time: that.dateValue?that.dateValue[1]:"",
                company_id: that.company_id
            };
            let paramsDetail = {
                page_index: that.page,
                page_size: that.pageSize,
                start_time: that.dateValue?that.dateValue[0]:"",
                end_time: that.dateValue?that.dateValue[1]:"",
                company_id: that.company_id
            };
            //集团带上stid参数
            if(this.isGroup){
                params.stid = stid;
                paramsDetail.stid = stid;
            }

            that.orderMakingTime = that.$moment().format("YYYY-MM-DD");
            let userInfo = localStorage.getItem("__userInfo__");
            if (
                userInfo &&
                (userInfo !== "" || userInfo !== "undefined")
            ) {
                that.orderMaker = JSON.parse(userInfo).name;
            }

            //油品销售汇总表
            let res = await getCompanySaleOilSum(params);
            that.loading = false;
            if(res.data.status == 200){
                this.tableData = res.data.data;
            }else{
                this.$message.error(res.data.info);
            }

            //车队油品销售明细汇总表
            let resDetail = await getCompanySaleOilSumDetail(paramsDetail);
            that.loadingDetail = false;
            if (resDetail.data.status == 200) {
                that.noMore = that.page * that.pageSize >= resDetail.data.data.total_count ? true : false;
                that.total = resDetail.data.data.total_count;
                if(resDetail.data.data.order_list.length){
                    that.card_oil = resDetail.data.data.order_list[0].card_oil;
                    that.tableDataDetail = resDetail.data.data.order_list;
                }else{
                    that.card_oil = [];
                    that.tableDataDetail = [];
                }
            } else {
                that.$message.error(resDetail.data.info);
            }
        },
        //切换页码
        handleCurrentChange(val){
            this.page = val;
            this.changeDate();
        },
        handleSizeChange(val){
            this.pageSize = val;
            this.changeDate();
        },
        //打印
        printContent() {
            let wpt = document.querySelector("#myTable");
            let newContent = wpt.innerHTML;
            let oldContent = document.body.innerHTML;
            document.body.innerHTML = newContent;
            document.getElementsByClassName("el-table__header")[0].style.width =
                "100%";
            document.getElementsByClassName("el-table__header")[0].style[
                "table-layout"
            ] = "auto";
            document.getElementsByClassName("el-table__body")[0].style.width =
                "100%";
            document.getElementsByClassName("el-table__body")[0].style[
                "table-layout"
            ] = "auto";
            document.getElementsByClassName("el-table__header")[1].style.width =
                "100%";
            document.getElementsByClassName("el-table__header")[1].style[
                "table-layout"
            ] = "auto";
            document.getElementsByClassName("el-table__body")[1].style.width =
                "100%";
            document.getElementsByClassName("el-table__body")[1].style[
                "table-layout"
            ] = "auto";
            window.print(); //打印方法
            history.go(0);
            document.body.innerHTML = oldContent;
        },
        //下载
        cardChargeDownload() {
            let params = {
                start_time: this.dateValue[0],
                end_time: this.dateValue[1],
                company_id: this.company_id,
                download_cost:this.checked?1:0
            }
            let stid;
            if(this.typeValue == 4){
                //按班结，油站是单选，类型要转换一下
                stid = [this.stationId]
            }else{
                stid = this.stationIdArr;
            }
            if(this.isGroup){
                params.stid = stid;
            }
            companySaleOilSumDetailDownLoad(params).then((res)=>{
                if (res.data.status == 200) {
                    this.showDownloadTips = true;
                } else {
                    this.$message.error(res.data.info);
                }
            });
        },
    },
};
</script>
<style scoped>
.report {
    position: relative;
    height: 100%;
    background: rgba(245, 245, 245, 1);
    margin: 0px auto;
}

.report-content {
    background: #fff;
    padding: 20px 0;
}

.tableData {
    text-align: center;
}

.tableData .report_title {
    font-size: 24px;
    font-weight: bolder;
    margin-top: 20px;
    margin-bottom: 10px;
}

.tableData .report_sum {
    padding: 4px 0;
    margin-top: 10px;
    margin-bottom: 10px;
    display: flex;
    width: 100%;
    border: 1px solid #ebeef5;
    height: 50px;
    line-height: 50px;
    color: #909399;
}

.tableData .report_sum div {
    flex: 1;
}

.rightBoder {
    padding-right: 10px;
    border-right: 1px solid #ebeef5;
}

.tableData .report_sum span {
    color: #000;
}

.tableData .report_header {
    display: flex;
    margin: 10px 20px;
}

.tableData .report_header div {
    min-width: 100px;
    text-align: left;
    margin-right: 40px;
}
.search-box {
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    margin-top: 10px;
}
.search-box .download-tips{
    display: flex;
    flex-flow: column;
    align-items: center;
    margin-left: 10px;
}
>>>.download-tips .el-checkbox__label{
    padding-left: 6px;
}
.des_bottom {
    display: flex;
    justify-content: flex-end;
    font-size: 14px;
    font-weight: 500;
    margin-top: 10px;
}
.des_bottom div {
    padding-right: 100px;
}
/* 页码 */
.page_content {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
}
</style>
