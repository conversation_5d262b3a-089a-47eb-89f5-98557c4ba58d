<template>
    <div>
        <div class="header">
            <div style="display: flex; align-items: center;">
                <el-date-picker
                    :default-time="['00:00:00', '23:59:59']"
                    @change="selectDate"
                    v-model="dateValue"
                    :picker-options="pickerOptions"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
                <div style="margin-left: 20px; margin-right: 20px;">车队名称：</div>
                <el-select v-model="value" filterable multiple clearable collapse-tags placeholder="请选择" @change="selectCompany">
                    <el-option
                    v-for="item in companyOptions"
                    :key="item.ID"
                    :label="item.CompanyName"
                    :value="item.ID">
                    </el-option>
                </el-select>
                <el-button type="primary" style="margin-left: 20px;" @click="getCompanyInvoiceAction">生成</el-button>
            </div>
            <div style="display: flex;">
                <el-button type="primary" style="margin-left: 20px;" @click="printAction">打印</el-button>
                <el-button type="primary" style="margin-left: 20px;" @click="downLoadAction">下载数据</el-button>
            </div>
        </div>
        <div style=" margin-top: 40px;" id="myTable">

            <div style="text-align: center; font-size: 24px; font-weight: 600;">车队充值开票汇总报表</div>
            <div class="report_header">
                <div v-if="isGroup">集团名称：{{getCurrentStation.label}}</div>
                <div v-else>油站名称：{{getCurrentStation.label}}</div>
                <!-- <div v-if="typeValue == 4">日期：{{dateBanciValue}}</div> -->
                <div >开始日期：{{dateValue?dateValue[0]:""}}</div>
                <div >结束日期：{{dateValue?dateValue[1]:""}}</div>
                <div>单位：元</div>
            </div>

            <el-table
            ref="table"
                v-loading="loading"
                align="center"
                :data="tableData"
                style="width: 100%">
                <el-table-column
                prop="CompanyID"
                label="车队ID"
                align="center"
                >
                </el-table-column>
                <el-table-column
                prop="CompanyName"
                label="车队名称"
                align="center"
                >
                </el-table-column>
                <el-table-column
                prop="CompanyContacts"
                label="管理员"
                align="center"
                >
                </el-table-column>
                <el-table-column
                prop="ContactsPhone"
                label="联系方式"
                align="center"
                >
                </el-table-column>
                <el-table-column label="充值总金额" align="center">
                    <el-table-column
                        prop="TOTAL_BJ"
                        label="本金"
                        align="center"
                        >
                    </el-table-column>
                    <el-table-column
                        prop="TOTAL_SKJ"
                        label="赠金"
                        align="center"
                        >
                    </el-table-column>
                    <el-table-column
                        prop="TOTAL"
                        label="小计"
                        align="center"
                        >
                    </el-table-column>
                </el-table-column>
                <el-table-column
                prop="KPMoney"
                label="已开票总金额"
                align="center"
                >
                </el-table-column>
                <el-table-column
                prop="WKPMoney"
                label="未开票总金额"
                align="center"
                >
                </el-table-column>
            </el-table>

            <div class="des_bottom" style="margin-top: 20px;">
                <div>制表人：{{orderMaker}}</div>
                <div>制表时间：{{orderMakingTime}}</div>
                <div>签字：</div>
            </div>
        </div>
        <!-- 页码 -->
        <div class="page_content">
            <el-pagination class="page_left"
                @current-change="handleCurrentChange"
                :current-page="page"
                :page-size="pageSize"
                layout="prev, pager, next"
                :total="total">
            </el-pagination>
            <el-pagination class="page_right"
                @size-change="handleSizeChange"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="pageSize"
                layout="total, sizes"
                :total="total">
            </el-pagination>
        </div>
        <!-- 下载中心提示 -->
        <download-tips :showDownloadTips.sync="showDownloadTips"></download-tips>
    </div>
</template>

<script>
import {mapGetters} from 'vuex'
import DownloadTips from '../DownloadTips.vue';
export default {
    name: "FleetTopUpInvoiceReport",
    components:{
        DownloadTips
    },
    data() {
        return {
            companyOptions: [{
                ID:"",
                CompanyName:"全部"
            }],
            value: [],
            tableData: [],
            page: 1,
            pageSize: 10,
            total: 0,
            loading: false,
            dateValue:["",""],
            orderMaker:"",
            orderMakingTime:"",
            isGroup:true,
            showDownloadTips: false,
            pickerOptions: {
                onPick: obj => {
                    if (obj) {
                        console.log(obj)
                        this.pickerMinDate = new Date(obj.minDate).getTime()
                        console.log(this.pickerMinDate)
                    }else {
                        this.pickerMinDate = ""
                    }
                },
                disabledDate: time => {
                    if (this.pickerMinDate) {
                        const twoMonth = 1000 * 3600 * 24 * 31 * 2
                        const maxTime = this.pickerMinDate + twoMonth
                        const minTime = this.pickerMinDate - twoMonth
                        return time.getTime() >= maxTime || time.getTime() <= minTime
                    }else {
                        return false
                    }
                }
            },
        }
    },
    mounted() {
        let currentDateStart = this.$moment(new Date()).subtract(1,'days').format('YYYY-MM-DD HH:mm:ss');
        let currentDateEnd = this.$moment().format('YYYY-MM-DD HH:mm:ss');

        this.dateValue = [currentDateStart,currentDateEnd];
        this.getCompanyList()
        this.getCompanyInvoice()
    },
    computed:{
        ...mapGetters({
            "getCurrentStation":"getCurrentStation"
        })
    },
    methods: {
        //获取车队信息列表
        getCompanyList() {
            let that = this;
            that.$axios
            .post("/CompanyCard/getSimpleCompanyList", {
                page: 1,
                page_size: 1250,
                input: "",
            })
            .then(function (res) {
                if (res.data.status == 200) {
                    that.companyOptions = [{
                        ID:"",
                        CompanyName:"全部"
                    }];
                    that.companyOptions = that.companyOptions.concat(
                        res.data.data.dt
                    );
                } else {
                    that.$message({
                        message: res.data.info,
                        type: "error",
                    });
                }
            })
            .catch(function (error) {});
        },
        selectCompany(e) {
            console.log(e)
            console.log(this.value)
            this.page = 1
            this.pageSize = 10
        },
        selectDate() {
            if(!this.dateValue){
                this.tableData = [];
                this.orderMakingTime = "";
                this.orderMaker = "";
                this.dateValue = ["",""]
            }else {
                let currentDateStart = this.$moment(this.dateValue[0]).format('YYYY-MM-DD HH:mm:ss')
                let currentDateEnd = this.$moment(this.dateValue[1]).format('YYYY-MM-DD HH:mm:ss')

                console.log(currentDateStart)
                console.log(currentDateEnd)

                this.dateValue = [currentDateStart,currentDateEnd];
            }
           
            this.pickerMinDate = ""
            this.pickerOptions = {
                onPick: obj => {
                    if (obj) {
                        console.log(obj)
                        this.pickerMinDate = new Date(obj.minDate).getTime()
                        console.log(this.pickerMinDate)
                    }else {
                        this.pickerMinDate = ""
                    }
                },
                disabledDate: time => {
                    if (this.pickerMinDate) {
                        const twoMonth = 1000 * 3600 * 24 * 31 * 2
                        const maxTime = this.pickerMinDate + twoMonth
                        const minTime = this.pickerMinDate - twoMonth
                        return time.getTime() >= maxTime || time.getTime() <= minTime
                    }else {
                        return false
                    }
                }
            }
            
        },
        getCompanyInvoiceAction() {
            this.page = 1
            this.pageSize = 10
            this.getCompanyInvoice()
        },
        getCompanyInvoice() {
            let that = this;
            this.loading = true
            that.$axios
            .get("/CompanyManager/companyInvoice", {
                params: {
                page: this.page,
                page_size: this.pageSize,
                company_id: this.value,
                start_time: this.dateValue[0],
                end_time: this.dateValue[1]
                }
            })
            .then(function (res) {
                if (res.data.status == 200) {
                    that.tableData = [];
                    that.tableData = that.tableData.concat(
                        res.data.data.list
                    );
                    that.total = res.data.data.total
                    that.orderMakingTime = that.$moment().format("YYYY-MM-DD");
                    let userInfo = localStorage.getItem('__userInfo__');
                    if(userInfo && (userInfo !== "" || userInfo !== "undefined")){
                        that.orderMaker = JSON.parse(userInfo).name;
                    }
                    console.log(that.tableData)
                } else {
                    that.$message({
                        message: res.data.info,
                        type: "error",
                    });
                }
                that.loading = false
            })
            .catch(function (error) {
                that.loading = false
            });
        },
        downLoadAction() {
            let that = this;
            this.loading = true
            var data = {
                page: this.page,
                page_size: this.pageSize,
                company_id: this.value,
                start_time: this.dateValue[0],
                end_time: this.dateValue[1]
            }
            that.$axios
            .get("/CompanyManager/downloadCompanyInvoice", {params: data})
            .then(function (res) {
                that.showDownloadTips = true
                that.loading = false
            })
            .catch(function (error) {
                that.loading = false
            });
        },
        handleCurrentChange(e) {
            console.log(e)
            this.page = e
            this.getCompanyInvoice()
        },
        handleSizeChange(e) {
            console.log(e)
            this.pageSize = e
            this.getCompanyInvoice()
        },
        printAction() {
            this.currentPage = 1
            this.tableData = [];
            this.loading = true
            this.getPrintData()
        },
        async getPrintData() {
            var data = {
                page: this.currentPage,
                page_size: 20,
                company_id: this.value,
                start_time: this.dateValue[0],
                end_time: this.dateValue[1]
            }
            const res = await this.$axios.get('/CompanyManager/companyInvoice', {params: data}).catch(res=>{
                this.$message.error('出错了哦，请重试');
                this.loading = false
                return
            })
            if (res.data.status == 200) {
                this.tableData = this.tableData.concat(
                    res.data.data.list
                );
                if (this.currentPage * 20 >= res.data.data.total) {
                    this.loading = false
                    var arr = []
                    for (let index = 0; index < this.tableData.length; index++) {
                        const element = this.tableData[index];
                        if (element.CompanyName) {
                            arr.push(element)
                        }
                    }
                    this.tableData = arr
                    this.printContent()
                }else {
                    this.currentPage = this.currentPage + 1
                    this.getPrintData()
                }
            }else {
                this.$message.error('出错了哦，请重试');
                this.loading = false
            }
        },
        //打印
        printContent() {
            this.showAllTitle = true
            this.$nextTick(() => {

                let wpt = document.querySelector("#myTable");
                let newContent = wpt.innerHTML;
                let oldContent = document.body.innerHTML;
                document.body.innerHTML = newContent;
                let headerDom = document.getElementsByClassName("el-table__header");
                let bodyDom = document.getElementsByClassName("el-table__body");
                let wrapperDom = document.getElementsByClassName(
                "el-table__body-wrapper"
                );
                let emptyDom = document.getElementsByClassName("el-table__empty-block");

                for (let i = 0; i < headerDom.length; i++) {
                headerDom[i].style.width = "100%";
                headerDom[i].style["table-layout"] = "auto";
                }
                for (let i = 0; i < bodyDom.length; i++) {
                bodyDom[i].style.width = "100%";
                bodyDom[i].style["table-layout"] = "auto";
                }
                for (let i = 0; i < wrapperDom.length; i++) {
                wrapperDom[i].style["overflow-x"] = "hidden";
                }
                for (let i = 0; i < emptyDom.length; i++) {
                if (emptyDom[i]) {
                    emptyDom[i].style.width = "100%";
                }
                }
                window.print(); //打印方法
                history.go(0);
                document.body.innerHTML = oldContent;
            })
        },
    }
}
</script>

<style scoped>
    .header {
        display: flex;
        align-items: center;
        margin-top: 20px;
        justify-content: space-between;
    }

    .report_header {
        display: flex;
        margin:10px 20px;
    }
    .report_header div{
        min-width: 100px;
        text-align: left;
        margin-right: 40px;
    }

    .page_content {
        margin-top: 20px;
        display: flex;
        justify-content: space-between;
    }

    .des_bottom {
        display: flex;
        justify-content: flex-end;
        font-size: 14px;
        font-weight: 500;
        margin-top: 10px;
    }
    .des_bottom div{
        padding-right: 100px;
    }
    @media print {
        .el-table__body-wrapper table thead {
            display: table-header-group;
        }
    }
</style>