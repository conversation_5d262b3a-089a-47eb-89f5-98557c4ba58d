<template>
    <div class="setting">
        <div class="item">
            <p class="title">扣款方式</p>
            <div class="content">
                <span class="tab">扣款方式：</span>
                <el-radio-group v-model="cardInstallVar[0].install_value">
                    <el-radio label="1">按比例扣款<span class="tips" :class="{active:cardInstallVar[0].install_value == 1}">赠金 ÷ (本金 + 赠金)</span></el-radio>
                    <el-radio label="0">本金优先</el-radio>
                </el-radio-group>
            </div>
        </div>
        <div class="item">
            <p class="title">卡组管理</p>
            <div class="content">
                <span class="tab">加入卡组：</span>
                <el-radio-group v-model="cardInstallVar[1].install_value">
                    <el-radio label="0">单卡加入多个卡组</el-radio>
                    <el-radio label="1">单卡加入单个卡组</el-radio>
                </el-radio-group>
            </div>
        </div>
        <div class="item">
            <div class="content">
                <el-button type="primary" @click="setting">确认</el-button>
            </div>
        </div>
    </div>
</template>

<script>
import { getCardConfig , setting } from "./api/index"
import {mapGetters} from 'vuex'
export default {
    name: 'Setting',
    data () {
        return {
            cardInstallVar:[
                {
                    id:"",
                    type:"",
                    install_value:"",
                    extend:""
                },
                {
                    id:"",
                    type:"",
                    install_value:"",
                    extend:""
                },
            ]
        }
    },
    mounted(){
        if(this.getCurrentStation.merchant_type == undefined || this.getCurrentStation.merchant_type == 0){
            return false;
        }
        this.getCardConfig();
    },
    computed:{
        ...mapGetters({
            "getCurrentStation":"getCurrentStation"
        })
    },
    methods: {
        //获取配置
        async getCardConfig(){
            //获取配置
            let res = await getCardConfig();
            if(res.data && res.data.status == 200){
                let data = res.data.data;

                data.forEach(element => {
                    if(element.Type == 'ZHFP'){
                        this.cardInstallVar[0].id = element.ID;
                        this.cardInstallVar[0].type = element.Type;
                        this.cardInstallVar[0].install_value = String(element.InstallValue);
                        this.cardInstallVar[0].extend = element.Extend;
                    }
                    if(element.Type == 'CardGroup'){
                        this.cardInstallVar[1].id = element.ID;
                        this.cardInstallVar[1].type = element.Type;
                        this.cardInstallVar[1].install_value = String(element.InstallValue);
                        this.cardInstallVar[1].extend = element.Extend;
                    }
                });
            }else{
                this.$message({
                    message: res.data.info,
                    type: 'error'
                });
            }
        },
        //系统设置
        setting(){
            setting({
                cardInstallVar:this.cardInstallVar
            }).then(res=>{
                if(res.data.status == 200){
                    this.$message({
                        message: "设置成功",
                        type: 'success'
                    });
                }else{
                    this.$message({
                        message: res.data.info,
                        type: 'error'
                    });
                }
            })
        }
    },
    watch:{
        getCurrentStation(newValue,oldValue){
            if(newValue.merchant_type != 0 && newValue.value != oldValue.value){
                this.getCardConfig();
            }
        }
    }
}
</script>

<style scoped>
.setting{
    font-size: 14px;
    color: #333;
}
.setting .item:last-child{
    padding: 50px 120px;
}
.setting .title{
    border-bottom: 1px solid #EAEAEA;
    font-weight: bolder;
    height: 60px;
    line-height: 60px;
}
.setting .content{
    padding: 20px 0 0;
}
.setting .content .tips{
    color:#999;
    padding-left: 10px;
}
.setting .content .tips.active{
    color: #32AF50;
}
.setting .tab{
    padding-left: 30px;
}
</style>
