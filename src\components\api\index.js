// 导入封装好的网络请求类工具
import Network from './network';

// 封装各种接口请求
// export const 接口名 = () => Network.get('/路由',参数对象);

//公共API
export const getStationList = (params) => Network.post('/Stations/getStationList', {});

//卡系统设置
export const getCardConfig = () => Network.get('/CardConfig/cardConfig', {});
export const setting = (params) => Network.post('/CardConfig/set', params);

//下载中心
export const getDowloadList = (params) => Network.get('/download/list', params);
export const downloadExcel = (params) => Network.get('/download/url', params);
export const deleteData = (params) => Network.post('/download/delete', params);

//客户油品销售表
export const getCompanySaleOilSumDetail = (params) => Network.post('/CardReport/getCompanySaleOilSumDetail', params);
export const getCompanySaleOilSum = (params) => Network.post('/CardReport/getCompanySaleOilSum', params);
export const companySaleOilSumDetailDownLoad = (params) => Network.post('/CardReport/companySaleOilSumDetailDownLoad', params);