webpackJsonp([38],{"9fch":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a("Xxa5"),i=a.n(n),o=a("exGp"),l=a.n(o),s=a("Dd8w"),r=a.n(s),c=a("NYxO"),p={name:"FleetTopUpInvoiceReport",components:{DownloadTips:a("FZmr").a},data:function(){var e=this;return{companyOptions:[{ID:"",CompanyName:"全部"}],value:[],tableData:[],page:1,pageSize:10,total:0,loading:!1,dateValue:["",""],orderMaker:"",orderMakingTime:"",isGroup:!0,showDownloadTips:!1,pickerOptions:{onPick:function(t){t?(console.log(t),e.pickerMinDate=new Date(t.minDate).getTime(),console.log(e.pickerMinDate)):e.pickerMinDate=""},disabledDate:function(t){if(e.pickerMinDate){var a=e.pickerMinDate+53568e5,n=e.pickerMinDate-53568e5;return t.getTime()>=a||t.getTime()<=n}return!1}}}},mounted:function(){var e=this.$moment(new Date).subtract(1,"days").format("YYYY-MM-DD HH:mm:ss"),t=this.$moment().format("YYYY-MM-DD HH:mm:ss");this.dateValue=[e,t],this.getCompanyList(),this.getCompanyInvoice()},computed:r()({},Object(c.c)({getCurrentStation:"getCurrentStation"})),methods:{getCompanyList:function(){var e=this;e.$axios.post("/CompanyCard/getSimpleCompanyList",{page:1,page_size:1250,input:""}).then(function(t){200==t.data.status?(e.companyOptions=[{ID:"",CompanyName:"全部"}],e.companyOptions=e.companyOptions.concat(t.data.data.dt)):e.$message({message:t.data.info,type:"error"})}).catch(function(e){})},selectCompany:function(e){console.log(e),console.log(this.value),this.page=1,this.pageSize=10},selectDate:function(){var e=this;if(this.dateValue){var t=this.$moment(this.dateValue[0]).format("YYYY-MM-DD HH:mm:ss"),a=this.$moment(this.dateValue[1]).format("YYYY-MM-DD HH:mm:ss");console.log(t),console.log(a),this.dateValue=[t,a]}else this.tableData=[],this.orderMakingTime="",this.orderMaker="",this.dateValue=["",""];this.pickerMinDate="",this.pickerOptions={onPick:function(t){t?(console.log(t),e.pickerMinDate=new Date(t.minDate).getTime(),console.log(e.pickerMinDate)):e.pickerMinDate=""},disabledDate:function(t){if(e.pickerMinDate){var a=e.pickerMinDate+53568e5,n=e.pickerMinDate-53568e5;return t.getTime()>=a||t.getTime()<=n}return!1}}},getCompanyInvoiceAction:function(){this.page=1,this.pageSize=10,this.getCompanyInvoice()},getCompanyInvoice:function(){var e=this;this.loading=!0,e.$axios.get("/CompanyManager/companyInvoice",{params:{page:this.page,page_size:this.pageSize,company_id:this.value,start_time:this.dateValue[0],end_time:this.dateValue[1]}}).then(function(t){if(200==t.data.status){e.tableData=[],e.tableData=e.tableData.concat(t.data.data.list),e.total=t.data.data.total,e.orderMakingTime=e.$moment().format("YYYY-MM-DD");var a=localStorage.getItem("__userInfo__");!a||""===a&&"undefined"===a||(e.orderMaker=JSON.parse(a).name),console.log(e.tableData)}else e.$message({message:t.data.info,type:"error"});e.loading=!1}).catch(function(t){e.loading=!1})},downLoadAction:function(){var e=this;this.loading=!0;var t={page:this.page,page_size:this.pageSize,company_id:this.value,start_time:this.dateValue[0],end_time:this.dateValue[1]};e.$axios.get("/CompanyManager/downloadCompanyInvoice",{params:t}).then(function(t){e.showDownloadTips=!0,e.loading=!1}).catch(function(t){e.loading=!1})},handleCurrentChange:function(e){console.log(e),this.page=e,this.getCompanyInvoice()},handleSizeChange:function(e){console.log(e),this.pageSize=e,this.getCompanyInvoice()},printAction:function(){this.currentPage=1,this.tableData=[],this.loading=!0,this.getPrintData()},getPrintData:function(){var e=this;return l()(i.a.mark(function t(){var a,n,o,l,s;return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return a={page:e.currentPage,page_size:20,company_id:e.value,start_time:e.dateValue[0],end_time:e.dateValue[1]},t.next=3,e.$axios.get("/CompanyManager/companyInvoice",{params:a}).catch(function(t){e.$message.error("出错了哦，请重试"),e.loading=!1});case 3:if(200==(n=t.sent).data.status)if(e.tableData=e.tableData.concat(n.data.data.list),20*e.currentPage>=n.data.data.total){for(e.loading=!1,o=[],l=0;l<e.tableData.length;l++)(s=e.tableData[l]).CompanyName&&o.push(s);e.tableData=o,e.printContent()}else e.currentPage=e.currentPage+1,e.getPrintData();else e.$message.error("出错了哦，请重试"),e.loading=!1;case 5:case"end":return t.stop()}},t,e)}))()},printContent:function(){this.showAllTitle=!0,this.$nextTick(function(){var e=document.querySelector("#myTable").innerHTML,t=document.body.innerHTML;document.body.innerHTML=e;for(var a=document.getElementsByClassName("el-table__header"),n=document.getElementsByClassName("el-table__body"),i=document.getElementsByClassName("el-table__body-wrapper"),o=document.getElementsByClassName("el-table__empty-block"),l=0;l<a.length;l++)a[l].style.width="100%",a[l].style["table-layout"]="auto";for(var s=0;s<n.length;s++)n[s].style.width="100%",n[s].style["table-layout"]="auto";for(var r=0;r<i.length;r++)i[r].style["overflow-x"]="hidden";for(var c=0;c<o.length;c++)o[c]&&(o[c].style.width="100%");window.print(),history.go(0),document.body.innerHTML=t})}}},d={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"header"},[a("div",{staticStyle:{display:"flex","align-items":"center"}},[a("el-date-picker",{attrs:{"default-time":["00:00:00","23:59:59"],"picker-options":e.pickerOptions,type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:e.selectDate},model:{value:e.dateValue,callback:function(t){e.dateValue=t},expression:"dateValue"}}),e._v(" "),a("div",{staticStyle:{"margin-left":"20px","margin-right":"20px"}},[e._v("车队名称：")]),e._v(" "),a("el-select",{attrs:{filterable:"",multiple:"",clearable:"","collapse-tags":"",placeholder:"请选择"},on:{change:e.selectCompany},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}},e._l(e.companyOptions,function(e){return a("el-option",{key:e.ID,attrs:{label:e.CompanyName,value:e.ID}})}),1),e._v(" "),a("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary"},on:{click:e.getCompanyInvoiceAction}},[e._v("生成")])],1),e._v(" "),a("div",{staticStyle:{display:"flex"}},[a("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary"},on:{click:e.printAction}},[e._v("打印")]),e._v(" "),a("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary"},on:{click:e.downLoadAction}},[e._v("下载数据")])],1)]),e._v(" "),a("div",{staticStyle:{"margin-top":"40px"},attrs:{id:"myTable"}},[a("div",{staticStyle:{"text-align":"center","font-size":"24px","font-weight":"600"}},[e._v("车队充值开票汇总报表")]),e._v(" "),a("div",{staticClass:"report_header"},[e.isGroup?a("div",[e._v("集团名称："+e._s(e.getCurrentStation.label))]):a("div",[e._v("油站名称："+e._s(e.getCurrentStation.label))]),e._v(" "),a("div",[e._v("开始日期："+e._s(e.dateValue?e.dateValue[0]:""))]),e._v(" "),a("div",[e._v("结束日期："+e._s(e.dateValue?e.dateValue[1]:""))]),e._v(" "),a("div",[e._v("单位：元")])]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",staticStyle:{width:"100%"},attrs:{align:"center",data:e.tableData}},[a("el-table-column",{attrs:{prop:"CompanyID",label:"车队ID",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"CompanyName",label:"车队名称",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"CompanyContacts",label:"管理员",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"ContactsPhone",label:"联系方式",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{label:"充值总金额",align:"center"}},[a("el-table-column",{attrs:{prop:"TOTAL_BJ",label:"本金",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"TOTAL_SKJ",label:"赠金",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"TOTAL",label:"小计",align:"center"}})],1),e._v(" "),a("el-table-column",{attrs:{prop:"KPMoney",label:"已开票总金额",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"WKPMoney",label:"未开票总金额",align:"center"}})],1),e._v(" "),a("div",{staticClass:"des_bottom",staticStyle:{"margin-top":"20px"}},[a("div",[e._v("制表人："+e._s(e.orderMaker))]),e._v(" "),a("div",[e._v("制表时间："+e._s(e.orderMakingTime))]),e._v(" "),a("div",[e._v("签字：")])])],1),e._v(" "),a("div",{staticClass:"page_content"},[a("el-pagination",{staticClass:"page_left",attrs:{"current-page":e.page,"page-size":e.pageSize,layout:"prev, pager, next",total:e.total},on:{"current-change":e.handleCurrentChange}}),e._v(" "),a("el-pagination",{staticClass:"page_right",attrs:{"page-sizes":[10,20,30,40],"page-size":e.pageSize,layout:"total, sizes",total:e.total},on:{"size-change":e.handleSizeChange}})],1),e._v(" "),a("download-tips",{attrs:{showDownloadTips:e.showDownloadTips},on:{"update:showDownloadTips":function(t){e.showDownloadTips=t},"update:show-download-tips":function(t){e.showDownloadTips=t}}})],1)},staticRenderFns:[]};var g=a("VU/8")(p,d,!1,function(e){a("iCHr")},"data-v-3bf5f7cf",null);t.default=g.exports},iCHr:function(e,t){}});